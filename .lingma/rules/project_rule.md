### **開發規範（適用於 Java 8）**

---

## **一、開發原則**
在開發過程中，請嚴格遵循以下原則：
- **SOLID 原則**：確保程式碼的可維護性、可擴展性
- **DRY（Don't Repeat Yourself）**：避免重複程式碼，提升可讀性
- **KISS（Keep It Simple, Stupid）**：保持程式碼簡單直覺
- **YAGNI（You Ain't Gonna Need It）**：避免過度設計，僅實作當前需求
- **OWASP 安全最佳實踐**：防範 SQL 注入、XSS、CSRF 等安全漏洞
- **分層架構設計**：確保系統模組間的職責分離
- **單元測試覆蓋率 ≥ 80%**：確保程式碼品質，降低潛在錯誤

---

## **二、技術棧**
### **1. 開發框架**
- **後端框架**：Spring Boot 2.x + Java 8
- **核心依賴**：
  - Spring Web、Spring Data JPA、Lombok
  - 資料庫：PostgreSQL Driver 或其他關聯式資料庫驅動
  - 其他：Swagger（SpringDoc）、Spring Security（如需權限控管）

---

## **三、應用邏輯架構**
### **1. 分層架構**
| 層級        | 職責                                             | 限制條件                                                |
|-------------|--------------------------------------------------|---------------------------------------------------------|
| **Controller** | 負責處理 HTTP 請求與回應，定義 API 介面         | - 禁止直接存取資料庫，必須透過 Service 層呼叫           |
| **Service**    | 負責業務邏輯實作、交易管理、資料驗證            | - 必須透過 Repository 存取資料庫，回傳 DTO 而非 Entity |
| **Repository** | 負責資料存取，定義查詢邏輯                     | - 必須繼承 `JpaRepository`，使用 `@EntityGraph` 避免 N+1 問題 |
| **Entity**     | 資料庫表格對應的物件                           | - 僅作為資料庫存取用途，不可直接回傳至前端              |

---

## **四、程式碼規範**
### **1. 實體類（Entity）**
```java
@Entity
@Data // Lombok 註解
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "使用者名稱不得為空")
    @Size(min = 3, max = 50)
    private String username;

    @Email
    private String email;

    // 關聯關係應使用 LAZY 加載
    @ManyToOne(fetch = FetchType.LAZY)
    private Department department;
}
```

### **2. 資料存取層（Repository）**
```java
public interface UserRepository extends JpaRepository<User, Long> {
    // 依名稱查詢
    Optional<User> findByUsername(String username);

    // 使用 JPQL 自訂查詢
    @Query("SELECT u FROM User u JOIN FETCH u.department WHERE u.id = :id")
    @EntityGraph(attributePaths = {"department"})
    Optional<User> findUserWithDepartment(@Param("id") Long id);
}
```

### **3. 服務層（Service）**
```java
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserRepository userRepository;

    @Transactional
    public ApiResponse<UserDTO> createUser(UserDTO dto) {
        // 業務邏輯
        User user = new User();
        user.setUsername(dto.getUsername());
        User savedUser = userRepository.save(user);
        return ApiResponse.success(UserDTO.fromEntity(savedUser));
    }
}
```

### **4. 控制器（RestController）**
```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    @Autowired
    private UserService userService;

    @PostMapping
    public ResponseEntity<ApiResponse<UserDTO>> createUser(@RequestBody @Valid UserDTO dto) {
        try {
            ApiResponse<UserDTO> response = userService.createUser(dto);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return GlobalExceptionHandler.errorResponseEntity(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
}
```

---

## **五、資料傳輸物件（DTO）**
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    @NotBlank
    private String username;

    @Email
    private String email;

    public static UserDTO fromEntity(User entity) {
        return new UserDTO(entity.getUsername(), entity.getEmail());
    }
}
```

---

## **六、錯誤處理**
### **1. 通用回應格式（ApiResponse）**
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private String result; // SUCCESS/ERROR
    private String message;
    private T data;

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>("SUCCESS", "操作成功", data);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>("ERROR", message, null);
    }
}
```

### **2. 全域例外處理（GlobalExceptionHandler）**
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    @ExceptionHandler(EntityNotFoundException.class)
    public ResponseEntity<ApiResponse<?>> handleEntityNotFound(EntityNotFoundException ex) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(ApiResponse.error(ex.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<?>> handleValidationErrors(MethodArgumentNotValidException ex) {
        String errorMessage = ex.getBindingResult()
            .getFieldErrors()
            .stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));
        return ResponseEntity.badRequest().body(ApiResponse.error(errorMessage));
    }
}
```

---

## **七、安全與效能最佳實踐**
1. **輸入驗證**：
   - 使用 `@Valid` 搭配 JSR-303 驗證（如 `@NotBlank`, `@Size`）
   - 避免直接拼接 SQL，防止 SQL 注入攻擊
2. **交易管理**：
   - `@Transactional` 只能標記在 Service 方法上
   - 避免在迴圈內頻繁提交交易
3. **效能優化**：
   - 使用 `@EntityGraph` 預加載關聯資料
   - 避免迴圈中多次查詢資料庫，應使用批次查詢

---

## **八、程式碼風格**
1. **命名規則**：
   - **類別名稱**：`UpperCamelCase`（例：`UserServiceImpl`）
   - **方法/變數名稱**：`lowerCamelCase`（例：`saveUser`）
   - **常數名稱**：`UPPER_SNAKE_CASE`（例：`MAX_LOGIN_ATTEMPTS`）
2. **註解規則**：
   - 所有方法必須加上 Javadoc 格式註解
   - **待辦事項** 用 `// TODO` 註明
   - **潛在錯誤點** 用 `// FIXME` 註明
3. **程式碼格式**：
   - 使用 IntelliJ IDEA 預設 Spring Boot 格式化
   - 禁止手動調整縮排，應透過 IDE 自動格式化

---

## **九、部署規範**
1. **環境管理**：
   - 透過 `application.properties` 來外部化配置
   - 使用 `Spring Profiles` 區分環境（如 `dev`, `prod`）
2. **安全建議**：
   - **避免記錄敏感資訊**（如密碼、token）
   - **關閉不必要的端口**，限制對外服務範圍

---

## **十、擴展性設計**
1. **介面優先**：
   - Service 層應先定義介面（`UserService`），實作類（`UserServiceImpl`）應獨立
2. **預留擴展點**：
   - 核心業務應提供 `Strategy` 或 `Template` 模式支援擴展
3. **日誌記錄**：
   - 使用 `SLF4J` 記錄日誌（禁止使用 `System.out.println`）
   - 核心操作使用 `INFO` 級別，錯誤使用 `ERROR` 級別