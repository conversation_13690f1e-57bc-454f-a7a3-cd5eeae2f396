variables:
  RUN_TASK_CMD: "bash -x ./builds/run-tasks.sh"
  PROJECT_NAME: "carplus_subscribe_${CI_JOB_ID}"
  CI_MODE: "true"
  TEAM_CHANNEL: '#cicd-notify'
  TEAM_CHANNEL_ALERT: '#cicd-alert'

  BUILD_START_NOTIFY_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":fast_forward: [`$CI_PROJECT_NAME`] Start to build image, URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"
  START_NOTIFY_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":fast_forward: [`$CI_PROJECT_NAME`] Start to deploy in $STAGE, URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"
  BUILD_SUCCESS_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":o: [`$CI_PROJECT_NAME`] image ${CI_REGISTRY_IMAGE} pushed success, URL: $CI_JOB_URL\"}"
  SUCCESS_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":o: [`$CI_PROJECT_NAME`] $STAGE deployed success, URL: $CI_JOB_URL\"}"
  FAIL_JSON: "{\"channel\":\"$TEAM_CHANNEL_ALERT\",\"text\":\":warning: [`$CI_PROJECT_NAME`] $STAGE deployed fail, URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"
  BUILD_FAIL_JSON: "{\"channel\":\"$TEAM_CHANNEL_ALERT\",\"text\":\":warning: [`$CI_PROJECT_NAME`] build image fail, URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"
  TAG_SUCCESS_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":o: [`$CI_PROJECT_NAME:$CI_COMMIT_TAG`] $STAGE deployed success \n URL: $CI_JOB_URL \n Release Note: $CI_PROJECT_URL/-/tags/$CI_COMMIT_TAG\"}"
  TAG_FAIL_JSON: "{\"channel\":\"$TEAM_CHANNEL_ALERT\",\"text\":\":x: [`$CI_PROJECT_NAME:$CI_COMMIT_TAG`] $STAGE deployed fail, \n URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"
  SONAR_SUCCESS_JSON: "{\"channel\":\"$TEAM_CHANNEL\",\"text\":\":mag: [`$CI_PROJECT_NAME`] **SonarQube** analysis success \n URL: $SONARQUBE_HOST_PUBLIC/dashboard?id=$CI_PROJECT_NAME, triggered by @$GITLAB_USER_LOGIN\"}"
  SONAR_FAIL_JSON: "{\"channel\":\"$TEAM_CHANNEL_ALERT\",\"text\":\":warning: [`$CI_PROJECT_NAME`] **SonarQube** analysis fail \n URL: $CI_JOB_URL, triggered by @$GITLAB_USER_LOGIN\"}"

stages:
  - push_image
  - deploy

push_image:latest:
  before_script:
    - echo > .status
    - echo -n ${ARTIFACT_REGISTRY_TOKEN} | docker login -u _json_key_base64 --password-stdin https://${ARTIFACT_REGISTRY_HOST}
    - curl -X POST -H "Content-Type:application/json" -d "${BUILD_START_NOTIFY_JSON}" "${CICD_WEB_HOOK}"
  image: ${ARTIFACT_REGISTRY_HOST}/share/util/docker
  stage: push_image
  script:
    - docker build -f Dockerfile --no-cache --build-arg SONARHOST=${SONARQUBE_HOST} --build-arg SONARTOKEN=${SONARQUBE_TOKEN} . -t ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}:latest
    - docker push ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}:latest
    - echo success > .status
  artifacts:
    paths:
      - .status
  only:
    - develop
  after_script:
    - if [ "$(cat .status)" == "success" ]; then curl -X POST -H "Content-Type:application/json" -d "${SONAR_SUCCESS_JSON}" "${CICD_WEB_HOOK}"; else curl -X POST -H "Content-Type:application/json" -d "${SONAR_FAIL_JSON}" "${CICD_WEB_HOOK_ALERT}"; fi
    - if [ "$(cat .status)" == "success" ]; then curl -X POST -H "Content-Type:application/json" -d "${BUILD_SUCCESS_JSON}" "${CICD_WEB_HOOK}"; else curl -X POST -H "Content-Type:application/json" -d "${BUILD_FAIL_JSON}" "${CICD_WEB_HOOK_ALERT}"; fi


push_image:cache:
  before_script:
    - echo > .status
    - echo -n ${ARTIFACT_REGISTRY_TOKEN} | docker login -u _json_key_base64 --password-stdin https://${ARTIFACT_REGISTRY_HOST}
    - curl -X POST -H "Content-Type:application/json" -d "${BUILD_START_NOTIFY_JSON}" "${CICD_WEB_HOOK}"
  image: ${ARTIFACT_REGISTRY_HOST}/share/util/docker
  stage: push_image
  script:
    - docker build -f Dockerfile.cache --no-cache --build-arg SONARHOST=${SONARQUBE_HOST} --build-arg SONARTOKEN=${SONARQUBE_TOKEN} . -t ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}/cache:latest
    - docker push ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}/cache:latest
    - echo success > .status
  when: manual
  artifacts:
    paths:
      - .status
  only:
    - develop
    - ci
  after_script:
    - if [ "$(cat .status)" == "success" ]; then curl -X POST -H "Content-Type:application/json" -d "${BUILD_SUCCESS_JSON}" "${CICD_WEB_HOOK}"; else curl -X POST -H "Content-Type:application/json" -d "${BUILD_FAIL_JSON}" "${CICD_WEB_HOOK_ALERT}"; fi

push_image:release:
  before_script:
    - echo -n ${ARTIFACT_REGISTRY_TOKEN} | docker login -u _json_key_base64 --password-stdin https://${ARTIFACT_REGISTRY_HOST}
  image: ${ARTIFACT_REGISTRY_HOST}/share/util/docker
  stage: push_image
  script:
    - docker build -f Dockerfile --no-cache --build-arg SONARHOST=${SONARQUBE_HOST} --build-arg SONARTOKEN=${SONARQUBE_TOKEN} . -t ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}:${CI_COMMIT_TAG}
    - docker push ${ARTIFACT_REGISTRY_HOST}/${CI_PROJECT_NAME}:${CI_COMMIT_TAG}
    - echo success > .status
  artifacts:
    paths:
      - .status
  only:
    - tags
    

deploy:int:
  before_script:
    - echo > .status
    - curl -X POST -H "Content-Type:application/json" -d "${START_NOTIFY_JSON}" "${CICD_WEB_HOOK}"
  image: ${ARTIFACT_REGISTRY_HOST}/deployer/ansible-docker/********:latest
  stage: deploy
  variables:
    PROJECT_NAME: ${CI_PROJECT_NAME}
    ARTIFACT_REGISTRY_HOST: ${ARTIFACT_REGISTRY_HOST}
    DEPLOYER_PROJECT_NAME: deployer/${CI_PROJECT_NAME}
    DEPLOYER_PROJECT_BRANCHE: feature
    STAGE: int
  only:
    - develop
  script:
    - sh -x /bin/update.sh
    - echo success > .status
  artifacts:
    paths:
      - .status
  after_script:
    - if [ "$(cat .status)" == "success" ]; then curl -X POST -H "Content-Type:application/json" -d "${SUCCESS_JSON}" "${CICD_WEB_HOOK}"; else curl -X POST -H "Content-Type:application/json" -d "${FAIL_JSON}" "${CICD_WEB_HOOK_ALERT}"; fi
