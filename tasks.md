# Implementation Plan

- [ ] 1. Create enhanced amount calculation utilities
  - Implement `AccountAmountCalculator` class with proper net payment amount calculation
  - Implement `TransactionAmountCalculator` class for aggregating order price info amounts
  - Add JSON parsing for `orderPriceAmounts` field with negative amount handling
  - Write unit tests for both calculator classes covering positive, negative, and mixed scenarios
  - _Requirements: 1.1, 1.2, 3.1, 3.5_

- [ ] 2. Implement tolerance-based amount comparison
  - Create `AmountComparator` class with configurable tolerance (default 0.01)
  - Add method to compare BigDecimal amounts within acceptable tolerance
  - Include logic to handle floating-point precision issues
  - Write unit tests for exact matches, within-tolerance, and out-of-tolerance scenarios
  - _Requirements: 1.3, 3.3_

- [ ] 3. Enhance CheckoutService validation logic
  - Modify `validateAmounts()` method to use new calculator classes
  - Replace existing payment amount calculation with `AccountAmountCalculator`
  - Replace existing transaction amount calculation with `TransactionAmountCalculator`
  - Integrate tolerance-based comparison using `AmountComparator`
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 4. Implement comprehensive validation logging
  - Add detailed logging in `calculateTotalPaymentAmount()` method
  - Add detailed logging in `calculateTotalTransactionAmount()` method
  - Log individual account records and their contributions to total
  - Log individual order price info items and their amounts
  - Log final comparison results with actual values and differences
  - _Requirements: 2.1, 2.2, 2.3, 2.5_

- [ ] 5. Create detailed error message builder
  - Implement `ValidationErrorBuilder` class for comprehensive error messages
  - Include payment breakdown showing total amount and refund components
  - Include transaction breakdown showing positive and negative amounts
  - Show actual calculated values and the difference between them
  - Replace generic error message with detailed breakdown
  - _Requirements: 1.4, 2.3_

- [ ] 6. Add ETag payment handling
  - Enhance payment calculation to properly include ETag amounts
  - Add specific logging for ETag payment processing
  - Ensure ETag payments are correctly aggregated in total payment amount
  - Write unit tests for orders with ETag payments
  - _Requirements: 2.4, 3.4_

- [ ] 7. Write comprehensive unit tests for validation logic
  - Create test cases for orders with only positive amounts
  - Create test cases for orders with mixed positive and negative amounts (refunds)
  - Create test cases for floating-point precision scenarios
  - Create test cases for ETag payment integration
  - Create test cases that verify detailed error message generation
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 8. Create integration tests with real data scenarios
  - Write integration test using the failing order M202507219932 data structure
  - Test complete settlement flow with various payment combinations
  - Test database integration with complex account record scenarios
  - Verify invoice integration works correctly with enhanced validation
  - _Requirements: 1.1, 1.2, 1.3, 3.2_

- [ ] 9. Implement validation result tracking and monitoring
  - Add structured logging for validation results
  - Create metrics for validation success/failure rates
  - Add monitoring for validation performance
  - Implement alerting for validation failures with detailed context
  - _Requirements: 2.5, 1.5_

- [ ] 10. Performance optimization and final integration
  - Optimize database queries for order price info and account records
  - Implement conditional logging based on log levels
  - Add caching for repeated calculations where appropriate
  - Integrate all components into existing CheckoutService
  - Verify the fix resolves the original failing scenario from logs
  - _Requirements: 1.1, 1.2, 1.3, 2.1, 2.2, 2.3_