# Account Settlement 修正的單元測試

## 問題背景

根據 `accountSettlement_log.csv` 中的錯誤日誌，訂單 `M202507219932` 在執行收支登打時出現錯誤：
```
立帳失敗，參數錯誤: 付款金額和其下的交易項目金額合計不同
```

## 問題原因

當同時包含收款和退款時，系統會創建兩筆 `AccountDetail`：
1. 收款 AccountDetail (amount: 26297)
2. 退款 AccountDetail (amount: -2898)

但是**兩筆都記錄了相同的完整 `orderPriceAmounts` Map**，導致立賬時重複計算這些項目。

## 測試數據 (來自實際 log)

```
訂單號: M202507219932
accountId: 407457
收款金額: 26297
退款金額: 2898
orderPriceAmounts: {"73880":20400,"73883":2999,"73882":2898,"73884":-2898}
應收總額: 23399
```

## 單元測試

我們提供了三種類型的測試：

### 🌟 1. 純邏輯測試：`AccountDetailLogicTest` （強烈推薦）

**優點**：
- ✅ 完全不依賴數據庫、Spring 或任何外部服務
- ✅ 運行速度快，執行穩定
- ✅ 直接模擬修正後的邏輯，清楚展示問題和解決方案
- ✅ 使用 JUnit 4，與現有測試框架一致

**測試內容**：
- `testCorrectedLogic_SeparatesChargeAndRefundAmounts`：驗證修正後的邏輯
- `testOldLogic_DuplicateOrderPriceAmounts_ShouldFail`：演示舊邏輯問題
- `testOnlyChargeAmount`：邊界測試（只有收款）
- `testOnlyRefundAmount`：邊界測試（只有退款）

### 2. 反射測試：`PaymentServiceV2UnitTest`

**特點**：
- 使用反射調用真實的 `getNewAccountDetails` 私有方法
- 使用 JUnit 5，但不依賴數據庫
- 需要實例化 PaymentServiceV2 對象

### 3. 集成測試：`PaymentServiceV2Test`

**特點**：
- 需要 Spring 容器和數據庫連接
- 使用真實的 Service 注入
- 適合集成測試和回歸測試

## 運行測試

### 純單元測試（不依賴數據庫/Spring）- 強烈推薦

```bash
# 運行完整的邏輯測試類
mvn test -Dtest=AccountDetailLogicTest

# 運行特定測試方法
mvn test -Dtest=AccountDetailLogicTest#testCorrectedLogic_SeparatesChargeAndRefundAmounts
mvn test -Dtest=AccountDetailLogicTest#testOldLogic_DuplicateOrderPriceAmounts_ShouldFail
```

### 純單元測試（使用 JUnit 5）

```bash
# 運行完整的單元測試類
mvn test -Dtest=PaymentServiceV2UnitTest

# 運行特定測試方法
mvn test -Dtest=PaymentServiceV2UnitTest#testGetNewAccountDetails_CorrectlySeparatesChargeAndRefundAmounts
mvn test -Dtest=PaymentServiceV2UnitTest#demonstrateOldLogicProblem
```

### 集成測試（需要數據庫）

```bash
# 運行特定測試類
mvn test -Dtest=PaymentServiceV2Test

# 或運行特定測試方法
mvn test -Dtest=PaymentServiceV2Test#testAccountSettlement_M202507219932_DuplicateOrderPriceAmounts
mvn test -Dtest=PaymentServiceV2Test#testOldLogic_DuplicateOrderPriceAmounts_ShouldFail
```

## 修正內容

在 `PaymentServiceV2.getNewAccountDetails()` 方法中：

```java
// 修正前：兩筆 AccountDetail 都記錄完整的 orderPriceAmounts
newAccountDetails.add(new AccountDetail(dbAccount.getId(), newAmount, dbAccount.getOrderPriceAmounts()));
newAccountDetails.add(new AccountDetail(dbAccount.getId(), newRefundAmount, dbAccount.getOrderPriceAmounts()));

// 修正後：分離收款和退款的 orderPriceInfo 項目
Map<Integer, Integer> chargeAmounts = new HashMap<>(); // 只包含正數項目
Map<Integer, Integer> refundAmounts = new HashMap<>();  // 只包含負數項目

orderPriceAmounts.forEach((priceInfoId, amount) -> {
    if (amount > 0) {
        chargeAmounts.put(priceInfoId, amount);
    } else if (amount < 0) {
        refundAmounts.put(priceInfoId, amount);
    }
});

newAccountDetails.add(new AccountDetail(dbAccount.getId(), newAmount, chargeAmounts));
newAccountDetails.add(new AccountDetail(dbAccount.getId(), newRefundAmount, refundAmounts));
```

## 預期結果

測試通過後，確認：
1. ✅ 修正後的邏輯正確分離收款和退款項目
2. ✅ 不再出現重複計算問題
3. ✅ 立賬時付款金額與交易項目金額一致
4. ✅ 避免「付款金額和其下的交易項目金額合計不同」錯誤