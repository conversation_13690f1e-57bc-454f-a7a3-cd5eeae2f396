package com.carplus.subscribe.config;

import com.carplus.subscribe.enums.NotifyType;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.carplus.subscribe.constant.CarPlusConstant.*;

@Setter
@Component
@ConfigurationProperties
public class AppProperties {

    private static String env;
    private Carplus carplus;
    private Notifications notifications;
    private GCS gcs;

    public static class Carplus {
        private static Service service;

        public static class Service {
            private static String feOfficial;
            private static String feSOfficial;

            public static String getFeOfficial() {
                return feOfficial;
            }

            public static String getFeSOfficial() {
                return feSOfficial;
            }

            public void setFeOfficial(String feOfficial) {
                Service.feOfficial = feOfficial;
            }

            public void setFeSOfficial(String feSOfficial) {
                Service.feSOfficial = feSOfficial;
            }
        }

        public void setService(Service service) {
            Carplus.service = service;
        }
    }

    public static class GCS {
        private static String url;

        public static String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            GCS.url = url;
        }
    }

    public static class Notifications {
        private static Test test;

        public static class Test {
            private static boolean enabled;
            private static List<String> to;
            private static List<String> cc;

            public static boolean isEnabled() {
                return enabled;
            }

            public static List<String> getTo() {
                return to;
            }

            public static List<String> getCc() {
                return cc;
            }

            public void setEnabled(boolean enabled) {
                Test.enabled = enabled;
            }

            public void setTo(List<String> to) {
                Test.to = to;
            }

            public void setCc(List<String> cc) {
                Test.cc = cc;
            }
        }

        private static Map<NotifyType.NotifyDepartment, String> emails = new EnumMap<>(NotifyType.NotifyDepartment.class);


        public static Map<NotifyType.NotifyDepartment, String> getEmails() {
            return emails;
        }

        public void setEmails(Map<NotifyType.NotifyDepartment, String> emails) {
            Notifications.emails = emails;
        }

        public void setTest(Test test) {
            Notifications.test = test;
        }
    }

    public static String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }


    /**
     * `https://www.int.car-plus.cool`
     */
    public static String getOfficialHost() {
        return AppProperties.Carplus.Service.getFeOfficial();
    }

    /**
     * 取得 Label by application config env : "【格上訂閱車】" / "【測試】"
     */
    public static String getStage() {
        return resolveEnv(AppProperties.getEnv());
    }

    /**
     * 取得完整URL : `https://s.int.car-plus.cool/s/SUBORDER`
     */
    public static String getFullShortOfficialUrl() {
        return buildFullUrl(AppProperties.Carplus.Service.getFeSOfficial());
    }

    private static String resolveEnv(String env) {
        return "prod".equals(env) ? CARPLUS_SUBSCRIBE_CAR : CARPLUS_SUBSCRIBE_CAR_TEST;
    }

    private static String buildFullUrl(String feSOfficial) {
        return feSOfficial + SHORT_URL;
    }

    /**
     * 取得 notification Emails
     */
    public static List<String> getNotifyEmails(@Nullable List<NotifyType.NotifyDepartment> departments, @Nullable List<String> additionalEmails, boolean isCC) {
        List<String> originalEmails = getEmails(departments, additionalEmails);
        if (testEnabled() || CollectionUtils.isEmpty(originalEmails)) {
            if (!isCC) {
                return replaceResolveMainEmails(originalEmails);
            } else {
                return replaceResolveCCEmails(originalEmails);
            }
        }
        return getEmails(departments, additionalEmails);
    }

    private static List<String> getEmails(List<NotifyType.NotifyDepartment> departments, List<String> additionalEmails) {
        Stream<String> configEmails = Optional.ofNullable(departments)
            .orElse(Collections.emptyList())
            .stream()
            .map(type -> AppProperties.Notifications.getEmails().get(type));

        Stream<String> otherEmails = Optional.ofNullable(additionalEmails)
            .map(Collection::stream)
            .orElse(Stream.empty());

        return Stream.concat(configEmails, otherEmails)
            .filter(Objects::nonNull)
//                .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 取得 notification Emails
     */
    public static List<String> resolveEmails(NotifyType.NotifyDepartment... departments) {
        return Arrays.stream(departments)
            .map(type -> AppProperties.Notifications.getEmails().get(type))
            .collect(Collectors.toList());
    }

    public static List<String> resolveEmails(List<NotifyType.NotifyDepartment> departments) {
        return departments.stream()
            .map(type -> AppProperties.Notifications.getEmails().get(type))
            .collect(Collectors.toList());
    }

    /**
     * 取得測試Emails
     */
    public static List<String> getTestMainEmails() {
        return AppProperties.Notifications.Test.getTo();
    }

    public static List<String> getTestCcEmails() {
        return AppProperties.Notifications.Test.getCc();
    }

    /**
     * 判斷測試是否啟用並替換成測試用Email
     */
    public static List<String> replaceResolveMainEmails(List<String> originalEmails) {
        List<String> replacedMain = getTestCcEmails();
        originalEmails.clear();
        originalEmails.addAll(replacedMain);
        return originalEmails;
    }

    public static List<String> replaceResolveCCEmails(List<String> originalEmails) {
        List<String> replacedCC = getTestMainEmails();
        originalEmails.clear();
        originalEmails.addAll(replacedCC);
        return originalEmails;
    }


    public static boolean testEnabled() {
        return AppProperties.Notifications.Test.isEnabled();
    }
}
