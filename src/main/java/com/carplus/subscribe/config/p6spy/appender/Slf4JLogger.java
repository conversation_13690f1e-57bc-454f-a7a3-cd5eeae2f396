package com.carplus.subscribe.config.p6spy.appender;


import com.carplus.subscribe.config.p6spy.properties.P6spyProperties;
import com.p6spy.engine.logging.Category;
import com.p6spy.engine.spy.P6SpyOptions;
import com.p6spy.engine.spy.appender.FormattedLogger;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class Slf4JLogger extends FormattedLogger {

    private final Map<String, String> defaults;
    private final boolean outagedetection;
    private final long outagedetectioninterval;
    private final String excludecategories;
    private final long executionThreshold;
    private final boolean p6spyLogInfoEnable;

    public Slf4JLogger() {

        defaults = P6SpyOptions.getActiveInstance().getDefaults();
        outagedetection = Boolean.valueOf(defaults.get(P6spyProperties.Fields.outagedetection));
        outagedetectioninterval = 1000L * Integer.valueOf(defaults.get(P6spyProperties.Fields.outagedetectioninterval));
        excludecategories = defaults.get(P6spyProperties.Fields.excludecategories);
        executionThreshold = 1000L * Integer.valueOf(defaults.get(P6spyProperties.Fields.executionThreshold));
        this.p6spyLogInfoEnable = Boolean.valueOf(defaults.get(P6spyProperties.Fields.outagedetection));
    }

    @Override
    public void logException(Exception e) {
    }

    @Override
    public void logText(String text) {
        if (!text.trim().isEmpty()) {
            log.info(text);
        }
    }

    @Override
    public void logSQL(int connectionId, String now, long elapsed, Category category, String prepared, String sql, String url) {
        if (excludecategories.indexOf(category.getName()) != -1) {
            return;
        }
        final String msg = strategy.formatMessage(connectionId, now, elapsed, category.toString(), prepared, sql, url);
        if (outagedetection && elapsed > outagedetectioninterval) {
            log.error("{}", msg);
        } else if (Category.ERROR.equals(category)) {
            log.error("{}", msg);
        } else if (Category.WARN.equals(category)) {
            log.warn("{}", msg);
        } else if (Category.DEBUG.equals(category)) {
            log.debug("{}", msg);
        } else {
            if (elapsed < executionThreshold) {
                return;
            }
            log.info("{}", msg);
        }
    }

    @Override
    public boolean isCategoryEnabled(Category category) {
        if (Category.ERROR.equals(category)) {
            return log.isErrorEnabled();
        } else if (Category.WARN.equals(category)) {
            return log.isWarnEnabled();
        } else if (Category.DEBUG.equals(category)) {
            return log.isDebugEnabled();
        } else {
            return log.isInfoEnabled() && p6spyLogInfoEnable;
        }
    }

}
