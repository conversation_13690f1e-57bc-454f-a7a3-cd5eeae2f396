package com.carplus.subscribe.config.p6spy.config;

import com.carplus.subscribe.config.p6spy.properties.P6spyProperties;
import com.p6spy.engine.spy.P6ModuleManager;
import com.p6spy.engine.spy.P6SpyDriver;
import com.p6spy.engine.spy.P6SpyOptions;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.lang.reflect.Field;
import java.util.Map;

@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(P6SpyDriver.class)
@EnableConfigurationProperties(P6spyProperties.class)
public class P6spyConfig implements ApplicationRunner {

    private final Environment environment;

    public P6spyConfig(Environment environment) {
        this.environment = environment;
    }

    public static void p6spyReload(Environment environment) {
        // p6spy預設
        Map<String, String> defaults = P6SpyOptions.getActiveInstance().getDefaults();
        Field[] fields = P6spyProperties.class.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            String propertiesName = P6spyProperties.CARPLUS_PREFIX.concat(".").concat(fieldName);
            if (environment.containsProperty(propertiesName)) {
                String systemPropertyValue = environment.getProperty(propertiesName, defaults.get(fieldName));
                defaults.put(fieldName, systemPropertyValue);
            }
        }
        P6SpyOptions.getActiveInstance().load(defaults);
        P6ModuleManager.getInstance().reload();
    }

    @Override
    public void run(ApplicationArguments args) {
        P6spyConfig.p6spyReload(environment);
    }
}
