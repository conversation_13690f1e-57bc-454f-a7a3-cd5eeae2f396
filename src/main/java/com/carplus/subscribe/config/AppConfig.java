package com.carplus.subscribe.config;

import com.carplus.subscribe.handler.AdminUserHandler;
import com.carplus.subscribe.handler.IPHandler;
import com.carplus.subscribe.interceptor.RateLimitInterceptor;
import com.carplus.subscribe.model.authority.AuthorityProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
@RequiredArgsConstructor
public class AppConfig implements WebMvcConfigurer {

    private final RateLimitInterceptor rateLimitInterceptor;

    @Bean
    @ConfigurationProperties(prefix = "authority")
    public AuthorityProperties authorityProperties() {
        return new AuthorityProperties();
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new IPHandler());
        resolvers.add(new AdminUserHandler());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(rateLimitInterceptor)
            .addPathPatterns("/subscribe/carWishlist");
    }
}
