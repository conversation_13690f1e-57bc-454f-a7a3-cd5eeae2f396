package com.carplus.subscribe.config.converter;

import com.carplus.subscribe.enums.NotifyType;
import org.springframework.boot.context.properties.ConfigurationPropertiesBinding;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
@ConfigurationPropertiesBinding
public class NotifyEnumConverter implements Converter<String, NotifyType.NotifyDepartment> {
    @Override
    public NotifyType.NotifyDepartment convert(@NonNull String source) {
        return NotifyType.NotifyDepartment.fromYamlKey(source);
    }
}
