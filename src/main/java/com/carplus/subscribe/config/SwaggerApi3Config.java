package com.carplus.subscribe.config;

import com.carplus.subscribe.constant.CarPlusConstant;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.GroupedOpenApi;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;

import java.util.Arrays;

@Configuration
public class SwaggerApi3Config {

    private final OperationCustomizer customInternalGlobalHeaders = (operation, handlerMethod) -> {
        Class<?>[] classes = handlerMethod.getMethod().getParameterTypes();
        if (Arrays.stream(classes).anyMatch(c -> HttpHeaders.class.getSimpleName().equals(c.getSimpleName()))) {
            Parameter empIdHeaderParam = new Parameter()
                .in(ParameterIn.HEADER.toString())
                .name(CarPlusConstant.AUTH_HEADER_ACCT)
                .schema(new StringSchema())
                .required(true);
            operation.addParametersItem(empIdHeaderParam);
        }
        return operation;
    };

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .components(new Components())
            .info(new Info().title("Subscribe").version("v1"));
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
            .group("subscribe-public")
            .pathsToMatch("/subscribe/**")
            .build();
    }

    @Bean
    public GroupedOpenApi internalApi() {
        return GroupedOpenApi.builder()
            .group("subscribe-internal")
            .pathsToMatch("/internal/**")
            .addOperationCustomizer(customInternalGlobalHeaders)
            .build();
    }

    @Bean
    public GroupedOpenApi adminApi() {
        return GroupedOpenApi.builder()
            .group("subscribe-admin")
            .pathsToMatch("/admin/**")
            .build();
    }

    @Bean
    public GroupedOpenApi commonApi() {
        return GroupedOpenApi.builder()
            .group("subscribe-common")
            .pathsToMatch("/common/**")
            .addOperationCustomizer(customInternalGlobalHeaders)
            .build();
    }

}
