package com.carplus.subscribe.config.mapper;

import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Component
public class StringToInstantRequestParamConverter implements Converter<String, Instant> {
    @Override
    public Instant convert(@NonNull String source) {
        return Instant.ofEpochMilli(Long.parseLong(source));
    }
}