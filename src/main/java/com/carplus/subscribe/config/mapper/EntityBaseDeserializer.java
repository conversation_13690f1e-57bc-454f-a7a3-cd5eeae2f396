package com.carplus.subscribe.config.mapper;

import com.carplus.subscribe.db.mysql.entity.change.*;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
public class EntityBaseDeserializer extends JsonDeserializer<EntityBase> {

    @Override
    public EntityBase deserialize(JsonParser jp, DeserializationContext context) throws IOException {

        ObjectMapper mapper = (ObjectMapper) jp.getCodec();
        JsonNode node = mapper.readTree(jp);

        if (node.has(EntityChange.Fields.fieldChanges)) {
            EntityChange entityChange = new EntityChange();
            entityChange.setEntityName(node.get(EntityBase.Fields.entityName).asText());
            entityChange.setEntityId(node.get(EntityBase.Fields.entityId).asText());

            List<FieldChangeResponse> fieldChangeResponses = new ArrayList<>();
            ArrayNode fieldChangesNode = (ArrayNode) node.get(EntityChange.Fields.fieldChanges);
            for (JsonNode fieldChangeNode : fieldChangesNode) {
                FieldChangeResponse fieldChangeResponse = new FieldChangeResponse();
                fieldChangeResponse.setFieldNameEn(fieldChangeNode.get(FieldChange.Fields.fieldNameEn).asText().replaceAll("\"", ""));
                fieldChangeResponse.setOldValue(fieldChangeNode.get(FieldChange.Fields.oldValue));
                fieldChangeResponse.setNewValue(fieldChangeNode.get(FieldChange.Fields.newValue));

                // 這裡需要設置 fieldName(中文名稱)，原始 JSON 中沒有這個字段，需要根據 entityName + fieldNameEn 查找對應的中文名稱
                fieldChangeResponse.setFieldName(FieldNameMapping.getFieldName(entityChange.getEntityName(), fieldChangeResponse.getFieldNameEn()));
                fieldChangeResponses.add(fieldChangeResponse);
            }
            entityChange.setFieldChanges(fieldChangeResponses);
            return entityChange;
        } else if (node.has(EntityInsertion.Fields.memo)) {
            return mapper.treeToValue(node, EntityInsertion.class);
        } else if (node.has(EntityDeletion.Fields.deletedEntity)) {
            return mapper.treeToValue(node, EntityDeletion.class);
        } else {
            throw new IllegalArgumentException("無法確定 EntityBase 的具體類型");
        }
    }
}