package com.carplus.subscribe.config.mapper;

import com.carplus.subscribe.enums.SubscribeMonth;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

public class SubscribeMonthDeserializer extends JsonDeserializer<SubscribeMonth> {
    @Override
    public SubscribeMonth deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        // jsonParser 可能接收值的型別為 Integer 或 String
        return SubscribeMonth.fromValue(jsonParser.getValueAsInt());
    }
}
