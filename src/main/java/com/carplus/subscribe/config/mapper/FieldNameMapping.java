package com.carplus.subscribe.config.mapper;

import com.carplus.subscribe.aspects.ChineseName;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.model.station.StationResponse;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class FieldNameMapping {

    private static final Map<String, String> fieldNameMapping = new HashMap<>();

    static {
        // 有註記 @ChineseName 欄位的類別
        initializeMapping(Cars.class);
        initializeMapping(Stations.class);
        initializeMapping(StationResponse.class);
        initializeMapping(SubscribeCalendar.class);
    }

    private static void initializeMapping(Class<?> clazz) {
        Arrays.stream(clazz.getDeclaredFields())
            .filter(field -> field.isAnnotationPresent(ChineseName.class))
            .forEach(field -> {
                ChineseName annotation = field.getAnnotation(ChineseName.class);
                String className = annotation.belongsTo() == void.class ? clazz.getSimpleName() : annotation.belongsTo().getSimpleName();
                String key = className + ":" + field.getName();
                fieldNameMapping.put(key, annotation.value());
            });
    }

    public static String getFieldName(String entityName, String fieldNameEn) {
        return fieldNameMapping.getOrDefault(entityName + ":" + fieldNameEn, fieldNameEn);
    }
}

