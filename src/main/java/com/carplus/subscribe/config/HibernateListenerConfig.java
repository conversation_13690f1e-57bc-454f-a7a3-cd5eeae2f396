package com.carplus.subscribe.config;

import com.carplus.subscribe.listener.EntityListener;
import com.carplus.subscribe.listener.OrderPriceInfoListener;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.hibernate.internal.SessionFactoryImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManagerFactory;

@Configuration
public class HibernateListenerConfig {

    @Autowired
    private EntityManagerFactory entityManagerFactory;

    @Autowired
    private EntityListener entityListener;

    @Autowired
    private OrderPriceInfoListener orderPriceInfoListener;

    @PostConstruct
    public void registerListeners() {
        SessionFactoryImpl sessionFactory = entityManagerFactory.unwrap(SessionFactoryImpl.class);
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);

        registry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(orderPriceInfoListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(orderPriceInfoListener);
        registry.getEventListenerGroup(EventType.POST_UPDATE).appendListeners(entityListener);
        registry.getEventListenerGroup(EventType.POST_INSERT).appendListeners(entityListener);
        registry.getEventListenerGroup(EventType.POST_DELETE).appendListener(entityListener);
    }
}
