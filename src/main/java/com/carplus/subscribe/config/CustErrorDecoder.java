package com.carplus.subscribe.config;


import feign.FeignException;
import feign.Response;
import feign.RetryableException;
import feign.Util;
import lombok.extern.slf4j.Slf4j;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;


public interface CustErrorDecoder extends feign.codec.ErrorDecoder {
    Exception decode(String var1, Response var2);

    class RetryAfterDecoder {
        static final DateFormat RFC822_FORMAT;

        static {
            RFC822_FORMAT = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss 'GMT'", Locale.US);
        }

        private final DateFormat rfc822Format;

        RetryAfterDecoder() {
            this(RFC822_FORMAT);
        }

        RetryAfterDecoder(DateFormat rfc822Format) {
            this.rfc822Format = Util.checkNotNull(rfc822Format, "rfc822Format");
        }

        protected long currentTimeMillis() {
            return System.currentTimeMillis();
        }

        public Date apply(String retryAfter) {
            if (retryAfter == null) {
                return null;
            } else if (retryAfter.matches("^[0-9]+\\.?0*$")) {
                retryAfter = retryAfter.replaceAll("\\.0*$", "");
                long deltaMillis = TimeUnit.SECONDS.toMillis(Long.parseLong(retryAfter));
                return new Date(this.currentTimeMillis() + deltaMillis);
            } else {
                synchronized (this.rfc822Format) {
                    Date var10000;
                    try {
                        var10000 = this.rfc822Format.parse(retryAfter);
                    } catch (ParseException var5) {
                        return null;
                    }

                    return var10000;
                }
            }
        }
    }

    @Slf4j
    class Default implements CustErrorDecoder {
        private final CustErrorDecoder.RetryAfterDecoder retryAfterDecoder = new CustErrorDecoder.RetryAfterDecoder();


        public Default() {
        }

        public Exception decode(String methodKey, Response response) {
            FeignException exception = FeignException.errorStatus(methodKey, response);
            Date retryAfter = this.retryAfterDecoder.apply(this.firstOrNull(response.headers(), "Retry-After"));
            log.error("[Feign] Error Response statusCode:{},request{}, body:{}", response.request().toString(), response.status(), exception.getMessage());
            return retryAfter != null ? new RetryableException(response.status(), exception.getMessage(), response.request().httpMethod(), exception, retryAfter, response.request()) : exception;
        }

        private <T> T firstOrNull(Map<String, Collection<T>> map, String key) {
            return map.containsKey(key) && !map.get(key).isEmpty() ? (T) ((Collection) map.get(key)).iterator().next() : null;
        }
    }
}
