package com.carplus.subscribe.config;

import feign.Request;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignLongTimeoutConfig {

    @Bean
    public Request.Options options(@Qualifier("longTimeout") Request.Options longTimeoutOptions) {
        return longTimeoutOptions;
    }
}
