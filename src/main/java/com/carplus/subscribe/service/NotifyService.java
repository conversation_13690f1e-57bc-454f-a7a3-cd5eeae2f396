package com.carplus.subscribe.service;

import carplus.common.enums.CRS.CRS;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.config.AppProperties;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.dto.MainContractOrderInfoDTO;
import com.carplus.subscribe.db.mysql.entity.Notify;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.authority.AuthorityProperties;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.cars.BuChangeNotify;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.notify.Email;
import com.carplus.subscribe.model.notify.Sms;
import com.carplus.subscribe.model.notify.SubscribeNotify;
import com.carplus.subscribe.model.order.ReturnLateCalculateResponse;
import com.carplus.subscribe.model.payment.TradeHistory;
import com.carplus.subscribe.model.payment.resp.TradeHistoryResp;
import com.carplus.subscribe.model.presign.GcsGetDownloadUrlReq;
import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.priceinfo.resp.CancelOrderCalculateResponse;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.order.OrderUpdateRequest;
import com.carplus.subscribe.server.*;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.ExcelUtil;
import com.carplus.subscribe.utils.HttpRequestUtils;
import com.carplus.subscribe.utils.PriceUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.crypt.EncryptionMode;
import org.apache.poi.poifs.crypt.Encryptor;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.NotifyCategory.*;
import static com.carplus.subscribe.enums.NotifyType.NotifyDepartment.*;
import static com.carplus.subscribe.enums.PayFor.SecurityDeposit;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.MEMBER_INFO_NOT_FOUND;
import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER;
import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER_WITHOUT_TIME;
import static java.net.URLEncoder.encode;

@Service
@Slf4j
public class NotifyService {

    private final String departPath = "subscribeDepart";
    private final String returnPath = "subscribeReturn";
    @Autowired
    private StationService stationService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CarModelService carModelService;
    @Autowired
    private EdmServer edmServer;
    @Autowired
    private PushServer pushServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AuthorityProperties authorityProperties;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private GoSmartServer goSmartServer;
    @Value("${carplus.service.cashier}")
    private String cashier;

    /************************ SMS ********************************/

    @Value("${carplus.service.cashier}")
    private String cashierHost;
    @Autowired
    private ShorterServer shorterServer;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private OrderService orderService;
    @Autowired
    @Lazy
    private PaymentServiceV2 paymentService;
    @Autowired
    @Lazy
    private PaymentServer paymentServer;
    @Autowired
    private CarBrandService carBrandService;
    @Autowired
    private ContractService contractService;

    /**
     * 授信需求，通知業務端
     */
    public void notifyCreditDemand(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyCreditDemand(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 授信需求，通知業務端
     */
    @Async
    public void notifyCreditDemand(@NonNull Orders order, AuthUser user) {
        try {
            String subject2B = "格上官網訂閱車 - 審核提醒 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>生意上門了~ 有勞授信團隊協助審核，感謝！").append("<a href=\"").append(cashier).append("/subscribeSearchOrder").append("\">前往審核</a>").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>保證金 : $").append(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()).append("</li>");
            sbf2B.append("<li>訂閱租期 : ").append(order.getMonth()).append(" 個月").append("</li>");
            sbf2B.append("<li>合約期間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" - ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            sbf2B.append("<li>基本月費 : $").append(mainContract.getOriginalPriceInfo().getUseMonthlyFee()).append("</li>");
            sbf2B.append("<li>里程費率 : $").append(mainContract.getOriginalPriceInfo().getMileageFee() > 0 ? mainContract.getOriginalPriceInfo().getMileageFee() : mainContract.getOriginalPriceInfo().getOriginalMileageFee()).append("</li>");
            if (contract.getDisclaimer() && contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getAllInsuranceFee()).append(" / 季 (全額保)").append("</li>");
            } else if (contract.getDisclaimer()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getDisclaimerFee()).append(" / 月 (其他駕駛保障)").append("</li>");
            } else if (contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getPremiumFee()).append(" / 季 (溢價險)").append("</li>");
            }
            if (contract.getReplacement()) {
                sbf2B.append("<li>代步車 : $").append(mainContract.getOriginalPriceInfo().getReplacementCarFee()).append(" / 月").append("</li>");
            }

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.SUB_CREDIT_DEMAND)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("授信需求，通知業務端失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 保證金付款完成通知
     */
    public void notifyPaySecurityDepositSuccess(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyPaySecurityDepositSuccess(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 保證金付款完成通知
     */
    @Async
    public void notifyPaySecurityDepositSuccess(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 新單確認 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>接收到客戶訂單，以下為訂單詳細資料：").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", departPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>保證金 : $").append(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()).append("</li>");
            sbf2B.append("<li>訂閱租期 : ").append(order.getMonth()).append(" 個月").append("</li>");
            sbf2B.append("<li>合約期間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" - ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            sbf2B.append("<li>預定出車時間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations departStation = stationService.findByStationCode(mainContract.getDepartStationCode());
            sbf2B.append("<li>出車站所 : ").append(departStation.getStationName()).append("</li>");
            sbf2B.append("<li>出車站所電話 : ").append(departStation.getTel()).append("</li>");
            sbf2B.append("<li>出車站所地址 : ").append(departStation.getAddr()).append("</li>");
            sbf2B.append("<li>基本月費 : $").append(mainContract.getOriginalPriceInfo().getUseMonthlyFee()).append("</li>");
            sbf2B.append("<li>里程費率 : $").append(mainContract.getOriginalPriceInfo().getMileageFee() > 0 ? mainContract.getOriginalPriceInfo().getMileageFee() : mainContract.getOriginalPriceInfo().getOriginalMileageFee()).append("</li>");
            if (contract.getDisclaimer() && contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getAllInsuranceFee()).append(" / 季 (全額保)").append("</li>");
            } else if (contract.getDisclaimer()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getDisclaimerFee()).append(" / 月 (其他駕駛保障)").append("</li>");
            } else if (contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getPremiumFee()).append(" / 季 (溢價險)").append("</li>");
            }

            if (contract.getReplacement()) {
                sbf2B.append("<li>代步車 : $").append(mainContract.getOriginalPriceInfo().getReplacementCarFee()).append(" / 月").append("</li>");
            }

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.SUB_COMPLETE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("保證金付款完成通知TO B失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 保證金付款完成通知
     */
    @Async
    public void notifyToFinancePaySecurityDepositSuccess(@NonNull Orders order, AuthUser user, PaymentQueue queue) {

        try {
            String subject2B = "【訂閱車－保證金入帳】訂單編號_" + order.getOrderNo();
            MainContract mainContract = order.getContract().getMainContract();
            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<ul>");
            sbf2B.append("<li>收文單位 : ").append("財務單位").append("</li>");
            sbf2B.append("<li>發文主旨 : ").append("訂閱車訂單 - 保證金入帳(TapPay)").append("</li>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>建立日期 : ").append(DateUtils.toDateString(new Date(order.getInstantCreateDate().toEpochMilli()), "yyyy/MM/dd hh:mm")).append("</li>");
            sbf2B.append("<li>刷卡日期 : ").append(DateUtils.toDateString(new Date(), "yyyy/MM/dd hh:mm")).append("</li>");
            sbf2B.append("<li>刷卡碼(前六後四) : ").append(queue.getCardNumber()).append("</li>");
            sbf2B.append("<li>刷卡金額 : ").append(queue.getAmount()).append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>身分證號 : ").append(user.getLoginId()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.TO_FINANCE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("發送財務保證金入賬通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 保證金退款通知
     */
    @Async
    public void notifyToFinanceRefundSecurityDeposit(@NonNull Orders order, AuthUser user, PaymentQueue queue, String cardNo) {
        try {
            String subject2B = "【訂閱車－保證金退刷】訂單編號_" + order.getOrderNo();
            MainContract mainContract = order.getContract().getMainContract();
            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<ul>");
            sbf2B.append("<li>收文單位 : ").append("財務單位").append("</li>");
            sbf2B.append("<li>發文主旨 : ").append("訂閱車訂單 - 保證金退刷(TapPay)").append("</li>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>建立日期 : ").append(DateUtils.toDateString(new Date(order.getInstantCreateDate().toEpochMilli()), "yyyy/MM/dd HH:mm")).append("</li>");
            sbf2B.append("<li>刷卡碼(前六後四) : ").append(cardNo).append("</li>");
            sbf2B.append("<li>刷卡金額 : ").append(queue.getAmount()).append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>身分證號 : ").append(user.getLoginId()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.TO_FINANCE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("發送財務保證金退款通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 保證金退款通知
     */
    public void notifyManualRefundSecurityDeposit(String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        AuthUser user = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        PaymentInfo securityDepositPay = paymentService.getPaymentsByOrder(orderNo).stream()
            .filter(p -> Objects.equals(p.getPayFor(), SecurityDeposit) && p.getPaymentCategory().equals(PayAuth) && Arrays.asList(OrderPaymentStatus.AUTHORIZED.getCode(), OrderPaymentStatus.BANK_CAPTURE.getCode()).contains(p.getStatus())).findAny()
            .orElse(null);
        if (securityDepositPay != null) {
            TradeHistoryResp tradeHistoryResp = paymentServer.getTappayHistory(securityDepositPay.getTradeId());
            Date transactionTime = Optional.ofNullable(tradeHistoryResp).map(TradeHistoryResp::getData).orElse(new ArrayList<>()).stream().filter(tradeHistory -> tradeHistory.getAction() == 0).findAny().map(TradeHistory::getMillis).map(Date::new)
                .orElse(new Date(securityDepositPay.getInstantCreateDate().toEpochMilli()));

            PaymentQueue paymentQueue = new PaymentQueue();
            paymentQueue.setAcquirer(securityDepositPay.getAcquirer());
            paymentQueue.setAmount(securityDepositPay.getAmount());
            paymentQueue.setTransactionNumber(securityDepositPay.getTransactionNumber());
            notifyManualRefundSecurityDeposit(orders, user, paymentQueue, securityDepositPay, transactionTime);
        }
    }

    /**
     * 保證金退款通知
     */
    @Async
    public void notifyManualRefundSecurityDeposit(@NonNull Orders order, AuthUser user, @NonNull PaymentQueue queue, PaymentInfo securityDepositPay, Date transactionTime) {

        try {

            File file = createManualSecurityDepositRefundExcel(order, queue.getAmount(), securityDepositPay.getAmount(), transactionTime, securityDepositPay, user);
            String url = uploadExcel(FileUtils.readFileToByteArray(file));

            String subject2B = String.format("【訂閱車】人工退款提醒_訂單_%s", securityDepositPay.getOrderId());

            String toB = "<ul>"
                + "<li>收文單位：訂閱車業務部</li>"
                + "<li>發文主旨：訂閱車訂單 - 需申請人工退款作業</li>"
                + "<li>訂單編號：" + order.getOrderNo() + "</li>"
                + "<li>交易編號：" + securityDepositPay.getTransactionNumber() + "</li>"
                + "<li>刷卡日期：" + DateUtils.toDateString(transactionTime, "yyyy/MM/dd HH:mm") + "</li>"
                + "<li>人工退款作業所需資訊如附件</li>"
                + "</ul>";

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_MANUAL_REFUND)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(toB)
                .attachmentUrl(url)
                .displayFilename(order.getOrderNo() + ".xlsx")
                .build();
            notify2B(order, subNotify);

            file.delete();
        } catch (Exception e) {
            Map<String, Object> msg = new LinkedHashMap<>();
            msg.put("訂單編號", order.getOrderNo());
            msg.put("tradeId", queue.getTradeId());
            msg.put("refundId", queue.getRefundId());
            mattermostServer.notify("人工退款通知失敗", msg, e);
        }
    }

    /**
     * 產生人工退費Excel
     */
    private File createManualSecurityDepositRefundExcel(Orders orders, Integer refundAmount, Integer originalAmount,
                                                        Date securityDepositDate, PaymentInfo securityDepositPay, AuthUser user) {
        String custName = "客戶姓名";
        String carNo = "刷卡號碼(前六後四)";
        String transactionNumber = "交易編號";
        String exchangeDate = "刷卡日期";
        String amount = "刷卡金額";
        String refundAmountStr = "待退金額";
        String authCode = "授權號碼";

        LinkedHashMap<String, Object> rowData = new LinkedHashMap<>();
        rowData.put(custName, user.getAcctName());
        rowData.put(carNo, securityDepositPay.getCardNumber());
        rowData.put(transactionNumber, securityDepositPay.getTransactionNumber());
        rowData.put(exchangeDate, DateUtils.toDateString(securityDepositDate, "yyyy/MM/dd"));
        rowData.put(amount, originalAmount);
        rowData.put(refundAmountStr, refundAmount);
        rowData.put(authCode, securityDepositPay.getAuthCode());


        ExcelUtil.SheetData refundSheet = new ExcelUtil.SheetData("保證金退費",
            new ArrayList<>(rowData.keySet()), Collections.singletonList(rowData));

        File file = new ExcelUtil().builder().setfileName(orders.getOrderNo() + ".xlsx")
            .addSheetData(refundSheet)
            .build().toFile();

        //Excel資料進行加密
        POIFSFileSystem fs = new POIFSFileSystem();
        EncryptionInfo encryptionInfo = new EncryptionInfo(EncryptionMode.standard);
        Encryptor enc = encryptionInfo.getEncryptor();
        FileOutputStream fos = null;
        OPCPackage opcPackage = null;
        OutputStream os = null;
        enc.confirmPassword("20200901");

        // soarlint,強制stream close一定要包在finally,但fos = new FileOutputStream(file.getName()); 執行後 opcPackage.close();必定噴錯,只能改成兩段try catch,然後再噴code 複雜度太高,不爽改了
        try {
            opcPackage = OPCPackage.open(file, PackageAccess.READ_WRITE);
            os = enc.getDataStream(fs);
            opcPackage.save(os);

        } catch (GeneralSecurityException | IOException | InvalidFormatException e) {
            log.error("excel 加密失敗:", e);
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
                if (opcPackage != null) {
                    opcPackage.close();
                }

            } catch (IOException e) {
                log.error("POIFSFileSystem close stram失敗:", e);
            }
        }
        try {
            fos = new FileOutputStream(file.getName());
            fs.writeFilesystem(fos);
        } catch (IOException e) {
            log.error("POIFSFileSystem close stram失敗:", e);
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
                fs.close();
            } catch (IOException e) {
                log.error("POIFSFileSystem close stram失敗:", e);
            }
        }
        return file;
    }

    private String uploadExcel(byte[] bytes) {
        // 呼叫 goSmart 服務取得上傳 url
        GcsGetUploadUrlReq gcsGetUploadUrlReq = goSmartServer.createGcsGetUploadUrlReq(FileTypeEnum.XLSX);
        log.info("uploadExcel, call goSmart api, GcsGetUploadUrlReq={}", gcsGetUploadUrlReq);
        GcsUrlRes gcsUrlRes = goSmartServer.getGcsUploadUrl(gcsGetUploadUrlReq);
        if (gcsUrlRes == null || CollectionUtils.isEmpty(gcsUrlRes.getSignedUrls())) {
            log.error("uploadExcel, call goSmart getGcsUploadUrl fail");
            throw new RuntimeException("call goSmart fail");
        }
        GcsUrlRes.TypeResponse typeResponse = gcsUrlRes.getSignedUrls().get(0);
        String uploadUrl = typeResponse.getSignedUrl();
        String mediaType = typeResponse.getMediaType();
        String fileName = typeResponse.getFileName();

        // 上傳 excel
        log.info("uploadExcel, call upload api, uploadUrl={}", uploadUrl);
        HttpRequestUtils.upload(uploadUrl, bytes, mediaType);

        // 呼叫 goSmart 服務取得下載 url
        GcsGetDownloadUrlReq gcsGetDownloadUrlReq = goSmartServer.createGcsGetDownloadUrlReq(fileName);
        log.info("uploadExcel, call goSmart api, GcsGetDownloadUrlReq={}", gcsGetUploadUrlReq);
        GcsUrlRes gcsDownloadUrlRes = goSmartServer.getGcsDownloadUrl(gcsGetDownloadUrlReq);
        List<GcsUrlRes.TypeResponse> signedUrls = gcsDownloadUrlRes.getSignedUrls();
        if (CollectionUtils.isEmpty(signedUrls)) {
            log.error("uploadExcel, call goSmart getGcsDownloadUrl fail");
            throw new RuntimeException("查無資料");
        }
        String downloadUrl = signedUrls.get(0).getSignedUrl();
        log.info("uploadExcel, downloadUrl={}", downloadUrl);

        // 回傳下載 url
        return downloadUrl;
    }

    /**
     * 訂單異動通知
     */
    @Async
    public void notifyOrderUpdate(@NonNull Orders order, AuthUser user, OrderUpdateRequest orderUpdateRequest) {
        try {
            String subject2B = "訂閱車 - 訂單變更 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append("接收到客戶訂單異動，以下為訂單詳細資料：")
                .append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", departPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>保證金 : $").append(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()).append("</li>");
            sbf2B.append("<li>訂閱租期 : ").append(order.getMonth()).append(" 個月").append("</li>");

            sbf2B.append("<li>合約期間 : ").append(DateUtil.getFormatString(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()), SLASH_FORMATTER))
                .append(" - ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER));
            if (Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()).toEpochMilli() != Optional.ofNullable(order.getStartDate()).orElse(orderUpdateRequest.getOriExpectDepartDate()).toEpochMilli()
                || order.getExpectEndDate().toEpochMilli() != orderUpdateRequest.getOriExpectReturnDate().toEpochMilli()) {
                sbf2B.append(String.format("(異動前:%s-%s)",
                    DateUtil.getFormatString(Optional.ofNullable(order.getStartDate()).orElse(orderUpdateRequest.getOriExpectDepartDate()), SLASH_FORMATTER),
                    DateUtil.getFormatString(orderUpdateRequest.getOriExpectReturnDate(), SLASH_FORMATTER)));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>預定出車時間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER));
            if (order.getExpectStartDate().toEpochMilli() != orderUpdateRequest.getOriExpectDepartDate().toEpochMilli()) {
                sbf2B.append(String.format("(異動前:%s)", DateUtil.getFormatString(orderUpdateRequest.getOriExpectDepartDate(), SLASH_FORMATTER)));

            }
            sbf2B.append("</li>");


            Stations departStation = stationService.findByStationCode(mainContract.getDepartStationCode());
            Stations oriDepartStation = stationService.findByStationCode(orderUpdateRequest.getOriDepartStationCode());
            sbf2B.append("<li>出車站所 : ").append(departStation.getStationName());
            if (!departStation.getStationCode().equals(oriDepartStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriDepartStation.getStationName()));
            }
            sbf2B.append("</li>");
            sbf2B.append("<li>出車站所電話 : ").append(departStation.getTel());
            if (!departStation.getStationCode().equals(oriDepartStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriDepartStation.getTel()));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>出車站所地址 : ").append(departStation.getAddr());
            if (!departStation.getStationCode().equals(oriDepartStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriDepartStation.getAddr()));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>預定還車時間 : ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER));
            if (order.getExpectEndDate().toEpochMilli() != orderUpdateRequest.getOriExpectReturnDate().toEpochMilli()) {
                sbf2B.append(String.format("(異動前:%s)", DateUtil.getFormatString(orderUpdateRequest.getOriExpectReturnDate(), SLASH_FORMATTER)));
            }
            sbf2B.append("</li>");
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            Stations oriReturnStation = stationService.findByStationCode(orderUpdateRequest.getOriReturnStationCode());
            sbf2B.append("<li>還車站所 : ").append(returnStation.getStationName());
            if (!returnStation.getStationCode().equals(oriReturnStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriReturnStation.getStationName()));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>還車站所電話 : ").append(returnStation.getTel());
            if (!returnStation.getStationCode().equals(oriReturnStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriReturnStation.getTel()));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>還車站所地址 : ").append(returnStation.getAddr());
            if (!returnStation.getStationCode().equals(oriReturnStation.getStationCode())) {
                sbf2B.append(String.format("(異動前:%s)", oriReturnStation.getAddr()));
            }
            sbf2B.append("</li>");

            sbf2B.append("<li>基本月費 : $").append(mainContract.getOriginalPriceInfo().getUseMonthlyFee()).append("</li>");
            sbf2B.append("<li>里程費率 : $").append(mainContract.getOriginalPriceInfo().getMileageFee() > 0 ? mainContract.getOriginalPriceInfo().getMileageFee() : mainContract.getOriginalPriceInfo().getOriginalMileageFee()).append("</li>");
            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_UPDATE_ORDER)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            List<String> emails = new ArrayList<>();
            emails.addAll(getToBMembersEmail(orderUpdateRequest.getOriReturnStationCode(), subNotify.getCategory()));
            emails.addAll(getToBMembersEmail(orderUpdateRequest.getOriDepartStationCode(), subNotify.getCategory()));
            emails.addAll(getToBMembersEmail(orderUpdateRequest.getDepartStationCode(), subNotify.getCategory()));
            emails.addAll(getToBMembersEmail(orderUpdateRequest.getReturnStationCode(), subNotify.getCategory()));

            notify2B(order, subNotify, emails);
        } catch (Exception e) {
            log.error("訂單異動通知失敗:", e);
            mattermostServer.notify("訂單異動通知To B通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 續約單成立通知
     */
    public void notifyRenewConfirm(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyRenewConfirm(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 續約單成立通知
     */
    @Async
    public void notifyRenewConfirm(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 續約確認 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>接收到客戶訂單，以下為訂單詳細資料：").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", departPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>保證金 : $").append(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()).append("</li>");
            sbf2B.append("<li>訂閱租期 : ").append(order.getMonth()).append(" 個月").append("</li>");
            sbf2B.append("<li>合約期間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" - ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            sbf2B.append("<li>預定出車時間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations departStation = stationService.findByStationCode(mainContract.getDepartStationCode());
            sbf2B.append("<li>出車站所 : ").append(departStation.getStationName()).append("</li>");
            sbf2B.append("<li>出車站所電話 : ").append(departStation.getTel()).append("</li>");
            sbf2B.append("<li>出車站所地址 : ").append(departStation.getAddr()).append("</li>");
            sbf2B.append("<li>基本月費 : $").append(mainContract.getOriginalPriceInfo().getUseMonthlyFee()).append("</li>");
            sbf2B.append("<li>里程費率 : $").append(mainContract.getOriginalPriceInfo().getMileageFee() > 0 ? mainContract.getOriginalPriceInfo().getMileageFee() : mainContract.getOriginalPriceInfo().getOriginalMileageFee()).append("</li>");
            if (contract.getDisclaimer() && contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getAllInsuranceFee()).append(" / 季 (全額保)").append("</li>");
            } else if (contract.getDisclaimer()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getDisclaimerFee()).append(" / 月 (其他駕駛保障)").append("</li>");
            } else if (contract.getPremium()) {
                sbf2B.append("<li>額外保障 : $").append(mainContract.getOriginalPriceInfo().getPremiumFee()).append(" / 季 (溢價險)").append("</li>");
            }

            if (contract.getReplacement()) {
                sbf2B.append("<li>代步車 : $").append(mainContract.getOriginalPriceInfo().getReplacementCarFee()).append(" / 月").append("</li>");
            }

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.SUB_RENEW_CONFIRM)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("續約單成立通知To B通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 訂單取消通知
     */
    public void notifyCancelOrder(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyCancelOrder(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 訂單取消通知
     */
    @Async
    public void notifyCancelOrder(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 訂單取消 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>接收到客戶取消訂單，以下為訂單詳細資料：").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", departPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>保證金 : $").append(mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()).append("</li>");
            sbf2B.append("<li>訂閱租期 : ").append(order.getMonth()).append(" 個月").append("</li>");
            sbf2B.append("<li>合約期間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" - ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            sbf2B.append("<li>預定出車時間 : ").append(DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations departStation = stationService.findByStationCode(mainContract.getDepartStationCode());
            sbf2B.append("<li>出車站所 : ").append(departStation.getStationName()).append("</li>");
            sbf2B.append("<li>出車站所電話 : ").append(departStation.getTel()).append("</li>");
            sbf2B.append("<li>出車站所地址 : ").append(departStation.getAddr()).append("</li>");
            sbf2B.append("<li>取消原因 : ").append(order.getCancelMemo()).append("</li>");
            MemberInfo memberInfo = authorityServer.getMemberInfos(order.getCancelUser()).stream().findFirst().orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
            sbf2B.append("<li>取消人員 : ").append(memberInfo.getMemberId()).append(memberInfo.getMemberName()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.SUB_CANCEL_ORDER)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("訂單取消通知 TO B 通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 續約提醒
     */
    public void notifyRenewCall(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyRenewCall(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 續約提醒
     */
    @Async
    public void notifyRenewCall(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 續約提醒 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append(user.getAcctName()).append(" 訂閱合約將於 ")
                .append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" 到期，請協助提醒客人線上續約。").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>預定還車時間 : ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            sbf2B.append("<li>還車站所 : ").append(returnStation.getStationName()).append("</li>");
            sbf2B.append("<li>還車站所電話 : ").append(returnStation.getTel()).append("</li>");
            sbf2B.append("<li>還車站所地址 : ").append(returnStation.getAddr()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_RENEW_CALL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("續約提醒 TO B 通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 確認不續約通知
     */
    public void notifyNotRenewConfirm(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyNotRenewConfirm(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 確認不續約通知
     */
    @Async
    public void notifyNotRenewConfirm(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 確認不續約 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append(user.getAcctName()).append(" 訂閱合約將於 ")
                .append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME))
                .append(" 到期，客戶已確認不續約，請記得提醒客戶準時還車。").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>預定還車時間 : ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            sbf2B.append("<li>還車站所 : ").append(returnStation.getStationName()).append("</li>");
            sbf2B.append("<li>還車站所電話 : ").append(returnStation.getTel()).append("</li>");
            sbf2B.append("<li>還車站所地址 : ").append(returnStation.getAddr()).append("</li>");
            sbf2B.append("<li>不續約原因 : ").append(order.getNonRenewRemark()).append("</li>");
            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_NOT_RENEW_CONFIRM)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("不續約通知失敗", e);
            mattermostServer.notify("確認不續約通知 To B失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 已還車(不續約)
     */
    @Async
    public void notifyReturnCar(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 已還車 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append(" 已完成還車，以下為訂單詳細資料，請盡早將車輛歸還(牽送或派拖)至就近中古車庫位，並提醒中古車人員辦理入庫 ")
                .append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>預定還車時間 : ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            sbf2B.append("<li>還車站所 : ").append(returnStation.getStationName()).append("</li>");
            sbf2B.append("<li>還車站所電話 : ").append(returnStation.getTel()).append("</li>");
            sbf2B.append("<li>還車站所地址 : ").append(returnStation.getAddr()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_RETURN_COMPLETE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("還車通知失敗:", e);
            mattermostServer.notify("還車 To B 通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 已還車未結案提醒
     */
    @Async
    public void notifyReturnNotCloseSummary(Map<GeoDefine.GeoRegion, List<Orders>> orderMap) {

        try {
            String subject2B = "【訂閱車】結案提醒_" + DateUtil.getFormatString(Instant.now(), SLASH_FORMATTER_WITHOUT_TIME);

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append(" 長官們好，以下為各區未結案訂單，有勞確認是否完成結案， 謝謝!").append("</p>");
            sbf2B.append("<ul>");
            for (GeoDefine.GeoRegion geo : GeoDefine.GeoRegion.values()) {
                List<Orders> ordersList = orderMap.get(geo);
                if (ordersList == null || ordersList.isEmpty()) {
                    continue;
                }
                sbf2B.append("<li>").append(geo.getName()).append("</li>");
                sbf2B.append("<ul>");
                for (Orders value : ordersList) {
                    sbf2B.append("<li>").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, value.getOrderNo())).append("\">").append(value.getOrderNo()).append("</a>").append("</li>");
                }
                sbf2B.append("</ul>");
            }
            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");
            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_RETURN_NOT_CLOSE_SUMMARY)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();
            Optional<List<Orders>> optional = orderMap.values().stream().findAny();
            optional.ifPresent(orders -> notify2B(orders.get(0), subNotify));
        } catch (Exception e) {
            mattermostServer.notify("已還車未結案整合 TO B 通知失敗", null, e);
        }
    }

    private String buildOrderSummaryHTML(Map<GeoDefine.GeoRegion, List<Orders>> orderMap, StringBuilder sbf2B) throws UnsupportedEncodingException {
        for (GeoDefine.GeoRegion geo : GeoDefine.GeoRegion.values()) {
            List<Orders> ordersList = orderMap.get(geo);
            if (ordersList == null || ordersList.isEmpty()) {
                continue;
            }
            sbf2B.append("<li>").append(geo.getName()).append("</li>");
            sbf2B.append("<ul>");
            for (Orders order : ordersList) {
                MainContract mainContract = order.getContract().getMainContract();
                sbf2B.append("<li>")
                    .append(stationService.findByStationCode(mainContract.getReturnStationCode()).getStationName())
                    .append(" / ")
                    .append(authServer.getUserWithRetry(mainContract.getAcctId()).getAcctName())
                    .append(" / ")
                    .append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo()))
                    .append("\">").append(order.getOrderNo()).append("</a>")
                    .append("</li>");
            }
            sbf2B.append("</ul>");
        }
        sbf2B.append("</ul>");
        return URLEncoder.encode(sbf2B.toString(), "UTF-8");
    }


    private String buildWaitForCarReturnHTML(Map<String, List<BuChangeNotify>> stationNotificationsMap) {
        Map<String, Stations> stationsMap = stationService.getStationsMap();
        StringBuilder sbf2B = new StringBuilder();
        for (Map.Entry<String, List<BuChangeNotify>> entry : stationNotificationsMap.entrySet()) {
            List<BuChangeNotify> notifyList = entry.getValue();
            if (notifyList == null || notifyList.isEmpty()) {
                continue;
            }
            String locationStationCode = entry.getKey();
            String locationStationName = Optional.ofNullable(stationsMap.get(locationStationCode)).map(Stations::getStationName).orElse("");
            sbf2B.append("<li>").append(locationStationName).append("</li>");
            sbf2B.append("<ul>");
            for (BuChangeNotify notify : notifyList) {
                sbf2B.append("<li>")
                    .append(notify.getPlateNo())
                    .append(" / ")
                    .append(Optional.ofNullable(stationsMap.get(notify.getReturnStationCode())).map(Stations::getStationName).orElse(""))
                    .append(" / ")
                    .append(Optional.ofNullable(DateUtil.getFormatString(notify.getEffectiveDate(), SLASH_FORMATTER_WITHOUT_TIME)).orElse("未知還車/取消日期"))
                    .append(" / ")
                    .append(Optional.ofNullable(notify.getCarHistory())
                        .map(history -> String.format("%s(%s)",
                            Optional.ofNullable(history.getSgStatus()).orElse("未知異動別"),
                            history.isCarInStock() ? Optional.ofNullable(history.getSgName()).orElse("未知入庫倉庫名稱")
                                : Optional.ofNullable(history.getSourceSG()).orElse("未知出庫倉庫名稱")))
                        .orElse("查無車輛出入庫歷程"))
                    .append("</li>");
            }
            sbf2B.append("</ul>");
        }
        sbf2B.append("</ul>");
        return sbf2B.toString();
    }

    /**
     * 逾期未還車提醒
     */
    @Async
    public void notifyDepartNotReturnSummary(Map<GeoDefine.GeoRegion, List<Orders>> orderMap) {

        try {
            String subject2B = "【訂閱車】逾期未還車訂單";

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<ul>")
                .append("<li>收文單位 : 訂閱車業務部</li>")
                .append("<li>發文主旨 : 訂閱車訂單 - 逾期未還車</li>")
                .append("<li>以下為各區逾期未還車訂單，再請確認是否已經完成還車，謝謝!</li>");
            String content2B = buildOrderSummaryHTML(orderMap, sbf2B);

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_DEPART_NOT_RETURN_SUMMARY)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            List<String> emails = new ArrayList<>(getToBMembersEmail(null, subNotify.getCategory()));

            notify2B(subNotify, emails);
        } catch (Exception e) {
            mattermostServer.notify("逾期未還車整合 TO B 通知失敗", null, e);
        }
    }

    /**
     * 逾期未結算提醒
     */
    @Async
    public void notifyMonthlyFeeUnpaidSummary(Map<GeoDefine.GeoRegion, List<Orders>> orderMap) {

        try {
            String subject2B = "【訂閱車】逾期未結算訂單";

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<ul>")
                .append("<li>收文單位 : 訂閱車業務部</li>")
                .append("<li>發文主旨 : 訂閱車訂單 - 逾期未結算</li>")
                .append("<li>以下為各區逾期未結算訂單，提供清單以利追蹤提醒客人完成付款， 謝謝!</li>");
            String content2B = buildOrderSummaryHTML(orderMap, sbf2B);

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_MONTHLY_FEE_UNPAID)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            List<String> emails = new ArrayList<>(getToBMembersEmail(null, subNotify.getCategory()));

            notify2B(subNotify, emails);
        } catch (Exception e) {
            mattermostServer.notify("逾期未結算整合 TO B 通知失敗", null, e);
        }
    }

    /**
     * 已還車未結案提醒
     */
    public void notifyReturnNotClose(@NonNull String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        notifyReturnNotClose(orders, authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId()));
    }

    /**
     * 已還車未結案提醒
     */
    @Async
    public void notifyReturnNotClose(@NonNull Orders order, AuthUser user) {

        try {
            String subject2B = "訂閱車 - 已還車未結案 #" + order.getOrderNo();

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<p>").append(" 長官您好，本訂單尚未結案，有勞確認是否完成結案：").append("</p>");
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂車人 : ").append(user.getAcctName()).append("</li>");
            sbf2B.append("<li>連絡電話 : ").append(user.getMainCell()).append("</li>");
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            String plateNo = mainContract.getPlateNo();
            CarBrandModelDTO modelDTO = carsService.getCarBrandModelByPlateNo(plateNo);
            sbf2B.append("<li>訂閱車款 : ").append(Objects.nonNull(modelDTO) ? modelDTO.getBrand().getBrandNameEn() + " " + modelDTO.getModel().getCarModelName() : "").append("</li>");
            sbf2B.append("<li>車牌號碼 : ").append(mainContract.getPlateNo()).append("</li>");
            sbf2B.append("<li>預定還車時間 : ").append(DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME)).append("</li>");
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            sbf2B.append("<li>還車站所 : ").append(returnStation.getStationName()).append("</li>");
            sbf2B.append("<li>還車站所電話 : ").append(returnStation.getTel()).append("</li>");
            sbf2B.append("<li>還車站所地址 : ").append(returnStation.getAddr()).append("</li>");

            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_RETURN_NOT_CLOSE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("已還車未結案 TO B 通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }


    /**
     * 已還車未結案請求
     */
    @Async
    public void notifyReturnNotCloseRequest(@NonNull Orders order, AdminUser adminUser) {

        try {
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            Stations returnStation = stationService.findByStationCode(mainContract.getReturnStationCode());
            String subject2B = String.format("【訂閱車】請求長官結案_%s_%s", order.getOrderNo(), returnStation.getStationName());

            StringBuilder sbf2B = new StringBuilder();
            sbf2B.append("<ul>");
            sbf2B.append("<li>訂單編號 : ").append(order.getOrderNo()).append("</li>");
            sbf2B.append("<li>訂單連結 : ").append("<a href=\"").append(cashier).append(String.format("/%s?orderNo=%s", returnPath, order.getOrderNo())).append("\">連結</a>").append("</li>");
            sbf2B.append("<li>訂單狀態 : ").append(OrderStatus.of(order.getStatus()).getName()).append("</li>");
            sbf2B.append("<li>請求門市 : ").append(returnStation.getStationName()).append("</li>");


            sbf2B.append("<li>請求人員 : ").append(adminUser.getMemberId()).append(" ").append(adminUser.getMemberName()).append("</li>");
            sbf2B.append("<li>此訂單已經確認結清相關費用，請求長官結案，謝謝!").append("</li>");
            sbf2B.append("</ul>");

            String content2B = URLEncoder.encode(sbf2B.toString(), "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_RETURN_NOT_CLOSE_REQUEST)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();
            notify2B(order, subNotify);
        } catch (Exception e) {
            mattermostServer.notify("已還車未結案 TO B 通知失敗", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 通知長租契約解除
     */
    @Async
    public void notifyContractCancel(IOrder order, String oriPlateNo, String newPlateNo, String lrentalContractNo) {

        if (StringUtils.isBlank(lrentalContractNo)) {
            return;
        }

        try {
            String subject2B = "【訂閱車】契約解除/新增提醒_" + order.getOrderNo();

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 契約解除提醒</li>"
                + "<li>(原)車牌號碼 : " + oriPlateNo + "</li>"
                + "<li>(現)車牌號碼 : " + newPlateNo + "</li>"
                + "<li>訂單編號 : " + order.getOrderNo() + "</li>"
                + "<li>訂單狀態 : " + order.getOrderStatusName() + "</li>"
                + "<li>契約編號 : " + lrentalContractNo + "</li>"
                + "</ul>"
                + "<p>"
                + "訂單有「換車行為」或者「取消訂單」，若是「換車」記得至收銀台 > 出車作業 > tab1 訂單明細 > 車籍契約 重建(長租)車籍契約，以利通知投保，避免屆時無法出車；\n"
                + "若車輛來源為「中古」會自動還車，若車輛來源為「短租」或「共享」，請記得至CRS操作一鍵還車，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(LRENTAL_CANCEL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();
            if (order instanceof Orders) {
                notify2B((Orders) order, subNotify);
            } else if (order instanceof DealerOrder) {
                notify2B((DealerOrder) order, subNotify, new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("契約解除/新增提醒 To B Fail:", e);
            mattermostServer.notify("契約解除/新增提醒 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }


    /**
     * 通知長租契約異動
     */
    @Async
    public void notifyContractChange(Orders order) {

        try {
            String subject2B = "【訂閱車】契約異動提醒_" + order.getOrderNo();

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 契約異動提醒</li>"
                + "<li>車牌號碼 : " + order.getPlateNo() + "</li>"
                + "<li>訂單編號 : " + order.getOrderNo() + "</li>"
                + "<li>契約編號 : " + order.getLrentalContractNo() + "</li>"
                + "</ul>"
                + "<p>"
                + "訂單有異動「預計出還時間」或「訂閱租期」，請確認是否需要通知契管單位手動調整，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(LRENTAL_CHANGE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("契約異動提醒 To B Fail:", e);
            mattermostServer.notify("契約異動提醒 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 通知長租契約異動
     */
    @Async
    public void notifyContractChange(DealerOrder dealerOrder) {

        try {
            String subject2B = "【訂閱車】契約異動提醒_" + dealerOrder.getOrderNo();

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 契約異動提醒</li>"
                + "<li>車牌號碼 : " + dealerOrder.getPlateNo() + "</li>"
                + "<li>訂單編號 : " + dealerOrder.getOrderNo() + "</li>"
                + "<li>契約編號 : " + dealerOrder.getLrentalContractNo() + "</li>"
                + "</ul>"
                + "<p>"
                + "訂單有異動「預計出還時間」或「訂閱租期」，請確認是否需要通知契管單位手動調整，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(LRENTAL_CHANGE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(dealerOrder, subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("契約異動提醒 To B Fail:", e);
            mattermostServer.notify("契約異動提醒 To B Fail", Collections.singletonMap("orderNo", dealerOrder.getOrderNo()), e);
        }
    }

    /**
     * 收訂尚未建約提醒
     */
    @Async
    public void notifyOrderWithoutContract(List<String> orderNoForRemindContractAdnInsurance) {
        if (CollectionUtils.isEmpty(orderNoForRemindContractAdnInsurance)) {
            return;
        }
        try {
            String subject2B = "【訂閱車】收訂尚未建約提醒_" + DateUtils.toDateString("yyyyMMdd");

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 格上訂單契約建立提醒</li>"
                + "<li>訂單編號 : " + String.join("、", orderNoForRemindContractAdnInsurance) + "</li>"
                + "</ul>"
                + "<p>"
                + "以上訂單尚未建立車籍契約&通知投保，請留意處理，避免屆時出車失敗，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(INSURANCE_REMIND)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("收訂尚未建約提醒 To B Fail:", e);
            mattermostServer.notify("收訂尚未建約提醒 To B Fail", Collections.singletonMap("orderNo", orderNoForRemindContractAdnInsurance), e);
        }
    }

    /**
     * 通知 SEALAND 訂單投保提醒
     */
    @Async
    public void notifyDealerOrderInsurance(List<String> dealerOrderNoForRemindInsurance) {
        if (dealerOrderNoForRemindInsurance == null || dealerOrderNoForRemindInsurance.isEmpty()) {
            return;
        }
        try {
            String subject2B = "【訂閱車】SEALAND訂單投保提醒";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : SEALAND訂單投保提醒</li>"
                + "<li>訂單編號 : " + String.join("、", dealerOrderNoForRemindInsurance) + "</li>"
                + "</ul>"
                + "<p>"
                + "上述訂單預計於三天後出車，但庫位異常或尚未產生契約，請盡速處理，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(INSURANCE_REMIND)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("SEALAND訂單投保提醒 To B Fail:", e);
            mattermostServer.notify("SEALAND訂單投保提醒 To B Fail", Collections.singletonMap("orderNo", dealerOrderNoForRemindInsurance), e);
        }
    }

    /**
     * 通知 SEALAND 訂單出車異常
     */
    @Async
    public void notifyDealerOrderDepartAbnormal(List<String> dealerOrderNoForDepartAbnormal) {
        if (dealerOrderNoForDepartAbnormal == null || dealerOrderNoForDepartAbnormal.isEmpty()) {
            return;
        }
        try {
            String subject2B = "【訂閱車】SEALAND訂單出車異常";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : SEALAND訂單出車異常提醒</li>"
                + "<li>訂單號碼 : " + String.join("、", dealerOrderNoForDepartAbnormal) + "</li>"
                + "</ul>"
                + "<p>"
                + "訂單完成出車，但庫位異常或未產生契約，請盡速處理，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_DEPART_ABNORMAL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("SEALAND訂單出車異常 To B Fail:", e);
            mattermostServer.notify("SEALAND訂單出車異常 To B Fail", Collections.singletonMap("orderNo", dealerOrderNoForDepartAbnormal), e);
        }
    }


    /**
     * 通知 SEALAND 同步訂單失敗
     */
    @Async
    public void notifySeaLandSyncFail(String orderNo, String syncType, String errorMessage, String systemKind) {
        if (!CsatOrderSource.SEALAND.name().equals(systemKind)) {
            return;
        }
        try {
            String subject2B = "【訂閱車】SEALAND同步訂單失敗";

            String sbf2B = "<ul>"
                + "<li>收文單位：訂閱車業務部</li>"
                + "<li>發文主旨：SEALAND新增/更新訂單失敗</li>"
                + "<li>同步類別：" + syncType + "</li>"
                + "<li>訂單編號：" + orderNo + "</li>"
                + "<li>失敗原因：" + errorMessage + "</li>"
                + "</ul>"
                + "<p>請盡速處理，謝謝!</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(NotifyCategory.SEALAND_SYNC_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("SEALAND同步訂單失敗通知 To B Fail:", e);
            mattermostServer.notify("SEALAND同步訂單失敗通知 To B Fail",
                Collections.singletonMap("orderNo", orderNo), e);
        }
    }

    /**
     * 通知投保使用人異動為客戶
     */
    @Async
    public void notifyApplyInsurance(Orders order) {

        try {
            String plateNo = order.getPlateNo();
            String subject2B = "【訂閱車】投保使用人異動為客戶_" + plateNo;

            String sbf2B = "<ul>"
                + "<li>收文單位 : 契約管理課</li>"
                + "<li>發文主旨 : 訂閱收訂 - 異動使用人為客戶</li>"
                + "<li>車牌號碼 : " + plateNo + "</li>"
                + "<li>契約編號 : " + order.getLrentalContractNo() + "</li>"
                + "</ul>"
                + "<p>"
                + "該車已確認訂閱收訂，有勞貴單位協助異動保險使用人資訊，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(APPLY_INSURANCE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("投保使用人異動為客戶 To B Fail:", e);
            mattermostServer.notify("投保使用人異動為客戶 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 通知投保使用人異動為客戶
     */
    @Async
    public void notifyCancelInsurance(Orders order) {

        try {
            String plateNo = order.getPlateNo();
            String subject2B = "【訂閱車】投保使用人異動為格上_" + plateNo;

            String sbf2B = "<ul>"
                + "<li>收文單位 : 契約管理課</li>"
                + "<li>發文主旨 : 訂閱退訂 - 異動使用人為格上</li>"
                + "<li>車牌號碼 : " + plateNo + "</li>"
                + "<li>契約編號 : " + order.getLrentalContractNo() + "</li>"
                + "</ul>"
                + "<p>"
                + "該車已確認訂閱退訂，有勞貴單位協助異動保險使用人資訊，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(CANCEL_INSURANCE)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("投保使用人異動為格上 To B Fail:", e);
            mattermostServer.notify("投保使用人異動為格上 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }


    /**
     * 通知Etag出車失敗
     *
     * @deprecated 2023/11/7 先不發送通知信，避免門市疑慮，改用mattermost由PM與RD追蹤
     */
    @Async
    @Deprecated
    public void notifyEtagDepartFail(@NonNull Orders order) {

        try {
            String subject2B = "訂閱車訂單 - 遠通出車失敗_" + order.getPlateNo();

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>訂單號碼 : " + order.getOrderNo() + "</li>"
                + "<li>失敗車號 : " + order.getPlateNo() + "</li>"
                + "</ul>"
                + "<p>"
                + " 請盡速將車輛加入遠通車隊或排除其他造成失敗原因，避免還車時 etag 異常。"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(ETAG_DEPART_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(order, subNotify);
        } catch (Exception e) {
            log.error("Etag出車失敗通知 To B Fail:", e);
            mattermostServer.notify("Etag出車失敗通知 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 通知Etag出車失敗(沒加入遠通車隊)
     */
    @Async
    public void notifyEtagDepartFailNotInCarGroup(List<String> plateNos) {

        try {
            String subject2B = "【訂閱車】遠通出車失敗_尚未加入車隊";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 訂閱車訂單 - 遠通出車失敗</li>"
                + "<li>失敗車號 : " + String.join("、", plateNos) + "</li>"
                + "<li>失敗原因：尚未加入車隊</li>"
                + "</ul>"
                + "<p>"
                + "請盡速將車輛加入遠通車隊，避免還車時 etag 異常。"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(ETAG_DEPART_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("Etag出車失敗通知 To B Fail:", e);
            mattermostServer.notify("Etag出車失敗通知 To B Fail", Collections.singletonMap("plateNo", plateNos), e);
        }
    }

    /**
     * 通知Etag出車失敗(沒加入遠通車隊)
     */
    @Async
    public void notifyEtagDepartFailNotApplyETag(List<String> plateNos) {

        try {
            String subject2B = "【訂閱車】遠通出車失敗_尚未申裝etc";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 訂閱車訂單 - 遠通出車失敗</li>"
                + "<li>失敗車號 : " + String.join("、", plateNos) + "</li>"
                + "<li>失敗原因：尚未申裝啟用etc</li>"
                + "</ul>"
                + "<p>"
                + "請盡速將車輛申裝或啟用etc服務，避免還車時 etag 異常。"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(ETAG_DEPART_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("Etag出車失敗通知 To B Fail:", e);
            mattermostServer.notify("Etag出車失敗通知 To B Fail", Collections.singletonMap("plateNo", plateNos), e);
        }
    }

    /**
     * 通知Etag出車失敗(沒加入遠通車隊)
     */
    @Async
    public void notifyAddCarFromCrs(String plateNo) {

        try {
            String subject2B = "【訂閱車】CRS新增車籍資料待確認";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : CRS新增車籍資料待確認</li>"
                + "<li>車牌號碼 : " + String.join("、", plateNo) + "</li>"
                + "</ul>"
                + "<p>"
                + "以上車輛為撥車或採購而新加入訂閱車隊，尚未設定訂閱車籍資料 (方案、類別、車型)，因此無法上架或收訂，請至系統台補充設定，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(ADD_CAR_FROM_CRS)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("CRS增加車籍通知 To B Fail:", e);
            mattermostServer.notify("CRS增加車籍通知 To B Fail", Collections.singletonMap("plateNo", plateNo), e);
        }
    }

    /**
     * 車輛等待申請撥還通知
     */
    @Async
    public void notifyWaitForCarReturn(Map<String, List<BuChangeNotify>> stationNotificationsMap) {

        try {

            String subject2B = "【訂閱車】車輛等待申請撥還通知";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 車輛等待申請撥還</li>"
                + "</ul>"
                + "<p>"
                + "以下為各站訂閱車退訂 (還車/換車/取消)，且初次領牌日至今>1年、非專案車、來源無撥車單或為一般撥車/自動營業撥車、尚未入庫中古所，提供清單以利追蹤完成入庫， 謝謝!"
                + buildWaitForCarReturnHTML(stationNotificationsMap)
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(ETAG_DEPART_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
            log.info("車輛等待申請撥還通知 To B Success");
        } catch (Exception e) {
            log.error("車輛等待申請撥還通知 To B Fail:", e);
            mattermostServer.notify("車輛等待申請撥還通知 To B Fail", Collections.singletonMap("stationNotificationsMap", stationNotificationsMap), e);
        }
    }

    /**
     * 通知立帳失敗
     */
//    @Async
    public void notifyCheckOutFail(List<String> orders) {

        try {
            String dateStr = DateUtils.toDateString(new Date(), "yyyy/MM/dd");
            String subject2B = "【訂閱車】日結異常_" + dateStr;

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 訂閱車訂單 - 有收款/發票金額不平訂單</li>"
                + "<li>日結日期 : " + dateStr + "</li>"
                + "<li>異常訂單 : " + String.join("、", orders) + "</li>"
                + "</ul>"
                + "<p>"
                + "請盡速確認並排除日結異常狀況，避免發生消費爭議，謝謝!"
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(CHECKOUT_FAIL)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("Etag出車失敗通知 To B Fail:", e);
            mattermostServer.notify("日結異常通知 To B Fail", Collections.singletonMap("orders", orders), e);
        }
    }

    /**
     * 通知Etag出車失敗(沒加入遠通車隊)
     */
    @Async
    public void notifyCarChange(Map<String, List<String>> plateNoListMap, String changeType, String changeMemo) {

        try {
            String subject2B = "【訂閱車】車輛管制異常通知";

            String sbf2B = "<ul>"
                + "<li>收文單位 : 訂閱車業務部</li>"
                + "<li>發文主旨 : 車輛管制異常通知</li>"
                + "<li>訂單編號 : " + plateNoListMap.values().stream().flatMap(Collection::stream).collect(Collectors.joining("、")) + "</li>"
                + "<li>車牌號碼 : " + String.join("、", plateNoListMap.keySet()) + "</li>"
                + "</ul>"
                + "<p>"
                + String.format("茲收到該車管制通知( %s ： %s )，因此將訂閱車輛異動為「不可使用」，但該車目前有進行中訂單，請盡速處理，避免造成出車失敗，謝謝!", changeType, changeMemo)
                + "</p>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(CAR_CHANGE_LIMIT)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();

            notify2B(subNotify, new ArrayList<>());
        } catch (Exception e) {
            log.error("Etag出車失敗通知 To B Fail:", e);
            mattermostServer.notify("Etag出車失敗通知 To B Fail", Collections.singletonMap("plateNo", plateNoListMap), e);
        }
    }

    /**
     * 訂閱確認收訂通知 <br/>
     * 固定收件者 <br/>
     * {@code mails} : [ DISPATCH_CENTER, {@code departStation}, {@code locationStationEmail} ] <br/>
     * {@code ccMails} : [ SUB, SUB_BUSINESS ] <br/>
     */
    @Async
    public void notifyOrderReceivedConfirm(IOrder order, Cars useCar, CarBaseInfoSearchResponse useCarCrs) {
        try {

            // to 調度中心 (固定)
            ArrayList<NotifyType.NotifyDepartment> mainMails = new ArrayList<>();
            mainMails.add(DISPATCH_CENTER);

            // cc 訂閱車顧客服務 (固定)
            // cc 訂閱車業務部 (固定)
            List<String> ccMails =
                AppProperties.getNotifyEmails(null, AppProperties.resolveEmails(SUB, SUB_BUSINESS), true);

            // to 訂單出車站所 (固定) {mainContract.departStationName}
            Stations departStation = stationService.findByStationCode(order.getDepartStationCode());
            List<String> additionalMails = new ArrayList<>();
            additionalMails.add(departStation.getEmail());
            String departStationName = departStation.getStationName();

            // PlateNo
            String plateNo = useCar.getPlateNo();

            // {cars.locationStationName} ｜ 非中古則寄給最新一筆還車單之還車站所 (動態)
            String locationStationName = "";
            String realCarLocationStationName = "";
            String locationStationEmail = "";
            if (useCar.getLocationStationCode() != null && !useCar.getLocationStationCode().isEmpty()) {
                Stations locationStation = stationService.findByStationCode(useCar.getLocationStationCode());
                StationDefine.CarplusService useCarCarplusService = locationStation.getCarplusService();
                locationStationEmail = locationStation.getEmail();
                if (Objects.equals(useCarCarplusService, StationDefine.CarplusService.PREOWNED)) {
                    additionalMails.add(locationStationEmail);
                    locationStationName = locationStation.getStationName();
                    realCarLocationStationName = locationStation.getStationName();
                } else {
                    List<MainContractOrderInfoDTO> infos = contractService.getMainContractOrderInfoByPlateNo(plateNo);
                    Optional<MainContractOrderInfoDTO> latestOrderOpt = infos.stream()
                        .filter(dto -> dto.getOrderEndDate() != null)
                        .max(Comparator.comparing(MainContractOrderInfoDTO::getOrderEndDate));
                    if (latestOrderOpt.isPresent()) {
                        String returnStationCode = latestOrderOpt.get().getReturnStationCode();
                        locationStation = stationService.findByStationCode(returnStationCode);
                        additionalMails.add(locationStationEmail);
                        locationStationName = locationStation.getStationName();
                    }
                }
            }

            // {brandName} {modelName}
            CarModel useCarModel = carModelService.findByCarModelCode(useCar.getCarModelCode());
            CarBrand useCarBrand = carBrandService.findByBrandCode(useCarModel.getBrandCode());
            String brandName = useCarBrand.getBrandName();
            String modelName = useCarModel.getCarModelName();
            // {orders.expectedDepartDate} - {orders.expectedReturnDate}
            String format = "yyyy-MM-dd HH:mm:ss";
            String expectedReturnDate = DateUtils.toDateString(Date.from(order.getExpectEndDate()), format);
            String expectedDepartDate = DateUtils.toDateString(Date.from(order.getExpectStartDate()), format);
            // {crs.car_license.licenseStatusName}
            String licenseStatus = CRS.LicenseStatus.of(useCarCrs.getCarLicense().getLicenseStatus()).getDesc();
            // crs.car_base.buName
            String buName = Objects.requireNonNull(BuIdEnum.ofEnum(useCarCrs.getBuId())).getName();

            String subject2B = "【訂閱車】訂閱確認收訂通知 - " + plateNo;
            String sbf2B = "<ul>"
                + "<li>"
                + "收文單位："
                + "<ul>"
                + "<li>" + "調度中心" + "</li>"
                + "<li>" + locationStationName + "</li>"
                + "<li>" + departStationName + "</li>"
                + "</ul>"
                + "</li>"
                + "<li>" + "發文主旨：訂閱車確認收訂通知" + "</li>"
                + "<li>" + "車輛確認收訂，請協助備車，訂閱部同仁將提供車輛申請單。" + "</li>"
                + "<li>" + "車號：" + plateNo + "</li>"
                + "<li>" + "車型：" + brandName + " " + modelName + "</li>"
                + "<li>" + "訂閱出車站點：" + departStationName + "</li>"
                + "<li>" + "實車所在站點：" + realCarLocationStationName + "</li>"
                + "<li>" + "預計租期：" + expectedDepartDate + " - " + expectedReturnDate + "</li>"
                + "<li>" + "車牌狀態：" + licenseStatus + "</li>"
                + "<li>" + "現在庫位：" + buName + "</li>"
                + "</ul>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_ORDER_RECEIVED_CONFIRMATION)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();
            List<String> emails = AppProperties.getNotifyEmails(mainMails, additionalMails, false);

            sendMail(subNotify, order.getOrderNo(), emails, ccMails);
        } catch (Exception e) {
            log.error("訂閱確認收訂通知 To B Fail:", e);
            mattermostServer.notify("訂閱確認收訂通知 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }

    /**
     * 通知出車中換車成功
     */
    @Async
    public void notifyReplaceCarSuccess(Orders order, Cars outCar, Cars inCar, String replaceDateString, String replacedCarMemo) {
        try {
            ArrayList<NotifyType.NotifyDepartment> mainMails = new ArrayList<>();
            mainMails.add(DISPATCH_CENTER);
            mainMails.add(SUB_BUSINESS);

            Stations departStation = stationService.findByStationCode(order.getContract().getMainContract().getDepartStationCode());
            List<String> additionalMails = new ArrayList<>();
            additionalMails.add(departStation.getEmail());
            String departStationName = departStation.getStationName();
            Stations locationStation = stationService.findByStationCode(inCar.getLocationStationCode());
            String locationStationName = locationStation.getStationName();
            additionalMails.add(locationStation.getEmail());

            String subject2B = "【訂閱車】出車中換車 - " + order.getOrderNo();
            String sbf2B =
                    "<ul>"
                        + "<li>"
                        + "收文單位："
                            + "<ul>"
                                + "<li>" + "訂閱管理課" + "</li>"
                                + "<li>" + "訂閱業務課" + "</li>"
                                + "<li>" + "存車站點：" + locationStationName + "</li>"
                                + "<li>" + "出車站點：" + departStationName +  "</li>"
                            + "</ul>"
                        + "</li>"
                        + "<li>" + "訂單編號：" + order.getOrderNo() + "</li>"
                        + "<li>" + "汰換車號：" + outCar.getPlateNo() + "</li>"
                        + "<li>" + "替代車號：" + inCar.getPlateNo() + "</li>"
                        + "<li>" + "換車原因：" + replacedCarMemo + "</li>"
                        + "<li>" + "換車日：" + replaceDateString + "</li>"
                    + "</ul>";

            String content2B = URLEncoder.encode(sbf2B, "UTF-8");

            SubscribeNotify subNotify = SubscribeNotify.builder()
                .category(SUB_REPLACE_CAR_SUCCESS)
                .emailSubject2B(subject2B)
                .emailHtmlTemplate2B(content2B)
                .build();
            List<String> emails = AppProperties.getNotifyEmails(mainMails, additionalMails, false);

            sendMail(subNotify, order.getOrderNo(), emails, null);
        } catch (Exception e) {
            log.error("訂閱確認收訂通知 To B Fail:", e);
            mattermostServer.notify("訂閱確認收訂通知 To B Fail", Collections.singletonMap("orderNo", order.getOrderNo()), e);
        }
    }


    /**
     * 通知 業務端 and 門市端
     **/
    private void notify2B(@NonNull Orders order, @NonNull SubscribeNotify subNotify) {
        notify2B(order, subNotify, new ArrayList<>());
    }

    /**
     * 通知 業務端 and 門市端
     **/
    private void notify2B(@NonNull Orders order, @NonNull SubscribeNotify subNotify, List<String> extraEmails) {

        String stationCode = order.getContract().getMainContract().getDepartStationCode();
        switch (subNotify.getCategory()) {
            case SUB_RENEW_CALL:
            case SUB_NOT_RENEW_CONFIRM:
            case SUB_RETURN_NOT_CLOSE:
            case SUB_RETURN_NOT_CLOSE_REQUEST:
                stationCode = order.getContract().getMainContract().getReturnStationCode();
                break;
            case SUB_RETURN_NOT_CLOSE_SUMMARY:
            case SUB_DEPART_ABNORMAL:
            case ETAG_DEPART_FAIL:
            case APPLY_INSURANCE:
            case CANCEL_INSURANCE:
            case INSURANCE_REMIND:
            case LRENTAL_CHANGE:
            case SUB_MANUAL_REFUND:
                stationCode = null;
                break;
            default:
                break;
        }
        Set<String> emails = getToBMembersEmail(stationCode, subNotify.getCategory());
        emails.addAll(extraEmails);
        Set<String> ccEmail = getToCCEmail(stationCode, subNotify.getCategory());
        sendMail(subNotify, order.getOrderNo(), emails, ccEmail);
    }

    /**
     * 通知 業務端 and 門市端
     **/
    private void notify2B(@NonNull DealerOrder dealerOrder, @NonNull SubscribeNotify subNotify, List<String> extraEmails) {
        String stationCode = dealerOrder.getDepartStation();
        NotifyCategory category = subNotify.getCategory();

        switch (category) {
            case SUB_RENEW_CALL:
            case SUB_NOT_RENEW_CONFIRM:
            case SUB_RETURN_NOT_CLOSE:
                stationCode = dealerOrder.getReturnStation();
                break;
            case SUB_RETURN_NOT_CLOSE_SUMMARY:
            case SUB_DEPART_ABNORMAL:
            case ETAG_DEPART_FAIL:
            case APPLY_INSURANCE:
            case CANCEL_INSURANCE:
            case INSURANCE_REMIND:
            case LRENTAL_CHANGE:
                stationCode = null;
                break;
            default:
                break;
        }
        Set<String> emails = getToBMembersEmail(stationCode, category);
        emails.addAll(extraEmails);
        Set<String> ccEmail = getToCCEmail(null, category);
        sendMail(subNotify, dealerOrder.getOrderNo(), emails, ccEmail);
    }

    private void notify2B(@NonNull SubscribeNotify subNotify, List<String> extraEmails) {
        Set<String> emails = getToBMembersEmail(null, subNotify.getCategory());
        emails.addAll(extraEmails);
        Set<String> ccEmail = getToCCEmail(null, subNotify.getCategory());
        sendMail(subNotify, null, emails, ccEmail);
    }

    private void sendMail(@NonNull SubscribeNotify subNotify, @Nullable String orderNo, Collection<String> emails, Collection<String> ccEmail) {
        try {
            if (!emails.isEmpty() && StringUtils.isNotBlank(subNotify.getEmailHtmlTemplate2B())) {
                Email email = Email.builder()
                    .withoutHtmlParams(true)
                    .receive(String.join(",", emails))
                    .cc((ccEmail == null || ccEmail.isEmpty()) ? null : String.join(",", ccEmail))
                    .subject(StringUtils.trim(subNotify.getEmailSubject2B()))
                    .content(subNotify.getEmailHtmlTemplate2B())
                    .build();
                Notify notify = Notify.builder()
                    .notifyType(NotifyType.EMAIL)
                    .category(subNotify.getCategory())
                    .status(NotifyStatus.pending)
                    .orderNo(StringUtils.trim(orderNo))
                    .defDate(new Date())
                    .notifyContent(objectMapper.writeValueAsString(email))
                    .attachmentUrl(subNotify.getAttachmentUrl())
                    .displayFilename(subNotify.getDisplayFilename())
                    .build();
                pushServer.notifyAndSave(notify);
            }
        } catch (Exception e) {
            log.error("發送訂閱車EMAIL 2B通知失敗, 錯誤={}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 取得給ToB的Email
     */
    private Set<String> getToBMembersEmail(@Nullable String stationCode, @NonNull NotifyCategory category) {
        Set<String> emails = Sets.newHashSet();
        boolean testEnabled = authorityProperties.getTest().isEnabled();
        // authority config
        AuthorityProperties.Department authorityDepartmentConfig = authorityProperties.getDepartmentCode();
        Stations station = StringUtils.isNotBlank(stationCode) ? stationService.findByStationCode(stationCode) : null;
        List<MemberInfo> memberInfos = new ArrayList<>();

        // 根據類別處理主要邏輯
        switch (category) {
            // 授信管理課 (全員)
            case SUB_CREDIT_DEMAND:
                if (testEnabled) {
                    memberInfos.addAll(authorityServer.getMemberInfos(authorityProperties.getTest().getMemberIdCdM()));
                } else if (authorityDepartmentConfig != null) {
                    emails.addAll(AppProperties.resolveEmails(AUDIT_CENTER, SUB, SUB_BUSINESS));
                }
                break;
            // 訂閱車業務管理課 (全員) + 站所
            case SUB_COMPLETE:
            case SUB_RENEW_CONFIRM:
            case SUB_RENEW_CALL:
            case SUB_NOT_RENEW_CONFIRM:
            case SUB_UPDATE_ORDER:
            case SUB_CANCEL_ORDER:
            case SUB_RETURN_COMPLETE:
            case SUB_DAILY_CLOSED_FAIL:
            case SUB_RETURN_NOT_CLOSE:
            case SUB_RETURN_NOT_CLOSE_SUMMARY:
            case SUB_DEPART_NOT_RETURN_SUMMARY:
            case SUB_MONTHLY_FEE_UNPAID:
            case SUB_MANUAL_REFUND:
            case LRENTAL_CANCEL:
            case LRENTAL_CHANGE:
//            case ETAG_DEPART_FAIL: // 待PO測試OK開放
                if (station != null) {
                    emails.add(station.getEmail());
                }
                if (testEnabled) {
                    memberInfos.addAll(authorityServer.getMemberInfos(authorityProperties.getTest().getMemberIdSCarM()));
                } else if (authorityDepartmentConfig != null) {
                    emails.addAll(AppProperties.resolveEmails(SUB, SUB_BUSINESS));
                }

                // 判斷是否加入管理者
                switch (category) {
                    case SUB_COMPLETE:
                    case SUB_RENEW_CONFIRM:
                    case SUB_RENEW_CALL:
                    case SUB_NOT_RENEW_CONFIRM:
                    case SUB_CANCEL_ORDER:
                    case SUB_RETURN_COMPLETE:
                    case SUB_UPDATE_ORDER:
                    case SUB_RETURN_NOT_CLOSE:
                        // 只有短租需寄給督導
                        if (station != null && StationDefine.CarplusService.SHORT_RENT.equals(station.getCarplusService())) {
                            memberInfos.addAll(authorityServer.getAllSupervisor());
                        }
                        break;
                    case SUB_RETURN_NOT_CLOSE_SUMMARY:
                        memberInfos.addAll(authorityServer.getAllSupervisor());
                        break;
                    default:
                        break;
                }

                // 判斷加入訂閱中心
                switch (category) {
                    case SUB_COMPLETE: // 訂閱訂單完成(保證金付完)+訂閱中心mail
                    case SUB_RETURN_COMPLETE:// 訂閱訂單已還車+訂閱中心mail
                    case SUB_CANCEL_ORDER:
                    case SUB_RENEW_CONFIRM:
                    case SUB_UPDATE_ORDER:
                        emails.addAll(AppProperties.resolveEmails(DISPATCH_CENTER));
                        break;
                    default:
                        break;
                }

                // 加入訂閱副總
                if (category == SUB_NOT_RENEW_CONFIRM) {
                    memberInfos.add(authorityServer.getScarGMaster());
                }

                break;
            case TO_FINANCE:
                emails.addAll(AppProperties.resolveEmails(FINANCE));
                break;
            case TO_CONTRACT:
            case APPLY_INSURANCE:
            case CANCEL_INSURANCE:
                emails.addAll(AppProperties.resolveEmails(CONTRACT_MANAGER));
                break;
            case ETAG_DEPART_FAIL:
            case INSURANCE_REMIND:
            case SUB_DEPART_ABNORMAL:
            case SEALAND_SYNC_FAIL:
            case ADD_CAR_FROM_CRS:
            case CHECKOUT_FAIL:
            case CAR_CHANGE_LIMIT:
                if (testEnabled) {
                    memberInfos.addAll(authorityServer.getMemberInfos(authorityProperties.getTest().getMemberIdSCarM()));
                } else if (authorityDepartmentConfig != null) {
                    emails.addAll(AppProperties.resolveEmails(SUB, SUB_BUSINESS));
                }
                break;
            case SUB_RETURN_NOT_CLOSE_REQUEST:
                if (testEnabled) {
                    memberInfos.addAll(authorityServer.getMemberInfos(authorityProperties.getTest().getMemberIdSCarM()));
                } else {
                    emails.addAll(getSupervisorEmail(station, AuditorLevel.MASTER).stream().map(MemberInfo::getEmail).collect(Collectors.toSet()));
                }
                break;
            default:
                break;
        }
        // 統一處理 memberInfos 轉換為 emails
        memberInfos.stream().map(MemberInfo::getEmail).filter(StringUtils::isNotBlank).distinct().forEach(emails::add);
        return emails;
    }


    /**
     * 取得給ToCC的Email
     */
    private Set<String> getToCCEmail(String stationCode, @NonNull NotifyCategory category) {
        Set<String> emails = new HashSet<>();
        boolean testEnabled = authorityProperties.getTest().isEnabled();

        // 先處理測試環境邏輯
        if (testEnabled) {
            return getEmailsFromMemberId(authorityProperties.getTest().getMemberIdSCarM());
        }

        // 獲取車站資訊（按需）
        Stations station = StringUtils.isNotBlank(stationCode) ? stationService.findByStationCode(stationCode) : null;

        // 根據不同通知類型獲取對應郵件
        switch (category) {
            case LRENTAL_CANCEL:
                Optional.ofNullable(station)
                    .map(Stations::getEmail)
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(emails::add);
                break;

            case APPLY_INSURANCE:
            case CANCEL_INSURANCE:
            case INSURANCE_REMIND:
            case SUB_DEPART_ABNORMAL:
            case SEALAND_SYNC_FAIL:
                if (hasAuthorityDepartmentConfig()) {
                    emails.addAll(AppProperties.resolveEmails(SUB, SUB_BUSINESS));
                }
                break;

            case SUB_RETURN_NOT_CLOSE_REQUEST:
                if (hasAuthorityDepartmentConfig()) {
                    emails.addAll(AppProperties.resolveEmails(SUB, SUB_BUSINESS));
                    Optional.ofNullable(station)
                        .map(Stations::getEmail)
                        .filter(StringUtils::isNotBlank)
                        .ifPresent(emails::add);
                }
                break;

            case CHECKOUT_FAIL:
                if (hasAuthorityDepartmentConfig()) {
                    emails.addAll(AppProperties.resolveEmails(FINANCE));
                }
                break;
            default:
                break;
        }

        return emails;
    }

    // 輔助方法：從會員ID獲取郵件列表
    private Set<String> getEmailsFromMemberId(String memberId) {
        return authorityServer.getMemberInfos(memberId).stream()
            .map(MemberInfo::getEmail)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toSet());
    }

    // 輔助方法：檢查是否有部門配置
    private boolean hasAuthorityDepartmentConfig() {
        return authorityProperties.getDepartmentCode() != null;
    }

    public List<MemberInfo> getSupervisorEmail(Stations station, AuditorLevel level) {
        return authorityServer.getMasterByStationAndAuditorLevel(station, level);
    }

    /**
     * 產生收銀台 裁決[取消訂閱訂單退訂政策調整] public 頁面
     */
    private String generatePublicCancelOrder(@NonNull Orders order, @NonNull OrderPriceInfo cancelBookingPriceInfo,
                                             @NonNull AuthUser user,
                                             CancelOrderCalculateResponse cancelOrderCalculateResponse,
                                             Stations stations,
                                             SubscribeLevel subscribeLevel)
        throws UnsupportedEncodingException {
        SecurityDepositInfo securityDepositInfo = order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo();
        cancelOrderCalculateResponse.getLevel().calculateCancelPolicy(securityDepositInfo.getSecurityDeposit());
        MainContract mainContract = order.getContract().getMainContract();
        PriceInfo priceInfo = mainContract.getOriginalPriceInfo();

        String url = cashierHost + "/public/cancelSubscribe?";
        url += "orderNo=" + order.getOrderNo(); // 訂單編號
        url += "&stationName=" + encode(stations.getStationName(), "UTF-8"); // 站所名稱
        url += "&key=" + cancelBookingPriceInfo.getUid(); // 裁決對應的請求
        url += "&empId=" + cancelBookingPriceInfo.getInfoDetail().getAdminId(); // 員工編號
        url += "&type=" + EmpDiscountDefine.DiscountType.CANCEL.name(); // 折扣項目類型
        url += "&remark=" + encode(StringUtils.trim(cancelBookingPriceInfo.getInfoDetail().getReason()), "UTF-8"); // 說明


        url += "&originalRate=" + cancelOrderCalculateResponse.getLevel().getCommissionRate(); // 原取消政策手續費率
        url += "&rate=" + cancelOrderCalculateResponse.getLevelRefundAmtMap().values().stream()
            .peek(c -> c.calculateCancelPolicy(securityDepositInfo.getSecurityDeposit()))
            .filter(value -> cancelBookingPriceInfo.getAmount().equals(value.getRefundAmt()))
            .findAny().map(CancellationPolicy::getCommissionRate).orElse((double) (100 - Math.round((cancelBookingPriceInfo.getAmount() * 1.0 / securityDepositInfo.getSecurityDeposit()) * 100.0d))); // 申請取消政策手續費率
        url += "&paidAmt=" + order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit(); // 已收保證金
        url += "&commissionFee=" + (securityDepositInfo.getPaidSecurityDeposit() - cancelBookingPriceInfo.getAmount()); // 手續費
        url += "&unpaidAmt=" + cancelBookingPriceInfo.getAmount(); // 退款金額
        url += "&carState=" + priceInfo.getCarState();
        url += "&months=" + order.getMonth(); // 月數
        url += "&monthlyFee=" + priceInfo.getMonthlyFee(); // 月費
        url += "&discountMonthlyFee=" + priceInfo.getDiscountMonthlyFee(); // 優惠月費
        url += "&useMonthlyFee=" + priceInfo.getUseMonthlyFee(); // 實際月費
        url += "&totalMonthlyFee=" + priceInfo.getTotalMonthlyFee(); // 總月費
        url += "&custName=" + encode(StringUtils.trim(user.getAcctName()), "UTF-8");
        url += "&plateNo=" + mainContract.getPlateNo();
        url += "&carLevel=" + encode(StringUtils.trim(subscribeLevel.getName()), "UTF-8");
        return url;
    }

    /**
     * 產生收銀台 裁決[提前還車退款] public 頁面
     */
    private String generatePublicEarlyReturn(@NonNull Orders order, Stations stations, @NonNull OrderPriceInfo orderPriceInfo, @NonNull AuthUser user, SubscribeLevel subscribeLevel) throws UnsupportedEncodingException {
        String url = cashierHost + "/public/subscribeEarlyReturn?";
        url += "orderNo=" + order.getOrderNo(); // 訂單編號
        url += "&stationName=" + encode(stations.getStationName(), "UTF-8"); // 站所名稱
        url += "&key=" + orderPriceInfo.getUid(); // 裁決對應的請求
        url += "&empId=" + orderPriceInfo.getInfoDetail().getAdminId(); // 員工編號
        url += "&type=" + EmpDiscountDefine.DiscountType.EARLY_RETURN.name(); // 折扣項目類型
        url += "&discount=" + orderPriceInfo.getInfoDetail().getDiscount(); // 折抵金額
        url += "&remark=" + encode(StringUtils.trim(orderPriceInfo.getInfoDetail().getReason()), "UTF-8"); // 說明
        url += "&oriDays=" + orderPriceInfo.getInfoDetail().getDay(); // 原合約天數
        url += "&days=" + orderPriceInfo.getInfoDetail().getDelayDays(); // 使用天數
        MainContract mainContract = order.getContract().getMainContract();
        PriceInfo priceInfo = mainContract.getOriginalPriceInfo();
        url += "&months=" + order.getMonth(); // 月數
        url += "&monthlyFee=" + priceInfo.getMonthlyFee(); // 月費
        url += "&discountMonthlyFee=" + priceInfo.getDiscountMonthlyFee(); // 優惠月費
        url += "&useMonthlyFee=" + priceInfo.getUseMonthlyFee(); // 實際月費
        url += "&totalMonthlyFee=" + priceInfo.getTotalMonthlyFee(); // 總月費
        url += "&originalDiscount=" + orderPriceInfo.getInfoDetail().getOriginAmount(); // 原預估折扣金額
        url += "&custName=" + encode(StringUtils.trim(user.getAcctName()), "UTF-8");
        url += "&plateNo=" + mainContract.getPlateNo();
        url += "&carLevel=" + encode(StringUtils.trim(subscribeLevel.getName()), "UTF-8");
        url += "&carState=" + priceInfo.getCarState();
        return url;
    }

    /**
     * 產生收銀台 裁決[延後還車退款] public 頁面
     */
    private String generatePublicDelayReturn(@NonNull Orders order, Stations stations, @NonNull OrderPriceInfo orderPriceInfo, @NonNull AuthUser user, SubscribeLevel subscribeLevel, ReturnLateCalculateResponse lateCalculate)
        throws UnsupportedEncodingException {
        String url = cashierHost + "/public/subscribeDelayReturn?";
        url += "orderNo=" + order.getOrderNo(); // 訂單編號
        url += "&stationName=" + encode(stations.getStationName(), "UTF-8"); // 站所名稱
        url += "&key=" + orderPriceInfo.getUid(); // 裁決對應的請求
        url += "&empId=" + orderPriceInfo.getInfoDetail().getAdminId(); // 員工編號
        url += "&type=" + EmpDiscountDefine.DiscountType.DELAY_RETURN.name(); // 折扣項目類型
        url += "&discount=" + orderPriceInfo.getInfoDetail().getDiscount(); // 折抵金額
        url += "&remark=" + encode(StringUtils.trim(orderPriceInfo.getInfoDetail().getReason()), "UTF-8"); // 說明
        url += "&delayReturnPrice=" + lateCalculate.getSumAmount(); // 原延後還車應收金額
        url += "&oriDays=" + orderPriceInfo.getInfoDetail().getDay(); // 原合約天數
        url += "&days=" + orderPriceInfo.getInfoDetail().getDelayDays(); // 延遲天數
        MainContract mainContract = order.getContract().getMainContract();
        PriceInfo priceInfo = mainContract.getOriginalPriceInfo();
        url += "&months=" + order.getMonth(); // 月數
        url += "&monthlyFee=" + priceInfo.getMonthlyFee(); // 月費
        url += "&discountMonthlyFee=" + priceInfo.getDiscountMonthlyFee(); // 優惠月費
        url += "&useMonthlyFee=" + priceInfo.getUseMonthlyFee(); // 實際月費
        url += "&totalMonthlyFee=" + priceInfo.getTotalMonthlyFee(); // 總月費
        url += "&custName=" + encode(StringUtils.trim(user.getAcctName()), "UTF-8");
        url += "&plateNo=" + mainContract.getPlateNo();
        url += "&carLevel=" + encode(StringUtils.trim(subscribeLevel.getName()), "UTF-8");
        url += "&carState=" + priceInfo.getCarState();
        return url;
    }

    /**
     * 產生收銀台 裁決[延後付款折扣] public 頁面
     */
    private String generatePublicDelayPaid(@NonNull Orders order, Stations stations, @NonNull OrderPriceInfo orderPriceInfo, @NonNull AuthUser user, SubscribeLevel subscribeLevel)
        throws UnsupportedEncodingException {
        String url = cashierHost + "/public/subscribeDelayPaid?";
        url += "orderNo=" + order.getOrderNo(); // 訂單編號
        url += "&stationName=" + encode(stations.getStationName(), "UTF-8"); // 站所名稱
        url += "&key=" + orderPriceInfo.getUid(); // 裁決對應的請求
        url += "&empId=" + orderPriceInfo.getInfoDetail().getAdminId(); // 員工編號
        url += "&type=" + EmpDiscountDefine.DiscountType.DELAY_PAID.name(); // 折扣項目類型
        url += "&discount=" + orderPriceInfo.getInfoDetail().getDiscount(); // 折抵金額
        url += "&remark=" + encode(StringUtils.trim(orderPriceInfo.getInfoDetail().getReason()), "UTF-8"); // 說明
        url += "&delayPaidPrice=" + orderPriceInfo.getInfoDetail().getOriginAmount(); // 原延後還車應收金額
        url += "&oriDays=" + orderPriceInfo.getInfoDetail().getDay(); // 原合約天數
        url += "&days=" + orderPriceInfo.getInfoDetail().getDelayDays(); // 延遲天數
        MainContract mainContract = order.getContract().getMainContract();
        PriceInfo priceInfo = mainContract.getOriginalPriceInfo();
        url += "&months=" + order.getMonth(); // 月數
        url += "&monthlyFee=" + priceInfo.getMonthlyFee(); // 月費
        url += "&discountMonthlyFee=" + priceInfo.getDiscountMonthlyFee(); // 優惠月費
        url += "&useMonthlyFee=" + priceInfo.getUseMonthlyFee(); // 實際月費
        url += "&totalMonthlyFee=" + priceInfo.getTotalMonthlyFee(); // 總月費
        url += "&custName=" + encode(StringUtils.trim(user.getAcctName()), "UTF-8");
        url += "&plateNo=" + mainContract.getPlateNo();
        url += "&carLevel=" + encode(StringUtils.trim(subscribeLevel.getName()), "UTF-8");
        url += "&carState=" + priceInfo.getCarState();
        return url;
    }

    /**
     * 產生收銀台 裁決[訂閱折扣] public 頁面
     */
    private String generatePublicHourAmount(@NonNull Orders order, Stations stations, @NonNull OrderPriceInfo orderPriceInfo, @NonNull AuthUser user, SubscribeLevel subscribeLevel) throws UnsupportedEncodingException {
        String url = cashierHost + "/public/subscribeDiscount?";
        url += "orderNo=" + order.getOrderNo(); // 訂單編號
        url += "&stationName=" + encode(stations.getStationName(), "UTF-8"); // 站所名稱
        url += "&key=" + orderPriceInfo.getUid(); // 裁決對應的請求
        url += "&empId=" + orderPriceInfo.getInfoDetail().getAdminId(); // 員工編號
        url += "&type=" + EmpDiscountDefine.DiscountType.DELAY_RETURN.name(); // 折扣項目類型
        url += "&discount=" + orderPriceInfo.getInfoDetail().getDiscount(); // 折抵金額
        url += "&remark=" + encode(StringUtils.trim(orderPriceInfo.getInfoDetail().getReason()), "UTF-8"); // 說明
        MainContract mainContract = order.getContract().getMainContract();
        PriceInfo priceInfo = mainContract.getOriginalPriceInfo();
        url += "&custName=" + encode(StringUtils.trim(user.getAcctName()), "UTF-8");
        url += "&plateNo=" + mainContract.getPlateNo();
        url += "&carLevel=" + encode(StringUtils.trim(subscribeLevel.getName()), "UTF-8");
        url += "&useMonthlyFee=" + priceInfo.getUseMonthlyFee();
        url += "&carState=" + priceInfo.getCarState();
        url += "&months=" + order.getMonth(); // 月數
        return url;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void addEmpDiscountRequest(@NonNull Orders order, List<OrderPriceInfo> orderPriceInfoList, PriceInfoDefinition.PriceInfoCategory category) {
        MemberInfo memberInfo = Optional.ofNullable(authorityServer.getScarMMaster()).orElseThrow(() -> new ServerException("無法取得授權人員資訊"));

        Stations stations = stationService.findByStationCode(order.getContract().getMainContract().getDepartStationCode());
        try {
            // 產生短網址
            String shortUrl = generateEmpDiscountShortUrl(order, orderPriceInfoList, stations, category);
            String content = getEmpSmsContent(category, shortUrl, stations);
            notifyToMaster(order, content, memberInfo);
        } catch (Exception e) {
            mattermostServer.notify(String.format("發送裁決請求簡訊失敗 訂單編號:%s", order.getOrderNo()), null, e);
            log.error("發送裁決請求簡訊失敗 訂單編號={}, 錯誤={}", order.getOrderNo(), e.getMessage());
        }
    }

    private String getEmpSmsContent(PriceInfoDefinition.PriceInfoCategory category, String shortUrl, Stations stations) {
        switch (category) {
            case CancelBooking:
                return String.format("%s 門市請求%s，請裁示。%s", stations.getStationName(), "退訂折扣", shortUrl);
            case ReturnEarly:
                return String.format("%s 門市請求%s，請裁示。%s", stations.getStationName(), "提前還車退款", shortUrl);
            case ReturnLate:
                return String.format("%s 門市請求%s，請裁示。%s", stations.getStationName(), "逾期還車折扣", shortUrl);
            case EmpDiscount:
                return String.format("%s 門市請求%s，請裁示。%s", stations.getStationName(), "訂閱折扣", shortUrl);
            case PayLate:
                return String.format("%s 門市請求%s，請裁示。%s", stations.getStationName(), "逾期付款折扣", shortUrl);
            default:
        }
        return "";
    }

    private String generateEmpDiscountShortUrl(Orders order, List<OrderPriceInfo> orderPriceInfoList, Stations stations, PriceInfoDefinition.PriceInfoCategory category) throws UnsupportedEncodingException {
        String url = "";
        AuthUser user = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
        SubscribeLevel level = subscribeLevelService.findByLevel(order.getContract().getMainContract().getGivenCarLevel());
        switch (category) {
            case CancelBooking:
                CancelOrderCalculateResponse cancelOrderCalculateResponse = PriceUtils.calculateCancelOrder(order, true);
                url = generatePublicCancelOrder(order, orderPriceInfoList.get(0), user, cancelOrderCalculateResponse, stations, level);
                break;
            case ReturnLate:
                ReturnLateCalculateResponse lateCalculate = orderService.dropOffCarLatelyView(order);
                url = generatePublicDelayReturn(order, stations, orderPriceInfoList.get(0), user, level, lateCalculate);
                break;
            case ReturnEarly:
                url = generatePublicEarlyReturn(order, stations, orderPriceInfoList.get(0), user, level);
                break;
            case PayLate:
                url = generatePublicDelayPaid(order, stations, orderPriceInfoList.get(0), user, level);
                break;
            default:
                url = generatePublicHourAmount(order, stations, orderPriceInfoList.get(0), user, level);
        }


        return shorterServer.createShort(Instant.now().plus(2, ChronoUnit.HOURS).toEpochMilli(), url);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void notifyToMaster(Orders order, String content, MemberInfo memberInfo) throws JsonProcessingException {
        Sms s = Sms.builder()
            .nationalCode("886")
            .msgcontent(content)
            .msgmemobile(Optional.ofNullable(memberInfo.getPhone()).orElseThrow(() -> new ServerException("無法取得授權人員電話")))
            .msgtype("0")
            .sender("GOSMART")
            .build();
        Notify n = Notify.builder()
            .notifyType(NotifyType.SMS)
            .status(NotifyStatus.pending)
            .orderNo(order.getOrderNo())
            .defDate(new Date(System.currentTimeMillis()))
            .notifyContent(objectMapper.writeValueAsString(s))
            .build();
        pushServer.notifyAndSave(n);
    }

}
