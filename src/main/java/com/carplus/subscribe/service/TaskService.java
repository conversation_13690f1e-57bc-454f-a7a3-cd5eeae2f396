package com.carplus.subscribe.service;

import com.carplus.subscribe.schedule.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TaskService {

    @Autowired
    private StationsTask stationsTask;

    @Autowired
    private OrderPriceInfoTask orderPriceInfoTask;

    @Autowired
    private ETagTask eTagTask;

    @Autowired
    private OrderTask orderTask;

    @Autowired
    private CheckoutTask checkoutTask;

    @Autowired
    private CarsService carsService;

    @Autowired
    private BuChangeService buChangeService;

    /**
     * 同步站點資料
     */
    @Async
    public void syncStationsData() {
        stationsTask.syncStationsDataTask();
    }

    /**
     * ETag 還車 查詢 出車
     */
    @Async
    public void returnAndQueryAndDepartCar() {
        eTagTask.returnAndQueryAndDepartCarTask();
    }

    /**
     * 累加 罰金
     */
    @Async
    public void accumulateFines() {
        orderPriceInfoTask.accumulateFinesTask();
    }

    /**
     * 檢查訂單有無未繳款狀態
     */
    @Async
    public void checkOrderIsUnpaid() {
        orderPriceInfoTask.checkOrderIsUnpaidTask();
    }

    /**
     * 通知每期費用繳款
     */
    @Async
    public void notifyPayStageFee() {
        orderPriceInfoTask.notifyPayStageFee();
    }


    /**
     * 續約提醒
     */
    @Async
    public void notifyRenewCall() {
        orderTask.notifyRenewCallTask();
    }

    /**
     * 還車提醒
     */
    @Async
    public void notifyReturn() {
        orderTask.executeNotifyReturnTask();
    }

    /**
     * 已出車且出車失敗的車五天後重新對遠通出車
     */
    @Async
    public void recallEtagDepartFail() {
        eTagTask.recallEtagDepartFail();
    }


    /**
     * 取消保證金未付款
     */
    @Async
    public void cancelOrderWithoutSecurityDeposit() {
        orderTask.cancelOrderWithoutSecurityDeposit();
    }

    /**
     * 長租契約自動展期
     */
    @Async
    public void autoExpendLrentalContract() {
        orderTask.autoExpendLrentalContract();
    }

    @Async
    public void autoExpendLrentalDealerContract() {
        orderTask.autoExpendLrentalDealerContract();
    }

    /**
     * 訂單日結
     */
    @Async
    public void checkoutTask() {
        checkoutTask.dailyCheckout();
    }

    /**
     * 已還車未結案通知
     */
    @Async
    public void notifyReturnNotClose() {
        orderTask.notifyReturnNotClose();
    }

    /**
     * 已出車未還車通知
     */
    public void notifyDepartNotReturn() {
        orderTask.notifyDepartNotReturn();
    }

    public void notifyMonthlyFeeUnpaid() {
        orderTask.notifyMonthlyFeeUnpaid();
    }

    public void syncStdPriceToCar() {
        carsService.syncStdPriceToCar();
    }

    public void syncEnergyTypeToCar() {
        carsService.syncEnergyTypeToCar();
    }

    public void notifyOrderWithoutContract() {
        orderTask.notifyOrderWithoutContract();
    }

    public void notifyDealerOrderInsurance() {
        orderTask.notifyDealerOrderInsurance();
    }

    public void notifyDealerOrderDepartAbnormal() {
        orderTask.notifyDealerOrderDepartAbnormal();
    }

    public void syncCarsMfgMonthFromCrs() {
        carsService.syncCarsMfgMonthFromCrs();
    }

    @Async
    public void syncCrsCarNoAndLaunchedToCar() {
        carsService.syncCrsCarNoAndLaunchedToCar();
    }

    @Async
    public void syncCrsIsProjectCar() {
        carsService.syncCrsIsProjectCar();
    }

    @Async
    public void notifyReturnCar() {
        buChangeService.notifyReturn();
    }
}
