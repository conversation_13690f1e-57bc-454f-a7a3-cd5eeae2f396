package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.CsatQuestRepository;
import com.carplus.subscribe.db.mysql.dao.CsatRefusedRepository;
import com.carplus.subscribe.db.mysql.dao.CsatRepository;
import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import com.carplus.subscribe.db.mysql.entity.csat.CsatRefused;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CsatOrderSource;
import com.carplus.subscribe.enums.CustSource;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserUnionQuery;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.csat.*;
import com.carplus.subscribe.model.request.dealer.DealerOrderCriteria;
import com.carplus.subscribe.model.request.order.OrdersCriteria;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.utils.CsvUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CSAT_STATUS_NOT_COMPLETED;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CSAT_TASK_NOT_FOUND;

@Service
public class CsatService {

    @Autowired
    private CsatRepository csatRepository;

    @Autowired
    private CsatQuestRepository csatQuestRepository;

    @Autowired
    private CsatRefusedRepository csatRefusedRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private DealerOrderService dealerOrderService;

    @Autowired
    private StationService stationService;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private CarsService carsService;

    @Transactional
    public void createCsatByOrders(List<String> orderNos) {
        List<Orders> ordersList = orderService.getOrders(orderNos);
        ordersList.forEach(this::createCsat);
        List<DealerOrder> dealerOrders = dealerOrderService.getOrdersByOrderNos(orderNos);
        dealerOrders.forEach(this::createCsat);
    }


    /**
     * 建立電訪任務
     */
    @Transactional
    public void createCsat(Orders order) {
        List<Csat> csatList = csatRepository.findByOrders(Collections.singletonList(order.getOrderNo()));
        if (csatList.isEmpty()) {
            Csat csat = new Csat();
            csat.setOrderNo(order.getOrderNo());
            csat.setStatus(CsatStatus.NOT_CALLED.getCode());
            csat.setSource(CsatOrderSource.SUBSCRIBE.getCode());
            csat.setQuestStatus(CsatQuestStatus.NOT_SURVEYED.getCode());
            csat.setPlateNo(order.getPlateNo());
            LocalDateTime dateTime = Instant.now().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay().plus(1, ChronoUnit.MONTHS).with(ChronoField.DAY_OF_MONTH, 1);
            csat.setAssignYearMonth(dateTime.getYear() * 100 + dateTime.getMonthValue());
            csatRepository.saveAndFlush(csat);
            CsatQuest quest = new CsatQuest();
            quest.setCsatId(csat.getId());
            quest.setVersion(0);
            csatQuestRepository.save(quest);
        }
    }

    /**
     * 建立電訪任務
     */
    public void createCsat(DealerOrder dealerOrder) {
        List<Csat> csatList = csatRepository.findByOrders(Collections.singletonList(dealerOrder.getOrderNo()));
        if (csatList.isEmpty()) {
            Csat csat = new Csat();
            csat.setOrderNo(dealerOrder.getOrderNo());
            csat.setStatus(CsatStatus.NOT_CALLED.getCode());
            csat.setSource(CsatOrderSource.of(dealerOrder.getDealerName()).getCode());
            csat.setQuestStatus(CsatQuestStatus.NOT_SURVEYED.getCode());
            csat.setPlateNo(dealerOrder.getPlateNo());
            LocalDateTime dateTime = Instant.now().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay().plusMonths(1).withDayOfMonth(1);
            csat.setAssignYearMonth(dateTime.getYear() * 100 + dateTime.getMonthValue());
            csatRepository.saveAndFlush(csat);
            CsatQuest quest = new CsatQuest();
            quest.setCsatId(csat.getId());
            quest.setVersion(0);
            csatQuestRepository.save(quest);
        }
    }


    public List<CsatResponse> search(PageRequest pageRequest, CsatCriteria criteria) {

        List<Csat> csatList = csatRepository.query(criteria, pageRequest.getLimit(), pageRequest.getSkip());
        CsatResponseDataWrapper wrapper = csatResponseDataPrepare(csatList);
        return csatList.stream().map(csat ->
            generateCsatResponse(wrapper, csat)
        ).collect(Collectors.toList());
    }

    private static CsatResponse generateCsatResponse(CsatResponseDataWrapper wrapper, Csat csat) {
        CsatResponse response = new CsatResponse();
        response.setId(csat.getId());
        response.setOrderNo(csat.getOrderNo());
        response.setSource(csat.getSource());

        response.setPlateNo(csat.getPlateNo());
        if (csat.isDealer()) {
            generateFromDealerOrderInfo(wrapper, csat, response);
        } else {
            generateFromOrderInfo(wrapper, csat, response);
        }
        MemberInfo memberInfo = wrapper.getMemberInfoMap().get(csat.getMemberId());
        response.setAssignMemberId(csat.getMemberId());
        if (memberInfo != null) {
            response.setAssignMemberName(memberInfo.getMemberName());
        }
        response.setStatus(CsatStatus.of(csat.getStatus()));
        response.setAssignDate(csat.getContractDate() != null ? Date.from(csat.getContractDate()) : null);
        CarBrandModelDTO carBrandModelDTO = wrapper.getCarBrandModelDTOMap().get(csat.getPlateNo());
        if (carBrandModelDTO != null) {
            Optional.ofNullable(carBrandModelDTO.getBrand()).ifPresent(brand -> response.setCarBrand(brand.getBrandNameEn()));
            Optional.ofNullable(carBrandModelDTO.getModel()).ifPresent(model -> response.setCarModel(model.getCarModelName()));
        }
        return response;
    }


    /**
     * 分頁式
     */
    public Page<CsatResponse> searchPage(PageRequest pageRequest, CsatCriteria criteria) {
        long total = csatRepository.count(criteria);
        if (total == 0) {
            return Page.of(total, new ArrayList<>(), pageRequest.getSkip(), pageRequest.getLimit());
        }
        List<CsatResponse> result = search(pageRequest, criteria);
        return Page.of(total, result, pageRequest.getSkip(), pageRequest.getLimit());
    }

    public CsvUtil.ByteArrayOutputStream2ByteBuffer csatExport(CsatCriteria criteria) {
        List<Csat> csatList = csatRepository.query(criteria, Integer.MAX_VALUE, 0);
        CsatResponseDataWrapper wrapper = csatResponseDataPrepare(csatList);
        List<CsatResponse> csatResponseList = csatList.stream().map(csat ->
            generateCsatResponse(wrapper, csat)
        ).collect(Collectors.toList());
        Map<Integer, CsatQuest> csatQuestMap = csatQuestRepository.findAllById(csatList.stream().map(Csat::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(CsatQuest::getCsatId, Function.identity()));

        List<CsatCsv> csvList =
            csatResponseList.stream().map(response -> new CsatCsv(response, wrapper.getCsatMap().get(response.getId()), convertToVersionCsatQuest(csatQuestMap.get(response.getId())), wrapper.getCarBrandModelDTOMap().get(response.getPlateNo())))
                .collect(Collectors.toList());

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            csvList,
            CsatCsv.HEADS,
            true,
            ',',
            out,
            Charset.forName("big5"),
            CsatCsv.class
        );
        return out;

    }


    private static void generateFromOrderInfo(CsatResponseDataWrapper wrapper, Csat csat, CsatResponse response) {
        Orders order = wrapper.getOrdersMap().get(csat.getOrderNo());
        AuthUser user = wrapper.getAuthUserMap().get(order.getContract().getMainContract().getAcctId());
        if (user != null) {
            response.setUserName(user.getAcctName());
            response.setMainCell(user.getMainCell());
            MemberInfo memberInfo = wrapper.getMemberInfoMap().get(order.getDepartMemberId());
            response.setDepartMemberId(order.getDepartMemberId());
            if (memberInfo != null) {
                response.setDepartMemberName(memberInfo.getMemberName());
            }
            CsatRefused csatRefused = wrapper.getRefusedMap().get(user.getLoginId());
            if (csatRefused != null && csatRefused.isRefused()) {
                response.setIsRefuse(csatRefused.isRefused());
                response.setRefusedMemo(csatRefused.getRefuseMemo());
            }
            response.setIdNo(user.getLoginId());
        }
        response.setDepartDate(order.getStartDate());
        response.setDepartStationName(wrapper.getStationsMap().get(order.getContract().getMainContract().getDepartStationCode()).getStationName());
        response.setIsNewOrder(order.getIsNewOrder());
    }

    private static void generateFromDealerOrderInfo(CsatResponseDataWrapper wrapper, Csat csat, CsatResponse response) {
        DealerOrderQueryResponse dealerOrder = wrapper.getDealerOrderMap().get(csat.getOrderNo());
        response.setUserName(dealerOrder.getCustomerInfo().getUserName());
        response.setMainCell(dealerOrder.getCustomerInfo().getMainCell());
        response.setDepartStationName(wrapper.getStationsMap().get(dealerOrder.getSubscriptionInfo().getDepartStation()).getStationName());
        CsatRefused csatRefused = wrapper.getRefusedMap().get(dealerOrder.getCustomerInfo().getIdNo());
        if (csatRefused != null && csatRefused.isRefused()) {
            response.setIsRefuse(csatRefused.isRefused());
            response.setRefusedMemo(csatRefused.getRefuseMemo());
        }
        response.setDepartDate(dealerOrder.getSubscriptionInfo().getDepartDate());
        response.setIdNo(dealerOrder.getCustomerInfo().getIdNo());
        response.setIsNewOrder(dealerOrder.getIsNewOrder());
    }


    private CsatResponseDataWrapper csatResponseDataPrepare(List<Csat> csatList) {
        CsatResponseDataWrapper wrapper = new CsatResponseDataWrapper();
        wrapper.setCsatMap(csatList.stream().collect(Collectors.toMap(Csat::getId, Function.identity())));
        csatList.forEach(csat -> {
            wrapper.getPlateNos().add(csat.getPlateNo());
            Optional.ofNullable(csat.getMemberId()).ifPresent(memberId -> wrapper.getMemberIds().add(memberId));
            if (csat.isDealer()) {
                wrapper.getDealerOrders().add(csat.getOrderNo());
            } else {
                wrapper.getOrderNos().add(csat.getOrderNo());
            }
        });
        wrapper.setOrdersMap(orderService.getOrders(new ArrayList<>(wrapper.getOrderNos()))
            .stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity())));

        wrapper.setDealerOrderMap(dealerOrderService.getOrders(new ArrayList<>(wrapper.getDealerOrders()))
            .stream().collect(Collectors.toMap(DealerOrderQueryResponse::getOrderNo, Function.identity())));

        wrapper.setCarBrandModelDTOMap(carsService.getCarBrandModelDTOByCars(new ArrayList<>(wrapper.getPlateNos()))
            .stream().collect(Collectors.toMap(dto -> dto.getCar().getPlateNo(), Function.identity())));

        wrapper.getOrdersMap().values().forEach(o -> {
            wrapper.getAcctIds().add(o.getContract().getMainContract().getAcctId());
            wrapper.getMemberIds().add(o.getDepartMemberId());
            wrapper.getMemberIds().add(o.getReturnMemberId());
        });
        for (List<Integer> acctIds : Lists.partition(new ArrayList<>(wrapper.getAcctIds()), 200)) {
            wrapper.getAuthUserMap().putAll(authServer.getUserAcctIds(acctIds.toArray(new Integer[0])).stream().collect(Collectors.toMap(AuthUser::getAcctId, Function.identity())));
        }

        for (List<Integer> acctIds : Lists.partition(wrapper.getDealerOrderMap().values().stream().map(DealerOrderQueryResponse::getDealerUserId).map(Long::intValue).collect(Collectors.toList()), 200)) {
            AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
            query.setIds(acctIds);
            wrapper.getDealerUserMap().putAll(authServer.getDealerUsers(query).stream().collect(Collectors.toMap(o -> o.getId().intValue(), Function.identity())));
        }
        wrapper.setMemberInfoMap(
            wrapper.getMemberIds().stream().map(memberId -> authorityServer.getMemberInfos(memberId)
                    .stream().findAny().orElse(null)).filter(Objects::nonNull)
                .collect(Collectors.toMap(MemberInfo::getMemberId, Function.identity())));
//        wrapper.getLoginId().addAll(wrapper.getAuthUserMap().values().stream().map(AuthUser::getLoginId).collect(Collectors.toList()));
//        wrapper.getLoginId().addAll(wrapper.getDealerOrderMap().values().stream().map(DealerOrderQueryResponse::getIdNo).collect(Collectors.toList()));
        wrapper.setRefusedMap(csatRefusedRepository.findByAcctIds(new ArrayList<>(wrapper.getAuthUserMap().keySet()))
            .stream().collect(Collectors.toMap(refused -> wrapper.getAuthUserMap().get(refused.getAcctId()).getLoginId(), Function.identity())));
        wrapper.getRefusedMap().putAll(csatRefusedRepository.findBySeaLandAcctIds(new ArrayList<>(wrapper.getDealerUserMap().keySet()))
            .stream().collect(Collectors.toMap(refused -> wrapper.getDealerUserMap().get(refused.getSeaLandAcctId()).getIdNo(), Function.identity())));
        wrapper.setStationsMap(stationService.findAll().stream().collect(Collectors.toMap(Stations::getStationCode, Function.identity())));
        return wrapper;
    }


    /**
     * 取得拒訪資訊
     */
    public CsatRefusedResponse getCsatRefused(String loginId) {
        CsatRefusedResponse response = new CsatRefusedResponse();
        response.setLoginId(loginId);
        AuthUser authUser = authServer.getUserAcctIds(null, null, loginId).stream().findAny().orElse(null);
        AuthDealerUserResponse authDealerUser = null;
        CsatRefused refused = null;

        if (authUser != null) {
            refused = csatRefusedRepository.findByAcctId(authUser.getAcctId());
            response.setAuthUserName(authUser.getAcctName());
        }

        if ((authDealerUser = authServer.getDealerUser(loginId)) != null) {
            if (refused == null) {
                refused = csatRefusedRepository.findBySeaLandAcctId(authDealerUser.getId().intValue());
            }
            response.setDealerUserName(authDealerUser.getUserName());
        }

        if (refused != null) {
            response.setIsRefused(refused.isRefused());
            response.setRefuseMemo(refused.getRefuseMemo());
            response.setRefuseDate(refused.getRefusalDate());
        }

        return response;
    }

    /**
     * 更新拒訪資訊
     */
    public void upsetRefused(CsatRefusedRequest request) {
        CsatRefused refused = null;
        AuthUser authUser;
        AuthDealerUserResponse authDealerUserResponse;

        // 嘗試獲取 dealer user 和 auth user
        authDealerUserResponse = authServer.getDealerUser(request.getLoginId());
        authUser = authServer.getUserAcctIds(null, null, request.getLoginId()).stream().findAny().orElse(null);

        // 根據 source 和用戶類型查找現有的拒訪記錄
        if (Objects.equals(request.getSource(), CustSource.SEALAND.getSource()) && authDealerUserResponse != null) {
            refused = csatRefusedRepository.findBySeaLandAcctId(authDealerUserResponse.getId().intValue());
        } else if (!Objects.equals(request.getSource(), CustSource.SEALAND.getSource()) && authUser != null) {
            refused = csatRefusedRepository.findByAcctId(authUser.getAcctId());
        }

        // 如果沒有找到現有記錄則創建新的
        if (refused == null) {
            refused = new CsatRefused();
        }

        // 設置 SeaLandAcctId 和 AcctId
        if (authDealerUserResponse != null) {
            refused.setSeaLandAcctId(authDealerUserResponse.getId().intValue());
        }
        if (authUser != null) {
            refused.setAcctId(authUser.getAcctId());
        }
        // 如果兩種用戶都沒找到則拋出異常
        if (authDealerUserResponse == null && authUser == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.USER_NOT_FOUND_BY_LOGIN_ID);
        }

        // 設置拒訪記錄
        refused.setRefused(request.getIsRefused());
        if (!request.getIsRefused()) {
            refused.setRefusalDate(null);
            refused.setRefuseMemo(null);
        } else {
            refused.setRefusalDate(request.getRefuseDate());
            refused.setRefuseMemo(request.getRefuseMemo());
        }

        // 保存拒訪記錄
        csatRefusedRepository.save(refused);
    }


    /**
     * 透過分分證取得電訪訂單紀錄
     */
    public List<CsatOrderResponse> getCsatOrderByLiginId(String loginId) {
        List<CsatOrderResponse> csatResponseList = new ArrayList<>();
        Map<String, Orders> orderMap = new HashMap<>();
        Map<String, DealerOrderQueryResponse> dealerOrderMap = new HashMap<>();
        Map<String, MemberInfo> memberInfoMap = new HashMap<>();

        OrdersCriteria ordersCriteria = new OrdersCriteria();
        ordersCriteria.setIdNo(loginId);
        AuthUser authUser = authServer.getUserAcctIds(null, null, loginId).stream().findAny().orElse(null);
        if (authUser != null) {
            orderMap = orderService.getOrdersByAcctIds(Collections.singletonList(authUser.getAcctId())).stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
        }
        DealerOrderCriteria dealerOrderCriteria = new DealerOrderCriteria();
        dealerOrderCriteria.setIdNo(loginId);
        List<DealerOrderQueryResponse> dealerOrderQueryResponseList = dealerOrderService.searchPage(dealerOrderCriteria, Integer.MAX_VALUE, 0);
        if (CollectionUtils.isNotEmpty(dealerOrderQueryResponseList)) {
            dealerOrderMap = dealerOrderQueryResponseList.stream().collect(Collectors.toMap(DealerOrderQueryResponse::getOrderNo, Function.identity()));
        }
        Map<String, Orders> finalOrderMap = orderMap;
        csatResponseList.addAll(csatRepository.findByOrders(orderMap.values().stream().map(Orders::getOrderNo).collect(Collectors.toList())).stream().map(csat -> {
            CsatQuest csatQuest = csatQuestRepository.findById(csat.getId()).orElse(null);
            CsatOrderResponse csatOrderResponse = new CsatOrderResponse(csat);
            csatOrderResponse.setDepartDate(Date.from((finalOrderMap.get(csat.getOrderNo()).getStartDate())));
            setMemberInfo(memberInfoMap, csat, csatOrderResponse);
            csatOrderResponse.setQuestMemo(Optional.ofNullable(csatQuest).map(this::convertToVersionCsatQuest).map(GenerateCsatQuest::questMemo).orElse(null));
            return csatOrderResponse;
        }).collect(Collectors.toList()));

        Map<String, DealerOrderQueryResponse> finalDealerOrderMap = dealerOrderMap;
        csatResponseList.addAll(csatRepository.findByOrders(dealerOrderMap.values().stream().map(DealerOrderQueryResponse::getOrderNo).collect(Collectors.toList())).stream().map(csat -> {
            CsatQuest csatQuest = csatQuestRepository.findById(csat.getId()).orElse(null);
            CsatOrderResponse csatOrderResponse = new CsatOrderResponse(csat);
            setMemberInfo(memberInfoMap, csat, csatOrderResponse);
            csatOrderResponse.setDepartDate(Date.from((finalDealerOrderMap.get(csat.getOrderNo()).getSubscriptionInfo().getDepartDate())));
            csatOrderResponse.setQuestMemo(Optional.ofNullable(csatQuest).map(this::convertToVersionCsatQuest).map(GenerateCsatQuest::questMemo).orElse(null));
            return csatOrderResponse;
        }).collect(Collectors.toList()));
        return csatResponseList;
    }

    /**
     * 取得所有電訪清單公司人員編號名稱
     */
    public Map<String, String> getCsatTaskAllMembers() {
        Map<String, MemberInfo> memberInfoMap = new HashMap<>();

        return csatRepository.getMemberIds().stream()
            .collect(Collectors.toMap(id -> id, id -> {
                    MemberInfo memberInfo = memberInfoMap.get(id);
                    if (memberInfo == null) {
                        memberInfo = authorityServer.getMemberInfos(id).stream().findAny().orElse(null);
                        memberInfoMap.put(id, memberInfo);
                    }
                    return Optional.ofNullable(memberInfo).map(MemberInfo::getMemberName).orElse("");
                }
            ));
    }


    /**
     * 拿取問券結果
     */
    public GenerateCsatQuestResponse getCsatQuest(Integer questId) {
        CsatQuest csatQuest = csatQuestRepository.findById(questId).orElse(null);
        if (csatQuest == null) {
            return null;
        }
        Csat csat = csatRepository.findById(csatQuest.getCsatId()).orElseThrow(() -> new SubscribeException(CSAT_TASK_NOT_FOUND));
        GenerateCsatQuest generateCsatQuest = convertToVersionCsatQuest(csatQuest);
        switch (csatQuest.getVersion()) {
            case 0:
            default:
                if (csat.isDealer()) {
                    DealerOrderQueryResponse dealerOrder = dealerOrderService.createDealerOrderQueryResponseWithUserInfo(dealerOrderService.getOrder(csat.getOrderNo()));
                    return new GenerateCsatQuestResponse(csat, generateCsatQuest, dealerOrder.getCustomerInfo().getIdNo());
                } else {
                    Orders order = orderService.getOrder(csat.getOrderNo());
                    AuthUser authUser = authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId());
                    return new GenerateCsatQuestResponse(csat, generateCsatQuest, Optional.ofNullable(authUser).map(AuthUser::getLoginId).orElse(""));
                }
        }
    }

    /**
     * 轉化成相對應的問券版本
     */
    private GenerateCsatQuest convertToVersionCsatQuest(CsatQuest csatQuest) {
        Csat csat = csatRepository.findById(csatQuest.getCsatId()).orElseThrow(() -> new SubscribeException(CSAT_TASK_NOT_FOUND));
        switch (csatQuest.getVersion()) {
            case 0:
            default:
                return new CsatQuestV0(csatQuest);
        }
    }

    /**
     * Request轉化成相對應的問券版本
     */
    private GenerateCsatQuest convertToVersionCsatQuest(CsatQuestGenerateRequest request) {
        switch (request.getVersion()) {
            case 0:
            default:
                return new CsatQuestV0Request(request).toVersionCsatQuest();
        }
    }


    /**
     * 新增並儲存
     */
    public void upsetCsatQuest(String memberId, CsatQuestGenerateRequest request) {
        GenerateCsatQuest generateCsatQuest = convertToVersionCsatQuest(request);
        Csat csat = csatRepository.findById(request.getCsatId()).orElseThrow(() -> new SubscribeException(CSAT_TASK_NOT_FOUND));
        if (request.isFinish()) {
            csat.setStatus(CsatStatus.COMPLETED.getCode());
            if (generateCsatQuest.isRefuse()) {
                csat.setQuestStatus(CsatQuestStatus.REFUSED_SURVEY.getCode());
            } else {
                csat.setQuestStatus(CsatQuestStatus.SURVEYED.getCode());
            }
        } else {
            csat.setStatus(CsatStatus.IN_PROGRESS.getCode());
            csat.setQuestStatus(CsatQuestStatus.NOT_SURVEYED.getCode());
        }
        if (csat.getContractDate() == null) {
            csat.setContractDate(Instant.now());
        }
        csat.setCsatMemo(request.getCsatMemo());
        csat.setMemberId(memberId);
        csatRepository.save(csat);
        csatQuestRepository.save(request.toCsatQuest());
    }

    /**
     * 將已完成電訪的電訪單狀態重新設置為電訪中
     */
    public CsatResponse reopenCompletedCsat(Integer csatId) {

        Csat csat = csatRepository.findById(csatId).orElseThrow(() -> new SubscribeException(CSAT_TASK_NOT_FOUND));

        if (!Objects.equals(CsatStatus.COMPLETED.getCode(), csat.getStatus())) {
            throw new SubscribeException(CSAT_STATUS_NOT_COMPLETED);
        }

        csat = updateStatus(csat, CsatStatus.IN_PROGRESS, CsatQuestStatus.NOT_SURVEYED);
        return generateCsatResponse(csatResponseDataPrepare(Collections.singletonList(csat)), csat);
    }

    private Csat updateStatus(Csat csat, CsatStatus csatStatus, CsatQuestStatus csatQuestStatus) {
        csat.setStatus(csatStatus.getCode());
        csat.setQuestStatus(csatQuestStatus.getCode());
        return csatRepository.save(csat);
    }

    private void setMemberInfo(Map<String, MemberInfo> memberInfoMap, Csat csat, CsatOrderResponse csatOrderResponse) {
        if (StringUtils.isNotBlank(csat.getMemberId())) {
            csatOrderResponse.setAssignMemberId(csat.getMemberId());
            MemberInfo memberInfo = memberInfoMap.get(csat.getMemberId());
            if (memberInfo == null) {
                memberInfo = authorityServer.getMemberInfos(csat.getMemberId()).stream().findAny().orElse(null);
                memberInfoMap.put(csat.getMemberId(), memberInfo);
            }
            csatOrderResponse.setAssignMemberName(Optional.ofNullable(memberInfo).map(MemberInfo::getMemberName).orElse(""));
        }
    }

    @Transactional
    public void updateCsatAssignYearMonth(CsatAssignYearMonthUpdateRequest request) {
        List<Csat> csatList = csatRepository.findByOrders(request.getOrderNos());

        if (csatList.isEmpty()) {
            throw new SubscribeException(CSAT_TASK_NOT_FOUND);
        }

        csatList.forEach(csat -> csat.setAssignYearMonth(Integer.valueOf(request.getAssignYearMonth())));
        csatRepository.saveAll(csatList);
    }

    /**
     * 更新車牌號碼，當訂單車牌號碼變更時，更新相關的電訪任務
     */
    @Transactional
    public void updatePlateNoCauseReplaceCar(Orders order) {
        List<Csat> csatList = csatRepository.findByOrders(Collections.singletonList(order.getOrderNo()));
        if (csatList.isEmpty()) {
            return; // 沒有相關的電訪任務
        }
        for (Csat csat : csatList) {
            if (!Objects.equals(csat.getPlateNo(), order.getPlateNo()) && Objects.equals(CsatOrderSource.SUBSCRIBE.getCode(), csat.getSource())) {
                csat.setPlateNo(order.getPlateNo());
                csatRepository.save(csat);
            }
        }
    }
}
