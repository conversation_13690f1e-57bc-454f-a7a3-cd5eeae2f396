package com.carplus.subscribe.service.strategy.econtract.impl;

import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.service.ContractService;
import com.carplus.subscribe.service.strategy.econtract.EContractRefEntityUpdater;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ContractUpdater implements EContractRefEntityUpdater {

    private final ContractService contractService;

    @Override
    public void updateEContractTemplateId(String econtractRefEntityNo, EContractTemplate newTemplate, List<EContract> esByType) {

        Contract contract = contractService.getContractByContractNo(econtractRefEntityNo);

        if (esByType.isEmpty()) {
            contract.setEContractTempVerId(newTemplate.getTemplateId().toString());
            contractService.updateContract(contract);
        }

        if (!esByType.isEmpty() && contract.getEContractTempVerId() != null && contract.getEContractId() != null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.REJECT_UPDATE_CONTRACT_TEMPLATE_ID);
        }
    }

    @Override
    public void updateEContractId(EContractReferencable econtractReferencable, Integer econtractId) {
        Contract contract = (Contract) econtractReferencable;
        contract.setEContractId(econtractId.toString());
        contractService.updateContract(contract);
    }
}
