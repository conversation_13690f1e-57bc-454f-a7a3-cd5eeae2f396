package com.carplus.subscribe.service.strategy.econtract.impl;

import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.service.ContractService;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.utils.ContractUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class EContractRefEntityFetcher {

    private final ContractService contractService;
    private final DealerOrderService dealerOrderService;

    public EContractReferencable fetch(String econtractRefEntityNo) {
        if (ContractUtils.isContract(econtractRefEntityNo)) {
            return Optional.ofNullable(contractService.getContractByContractNo(econtractRefEntityNo))
                .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CONTRACT_NOT_FOUND, econtractRefEntityNo));
        } else {
            return dealerOrderService.getOrder(econtractRefEntityNo);
        }
    }

    public List<Contract> getContracts(String mainContractNo) {
        return contractService.getContractByMainContract(mainContractNo);
    }

    public Contract getContractAndOrdersByContractNo(String contractNo) {
        return contractService.getContractAndOrdersByContractNo(contractNo);
    }
}
