package com.carplus.subscribe.service.strategy.dealerorder.impl;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.model.request.dealer.DealerOrderCancelRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 取消經銷商訂單策略
 */
@Order(2)
@Component
public class CancelDealerOrderStrategy implements DealerOrderExcelProcessStrategy {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Override
    public boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        // 適用於訂單已存在於 DB，狀態為建立(CREATE)，且有取消相關資訊
        return dbDealerOrder != null && ContractStatus.CREATE.getCode() == dbDealerOrder.getOrderStatus()
            && (dealerOrderExcel.getCancelDate() != null || dealerOrderExcel.getIsCancel());
    }

    @Override
    public void process(DealerOrderExcelProcessContext context) {
        // 驗證取消訂單欄位
        validateCancelDealerOrderExcelFields(context.getDealerOrderExcel(), context.getErrorMessages());

        if (context.getErrorMessages().isEmpty()) {
            // 建立取消訂單請求
            DealerOrderCancelRequest cancelRequest = new DealerOrderCancelRequest(context.getDbDealerOrder(), context.getDealerOrderExcel());

            // 添加到成功處理列表
            context.getSuccessOrders().add(context.getDealerOrderExcel());
            context.getExistingOrderNosInExcel().add(context.getDealerOrderExcel().getOrderNo());

            // 執行取消訂單
            dealerOrderService.cancelDealerOrder(cancelRequest);
        }
    }

    private void validateCancelDealerOrderExcelFields(DealerOrderExcel dealerOrderExcel, List<String> errorMessages) {
        if (dealerOrderExcel.getCancelDate() == null || !dealerOrderExcel.getIsCancel()) {
            errorMessages.add(String.format("取消訂單須%s、%s皆為必填",
                DealerOrderExcelColumn.IS_CANCEL.getDescription(),
                DealerOrderExcelColumn.CANCEL_DATE.getDescription()));
        }
    }
} 