package com.carplus.subscribe.service.strategy.dealerorder.impl;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.request.dealer.DealerOrderDepartRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 出車經銷商訂單策略
 */
@Order(3)
@Component
public class DepartDealerOrderStrategy implements DealerOrderExcelProcessStrategy {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Override
    public boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        // 適用於訂單已存在於 DB，狀態為建立(CREATE)，且有出車相關資訊
        return dbDealerOrder != null && ContractStatus.CREATE.getCode() == dbDealerOrder.getOrderStatus()
            && (StringUtils.isNotBlank(dealerOrderExcel.getDepartStationName())
            || dealerOrderExcel.getDepartDate() != null || dealerOrderExcel.getBeginAmt() != null);
    }

    @Override
    public void process(DealerOrderExcelProcessContext context) {
        // 驗證出車欄位
        dealerOrderService.validateDepartDealerOrderExcelFields(
            context.getDealerOrderExcel(),
            context.getDbDealerOrder(),
            context.getCar(),
            context.getErrorMessages(),
            context.getStationsNameCodeMap()
        );

        if (context.getErrorMessages().isEmpty()) {
            // 建立出車請求
            DealerOrderDepartRequest departRequest = new DealerOrderDepartRequest(context.getDbDealerOrder(), context.getDealerOrderExcel(), context.getStationsNameCodeMap());

            // 添加到成功處理列表
            context.getSuccessOrders().add(context.getDealerOrderExcel());
            context.getExistingOrderNosInExcel().add(context.getDealerOrderExcel().getOrderNo());

            // 執行出車訂單
            dealerOrderService.departDealerOrder(departRequest, context.getHeaderMemberId(), null);
        }
    }
}
