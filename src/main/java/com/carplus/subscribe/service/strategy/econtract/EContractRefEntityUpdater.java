package com.carplus.subscribe.service.strategy.econtract;

import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;

import java.util.List;

public interface EContractRefEntityUpdater {

    void updateEContractTemplateId(String econtractRefEntityNo, EContractTemplate newTemplate, List<EContract> esByType);

    void updateEContractId(EContractReferencable econtractReferencable, Integer econtractId);
}
