package com.carplus.subscribe.service.strategy.econtract.impl;

import com.carplus.subscribe.service.strategy.econtract.EContractRefEntityUpdater;
import com.carplus.subscribe.utils.ContractUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class EContractRefEntityUpdaterFactory {

    private final ContractUpdater contractUpdater;
    private final DealerOrderUpdater dealerOrderUpdater;

    public EContractRefEntityUpdater getUpdater(String econtractRefEntityNo) {
        if (ContractUtils.isContract(econtractRefEntityNo)) {
            return contractUpdater;
        } else {
            return dealerOrderUpdater;
        }
    }
}
