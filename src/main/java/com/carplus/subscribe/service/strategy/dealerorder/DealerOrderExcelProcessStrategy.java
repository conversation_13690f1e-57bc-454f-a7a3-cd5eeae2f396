package com.carplus.subscribe.service.strategy.dealerorder;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;

/**
 * 經銷商訂單 Excel 處理策略介面
 */
public interface DealerOrderExcelProcessStrategy {

    /**
     * 判斷策略是否適用
     */
    boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel);
    
    /**
     * 執行訂單處理策略
     */
    void process(DealerOrderExcelProcessContext context);
} 