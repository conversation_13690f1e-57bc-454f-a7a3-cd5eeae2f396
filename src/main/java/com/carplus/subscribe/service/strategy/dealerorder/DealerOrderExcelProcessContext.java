package com.carplus.subscribe.service.strategy.dealerorder;

import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Getter
public class DealerOrderExcelProcessContext {
    private final DealerOrderExcel dealerOrderExcel;
    private final DealerOrder dbDealerOrder;
    private final List<String> errorMessages;
    private final Map<String, String> stationsNameCodeMap;
    private final Map<Long, AuthDealerUserResponse> dealerUserById;
    private final Map<String, AuthDealerUserResponse> dealerUserByIdNo;
    private final Cars car;
    private final List<DealerOrderExcel> successOrders;
    private final List<String> existingOrderNosInExcel;
    private final String headerMemberId;

    public DealerOrderExcelProcessContext(DealerOrderExcel dealerOrderExcel,
                                          DealerOrder dbDealerOrder,
                                          List<String> errorMessages,
                                          Map<String, String> stationsNameCodeMap,
                                          Map<Long, AuthDealerUserResponse> dealerUserById,
                                          Map<String, AuthDealerUserResponse> dealerUserByIdNo,
                                          Cars car,
                                          List<DealerOrderExcel> successOrders,
                                          List<String> existingOrderNosInExcel,
                                          String headerMemberId
    ) {
        this.dealerOrderExcel = dealerOrderExcel;
        this.dbDealerOrder = dbDealerOrder;
        this.errorMessages = errorMessages;
        this.stationsNameCodeMap = stationsNameCodeMap;
        this.dealerUserById = dealerUserById;
        this.dealerUserByIdNo = dealerUserByIdNo;
        this.car = car;
        this.successOrders = successOrders;
        this.existingOrderNosInExcel = existingOrderNosInExcel;
        this.headerMemberId = headerMemberId;
    }
}
