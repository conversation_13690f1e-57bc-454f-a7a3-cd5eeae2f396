package com.carplus.subscribe.service.strategy.dealerorder.impl;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.model.request.dealer.DealerOrderCloseRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.DEALER_RETURN_STATION_NOT_EXIST;

/**
 * 還車經銷商訂單策略
 */
@Order(5)
@Component
public class CloseDealerOrderStrategy implements DealerOrderExcelProcessStrategy {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Override
    public boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        // 適用於訂單已存在於 DB，狀態為出車中(GOING)，且含有還車相關資訊
        return dbDealerOrder != null && ContractStatus.GOING.getCode() == dbDealerOrder.getOrderStatus()
            && (StringUtils.isNotBlank(dealerOrderExcel.getReturnStationName())
            || dealerOrderExcel.getReturnDate() != null || dealerOrderExcel.getCloseAmt() != null);
    }

    @Override
    public void process(DealerOrderExcelProcessContext context) {
        // 驗證還車欄位
        validateCloseDealerOrderExcelFields(context.getDealerOrderExcel(), context.getErrorMessages(), context.getStationsNameCodeMap());

        if (context.getErrorMessages().isEmpty()) {
            // 建立還車請求
            DealerOrderCloseRequest closeRequest = new DealerOrderCloseRequest(
                context.getDbDealerOrder(),
                context.getDealerOrderExcel(),
                context.getStationsNameCodeMap()
            );
            
            // 添加到成功處理列表
            context.getSuccessOrders().add(context.getDealerOrderExcel());
            context.getExistingOrderNosInExcel().add(context.getDealerOrderExcel().getOrderNo());
            
            // 執行還車操作
            dealerOrderService.closeDealerOrder(closeRequest, context.getHeaderMemberId());
        }
    }

    private void validateCloseDealerOrderExcelFields(DealerOrderExcel dealerOrderExcel, List<String> errorMessages, Map<String, String> stationsNameCodeMap) {
        if (carplus.common.utils.StringUtils.isBlank(dealerOrderExcel.getReturnStationName())
            || dealerOrderExcel.getReturnDate() == null || dealerOrderExcel.getCloseAmt() == null) {
            errorMessages.add(String.format("還車須%s、%s、%s皆為必填",
                DealerOrderExcelColumn.RETURN_STATION.getDescription(),
                DealerOrderExcelColumn.RETURN_DATE.getDescription(),
                DealerOrderExcelColumn.CLOSE_AMT.getDescription()));
        }
        if (!stationsNameCodeMap.containsKey(dealerOrderExcel.getReturnStationName())) {
            errorMessages.add(DEALER_RETURN_STATION_NOT_EXIST.getMsg());
        }
    }
} 