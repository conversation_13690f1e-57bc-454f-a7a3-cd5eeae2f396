package com.carplus.subscribe.service.strategy.dealerorder.impl;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.request.dealer.DealerOrderUpdateRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.DEALER_USER_ID_NO_CAN_NOT_CHANGE;

/**
 * 更新經銷商訂單策略
 */
@Order(4)
@Component
public class UpdateDealerOrderStrategy implements DealerOrderExcelProcessStrategy {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Override
    public boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        // 適用於訂單已存在於 DB，狀態為建立(CREATE)，且沒有取消或出車相關資訊
        return dbDealerOrder != null && ContractStatus.CREATE.getCode() == dbDealerOrder.getOrderStatus()
            && !(dealerOrderExcel.getCancelDate() != null || dealerOrderExcel.getIsCancel())
            && !(StringUtils.isNotBlank(dealerOrderExcel.getDepartStationName())
            || dealerOrderExcel.getDepartDate() != null || dealerOrderExcel.getBeginAmt() != null);
    }

    @Override
    public void process(DealerOrderExcelProcessContext context) {
        // 驗證編輯訂單欄位
        if (allUpdateFieldsEmpty(context.getDealerOrderExcel())) {
            context.getErrorMessages().add(String.format("更新訂單資訊須%s~%s欄位至少一欄位有值",
                DealerOrderExcelColumn.PLATE_NO.getDescription(),
                DealerOrderExcelColumn.PREPAID_MONTHS.getDescription()));
        }
        
        // 驗證預定出車、還車站點
        dealerOrderService.validateExpectDepartReturnStationFields(context.getDealerOrderExcel(), context.getErrorMessages(), context.getStationsNameCodeMap());
        
        // 檢查身分證號是否可異動
        String idNo = context.getDealerOrderExcel().getIdNo();
        Long dealerUserId = context.getDbDealerOrder().getDealerUserId();
        AuthDealerUserResponse existingUser = dealerUserId > 0L ? context.getDealerUserById().get(dealerUserId) : null;
        
        if (existingUser != null && StringUtils.isNotBlank(idNo) && !Objects.equals(existingUser.getIdNo(), idNo)) {
            context.getErrorMessages().add(DEALER_USER_ID_NO_CAN_NOT_CHANGE.getMsg());
        }
        
        if (context.getErrorMessages().isEmpty()) {
            // 建立更新請求
            DealerOrderUpdateRequest updateRequest = new DealerOrderUpdateRequest(
                context.getDbDealerOrder(),
                context.getDealerOrderExcel(),
                existingUser,
                context.getStationsNameCodeMap()
            );
            
            // 添加到成功處理列表
            context.getSuccessOrders().add(context.getDealerOrderExcel());
            context.getExistingOrderNosInExcel().add(context.getDealerOrderExcel().getOrderNo());
            
            // 執行更新操作
            dealerOrderService.updateDealerOrder(updateRequest);
        }
    }

    private boolean allUpdateFieldsEmpty(DealerOrderExcel dealerOrderExcel) {
        if (dealerOrderExcel.getPlateNo() != null) {
            return false;
        }
        if (dealerOrderExcel.getUserName() != null) {
            return false;
        }
        if (dealerOrderExcel.getIdNo() != null) {
            return false;
        }
        if (dealerOrderExcel.getNationalCode() != null) {
            return false;
        }
        if (dealerOrderExcel.getMainCell() != null) {
            return false;
        }
        if (dealerOrderExcel.getBirthDay() != null) {
            return false;
        }
        if (dealerOrderExcel.getEmail() != null) {
            return false;
        }
        if (dealerOrderExcel.getCity() != null) {
            return false;
        }
        if (dealerOrderExcel.getArea() != null) {
            return false;
        }
        if (dealerOrderExcel.getAddress() != null) {
            return false;
        }
        if (dealerOrderExcel.getVatNumber() != null) {
            return false;
        }
        if (dealerOrderExcel.getCompanyName() != null) {
            return false;
        }
        if (dealerOrderExcel.getExpectDepartStation() != null) {
            return false;
        }
        if (dealerOrderExcel.getExpectReturnStation() != null) {
            return false;
        }
        if (dealerOrderExcel.getExpectDepartDate() != null) {
            return false;
        }
        if (dealerOrderExcel.getExpectReturnDate() != null) {
            return false;
        }
        if (dealerOrderExcel.getSecurityDeposit() != null) {
            return false;
        }
        if (dealerOrderExcel.getMonthlyFee() != null) {
            return false;
        }
        if (dealerOrderExcel.getActualMileageRate() != null) {
            return false;
        }
        if (dealerOrderExcel.getOriginalMileageRate() != null) {
            return false;
        }
        if (dealerOrderExcel.getSubscribeMonth() != null) {
            return false;
        }
        return dealerOrderExcel.getPrepaidMonths() == null;
    }
} 