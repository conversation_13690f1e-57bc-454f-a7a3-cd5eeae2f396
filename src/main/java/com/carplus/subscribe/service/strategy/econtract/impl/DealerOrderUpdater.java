package com.carplus.subscribe.service.strategy.econtract.impl;

import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.econtract.EContractRefEntityUpdater;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class DealerOrderUpdater implements EContractRefEntityUpdater {

    private final DealerOrderService dealerOrderService;

    @Override
    public void updateEContractTemplateId(String econtractRefEntityNo, EContractTemplate newTemplate, List<EContract> esByType) {

        DealerOrder dealerOrder = dealerOrderService.getOrder(econtractRefEntityNo);

        if (esByType.isEmpty()) {
            dealerOrder.setEContractTemplateId(newTemplate.getTemplateId());
            dealerOrderService.update(dealerOrder);
        }

        if (!esByType.isEmpty() && dealerOrder.getEContractTemplateId() != null && dealerOrder.getEContractId() != null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.REJECT_UPDATE_CONTRACT_TEMPLATE_ID);
        }
    }

    @Override
    public void updateEContractId(EContractReferencable econtractReferencable, Integer econtractId) {
        DealerOrder dealerOrder = (DealerOrder) econtractReferencable;
        dealerOrder.setEContractId(econtractId);
        dealerOrderService.update(dealerOrder);
    }
}
