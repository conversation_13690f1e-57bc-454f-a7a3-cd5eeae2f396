package com.carplus.subscribe.service.strategy.dealerorder.impl;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.request.car.CarValidationResult;
import com.carplus.subscribe.model.request.dealer.DealerOrderCreateRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.DEALER_USER_ID_NO_LENGTH_ERROR;

/**
 * 新增經銷商訂單策略
 */
@Order(1)
@Component
public class CreateDealerOrderStrategy implements DealerOrderExcelProcessStrategy {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Override
    public boolean isApplicable(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        // 當訂單不存在於 DB 時，應用此策略
        return dbDealerOrder == null;
    }

    @Override
    public void process(DealerOrderExcelProcessContext context) {
        // 檢查建立訂單必填欄位
        StringBuilder unfilledFields = getUnfilledFieldsForCreateDealerOrder(context.getDealerOrderExcel());
        if (unfilledFields.length() > 0) {
            // remove last comma and append "為建單必填欄位"
            unfilledFields.deleteCharAt(unfilledFields.length() - 1);
            context.getErrorMessages().add(unfilledFields + "為建單必填欄位");
        }

        if (context.getErrorMessages().isEmpty()) {
            // 檢查建立訂單欄位是否有誤
            validateCreateDealerOrderExcelFields(context.getDealerOrderExcel(), context.getErrorMessages(), context.getStationsNameCodeMap());
        }

        if (context.getErrorMessages().isEmpty()) {
            AuthDealerUserResponse dealerUser = context.getDealerUserByIdNo().get(context.getDealerOrderExcel().getIdNo());
            CarValidationResult carValidationResult = Optional.ofNullable(context.getCar())
                .map(existingCar -> {
                    // 若 車輛存在 -> 車輛須為空車 或 有進行中經銷商訂單但同客戶
                    if (!CarDefine.CarStatus.Free.getCode().equals(existingCar.getCarStatus())
                        && dealerOrderService.hasProcessingOrdersNotAllFromSameUser(dealerUser, existingCar)) {
                        context.getErrorMessages().add(CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER.getMsg());
                    }
                    return new CarValidationResult(existingCar.getPlateNo(), context.getErrorMessages());
                })
                .orElseGet(() -> dealerOrderService.validateAndRetrieveCar(context.getDealerOrderExcel().getPlateNo(), dealerUser, context.getErrorMessages()));
            if (context.getErrorMessages().isEmpty()) {
                // 建立經銷商訂單請求
                DealerOrderCreateRequest request = new DealerOrderCreateRequest(context.getDealerOrderExcel(), context.getStationsNameCodeMap(), carValidationResult);

                // 添加到成功處理列表
                context.getSuccessOrders().add(context.getDealerOrderExcel());
                context.getExistingOrderNosInExcel().add(context.getDealerOrderExcel().getOrderNo());

                // 執行建立經銷商訂單
                dealerOrderService.createDealerOrder(request, true);
            }
        }
    }

    private StringBuilder getUnfilledFieldsForCreateDealerOrder(DealerOrderExcel dealerOrderExcel) {
        StringBuilder unfilledFields = new StringBuilder();
        if (dealerOrderExcel.getDealerName() == null) {
            unfilledFields.append(DealerOrderExcelColumn.DEALER_NAME.getDescription()).append(",");
        }
        if (dealerOrderExcel.getSecurityDepositDate() == null) {
            unfilledFields.append(DealerOrderExcelColumn.SECURITY_DEPOSIT_DATE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getParentOrderNo() == null) {
            unfilledFields.append(DealerOrderExcelColumn.PARENT_ORDER_NO.getDescription()).append(",");
        }
        if (dealerOrderExcel.getStage() == null) {
            unfilledFields.append(DealerOrderExcelColumn.STAGE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getPlateNo() == null) {
            unfilledFields.append(DealerOrderExcelColumn.PLATE_NO.getDescription()).append(",");
        }
        if (dealerOrderExcel.getUserName() == null) {
            unfilledFields.append(DealerOrderExcelColumn.USER_NAME.getDescription()).append(",");
        }
        if (dealerOrderExcel.getIdNo() == null) {
            unfilledFields.append(DealerOrderExcelColumn.ID_NO.getDescription()).append(",");
        }
        if (dealerOrderExcel.getNationalCode() == null) {
            unfilledFields.append(DealerOrderExcelColumn.NATIONAL_CODE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getMainCell() == null) {
            unfilledFields.append(DealerOrderExcelColumn.MAIN_CELL.getDescription()).append(",");
        }
        if (dealerOrderExcel.getBirthDay() == null) {
            unfilledFields.append(DealerOrderExcelColumn.BIRTHDAY.getDescription()).append(",");
        }
        if (dealerOrderExcel.getEmail() == null) {
            unfilledFields.append(DealerOrderExcelColumn.EMAIL.getDescription()).append(",");
        }
        if (dealerOrderExcel.getCity() == null || dealerOrderExcel.getArea() == null) {
            unfilledFields.append(DealerOrderExcelColumn.POSTAL_CODE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getAddress() == null) {
            unfilledFields.append(DealerOrderExcelColumn.ADDRESS.getDescription()).append(",");
        }
        if (dealerOrderExcel.getExpectDepartStationName() == null) {
            unfilledFields.append(DealerOrderExcelColumn.EXPECT_DEPART_STATION.getDescription()).append(",");
        }
        if (dealerOrderExcel.getExpectReturnStationName() == null) {
            unfilledFields.append(DealerOrderExcelColumn.EXPECT_RETURN_STATION.getDescription()).append(",");
        }
        if (dealerOrderExcel.getExpectDepartDate() == null) {
            unfilledFields.append(DealerOrderExcelColumn.EXPECT_DEPART_DATE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getExpectReturnDate() == null) {
            unfilledFields.append(DealerOrderExcelColumn.EXPECT_RETURN_DATE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getSecurityDeposit() == null) {
            unfilledFields.append(DealerOrderExcelColumn.SECURITY_DEPOSIT.getDescription()).append(",");
        }
        if (dealerOrderExcel.getMonthlyFee() == null) {
            unfilledFields.append(DealerOrderExcelColumn.MONTHLY_FEE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getActualMileageRate() == null) {
            unfilledFields.append(DealerOrderExcelColumn.ACTUAL_MILEAGE_RATE.getDescription()).append(",");
        }
        if (dealerOrderExcel.getSubscribeMonth() == null) {
            unfilledFields.append(DealerOrderExcelColumn.SUBSCRIBE_MONTH.getDescription()).append(",");
        }

        return unfilledFields;
    }

    private void validateCreateDealerOrderExcelFields(DealerOrderExcel dealerOrderExcel, List<String> errorMessages, Map<String, String> stationsNameCodeMap) {
        if (StringUtils.isNotBlank(dealerOrderExcel.getIdNo()) && dealerOrderExcel.getIdNo().length() != 10) {
            errorMessages.add(DEALER_USER_ID_NO_LENGTH_ERROR.getMsg());
        }
        dealerOrderService.validateExpectDepartReturnStationFields(dealerOrderExcel, errorMessages, stationsNameCodeMap);
    }
} 