package com.carplus.subscribe.service;

import carplus.common.redis.cache.Del;
import carplus.common.redis.cache.Get;
import com.carplus.subscribe.db.mysql.dao.ConfigRepository;
import com.carplus.subscribe.db.mysql.entity.ConfigEntity;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.config.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.SubscribeConfig.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CONFIG_EXIST;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CONFIG_NOT_FUND;

@Service
@Slf4j
public class ConfigService {

    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private ObjectMapper objectMapper;

    public List<ConfigResponse> getAllConfig() {
        return configRepository.findAll().stream().map(ConfigResponse::new).collect(Collectors.toList());
    }

    public ConfigResponse getConfig(String code) {
        return new ConfigResponse(configRepository.getConfigByCode(code));
    }

    public ConfigResponse addConfig(ConfigRequest request) {
        ConfigEntity configEntity = configRepository.getConfigByCode(request.getKey());
        if (configEntity == null) {
            configEntity = new ConfigEntity();
            configEntity.setCode(request.getKey());

            configEntity.setValue(new Gson().toJson(request.getValue()));

            configRepository.save(configEntity);
            return new ConfigResponse(configEntity);
        }
        throw new SubscribeException(CONFIG_EXIST);
    }

    public ConfigResponse updateConfig(ConfigRequest request) {
        ConfigEntity configEntity = configRepository.getConfigByCode(request.getKey());
        if (configEntity != null) {
            configEntity.setValue(new Gson().toJson(request.getValue()));
            configRepository.save(configEntity);
            return new ConfigResponse(configEntity);
        }
        throw new SubscribeException(CONFIG_NOT_FUND);
    }

    /**
     * 停用電子出租單站點
     */
    @Get(group = ConfigEntity.class, key = "'PAUSE_ECONTRACT_STATION'", ttl = 60 * 10)
    public List<String> getPauseEContractStationList() {
        ConfigEntity configEntity = configRepository.getConfigByCode(PAUSE_ECONTRACT_STATION.name());
        if (configEntity == null) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<List<String>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getPauseEContractStationList fail.", e);
            return null;
        }
    }

    /**
     * 異動電子出租單站點
     */
    @Del(group = ConfigEntity.class, key = "'PAUSE_ECONTRACT_STATION'")
    public String setPauseEContractStation(String stationCode, Boolean isPause) {
        List<String> list = getPauseEContractStationList();
        if (isPause && !list.contains(stationCode)) {
            list.add(stationCode);
            list.sort(String::compareTo);
        } else if (!isPause) {
            list.remove(stationCode);
        }

        ConfigResponse configResponse = updateConfig(new ConfigRequest(PAUSE_ECONTRACT_STATION.name(), list));
        return configResponse.getValue().toString();
    }

    @Get(group = ConfigEntity.class, key = "'PREOWNED_INV_MAPPING'", ttl = 60 * 10)
    public List<PreownedInv> getPreOwnInvMappingList() {
        ConfigEntity configEntity = configRepository.getConfigByCode(PREOWNED_INV_MAPPING.name());
        if (configEntity == null) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<List<PreownedInv>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getPauseEContractStationList fail.", e);
            return null;
        }
    }


    @Get(group = ConfigEntity.class, key = "'SEALAND_STATIONS'", ttl = 60 * 10)
    public List<String> getSeaLandStationList() {
        ConfigEntity configEntity = configRepository.getConfigByCode(SEALAND_STATIONS.name());
        if (configEntity == null) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<List<String>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getPauseEContractStationList fail.", e);
            return null;
        }
    }

    @Get(group = ConfigEntity.class, key = "'LRENTAL_CONFIG'", ttl = 60 * 10)
    public SubscribeLRentalConfig getLRentalConfig() {
        ConfigEntity configEntity = configRepository.getConfigByCode(LRENTAL_CONFIG.name());
        if (configEntity == null) {
            return null;
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<SubscribeLRentalConfig>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getLRentalConfig fail.", e);
            return null;
        }
    }

    @Get(group = ConfigEntity.class, key = "'YES_CHARGING_POINT'", ttl = 60 * 10)
    public List<YesChargingPoint> getYesChargingPointConfig() {
        ConfigEntity configEntity = configRepository.getConfigByCode(YES_CHARGING_POINT.name());
        if (configEntity == null) {
            return null;
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<List<YesChargingPoint>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getYesChargingPointConfig fail.", e);
            return null;
        }
    }


    @Get(group = ConfigEntity.class, key = "'SUBSCRIBE_CONFIG'", ttl = 60 * 10)
    public SubscribeConfig getSubscribeConfig() {
        ConfigEntity configEntity = configRepository.getConfigByCode(SUBSCRIBE_CONFIG.name());
        if (configEntity == null) {
            return null;
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<SubscribeConfig>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getSubscribeConfig fail.", e);
            return null;
        }
    }

    @Get(group = ConfigEntity.class, key = "'ROLE_PERMISSION_CONFIG'", ttl = 60 * 10)
    public List<AdminRoleConfig> getAdminRoleConfig() {
        ConfigEntity configEntity = configRepository.getConfigByCode(ROLE_PERMISSION_CONFIG.name());
        if (configEntity == null) {
            return null;
        }
        try {
            return objectMapper.readValue(configEntity.getValue(), new TypeReference<List<AdminRoleConfig>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("getSubscribeConfig fail.", e);
            return null;
        }
    }

}
