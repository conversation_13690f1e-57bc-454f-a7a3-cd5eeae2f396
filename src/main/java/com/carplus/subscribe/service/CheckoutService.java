package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.*;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.AccountDetail;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.enums.finbus.InvoiceStatusEnum;
import com.carplus.subscribe.enums.finbus.PaymentMethodCodeEnum;
import com.carplus.subscribe.enums.finbus.TransactionItemCodeEnum;
import com.carplus.subscribe.enums.finbus.TypeEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.finbus.*;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.PriceUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.InvoiceDefine.InvType.Biz;
import static com.carplus.subscribe.enums.PayFor.SecurityDeposit;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.ETag;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MonthlyFee;
import static com.carplus.subscribe.enums.finbus.PaymentMethodCodeEnum.*;
import static com.carplus.subscribe.enums.finbus.TransactionItemCodeEnum.MONTHLY_CHARGE;
import static com.carplus.subscribe.enums.finbus.TypeEnum.CHARGE;
import static com.carplus.subscribe.enums.finbus.TypeEnum.REFUND;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CheckoutService {

    // 其他微服務
    private final AuthServer authServer;
    private final MattermostServer mattermostServer;
    private final FinServiceBusClient finServiceBusClient;
    // service
    private final PriceInfoService priceInfoService;
    private final OrderService orderService;
    private final CarsService carsService;
    private final InvoiceServiceV2 invoiceService;
    // repo
    private final OrderPriceInfoRepository orderPriceInfoRepository;
    private final PaymentInfoRepository paymentInfoRepository;
    private final AccountRepository accountRepository;
    private final AccountDetailRepository accountDetailRepository;
    private final EtagInfoRepository etagInfoRepository;

    @Getter
    @AllArgsConstructor
    enum CheckOutActionType {
        SECURITY_DEPOSIT(CHECK_OUT_SECURITY_DEPOSIT_FAIL, false),
        ADVANCE(ADVANCE_CHECK_OUT_FAIL, false),
        ACCOUNT(CHECK_OUT_FAIL, true),
        ORDER_CANCEL(CHECK_OUT_CANCEL_ORDER_FAIL, true),
        ;

        private final SubscribeHttpExceptionCode error;
        private final boolean throwEx;
    }

    /**
     * checkout main
     */
    private void doCheckout(
        Orders order,
        BuildAccountReq buildAccountReq,
        CheckOutActionType actionType
    ) {
        Result<?> result = null;
        try {
            result = finServiceBusClient.checkout(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, buildAccountReq);
            if (result.getStatusCode() != 0) {
                throw new RuntimeException();
            }
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("response", result);
            alert.put("type", actionType);
            mattermostServer.notify(String.format("%s,OrderNo:%s", actionType.getError().getMsg(), order.getOrderNo()), alert, null);
            if (actionType.isThrowEx()) {
                throw new SubscribeException(actionType.getError());
            }
        }
    }

    /**
     * 預設立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void checkOut(String orderNo) {
        Orders orders = orderService.getOrder(orderNo);
        checkOut(orders);
    }

    /**
     * 預設立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void checkOut(Orders order) {
        checkOut(order, null);
    }

    /**
     * 預設立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void checkOut(Orders order, Date transactionDate) {
        String orderNo = order.getOrderNo();

        // 取得帳目資訊
        List<Account> accounts = accountRepository.getAccountsByOrderNo(orderNo);
        // 取得發票資訊
        List<Invoices> invoicesList = invoiceService.getInvoice(orderNo).stream()
            .filter(inv -> !Objects.equals(inv.getIsCheckout(), InvoiceDefine.InvCheckoutStatus.NONE_CHECKOUT.getStatus()))
            .collect(Collectors.toList());
        // 取得 ETag 資訊
        List<ETagInfo> etagInfoList = etagInfoRepository.getETagInfosByOrderNo(orderNo).stream()
            .filter(etag -> !etag.isUploaded() && etag.getPaidETagAmt() != null && etag.getPaidETagAmt() > 0 && etag.getOrderPriceInfoId() != null)
            .collect(Collectors.toList());

        BuildAccountReq buildAccountReq = createBuildAccount(order, transactionDate, accounts, invoicesList, etagInfoList);
        // 如果沒有變更，則不進行立帳
        if (isCheckoutNotNeeded(buildAccountReq)) {
            log.info("No changes, checkout not needed");
            return;
        }

        doCheckout(order, buildAccountReq, CheckOutActionType.ACCOUNT);

        try {
            // 更新 isCheckout 狀態
            accountDetailRepository.updateCheckout(accounts.stream().map(Account::getId).collect(Collectors.toList()));
            invoiceService.setCheckout(invoicesList);
            etagInfoRepository.updateCheckout(etagInfoList.stream().map(ETagInfo::getId).collect(Collectors.toList()));
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("exception", e.getMessage());
            mattermostServer.notify(String.format("%s,OrderNo:%s", "登打狀態異動失敗", order.getOrderNo()), alert, e);
        }

        invoiceService.cleanInvoiceCache(orderNo);
    }

    /**
     * 暫收款立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW, noRollbackFor = {Exception.class, IOException.class})
    public void advanceCheckOut(PaymentQueue queue, Orders order) {
        List<TransactionItemReq> transactionItems = Collections.singletonList(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.TAPPAY_ADVANCE, queue.getAmount()));

        BuildAccountReq buildAccountReq = buildAccountBuilder(order, new Date());
        buildAccountReq.setTransactionItems(transactionItems);
        buildAccountReq.setPaymentMethods(Collections.singletonList(
            paymentMethodBuilder(CHARGE, PaymentMethodCodeEnum.getPaymentMethodCodeEnum(queue.getCardNumber()), queue.getAmount(), queue.getTransactionNumber(), transactionItems)));

        doCheckout(order, buildAccountReq, CheckOutActionType.ADVANCE);
    }

    /**
     * 保證金立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void securityDepositCheckOut(Orders order, PaymentQueue queue, boolean isManualRefund) {
        MainContract mainContract = order.getContract().getMainContract();

        if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit() == 0) {
            return;
        }

        Cars cars = carsService.findByPlateNo(mainContract.getPlateNo());

        BuildAccountReq buildAccountReq = buildAccountBuilder(order);
        updateCarNumber(cars, buildAccountReq);
        updateTransactionTime(isManualRefund, queue, mainContract, buildAccountReq);

        buildAccountReq.setInvoices(new ArrayList<>());

        TypeEnum type = PaymentCategory.Refund.equals(queue.getPaymentCategory()) ? TypeEnum.REFUND : CHARGE;
        List<TransactionItemReq> transactionItems = Collections.singletonList(transactionItemBuilder(type, TransactionItemCodeEnum.MARGIN, queue.getAmount()));
        buildAccountReq.setTransactionItems(transactionItems);

        PaymentInfo securityDeposit = getSecurityDepositPayment(order, type);
        PaymentMethodCodeEnum code = isAECard(queue.getCardNumber()) || (securityDeposit != null && isAECard(securityDeposit.getCardNumber())) ? TAPPAY_NCC : TAPPAY_TSIB;
        String referenceKey = securityDeposit == null ? queue.getTransactionNumber() : securityDeposit.getTransactionNumber();
        buildAccountReq.setPaymentMethods(Collections.singletonList(
            paymentMethodBuilder(type, code, queue.getAmount(), referenceKey, transactionItems)));

        doCheckout(order, buildAccountReq, CheckOutActionType.SECURITY_DEPOSIT);
    }

    /**
     * 人工退款立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void manualRefundSecurityDepositCheckOut(Orders orders) {
        PaymentInfo paymentInfo = paymentInfoRepository.getExpiredPaymentInfosByOrderNo(orders.getOrderNo()).stream().filter(p -> p.getStatus() == OrderPaymentStatus.EXPIRED_REFUND.getCode()).findAny()
            .orElseThrow(() -> new SubscribeException(MANUAL_REFUND_SECURITY_DEPOSIT_NOT_FUND));
        PaymentQueue paymentQueue = BeanUtils.copyProperties(paymentInfo, new PaymentQueue());
        paymentQueue.setPaymentId(paymentQueue.getPaymentId());
        paymentQueue.setPaymentCategory(paymentQueue.getPaymentCategory());
        paymentQueue.setAmount(paymentInfo.getAmount());
        paymentQueue.setTransactionNumber(paymentInfo.getTransactionNumber());
        paymentQueue.setTradeId(paymentInfo.getTradeId());
        paymentQueue.setRefundId(paymentInfo.getRefundId());

        securityDepositCheckOut(orders, paymentQueue, true);
    }

    /**
     * 取消訂單立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderRefundCheckOut(Orders order, OrderPriceInfo refundOrderPriceInfo) {
        orderRefundCheckOut(order, refundOrderPriceInfo, false);
    }

    /**
     * 取消訂單立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void orderRefundCheckOut(Orders order, OrderPriceInfo refundOrderPriceInfo, boolean isLegal) {
        invoiceService.cleanInvoiceCache(order.getOrderNo());
        /*
         * 交易方式：RENT_SUBSCRIBE收入付款6000,  MARGIN退款4000
         * 付款方式：MARGIN收入付款6000,TAPPAY_TSIB退款4000
         */
        MainContract mainContract = order.getContract().getMainContract();
        Cars cars = carsService.findByPlateNo(mainContract.getPlateNo());
        BuildAccountReq buildAccountReq = buildAccountBuilder(order);
        updateCarNumber(cars, buildAccountReq);

        List<OrderPriceInfo> orderPriceInfoList = orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder().orderNo(Collections.singletonList(order.getOrderNo())).build());
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = orderPriceInfoList.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        if (refundOrderPriceInfo != null) {
            orderPriceInfoMap.put(refundOrderPriceInfo.getId(), refundOrderPriceInfo);
        }

        PaymentInfo securityDepositPayment = getPaymentsByOrder(order.getOrderNo()).stream()
            .filter(p -> SecurityDeposit == p.getPayFor()).findAny().orElse(null); // null 代表 order 為續約單
        if (securityDepositPayment == null) {
            if (order.getIsNewOrder() && mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit() > 0) {
                throw new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND);
            } else if (mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit() == 0 || !order.getIsNewOrder()) {
                return;
            }
            // FIXME: ?? 情境缺失, 可能造成後段程式 NPE
        }
        List<Integer> writtenOffETagPriceInfoIds = priceInfoService.getPriceInfoWrapper(order.getOrderNo()).toFilter()
            .category(ETag).add(opi -> Objects.equals(securityDepositPayment.getPaymentId(), opi.getPaymentId())).collect()
            .stream().map(OrderPriceInfo::getId).collect(Collectors.toList());
        List<ETagInfo> writtenOffETagInfos = etagInfoRepository.getByOrderPriceInfoIdIn(writtenOffETagPriceInfoIds);

        // TAPPAY_TSIB退款
        buildAccountReq.setInvoices(new ArrayList<>());
        buildAccountReq.setPaymentMethods(new ArrayList<>());

        PaymentMethodReq securityDeposit = paymentMethodBuilder(
            TypeEnum.REFUND,
            PaymentMethodCodeEnum.getPaymentMethodCodeEnum(securityDepositPayment.getCardNumber()),
            securityDepositPayment.getAmount(),
            // 財務與訂閱PM要求將 referenceKey 由 orderNo 改為 TransactionNumber
            securityDepositPayment.getTransactionNumber(),
            Collections.singletonList(transactionItemBuilder(TypeEnum.REFUND, TransactionItemCodeEnum.MARGIN, securityDepositPayment.getAmount()))
        );

        if (!isLegal) {
            buildAccountReq.getPaymentMethods().add(securityDeposit);
        }

        int writtenOffETagAmount = 0;
        if (isLegal && CollectionUtils.isNotEmpty(writtenOffETagInfos)) {
            writtenOffETagAmount = writtenOffETagInfos.stream()
                .mapToInt(ETagInfo::getPaidETagAmt)
                .sum();
        }

        // 部分退保證金
        boolean isPartialRefund = refundOrderPriceInfo != null && !Objects.equals(refundOrderPriceInfo.getAmount(), securityDepositPayment.getAmount());
        if (isPartialRefund) {
            // MARGIN收入付款
            int chargeAmount = securityDepositPayment.getAmount() - refundOrderPriceInfo.getAmount() - writtenOffETagAmount;
            // 只有當 chargeAmount > 0 時才加入付款方式，避免負數或零金額造成立帳錯誤
            if (chargeAmount > 0) {
                List<TransactionItemReq> transactionItems = Collections.singletonList(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.RENT, chargeAmount));
                buildAccountReq.getPaymentMethods().add(paymentMethodBuilder(CHARGE, PaymentMethodCodeEnum.MARGIN, chargeAmount, securityDepositPayment.getTransactionNumber(), transactionItems));
            }

            // 更新保證金退款金額為取消訂單的金額(部分退款的情況)
            securityDeposit.setAmount(refundOrderPriceInfo.getAmount());
            // 同步更新交易項目中的退款金額
            securityDeposit.getTransactionItems().get(0).setAmount(refundOrderPriceInfo.getAmount());
        }

        /*
         * RENT_SUBSCRIBE收入付款6000,  MARGIN退款4000
         */
        buildAccountReq.setTransactionItems(new ArrayList<>());
        // 部分退保證金
        TransactionItemReq refundItem = transactionItemBuilder(TypeEnum.REFUND, TransactionItemCodeEnum.MARGIN, securityDepositPayment.getAmount());
        if (!isLegal) {
            buildAccountReq.getTransactionItems().add(refundItem);
        }

        if (isPartialRefund) {
            // MARGIN收入付款
            int chargeAmount = securityDepositPayment.getAmount() - refundOrderPriceInfo.getAmount() - writtenOffETagAmount;
            // 只有當 chargeAmount > 0 時才加入 RENT 交易項目，避免負數或零金額造成立帳錯誤
            if (chargeAmount > 0) {
                buildAccountReq.getTransactionItems().add(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.RENT, chargeAmount));
            }
            // 更新交易項目中的保證金退款金額,使其與取消訂單的退款金額一致
            refundItem.setAmount(refundOrderPriceInfo.getAmount());
        }
        List<Invoices> invoicesList = invoiceService.getInvoiceWithNewTransaction(order.getOrderNo()).stream()
            .filter(inv -> inv.getIsCheckout() != InvoiceDefine.InvCheckoutStatus.NONE_CHECKOUT.getStatus())
            .collect(Collectors.toList());
        List<InvoiceReq> invoices = createCheckoutInvoices(order, invoicesList, orderPriceInfoMap);
        buildAccountReq.setInvoices(invoices);

        // 執行法務作業且沖銷 etag 金額 > 0
        if (isLegal && CollectionUtils.isNotEmpty(writtenOffETagInfos)) {
            List<TransactionItemReq> transactionItems = buildAccountReq.getTransactionItems();
            transactionItems.add(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.ETAG, writtenOffETagAmount));
            buildAccountReq.setTransactionItems(transactionItems);

            // 付款資訊
            List<PaymentInfo> paymentInfoList = getPaymentsByOrder(order.getOrderNo()).stream()
                .filter(p -> PayAuth == p.getPaymentCategory())
                .collect(Collectors.toList());
            Map<Integer, PaymentInfo> paymentInfoIdMap = paymentInfoList.stream()
                .collect(Collectors.toMap(PaymentInfo::getPaymentId, Function.identity()));
            processEtagPayments(writtenOffETagInfos, Collections.emptyMap(), paymentInfoIdMap, buildAccountReq.getPaymentMethods());
        }

        // 過濾金額為 0 的項目
        buildAccountReq.setPaymentMethods(buildAccountReq.getPaymentMethods().stream()
            .filter(p -> p.getAmount() > 0)
            .peek(p -> {
                // 同時過濾付款方式內部金額為 0 的交易項目
                List<TransactionItemReq> filteredTransactionItems = p.getTransactionItems().stream()
                    .filter(t -> t.getAmount() > 0)
                    .collect(Collectors.toList());
                p.setTransactionItems(filteredTransactionItems);
            })
            .collect(Collectors.toList()));
        buildAccountReq.setTransactionItems(buildAccountReq.getTransactionItems().stream().filter(t -> t.getAmount() > 0).collect(Collectors.toList()));
        generateUnReconciliationPayments(buildAccountReq, order);

        // 執行立帳
        doCheckout(order, buildAccountReq, CheckOutActionType.ORDER_CANCEL);

        try {
            // 更新 isCheckout 狀態
            List<Long> accountIds = accountRepository.getAccountsByOrderNo(order.getOrderNo()).stream()
                .map(Account::getId)
                .collect(Collectors.toList());
            accountDetailRepository.updateCheckout(accountIds);
            invoiceService.setCheckout(invoicesList);
        } catch (Exception e) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("request", buildAccountReq);
            alert.put("exception", e.getMessage());
            mattermostServer.notify(String.format("%s,OrderNo:%s", "登打狀態異動失敗", order.getOrderNo()), alert, e);
        }
    }

    /**
     * BuildAccountReq
     */
    private BuildAccountReq buildAccountBuilder(Orders order) {
        return buildAccountBuilder(order, null);
    }

    /**
     * BuildAccountReq
     */
    private BuildAccountReq buildAccountBuilder(Orders order, Date transactionDate) {
        MainContract mainContract = order.getContract().getMainContract();
        AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());

        return BuildAccountReq.builder()
            .departmentCode(CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE)
            .orderNumber(order.getOrderNo())
            .contractNumber(mainContract.getMainContractNo())
            .carNumber(mainContract.getPlateNo())
            .customerName(authUser.getAcctName())
            .customerIdNumber(authUser.getLoginId())
            .transactionTime(transactionDate == null ? new Date() : transactionDate)
            .startDate(new Date(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()).toEpochMilli()))
            .endDate(new Date(Optional.ofNullable(order.getEndDate()).orElse(order.getExpectEndDate()).toEpochMilli()))
            .customerType("NATURAL")
            .transactionItems(new ArrayList<>())
            .invoices(new ArrayList<>())
            .paymentMethods(new ArrayList<>())
            // 訂單是否期滿結束(false表示提前解約)
            // true: 實際結束日期 >= 預期結束日期 (期滿)
            // false: 1. 尚未結束(結束日期為空) 2. 實際結束日期 < 預期結束日期 (提前解約)
            .isFinish(Optional.ofNullable(order.getEndDate())
                .map(DateUtil::convertToStartOfInstant)
                .map(actualEnd -> !actualEnd.isBefore(DateUtil.convertToStartOfInstant(order.getExpectEndDate())))
                .orElse(false))
            .build();
    }

    /**
     * BuildAccountReq
     */
    private BuildAccountReq createBuildAccount(Orders order, Date transactionDate, List<Account> accounts, List<Invoices> invoicesList, List<ETagInfo> etagInfoList) {
        // 帳目資訊
        Map<Long, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getId, Function.identity()));
        // 取得未結帳金額
        Map<Long, List<AccountDetail>> accountDetailMap = accountDetailRepository.findUncheckOutAmountByAccountIds(accountMap.keySet());
        // 付款資訊
        Map<Integer, PaymentInfo> paymentInfoIdMap = getPaymentsByOrder(order.getOrderNo()).stream()
            .filter(p -> PayAuth == p.getPaymentCategory())
            .collect(Collectors.toMap(PaymentInfo::getPaymentId, Function.identity()));
        // 費用資訊
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = priceInfoService.getPriceInfoWrapper(order.getOrderNo()).getList().stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        // 驗證金額
        AtomicInteger invoiceAmt = new AtomicInteger();
        AtomicInteger paymentAmt = new AtomicInteger();
        // - 計算發票金額
        for (Invoices i : invoicesList) {
            if (InvoiceDefine.InvStatus.CREATE.name().equals(i.getStatus())) {
                invoiceAmt.addAndGet(i.getAmount());
            } else if (!i.isMultipleCheckout()) {
                invoiceAmt.addAndGet(-i.getAmount());
            }
        }
        // - 計算收支金額
        for (List<AccountDetail> accountDetails : accountDetailMap.values()) {
            for (AccountDetail accountDetail : accountDetails) {
                paymentAmt.addAndGet(accountDetail.getAmount());
            }
        }
        validateAmounts(order.getOrderNo(), paymentAmt, invoiceAmt);

        BuildAccountReq buildAccountReq = buildAccountBuilder(order, transactionDate);

        // 設置發票請求
        List<InvoiceReq> invoiceReqList = createCheckoutInvoices(order, invoicesList, orderPriceInfoMap);
        buildAccountReq.setInvoices(invoiceReqList);
        // 設置交易項目
        // - 從發票請求提取交易項目
        List<TransactionItemReq> transactionItems = invoiceReqList.stream().map(InvoiceReq::getTransactionItems).flatMap(Collection::stream).collect(Collectors.toList());
        // - eTag 交易項目
        etagInfoList.forEach(etagInfo ->
            transactionItems.add(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.ETAG, Math.abs(etagInfo.getPaidETagAmt()))));
        buildAccountReq.setTransactionItems(transactionItems);
        // 設置付款方式
        List<PaymentMethodReq> paymentMethods = createPaymentMethods(order, accountMap, accountDetailMap, orderPriceInfoMap);
        buildAccountReq.setPaymentMethods(paymentMethods);
        // - ETag 付款
        processEtagPayments(etagInfoList, accountMap, paymentInfoIdMap, paymentMethods);

        return buildAccountReq;
    }

    /**
     * InvoiceReq
     */
    private InvoiceReq checkoutInvoiceBuilder(InvoiceStatusEnum type, String number, Integer amount, String businessIdNumber, List<TransactionItemReq> transactionItems) {
        return InvoiceReq.builder()
            .type(type.name())
            .number(number)
            .amount(amount)
            .businessIdNumber(businessIdNumber)
            .transactionItems(transactionItems)
            .build();
    }

    /**
     * InvoiceReq
     */
    private List<InvoiceReq> createCheckoutInvoices(Orders order, List<Invoices> invoicesList, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        Map<Boolean, List<Invoices>> partitionedInvoices = invoicesList.stream()
            .collect(Collectors.partitioningBy(Invoices::isMultipleCheckout));

        List<InvoiceReq> invoiceReqList = partitionedInvoices.get(false).stream()
            .map(invoice -> {
                InvoiceStatusEnum type = Optional.ofNullable(InvoiceStatusEnum.of(InvoiceDefine.InvStatus.valueOf(invoice.getStatus())))
                    .orElseThrow(() -> new IllegalStateException("無效的發票狀態: " + invoice.getStatus()));
                TypeEnum transactionType = type == InvoiceStatusEnum.NEW ? CHARGE : TypeEnum.REFUND;
                List<TransactionItemReq> transactionItems = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                    transactionItems.addAll(createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap)));
                } else {
                    transactionItems.add(transactionItemBuilder(transactionType, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                }
                if (transactionType == REFUND) {
                    upSideDownTransactionType(transactionItems);
                }
                return checkoutInvoiceBuilder(type, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
            })
            .collect(Collectors.toList());
        // 處理發票開立後又作廢的情況，補充一筆開立的資訊
        invoiceReqList.addAll(
            partitionedInvoices.get(true).stream()
                .map(invoice -> {
                    List<TransactionItemReq> transactionItems = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                        transactionItems.addAll(createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap)));
                    } else {
                        transactionItems.add(transactionItemBuilder(CHARGE, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                    }
                    return checkoutInvoiceBuilder(InvoiceStatusEnum.NEW, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
                })
                .collect(Collectors.toList())
        );
        // 作一筆反向發票沖銷帳
        invoiceReqList.addAll(
            partitionedInvoices.get(true).stream()
                .map(invoice -> {
                    List<TransactionItemReq> transactionItems = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(invoice.getRefPriceInfoIds())) {
                        transactionItems = createTransactionItems(order, getInvoiceOrderPriceInfos(invoice.getRefPriceInfoIds(), orderPriceInfoMap));
                        upSideDownTransactionType(transactionItems);
                    } else {
                        transactionItems.add(transactionItemBuilder(REFUND, TransactionItemCodeEnum.RENT, invoice.getAmount()));
                    }
                    return checkoutInvoiceBuilder(InvoiceStatusEnum.VOID, invoice.getInvNo(), invoice.getAmount(), getBusinessIdNumber(invoice), transactionItems);
                })
                .collect(Collectors.toList())
        );

        return invoiceReqList;
    }

    /**
     * PaymentMethodReq
     */
    private PaymentMethodReq paymentMethodBuilder(TypeEnum type, PaymentMethodCodeEnum code, Integer amount, String referenceKey, List<TransactionItemReq> transactionItems) {
        return PaymentMethodReq.builder()
            .type(type.name())
            .code(code.name())
            .amount(amount)
            .referenceKey(referenceKey)
            .transactionItems(transactionItems)
            .build();
    }

    /**
     * PaymentMethodReq
     */
    private List<PaymentMethodReq> createPaymentMethods(
        Orders order,
        Map<Long, Account> accountMap,
        Map<Long, List<AccountDetail>> accountDetailMap,
        Map<Integer, OrderPriceInfo> orderPriceInfoMap
    ) {
        List<PaymentMethodReq> paymentMethods = new ArrayList<>();
        for (Map.Entry<Long, List<AccountDetail>> entry : accountDetailMap.entrySet()) {
            Account account = accountMap.get(entry.getKey());
            if (account == null) {
                continue;
            }
            List<AccountDetail> accountDetails = entry.getValue();
            int unCheckoutAmount = accountDetails.stream().mapToInt(AccountDetail::getAmount).sum();
            if (unCheckoutAmount == 0) {
                continue;
            }
            TypeEnum paymentMethodReqType = unCheckoutAmount > 0 ? CHARGE : TypeEnum.REFUND;
            PaymentMethodCodeEnum paymentMethodCodeEnum = StringUtils.isNotBlank(account.getTradeId())
                ? (paymentMethodReqType == CHARGE ? TAPPAY_ADVANCE : PaymentMethodCodeEnum.getPaymentMethodCodeEnum(account.getCardNumber()))
                : PaymentMethodCodeEnum.REMIT;
            String referenceKey = paymentMethodCodeEnum == REMIT ? String.valueOf(account.getRemitNo()) : account.getTransactionNumber();

            paymentMethods.add(
                paymentMethodBuilder(
                    paymentMethodReqType,
                    paymentMethodCodeEnum,
                    Math.abs(unCheckoutAmount),
                    referenceKey,
                    createTransactionItemsInPaymentMethod(accountDetails, order, orderPriceInfoMap)));
        }

        return paymentMethods;
    }

    /**
     * TransactionItemReq
     */
    private TransactionItemReq transactionItemBuilder(TypeEnum type, TransactionItemCodeEnum code, Integer amount) {
        return transactionItemBuilder(type, code, amount, null, null);
    }

    /**
     * TransactionItemReq
     */
    private TransactionItemReq transactionItemBuilder(TypeEnum type, TransactionItemCodeEnum code, Integer amount, Instant apportionStartDate, Instant apportionEndDate) {
        return TransactionItemReq.builder().type(type.name()).code(code.name()).amount(amount)
            .apportionEndDate(apportionEndDate)
            .apportionStartDate(apportionStartDate)
            .build();
    }

    /**
     * TransactionItemReq
     */
    private List<TransactionItemReq> createTransactionItems(Orders order, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        List<TransactionItemReq> transactionItems = new ArrayList<>();
        TransactionItemCodeEnum transactionItem = PriceUtils.areAllPriceInfosMerchandiseRelated(new ArrayList<>(orderPriceInfoMap.values()), null)
            ? TransactionItemCodeEnum.MERCHANDISE : TransactionItemCodeEnum.RENT;
        if (transactionItem == TransactionItemCodeEnum.MERCHANDISE) {
            int amount = orderPriceInfoMap.values().stream().mapToInt(PriceInfoInterface::getActualReceivePrice).sum();
            if (amount != 0) {
                transactionItems.add(transactionItemBuilder(amount > 0 ? CHARGE : REFUND, transactionItem, amount));
            }
        } else {
            Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));
            Map<Integer, Integer> orderPriceInfoGroup = new HashMap<>();
            orderPriceInfoMap.forEach((id, opi) -> {
                if (opi.getActualPrice() == 0) {
                    return;
                }
                Integer key = opi.getRefPriceInfoNo() == null ? opi.getId() : opi.getRefPriceInfoNo();
                orderPriceInfoGroup.putIfAbsent(key, 0);
                orderPriceInfoGroup.put(key, orderPriceInfoGroup.get(key) + opi.getActualPrice());
            });
            orderPriceInfoGroup.forEach((id, amt) -> {
                OrderPriceInfo orderPriceInfo = orderPriceInfoMap.get(id);
                if (orderPriceInfo == null) {
                    log.error("立帳查不到對應款項資訊, orderNo:{}, id:{}", order.getOrderNo(), id);
                    return;
                }
                if (amt == 0) {
                    return;
                }
                TypeEnum type = amt > 0 ? CHARGE : REFUND;
                // 月費需要給攤提時間與不同交易項目
                boolean isMonthlyFee = orderPriceInfo.getCategory() == MonthlyFee
                    || Optional.ofNullable(orderPriceInfo.getRefPriceInfoNo()).map(orderPriceInfoMap::get).filter(opi2 -> opi2.getCategory() == MonthlyFee).isPresent();
                if (isMonthlyFee) {
                    CalculateStage stage = calculateStageMap.get(orderPriceInfo.getStage());
                    transactionItems.add(
                        transactionItemBuilder(type, MONTHLY_CHARGE, amt, compareDate(stage.getStartDate(), order.getEndDate()), compareDate(stage.getEndDate(), order.getEndDate())));
                } else {
                    transactionItems.add(transactionItemBuilder(type, transactionItem, amt));
                }
            });
        }
        return transactionItems;
    }

    /**
     * TransactionItemReq in PaymentMethod
     */
    private List<TransactionItemReq> createTransactionItemsInPaymentMethod(List<AccountDetail> details, Orders order, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        List<TransactionItemReq> reqList = new ArrayList<>();
        for (AccountDetail accountDetail : details) {
            if (MapUtils.isEmpty(accountDetail.getOrderPriceAmounts())) {
                TypeEnum type = accountDetail.getAmount() > 0 ? CHARGE : REFUND;
                reqList.add(transactionItemBuilder(type, TransactionItemCodeEnum.RENT, Math.abs(accountDetail.getAmount())));
            } else {
                Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));

                for (Map.Entry<Integer, Integer> entry : accountDetail.getOrderPriceAmounts().entrySet()) {
                    OrderPriceInfo opi = orderPriceInfoMap.get(entry.getKey());
                    TypeEnum type = opi.getActualPrice() > 0 ? CHARGE : REFUND;
                    TransactionItemCodeEnum transactionItemCodeEnum = TransactionItemCodeEnum.getTransactionItemCodeEnum(opi.getCategory());
                    if (opi.getActualPrice() == 0 || opi.getCategory() == PriceInfoDefinition.PriceInfoCategory.ETag) {
                        continue;
                    }
                    int amount = Math.abs(entry.getValue());
                    if (CollectionUtils.isNotEmpty(opi.getRemitAccountIds()) && opi.getRemitAccountIds().size() > 1) {
                        amount = Math.min(Math.abs(entry.getValue()), Math.abs(accountDetail.getAmount()));
                    }
                    if (transactionItemCodeEnum == MONTHLY_CHARGE) {
                        CalculateStage stage = calculateStageMap.get(opi.getStage());
                        reqList.add(transactionItemBuilder(type, TransactionItemCodeEnum.MONTHLY_CHARGE, amount, compareDate(stage.getStartDate(), order.getEndDate()),
                            compareDate(stage.getEndDate(), order.getEndDate())));
                    } else {
                        reqList.add(transactionItemBuilder(type, TransactionItemCodeEnum.RENT, amount));
                    }
                }
            }

        }
        return reqList;
    }

    public void modifyMonthlyItem(Orders order) {
        if (order.getStatus() == OrderStatus.BOOKING.getStatus()
            || (order.getStatus() == OrderStatus.DEPART.getStatus() && order.getStartDate() != null && !DateUtils.isSameDay(Date.from(order.getStartDate()), Date.from(order.getExpectStartDate())))
        ) {
            PriceInfoWrapper monthlyFeeWrapper = priceInfoService.getPriceInfoWrapper(order.getOrderNo()).toFilter()
                .category(MonthlyFee).paid().wrap();
            if (monthlyFeeWrapper.isNotEmpty()) {
                Map<Integer, CalculateStage> calculateStageMap = DateUtil.calculateStageAndDate(order).stream().collect(Collectors.toMap(CalculateStage::getStage, Function.identity()));
                ModifyMonthlyItemReq modifyMonthlyItemReq = new ModifyMonthlyItemReq(order.getOrderNo());
                modifyMonthlyItemReq.setPeriods(new ArrayList<>());
                modifyMonthlyItemReq.setOrderNumber(order.getOrderNo());
                for (OrderPriceInfo orderPriceInfo : monthlyFeeWrapper.getList()) {
                    CalculateStage stage = calculateStageMap.get(orderPriceInfo.getStage());
                    if (stage == null) {
                        continue;
                    }
                    modifyMonthlyItemReq.getPeriods().add(new ModifyMonthlyItemReq.Period(stage.getStartDate(), stage.getEndDate(), orderPriceInfo.getActualPrice()));
                }
                modifyMonthlyItemReq.setTransactionTime(new Date());
                finServiceBusClient.modifyMonthlyItem(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, modifyMonthlyItemReq);
            }
        }
    }

    /**
     * 比較日期，若 actualDate 為 null 則回傳 expectDate
     */
    private Instant compareDate(Instant expectDate, Instant actualDate) {
        if (actualDate == null) {
            return expectDate;
        }
        return expectDate.isBefore(actualDate) ? expectDate : actualDate;
    }

    private void validateAmounts(String orderNo, AtomicInteger paymentAmt, AtomicInteger invoiceAmt) {
        if (paymentAmt.get() != invoiceAmt.get()) {
            Map<String, Object> alert = new LinkedHashMap<>();
            alert.put("發票金額", invoiceAmt.get());
            alert.put("帳務金額", paymentAmt.get());
            mattermostServer.notify(String.format("%s,OrderNo:%s", CHECK_OUT_FAIL.getMsg(), orderNo), alert, null);
            throw new SubscribeException(PRICE_AND_INVOICE_AMOUNT_NOT_EQUAL);
        }
    }

    private void processEtagPayments(List<ETagInfo> etagInfoList, Map<Long, Account> accountMap, Map<Integer, PaymentInfo> paymentInfoIdMap, List<PaymentMethodReq> paymentMethods) {
        for (ETagInfo eTagInfo : etagInfoList) {
            OrderPriceInfo orderPriceInfo = orderPriceInfoRepository.findById(eTagInfo.getOrderPriceInfoId())
                .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

            TypeEnum type = CHARGE;
            Integer amount = orderPriceInfo.getReceivedAmount();
            TransactionItemReq transactionItem = transactionItemBuilder(type, TransactionItemCodeEnum.ETAG, amount);

            String referenceKey;

            if (orderPriceInfo.getPaymentId() != null) {
                PaymentInfo paymentInfo = paymentInfoIdMap.get(orderPriceInfo.getPaymentId());
                referenceKey = paymentInfo.getTransactionNumber();

                if (amount.equals(paymentInfo.getAmount())) {
                    paymentMethods.add(paymentMethodBuilder(type, TAPPAY_ADVANCE, amount, referenceKey, Collections.singletonList(transactionItem)));
                } else {
                    boolean isExist = updateExistingPaymentMethod(paymentMethods, referenceKey, type, amount, transactionItem);
                    if (!isExist) {
                        paymentMethods.add(paymentMethodBuilder(type, TAPPAY_ADVANCE, amount, referenceKey, Collections.singletonList(transactionItem)));
                    }
                }
            } else {
                List<Account> accounts = orderPriceInfo.getRemitAccountIds().stream().map(accountMap::get).collect(Collectors.toList());

                if (accounts.size() > 1) {
                    throw new SubscribeException(CHECK_OUT_ETAG_MULTIPLE_REMIT_FAIL);
                }

                Account account = accounts.get(0);
                referenceKey = String.valueOf(account.getRemitNo());

                if (account.getAmount() == 0) {
                    paymentMethods.add(paymentMethodBuilder(type, PaymentMethodCodeEnum.REMIT, amount, referenceKey, Collections.singletonList(transactionItem)));
                } else {
                    boolean isExist = updateExistingPaymentMethod(paymentMethods, referenceKey, type, amount, transactionItem);
                    if (!isExist) {
                        paymentMethods.add(paymentMethodBuilder(type, PaymentMethodCodeEnum.REMIT, amount, referenceKey, Collections.singletonList(transactionItem)));
                    }
                }
            }
        }
    }

    private boolean updateExistingPaymentMethod(List<PaymentMethodReq> paymentMethods, String referenceKey, TypeEnum type, Integer amount, TransactionItemReq transactionItem) {
        AtomicBoolean isUpdated = new AtomicBoolean(false);
        paymentMethods.stream()
            .filter(p -> p.getReferenceKey().equals(referenceKey) && p.getType().equals(type.name()))
            .findAny()
            .ifPresent(paymentMethodReq -> {
                paymentMethodReq.setAmount(paymentMethodReq.getAmount() + amount);
                List<TransactionItemReq> transactionItems = new ArrayList<>(paymentMethodReq.getTransactionItems());
                transactionItems.add(transactionItem);
                paymentMethodReq.setTransactionItems(transactionItems);
                isUpdated.set(true);
            });
        return isUpdated.get();
    }

    private boolean isCheckoutNotNeeded(BuildAccountReq buildAccountReq) {
        return buildAccountReq.getPaymentMethods().isEmpty()
            && buildAccountReq.getTransactionItems().isEmpty()
            && buildAccountReq.getInvoices().isEmpty();
    }

    private static void upSideDownTransactionType(List<TransactionItemReq> transactionItems) {
        transactionItems.forEach(transactionItemReq -> {
            if (transactionItemReq.getType().equals(CHARGE.name())) {
                transactionItemReq.setType(REFUND.name());
            } else if (transactionItemReq.getType().equalsIgnoreCase(REFUND.name())) {
                transactionItemReq.setType(CHARGE.name());
            }
        });
    }

    private Map<Integer, OrderPriceInfo> getInvoiceOrderPriceInfos(List<Integer> ids, Map<Integer, OrderPriceInfo> orderPriceInfoMap) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyMap();

        }
        Map<Integer, OrderPriceInfo> result = ids.stream()
            .map(orderPriceInfoMap::get)
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
        if (result.size() != ids.size()) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        return result;
    }

    private String getBusinessIdNumber(Invoices invoice) {
        return Optional.ofNullable(invoice.getInvoice())
            .filter(inv -> Objects.equals(inv.getType(), Biz.getType()))
            .map(Invoice::getId)
            .orElse(null);
    }

    /**
     * 拿取訂單所有付款資訊
     */
    private List<PaymentInfo> getPaymentsByOrder(String orderNo) {
        return paymentInfoRepository.getPaymentInfosByOrderNo(orderNo);
    }

// Helper methods extracted for readability and reuse

    private void updateCarNumber(Cars cars, BuildAccountReq buildAccountReq) {
        if (cars.isVirtualCar()) {
            buildAccountReq.setCarNumber(null);
        }
    }

    private void updateTransactionTime(boolean isManualRefund, PaymentQueue queue, MainContract mainContract, BuildAccountReq buildAccountReq) {
        if (isManualRefund && PaymentCategory.Refund.equals(queue.getPaymentCategory())) {
            SecurityDepositInfo securityDepositInfo = mainContract.getOriginalPriceInfo().getSecurityDepositInfo();
            buildAccountReq.setTransactionTime(Optional.ofNullable(securityDepositInfo.getManualRefundUpdateDate())
                .orElseThrow(() -> new SubscribeException(MANUAL_REFUND_SECURITY_DEPOSIT_TIME_NOT_FUND)));
        }
    }

    private PaymentInfo getSecurityDepositPayment(Orders order, TypeEnum type) {
        if (type == TypeEnum.REFUND) {
            return getPaymentsByOrder(order.getOrderNo()).stream()
                .filter(p -> PayAuth.equals(p.getPaymentCategory())
                    && p.getPayFor().equals(PayFor.SecurityDeposit)
                    && (p.getStatus() == OrderPaymentStatus.AUTHORIZED.getCode()
                    || p.getStatus() == OrderPaymentStatus.BANK_CAPTURE.getCode()))
                .findAny()
                .orElseThrow(() -> new BadRequestException("保證金退款時，找不到保證金付款資料"));
        }
        return null;
    }

    /**
     * 取得未收支登打的付款
     */
    private List<PaymentInfo> getUnReconciliationPayments(String orderNo) {
        List<PaymentInfo> paymentInfoList = getPaymentsByOrder(orderNo);
        Set<String> tradeId = new HashSet<>();
        accountRepository.getAccountsByOrderNo(orderNo).stream().filter(account -> account.getAccountType() == AccountType.Credit)
            .forEach(account -> tradeId.add(account.getTradeId()));
        String securityDepositTradeId = paymentInfoList.stream().filter(p -> p.getPayFor() == SecurityDeposit).findAny().map(PaymentInfo::getTradeId).orElse("None");
        List<Integer> paymentIds =
            priceInfoService.getPriceInfosByOrder(orderNo).stream().filter(opi -> opi.getPaymentId() != null && !tradeId.contains(opi.getRecTradeId()) && opi.getCategory() != PriceInfoDefinition.PriceInfoCategory.SecurityDeposit)
                .map(OrderPriceInfo::getPaymentId).collect(Collectors.toList());
        // 濾掉保證金付款(可能有退款)
        return paymentInfoList.stream().filter(paymentInfo -> paymentIds.contains(paymentInfo.getPaymentId()) && !paymentInfo.getTradeId().equalsIgnoreCase(securityDepositTradeId)).collect(Collectors.toList());
    }

    /**
     * 生成未對帳要退款的付款
     */
    private void generateUnReconciliationPayments(BuildAccountReq buildAccountReq, Orders order) {
        List<PaymentInfo> paymentInfoList = getUnReconciliationPayments(order.getOrderNo());
        if (CollectionUtils.isNotEmpty(paymentInfoList)) {
            List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());
            List<PaymentMethodReq> paymentMethodReqList = Optional.ofNullable(buildAccountReq.getPaymentMethods()).orElseGet(ArrayList::new);
            List<TransactionItemReq> transactionItemReqList = Optional.ofNullable(buildAccountReq.getTransactionItems()).orElseGet(ArrayList::new);
            paymentInfoList.forEach(paymentInfo -> {
                Map<Integer, OrderPriceInfo> orderPriceInfoMap = orderPriceInfoList.stream().filter(opi -> Objects.equals(opi.getPaymentId(), paymentInfo.getPaymentId())).collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));
                boolean isIncludeETag = orderPriceInfoMap.values().stream().anyMatch(opi -> opi.getCategory() == ETag);
                if (isIncludeETag) {
                    Integer totalAmt = orderPriceInfoMap.values().stream().mapToInt(OrderPriceInfo::getActualPrice).sum();
                    if (paymentInfo.getAmount().equals(totalAmt)) {
                        return;
                    }
                }
                Map<Integer, OrderPriceInfo> orderPriceInfoMapWithoutMerchandise = new HashMap<>();
                Map<Integer, OrderPriceInfo> orderPriceInfoMapWithMerchandise = new HashMap<>();
                orderPriceInfoMap.forEach((key, value) -> {
                    if (value.getCategory() == PriceInfoDefinition.PriceInfoCategory.Merchandise) {
                        orderPriceInfoMapWithMerchandise.put(key, value);
                    } else {
                        orderPriceInfoMapWithoutMerchandise.put(key, value);
                    }
                });
                List<TransactionItemReq> subtransactionItemReqList = createTransactionItems(order, orderPriceInfoMapWithoutMerchandise)
                    .stream().peek(transactionItemReq -> transactionItemReq.setCode(TransactionItemCodeEnum.TAPPAY_ADVANCE.name())).collect(Collectors.toList());
                if (!orderPriceInfoMapWithMerchandise.isEmpty()) {
                    subtransactionItemReqList.addAll(createTransactionItems(order, orderPriceInfoMapWithMerchandise)
                        .stream().peek(transactionItemReq -> transactionItemReq.setCode(TransactionItemCodeEnum.TAPPAY_ADVANCE.name())).collect(Collectors.toList()));
                }
                upSideDownTransactionType(subtransactionItemReqList);
                transactionItemReqList.addAll(subtransactionItemReqList);
                PaymentMethodReq paymentMethodReq =
                    paymentMethodBuilder(TypeEnum.REFUND, PaymentMethodCodeEnum.getPaymentMethodCodeEnum(paymentInfo.getCardNumber()), paymentInfo.getAmount(), paymentInfo.getTransactionNumber(), subtransactionItemReqList);
                paymentMethodReq.setTransactionItems(subtransactionItemReqList);
                paymentMethodReqList.add(paymentMethodReq);
                buildAccountReq.setPaymentMethods(paymentMethodReqList);
                buildAccountReq.setTransactionItems(transactionItemReqList);
            });
        }
    }
}
