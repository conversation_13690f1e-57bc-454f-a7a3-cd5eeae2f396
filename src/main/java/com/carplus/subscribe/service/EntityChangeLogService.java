package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dao.EntityChangeLogRepository;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.change.EntityChange;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.db.mysql.entity.change.EntityDeletion;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.calendar.CalendarUpdateRequest;
import com.carplus.subscribe.server.AuthorityServer;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EntityChangeLogService {

    @Autowired
    private EntityChangeLogRepository entityChangeLogRepository;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private StationService stationService;

    public <E> Page<EntityChangeLog> getByMainEntityAndPrimaryKey(Class<E> mainEntity, String mainEntityId, PageRequest pageRequest) {

        int skip = pageRequest.getSkip();
        int limit = pageRequest.getLimit();

        long count = entityChangeLogRepository.countByMainEntityNameAndMainEntityId(mainEntity.getSimpleName(), mainEntityId);
        if (count == 0) {
            return Page.of(0, Collections.emptyList(), skip, limit);
        }

        List<EntityChangeLog> entityChangeLogList = entityChangeLogRepository.getByMainEntityNameAndMainEntityId(mainEntity.getSimpleName(), mainEntityId, skip, limit);

        applyEnhancements(entityChangeLogList);

        return Page.of(count, entityChangeLogList, skip, limit);
    }

    public <E> List<EntityChangeLog> getByMainEntityAndPrimaryKey(Class<E> mainEntity, String mainEntityId) {

        List<EntityChangeLog> entityChangeLogList = entityChangeLogRepository.getByMainEntityNameAndMainEntityId(mainEntity.getSimpleName(), mainEntityId);

        applyEnhancements(entityChangeLogList);

        return entityChangeLogList;
    }

    private void applyEnhancements(List<EntityChangeLog> entityChangeLogList) {
        Map<String, String> stationCodeToName = stationService.findAll().stream()
            .collect(Collectors.toMap(Stations::getStationCode, Stations::getStationName, (v1, v2) -> v1));

        Map<String, String> mapMemberIdToName = new ConcurrentHashMap<>();

        Gson gson = new GsonBuilder()
            .registerTypeAdapter(Date.class, (JsonDeserializer<Date>) (json, typeOfT, context)
                -> new Date(json.getAsJsonPrimitive().getAsLong())).create();

        entityChangeLogList.forEach(entityChangeLog -> {
            translateFieldValuesToChineseName(entityChangeLog, stationCodeToName, gson);
            String changedBy = entityChangeLog.getChangedBy();
            if (changedBy.matches("K\\d{4}")) {
                mapMemberIdToName.computeIfAbsent(changedBy, memberId -> {
                    List<MemberInfo> memberInfos = authorityServer.getMemberInfos(memberId);
                    return memberInfos.isEmpty() ? "Unknown" : memberInfos.get(0).getMemberName();
                });
                entityChangeLog.setOperatorName(mapMemberIdToName.get(changedBy));
            }
        });
    }

    /**
     * 將異動紀錄中的欄位值轉換為中文名稱
     * 目前支援以下轉換：
     * 1. 車輛狀態代碼轉換為中文狀態名稱
     * 2. 所在站所代碼轉換為站所名稱
     * 3. ETagModel 轉換為中文描述
     * 4. 行事曆適用站點代碼列表轉換為站所中文名稱列表
     */
    private void translateFieldValuesToChineseName(EntityChangeLog entityChangeLog, Map<String, String> stationCodeToName, Gson gson) {

        Map<String, BiFunction<String, Map<String, String>, String>> fieldTransformers = createFieldTransformerMap();

        entityChangeLog.getChanges().forEach(change -> {
            if (change instanceof EntityChange) {
                handleEntityChange((EntityChange) change, fieldTransformers, stationCodeToName);
            } else if (change instanceof EntityDeletion) {
                handleEntityDeletion(entityChangeLog.getMainEntityName(), (EntityDeletion) change, stationCodeToName, gson);
            }
        });

        // 處理 SubscribeCalendar 的特殊情況
        handleSubscribeCalendarRequestBody(entityChangeLog, stationCodeToName, gson);
    }

    private Map<String, BiFunction<String, Map<String, String>, String>> createFieldTransformerMap() {
        Map<String, BiFunction<String, Map<String, String>, String>> transformers = new HashMap<>();

        // 車輛狀態轉換
        transformers.put(Cars.Fields.carStatus, (carStatusCode, stationMap) ->
            Optional.ofNullable(CarDefine.CarStatus.of(carStatusCode))
                .map(CarDefine.CarStatus::getName)
                .orElse("未知狀態")
        );

        // 所在站所代碼轉換
        transformers.put(Cars.Fields.locationStationCode, (stationCode, stationMap) ->
            stationMap.getOrDefault(stationCode, stationCode)
        );

        // ETagModel 轉換
        transformers.put(Cars.Fields.etagModel, (etagModel, stationMap) ->
            Optional.ofNullable(etagModel)
                .filter(value -> !"null".equals(value))
                .map(value -> ETagModelEnum.valueOf(value).getDescription())
                .orElse(null));

        // 站點列表轉換
        transformers.put(SubscribeCalendar.Fields.stationCodes, (stationCodes, stationMap) -> {
            if (stationCodes == null || stationCodes.length() <= 2) {
                return stationCodes;
            }

            try {
                return "[" + Arrays.stream(stationCodes.substring(1, stationCodes.length() - 1).split(","))
                    .map(code -> stationMap.getOrDefault(code, code))
                    .collect(Collectors.joining(",")) + "]";
            } catch (Exception e) {
                return stationCodes;
            }
        });

        return transformers;
    }

    private void handleEntityChange(EntityChange entityChange,
                                    Map<String, BiFunction<String, Map<String, String>, String>> fieldTransformers,
                                    Map<String, String> stationCodeToName) {
        entityChange.getFieldChanges().forEach(fieldChange -> {
            String fieldNameEn = fieldChange.getFieldNameEn();

            // 標準化值
            String oldValue = normalizeValue(fieldChange.getOldValue());
            String newValue = normalizeValue(fieldChange.getNewValue());

            // 檢查是否有對應的轉換器
            BiFunction<String, Map<String, String>, String> transformer = fieldTransformers.get(fieldNameEn);
            if (transformer != null) {
                fieldChange.setOldValue(transformer.apply(oldValue, stationCodeToName));
                fieldChange.setNewValue(transformer.apply(newValue, stationCodeToName));
            } else {
                // 沒有特殊處理，直接使用標準化後的值
                fieldChange.setOldValue(oldValue);
                fieldChange.setNewValue(newValue);
            }
        });
    }

    private String normalizeValue(Object value) {
        return value == null ? "" : String.valueOf(value).replace("\"", "");
    }

    /**
     * 針對 mainEntityName 為 SubscribeCalendar 的 DELETE 異動歷程，將 changes[*].deletedEntity 中的 stationCodes 轉換為中文站所名稱
     */
    private void handleEntityDeletion(String entityName, EntityDeletion entityDeletion, Map<String, String> stationCodeToName, Gson gson) {
        if (SubscribeCalendar.class.getSimpleName().equals(entityName)) {
            SubscribeCalendar subscribeCalendar = gson.fromJson(gson.toJson(entityDeletion.getDeletedEntity()), SubscribeCalendar.class);
            subscribeCalendar.getStationCodes().replaceAll(stationCode -> stationCodeToName.getOrDefault(stationCode, stationCode));
            entityDeletion.setDeletedEntity(subscribeCalendar);
        }
    }

    /**
     * 針對 mainEntityName 為 SubscribeCalendar 的異動歷程，將 requestBody JSON 中的 stationCodes 轉換為中文站所名稱
     */
    private void handleSubscribeCalendarRequestBody(EntityChangeLog entityChangeLog, Map<String, String> stationCodeToName, Gson gson) {
        if (SubscribeCalendar.class.getSimpleName().equals(entityChangeLog.getMainEntityName())) {
            Object parsed = convertRequestBodyToObject(entityChangeLog.getRequestBody(), stationCodeToName, gson);
            entityChangeLog.setRequestBody(gson.toJson(parsed));
        }
    }

    private Object convertRequestBodyToObject(String rawJson, Map<String, String> stationCodeToName, Gson gson) {
        try {
            // 嘗試先 parse 成 CalendarUpdateRequest（包含 dateList 和 stationCodes）
            CalendarUpdateRequest req = gson.fromJson(rawJson, CalendarUpdateRequest.class);

            Optional.ofNullable(req.getStationCodes())
                .ifPresent(stationCodes -> stationCodes.replaceAll(stationCode -> stationCodeToName.getOrDefault(stationCode, stationCode)));

            return req;
        } catch (JsonSyntaxException e) {
            log.warn("requestBody 解析失敗，非標準 JSON 格式: {}", rawJson, e);
            return rawJson;
        }
    }
}