package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.redis.cache.Get;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.DealerOrderRepository;
import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.event.car.CarDepartEvent;
import com.carplus.subscribe.event.car.CarReturnEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.auth.DriverDto;
import com.carplus.subscribe.model.auth.req.CustomerDriverPostReq;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserUnionQuery;
import com.carplus.subscribe.model.auth.resp.Phone;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoQueryResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.model.etag.EtagCloseRequest;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.lrental.ContractAddReq;
import com.carplus.subscribe.model.lrental.ContractCustomerCreateRequest;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.car.CarValidationResult;
import com.carplus.subscribe.model.request.dealer.*;
import com.carplus.subscribe.model.response.dealer.*;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.service.factory.DealerOrderExcelProcessStrategyFactory;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessContext;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static carplus.common.utils.BeanUtils.ignorePropertyNames;
import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_CODE;
import static com.carplus.subscribe.constant.CarPlusConstant.SEALAND_VIRTUAL_PLATE_NO;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.model.order.DealerOrderPriceInfoRequest.getDealerOrderPriceInfoRequest;
import static com.carplus.subscribe.utils.ExcelUtil.isCellNullOrBlank;
import static java.time.temporal.ChronoUnit.DAYS;
import static org.springframework.beans.BeanUtils.copyProperties;

@Service
@Slf4j
public class DealerOrderService {

    @Autowired
    private DealerOrderRepository dealerOrderRepository;
    @Autowired
    private StationService stationService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private GoSmartServer goSmartServer;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private LrentalServer lrentalServer;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    InsuranceService insuranceService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private CsatService csatService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private BuChangeService buChangeService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private DealerOrderPriceInfoService dealerOrderPriceInfoService;
    @Autowired
    private EtagInfoRepository etagInfoRepository;
    @Autowired
    private OrderService orderService;
    @Autowired
    private LrentalContractService lrentalContractService;
    @Autowired
    private DealerOrderExcelProcessStrategyFactory dealerOrderExcelProcessStrategyFactory;
    @Lazy
    @Autowired
    private EContractService eContractService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 新增經銷商訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrderQueryResponse createDealerOrder(DealerOrderCreateRequest request, boolean fromImportExcel) {

        validateExpectStationAndCityArea(request);

        if (!fromImportExcel) {
            dealerOrderRepository.findById(request.getOrderNo()).ifPresent(dealerOrder -> {
                throw new SubscribeException(DEALER_ORDER_EXIST);
            });
            AuthDealerUserResponse dealerUserResponse = authServer.getDealerUser(request.getCustomerInfo().getIdNo());
            CarValidationResult carValidationResult = validateAndRetrieveCar(request.getPlateNo(), dealerUserResponse, new ArrayList<>());
            request.setPlateNo(carValidationResult.getPlateNo());
            carValidationResult.getErrorMessages().forEach(errorMessage -> throwExceptionAndNotify(errorMessage, request.getOrderNo()));
        }

        DealerOrder dealerOrder = new DealerOrder();
        fillDealerOrderFromRequest(request, dealerOrder);
        dealerOrder.setIsCreateAccount(request.getIsRegister());

        // 向 AuthServer 查詢是否已有存客戶資訊，若不存在，則向 AuthServer 儲存客戶資訊
        DealerCustomerInfoForCreate customerInfo = request.getCustomerInfo();
        Optional<AuthDealerUserResponse> dealerUser = authServer.saveDealerUsers(customerInfo.buildAuthDealerUserSaveRequest()).stream().findFirst();
        // 將 response 中 id 存在 dealerOrder 之 dealerUserId
        dealerUser.ifPresent(user -> dealerOrder.setDealerUserId(user.getId()));
        dealerOrderRepository.persist(dealerOrder);

        // 將經銷商用戶註冊至格上會員
        if (Boolean.TRUE.equals(request.getIsRegister())) {
            registerUser(request);
        }

        List<City> cities = goSmartServer.getCityArea();
        // 寫入長租Da71資料庫
        addUserToDA71(dealerOrder.getOrderNo(), cities, customerInfo);

        // 判斷是否為續約訂單並處理相應邏輯
        if (isRenewalOrder(request, dealerOrder)) {
            handleRenewalOrder(request, dealerOrder);
            // 當車牌異動，需將車籍上下架與異動狀態
            if (hasPlateNoChanged(request.getPlateNo(), dealerOrder)) {
                changePlateNo(dealerOrder, request.getPlateNo());
            }
        } else {
            Cars car = Optional.ofNullable(carsService.findByPlateNo(dealerOrder.getPlateNo())).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
            lockCarToOrder(car, dealerOrder.getOrderNo());
            eContractService.updateEContractBeforeSign(dealerOrder, car.getCarNo());
        }

        return createDealerOrderQueryResponseWithUserInfo(dealerOrder);
    }

    private void lockCarToOrder(Cars car, String orderNo) {
        car.setBookingOrderNo(orderNo);
        updateCarStatusBasedOnOrders(car, orderNo);
    }

    private boolean isRenewalOrder(BaseDealerOrderRequest request, DealerOrder dealerOrder) {
        return !dealerOrder.getIsNewOrder() && StringUtils.isNotBlank(request.getPreviousOrderNo());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void handleRenewalOrder(BaseDealerOrderRequest request, DealerOrder dealerOrder) {
        DealerOrder previousOrder = getOrder(request.getPreviousOrderNo());
        if (previousOrder != null) {
            previousOrder.setNextStageOrderNo(request.getOrderNo());
            dealerOrderRepository.save(previousOrder);
            createLrentalContractAfterRenew(configService.getSubscribeConfig().getSubscribeDefaultMemberId(), previousOrder, dealerOrder);
        }
    }

    private void throwExceptionAndNotify(String errorMessage, String orderNo) {
        if (CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER.getMsg().equals(errorMessage)) {
            SubscribeException subscribeException = new SubscribeException(CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER);
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", orderNo);
            map.put("errMsg", errorMessage);
            mattermostServer.notify("經銷商訂單處理失敗", map, subscribeException);
            throw subscribeException;
        }
    }

    public DealerOrderQueryResponse createDealerOrderQueryResponseWithUserInfo(DealerOrder dealerOrder) {
        DealerOrderQueryResponse dealerOrderQueryResponse = new DealerOrderQueryResponse(dealerOrder);
        // 設定經銷商客戶資訊
        setDealerUserInfo(dealerOrderQueryResponse.getCustomerInfo());
        return dealerOrderQueryResponse;
    }

    public List<DealerOrderQueryResponse> createDealerOrderQueryResponsesWithUserInfo(List<? extends DealerOrder> dealerOrders) {
        Set<Long> dealerUserIds = dealerOrders.stream()
            .map(DealerOrder::getDealerUserId)
            .collect(Collectors.toSet());
        Map<Long, AuthDealerUserResponse> authDealerUserMap = authServer.generateDealerUserMap(dealerUserIds);

        // Convert List<DealerOrder> to List<DealerOrderQueryResponse> and set dealerUserInfo
        return dealerOrders.stream()
            .map(DealerOrderQueryResponse::new)
            .peek(response -> {
                if (authDealerUserMap.containsKey(response.getDealerUserId())) {
                    response.getCustomerInfo().setDealerUserInfo(authDealerUserMap.get(response.getDealerUserId()));
                }
            }).collect(Collectors.toList());
    }

    private void setDealerUserInfo(List<? extends DealerUser> dealerUsers) {
        // 從 List<DealerOrderQueryResponse> 取得經銷商客戶編號集合 -> dealerUserIds
        Set<Long> dealerUserIds = DealerUser.getDealerUserIds(dealerUsers);
        Map<Long, AuthDealerUserResponse> authDealerUserMap = authServer.generateDealerUserMap(dealerUserIds);

        dealerUsers.forEach(dealerUser -> {
            if (authDealerUserMap.containsKey(dealerUser.getDealerUserId())) {
                dealerUser.setDealerUserInfo(authDealerUserMap.get(dealerUser.getDealerUserId()));
            }
        });
    }

    /**
     * 設定經銷商客戶資訊
     */
    private void setDealerUserInfo(DealerUser dealerUser) {
        Long dealerUserId = dealerUser.getDealerUserId();
        if (dealerUserId != null && dealerUserId > 0) {
            AuthDealerUserResponse authDealerUser = authServer.getDealerUser(dealerUserId);
            // 設置經銷商客戶資訊
            dealerUser.setDealerUserInfo(authDealerUser);
        }
    }

    /**
     * 請求資料檢核API，如站點、城市、區域
     */
    private void validateExpectStationAndCityArea(BaseDealerOrderRequest request) {

        if (request.getSubscriptionInfo() != null) {
            validateExpectStation(request.getSubscriptionInfo());
        }
        if (request.getCustomerInfo() != null) {
            validateCustomerInfoCityAndArea(request.getCustomerInfo());
        }
    }

    private static void fillDealerOrderFromRequest(BaseDealerOrderRequest request, DealerOrder dealerOrder) {

        copyProperties(request, dealerOrder, ignorePropertyNames(request));
        DealerSubscriptionInfoInterface subscriptionInfo = request.getSubscriptionInfo();
        
        // 先複製所有屬性，但預定出/還車站點需要特殊處理
        String[] ignoreStationProperties = Stream.concat(
            Arrays.stream(ignorePropertyNames(subscriptionInfo)),
            Stream.of(DealerOrder.Fields.expectDepartStation, DealerOrder.Fields.expectReturnStation)
        ).toArray(String[]::new);
        
        copyProperties(subscriptionInfo, dealerOrder, ignoreStationProperties);
        
        // 只有當預定出/還車站點不為空時才複製
        if (StringUtils.isNotBlank(subscriptionInfo.getExpectDepartStation())) {
            dealerOrder.setExpectDepartStation(subscriptionInfo.getExpectDepartStation());
        }
        if (StringUtils.isNotBlank(subscriptionInfo.getExpectReturnStation())) {
            dealerOrder.setExpectReturnStation(subscriptionInfo.getExpectReturnStation());
        }
    }

    /**
     * 編輯經銷商訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrderQueryResponse updateDealerOrder(DealerOrderUpdateRequest request) {

        validateExpectStationAndCityArea(request);
        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
        // 當車牌異動，需將車籍上下架與異動狀態
        if (hasPlateNoChanged(request.getPlateNo(), dealerOrder)) {
            changePlateNo(dealerOrder, request.getPlateNo());
        }

        fillDealerOrderFromRequest(request, dealerOrder);

        DealerCustomerInfoAllOptional customerInfo = request.getCustomerInfo();
        Long dealerUserId = dealerOrder.getDealerUserId();
        AuthDealerUserResponse dealerUser = authServer.getDealerUser(dealerUserId == 0L && StringUtils.isNotBlank(customerInfo.getIdNo()) ? customerInfo.getIdNo() : dealerUserId);
        if (isDealerCustomerInfoChanged(customerInfo, dealerUser, dealerUserId)) {
            // 向 AuthServer 新增/更新客戶資訊
            dealerUser = authServer.saveDealerUsers(customerInfo.buildAuthDealerUserSaveRequest())
                .stream().findFirst().orElseThrow(() -> new SubscribeException(DEALER_USER_NOT_FOUND));
        }
        if (dealerUser != null) {
            dealerOrder.setDealerUserId(dealerUser.getId());
        }

        return createDealerOrderQueryResponseWithUserInfo(dealerOrderRepository.save(dealerOrder));
    }

    /**
     * 比較經銷商客戶資訊是否異動(除了idNo)
     */
    private boolean isDealerCustomerInfoChanged(DealerCustomerInfoAllOptional customerInfo, AuthDealerUserResponse dealerUser, Long dealerUserId) {
        if (customerInfo == null || StringUtils.isBlank(customerInfo.getIdNo()) && dealerUserId == 0L) {
            return false;
        }
        // authServer 查不到客戶資訊，代表 authServer 尚未建立此 idNo 的 user ，故向 authServer 新增客戶資訊
        if (dealerUser == null) {
            // 驗證新增客戶資訊必填欄位
            if (StringUtils.isBlank(customerInfo.getUserName()) || StringUtils.isBlank(customerInfo.getNationalCode()) || StringUtils.isBlank(customerInfo.getMainCell()) || StringUtils.isBlank(customerInfo.getBirthDay())) {
                throw new SubscribeException(DEALER_USER_CREATE_REQUIRED);
            }
            return true;
        }
        if (!Objects.equals(customerInfo.getIdNo(), dealerUser.getIdNo())) {
            throw new SubscribeException(DEALER_USER_ID_NO_CAN_NOT_CHANGE);
        }
        fillCustomerInfoNullFields(customerInfo, dealerUser);

        return (!Objects.equals(customerInfo.getUserName(), dealerUser.getUserName())
            || !Objects.equals(customerInfo.getNationalCode(), dealerUser.getNationalCode())
            || !Objects.equals(customerInfo.getMainCell(), dealerUser.getMainCell())
            || !Objects.equals(customerInfo.getBirthDay(), dealerUser.getBirthDay())
            || !Objects.equals(customerInfo.getEmail(), dealerUser.getEmail())
            || !Objects.equals(customerInfo.getCity(), dealerUser.getHhcityId())
            || !Objects.equals(customerInfo.getArea(), dealerUser.getHhareaId())
            || !Objects.equals(customerInfo.getAddress(), dealerUser.getHhaddress())
            || !Objects.equals(customerInfo.getVatNumber(), dealerUser.getVatNumber())
            || !Objects.equals(customerInfo.getCompanyName(), dealerUser.getCompanyName())
            || !Objects.equals(customerInfo.getCompanyLocation(), dealerUser.getCompanyLocation()));
    }

    private void fillCustomerInfoNullFields(DealerCustomerInfoAllOptional customerInfo, AuthDealerUserResponse dealerUser) {
        if (customerInfo.getUserName() == null) {
            customerInfo.setUserName(dealerUser.getUserName());
        }
        if (customerInfo.getNationalCode() == null) {
            customerInfo.setNationalCode(dealerUser.getNationalCode());
        }
        if (customerInfo.getMainCell() == null) {
            customerInfo.setMainCell(dealerUser.getMainCell());
        }
        if (customerInfo.getBirthDay() == null) {
            customerInfo.setBirthDay(dealerUser.getBirthDay());
        }
        if (customerInfo.getEmail() == null) {
            customerInfo.setEmail(dealerUser.getEmail());
        }
        if (customerInfo.getCity() == null) {
            customerInfo.setCity(dealerUser.getHhcityId());
        }
        if (customerInfo.getArea() == null) {
            customerInfo.setArea(dealerUser.getHhareaId());
        }
        if (customerInfo.getAddress() == null) {
            customerInfo.setAddress(dealerUser.getHhaddress());
        }
        if (customerInfo.getVatNumber() == null) {
            customerInfo.setVatNumber(dealerUser.getVatNumber());
        }
        if (customerInfo.getCompanyName() == null) {
            customerInfo.setCompanyName(dealerUser.getCompanyName());
        }
        if (customerInfo.getCompanyLocation() == null) {
            customerInfo.setCompanyLocation(dealerUser.getCompanyLocation());
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void changePlateNo(DealerOrder dealerOrder, String newPlateNo) {
        Cars oriCar = carsService.findByPlateNo(dealerOrder.getPlateNo());
        Cars newCar = carsService.findByPlateNo(newPlateNo);
        if (newCar == null) {
            throw new SubscribeException(CAR_NOT_FOUND);
        }
        // 只有在建立或更新經銷商訂單時更新電子合約範本id (經銷商訂單狀態在出車之前)
        if (dealerOrder.getOrderStatus() < ContractStatus.GOING.getCode()) {
            eContractService.updateEContractBeforeSign(dealerOrder, newCar.getCarNo());
        }
        changeCar(dealerOrder, newCar, oriCar, configService.getSubscribeConfig().getSubscribeDefaultMemberId());
    }

    /**
     * 經銷商訂單 eTag 通行費用查詢
     */
    public EtagInfoResponse queryEtagAmt(String orderNo, String headerMemberId) {

        DealerOrder dealerOrder = dealerOrderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));

        ETagInfo eTag = Optional.ofNullable(eTagService.getLatestNotReturnETagInfo(dealerOrder, true))
            .orElseGet(() ->
                Optional.ofNullable(eTagService.getLatestReturnETagInfo(dealerOrder))
                    .orElseThrow(() -> new SubscribeException(ETAG_INFO_NOT_FUND))
            );

        return eTagService.dealerOrderQuery(dealerOrder, eTag, dealerOrder.getPlateNo(), headerMemberId);
    }

    /**
     * 經銷商訂單出車作業異動
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrder departDealerOrder(DealerOrderDepartRequest request, String departMemberId, String systemKind) {

        validateExpectStationAndCityArea(request);
        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
        validateDepartUpdate(request, dealerOrder);

        Cars oriCar = Optional.ofNullable(carsService.findByPlateNo(dealerOrder.getPlateNo())).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
        Cars newCar = null;
        // 當車牌異動，需將車籍上下架與異動狀態
        if (hasPlateNoChanged(request.getPlateNo(), dealerOrder)) {
            newCar = Optional.ofNullable(carsService.findByPlateNo(request.getPlateNo())).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
            // 檢查新車是否為 SeaLand 虛擬車牌
            validateSeaLandVirtualPlateNo(dealerOrder, newCar, systemKind);
            changeCar(dealerOrder, newCar, oriCar, configService.getSubscribeConfig().getSubscribeDefaultMemberId());
        } else {
            // 檢查原車是否為 SeaLand 虛擬車牌
            validateSeaLandVirtualPlateNo(dealerOrder, oriCar, systemKind);
        }

        Cars carToDepart = (newCar != null) ? newCar : oriCar;
        String plateNo = carToDepart.getPlateNo();

        validateCarBuId(plateNo, dealerOrder);

        carsService.updateStatus(carToDepart, CarDefine.CarStatus.BizOut);

        // 判斷是否為續約訂單並處理相應邏輯
        if (isRenewalOrder(request, dealerOrder)) {
            handleRenewalOrder(request, dealerOrder);
        }

        fillDealerOrderFromRequest(request, dealerOrder);
        Integer beginAmt = request.getSubscriptionInfo().getBeginAmt();
        if (request.getSubscriptionInfo().getPaidAmt() == null) {
            dealerOrder.setPaidAmt(dealerOrder.getPaidAmt() + beginAmt);
        }
        if (request.getSubscriptionInfo().getTotalAmt() == null) {
            dealerOrder.setTotalAmt(dealerOrder.getTotalAmt() + beginAmt);
        }

        // 若非續約訂單且為格上車，檢查長租契約日期是否與經銷商訂單起迄日期一致，不一致則重新建立長租契約
        handleLrentalContractRecreation(dealerOrder);

        // 通知 Etag 出車
        eTagService.rentCarForDealerOrder(dealerOrder, departMemberId);
        // 所屬公司統編為格上才建立電訪任務 (只在第一次出車時建立)
        if (CarsUtil.isCarPlusCar(carToDepart.getVatNo())) {
            csatService.createCsat(dealerOrder);
        }
        CarUpdateKmDto carUpdateKmDto = CarUpdateKmDto.from(carToDepart, carToDepart.getCurrentMileage(), dealerOrder.getOrderNo());
        eventPublisher.publishEvent(new CarDepartEvent(this, carUpdateKmDto, crsService.getCar(plateNo), departMemberId));
        return dealerOrderRepository.save(dealerOrder);
    }

    private void validateCarBuId(String plateNo, DealerOrder dealerOrder) {
        if (CarsUtil.isCarPlusCar(plateNo)) {
            // 檢查車輛庫位
            CarBaseInfoSearchResponse carBase = Optional.ofNullable(crsService.getCar(plateNo)).orElseThrow(() -> new SubscribeException(CRS_CAR_NOT_FOUND));
            Integer buId = carBase.getBuId();
            if (BuIdEnum.isNotValidForSubscribe(buId)) {
                throw new SubscribeException(CRS_CAR_NOT_BELONG_LRENTAL_NOR_SUBSCRIBE);
            }
            if (BuIdEnum.subscribe.getCode().equals(buId) && dealerOrder.getLrentalContractNo() == null) {
                throw new SubscribeException(LRENTAL_CONTRACT_NOT_CREATED_YET);
            }
        }
    }

    private void handleLrentalContractRecreation(DealerOrder dealerOrder) {
        lrentalContractService.prepareLrentalContractRecreationRequest(dealerOrder)
            .ifPresent(request -> createLrentalContract(dealerOrder, request, configService.getSubscribeConfig().getSubscribeDefaultMemberId()));
    }

    private boolean hasPlateNoChanged(String requestPlateNo, DealerOrder dealerOrder) {
        return requestPlateNo != null && !Objects.equals(requestPlateNo, dealerOrder.getPlateNo());
    }

    private void validateSeaLandVirtualPlateNo(DealerOrder dealerOrder, Cars newCar, String systemKind) {
        if (SEALAND_VIRTUAL_PLATE_NO.equals(newCar.getPlateNo())) {
            throw new SubscribeException(DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART);
        }
    }

    /**
     * 經銷商訂單結案
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrder closeDealerOrder(DealerOrderCloseRequest request, String headerMemberId) {

        validateExpectStationAndCityArea(request);
        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
        validateReturnUpdate(request, dealerOrder);

        fillDealerOrderFromRequest(request, dealerOrder);
        Integer closeAmt = request.getSubscriptionInfo().getCloseAmt();
        Optional.ofNullable(request.getSubscriptionInfo().getPaidAmt())
            .ifPresent(paidAmt -> dealerOrder.setPaidAmt(dealerOrder.getPaidAmt() + closeAmt));
        Optional.ofNullable(request.getSubscriptionInfo().getTotalAmt())
            .ifPresent(totalAmt -> dealerOrder.setTotalAmt(dealerOrder.getTotalAmt() + closeAmt));
        dealerOrderRepository.save(dealerOrder);

        Cars cars = Optional.ofNullable(carsService.findByPlateNo(dealerOrder.getPlateNo())).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
        updateCarStatusBasedOnOrders(cars, dealerOrder.getOrderNo());
        CarBaseInfoSearchResponse carBase = crsService.getCar(cars.getPlateNo());
        handleGenerateCenterContract(dealerOrder, cars, null, carBase);
        // 實際還車
        if (request.getSubscriptionInfo().getIsReturned() && cars.getBuChangeMasterId() != null) {
            // 檢查車輛狀態是否為空車，才可以撥還車
            if (CarDefine.CarStatus.Free.getCode().equals(cars.getCarStatus())) {
                buChangeService.changeReturn(dealerOrder, carBase, headerMemberId, cars);
                carsService.update(cars);
            } else {
                log.warn("車輛狀態非空車，無法撥還車輛，車牌號碼: {}, 車輛狀態: {}, 經銷商訂單: {}", cars.getPlateNo(), cars.getCarStatus(), dealerOrder.getOrderNo());
            }
        }

        eTagService.close(dealerOrder, dealerOrder.getPlateNo(), headerMemberId, dealerOrder.getReturnStation(), true);

        eventPublisher.publishEvent(new CarReturnEvent(this, CarUpdateKmDto.from(cars, cars.getCurrentMileage(), dealerOrder.getOrderNo()), carBase, headerMemberId));

        return dealerOrderRepository.save(dealerOrder);
    }

    /**
     * 依據車輛其他訂單狀態異動車輛狀態
     *
     * @param cars                 要更新的車輛
     * @param currentDealerOrderNo 當前處理中的訂單編號
     */
    private void updateCarStatusBasedOnOrders(Cars cars, String currentDealerOrderNo) {
        String plateNo = cars.getPlateNo();
        List<Orders> processingOrders = orderService.getProcessingOrdersByPlateNo(plateNo);
        List<DealerOrderQueryResponse> allProcessingDealerOrders = getProcessingDealerOrdersByPlateNo(plateNo);

        if (processingOrders.isEmpty() && allProcessingDealerOrders.isEmpty()) {
            carsService.updateStatus(cars, CarDefine.CarStatus.Free);
            return;
        }

        List<DealerOrderQueryResponse> otherProcessingDealerOrders = allProcessingDealerOrders.stream()
            .filter(order -> !order.getOrderNo().equals(currentDealerOrderNo))
            .collect(Collectors.toList());

        // 完全沒有其他出車中訂單，則將車輛狀態異動為已訂閱
        if (!(processingOrders.stream().anyMatch(order -> order.getStatus().equals(OrderStatus.DEPART.getStatus()))
            || otherProcessingDealerOrders.stream().anyMatch(order -> order.getOrderStatus().equals(ContractStatus.GOING.getCode())))) {
            carsService.updateStatus(cars, CarDefine.CarStatus.Subscribed);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public ETagInfo etagReturn(String orderNo, String headerMemberId, EtagCloseRequest request) {

        DealerOrder dealerOrder = dealerOrderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));

        ETagInfo eTagInfo = eTagService.returnCar(dealerOrder, dealerOrder.getPlateNo(), headerMemberId, dealerOrder.getExpectReturnStation());

        eTagInfo.setReturnDate(request.getReturnDate());

        Optional<DealerOrderPriceInfo> eTagPriceInfo = dealerOrderPriceInfoService.getEtagPriceInfo(orderNo);
        eTagInfo.setDealerOrderPriceInfoId(eTagPriceInfo.map(DealerOrderPriceInfo::getTradeId).orElseGet(() -> {
            DealerOrderPriceInfo dealerOrderPriceInfo = dealerOrderPriceInfoService.createDealerOrderPriceInfo(getDealerOrderPriceInfoRequest(eTagInfo));
            return dealerOrderPriceInfo.getTradeId();
        }));

        return etagInfoRepository.save(eTagInfo);
    }

    /**
     * 取消訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrder cancelDealerOrder(DealerOrderCancelRequest request) {

        validateExpectStationAndCityArea(request);
        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
        validateCancelUpdate(request, dealerOrder);
        fillDealerOrderFromRequest(request, dealerOrder);
        dealerOrder.setPaidAmt(request.getSubscriptionInfo().getTotalAmt());
        dealerOrder.setIsCancel(true);
        List<DealerOrder> dealerOrders = dealerOrderRepository.getDealerOrdersByParentOrderNo(dealerOrder.getParentOrderNo());
        DealerOrder previousDealerOrder = dealerOrderRepository.getPreviousDealerOrder(dealerOrder.getOrderNo());
        Cars cars = carsService.findByPlateNo(dealerOrder.getPlateNo());

        // 為新單或是除了自己沒有進行中的訂單
        if (dealerOrder.getIsNewOrder() || dealerOrders.stream().anyMatch(order -> !order.getOrderNo().equals(request.getOrderNo()) && !order.getOrderStatus().equals(ContractStatus.GOING.getCode()))) {
            CarBaseInfoSearchResponse carBase = crsService.getCar(cars.getPlateNo());
            handleGenerateCenterContract(dealerOrder, cars, null, carBase);

            if (cars.getBuChangeMasterId() != null) {
                buChangeService.changeReturn(dealerOrder, carBase, configService.getSubscribeConfig().getSubscribeDefaultMemberId(), cars);
                carsService.update(cars);
            }
        }
        if (previousDealerOrder != null) {
            previousDealerOrder.setNextStageOrderNo(null);
            dealerOrderRepository.save(previousDealerOrder);
        }
        updateCarStatusBasedOnOrders(cars, dealerOrder.getOrderNo());
        return dealerOrderRepository.save(dealerOrder);
    }

    /**
     * 驗證出車API
     */
    private void validateDepartUpdate(DealerOrderDepartRequest request, DealerOrder dealerOrder) {

        if (!dealerOrder.getOrderStatus().equals(ContractStatus.CREATE.getCode())) {
            throw new BadRequestException("訂單非建立中狀態，不可出車");
        }
        if (request.getOrderStatus() == null) {
            request.setOrderStatus(ContractStatus.GOING.getCode());
        }
        if (request.getOrderStatus() == ContractStatus.GOING.getCode()) {
            DealerSubscriptionInfoForDepart subscriptionInfo = request.getSubscriptionInfo();
            subscriptionInfo.setReturnStation(null);
            subscriptionInfo.setReturnDate(null);
            if (subscriptionInfo.getDepartStation() == null) {
                throw new BadRequestException(String.format("出車中訂單異動，%s不可為空", DealerOrderExcelColumn.DEPART_STATION.getDescription()));
            }
            if (subscriptionInfo.getDepartDate() == null) {
                throw new BadRequestException(String.format("出車中訂單異動，%s不可為空", DealerOrderExcelColumn.DEPART_DATE.getDescription()));
            }
        } else {
            throw new BadRequestException("訂單狀態參數不為出車狀態，不可執行出車API");
        }
    }

    /**
     * 檢查還車API
     */
    private void validateReturnUpdate(DealerOrderCloseRequest request, DealerOrder dealerOrder) {

        if (!dealerOrder.getOrderStatus().equals(ContractStatus.GOING.getCode())) {
            throw new BadRequestException("訂單狀態非進行中，無法還車，請洽格上訂閱營業人員");
        }
        if (hasPlateNoChanged(request.getPlateNo(), dealerOrder)) {
            throw new BadRequestException("車牌號碼與訂單車牌號碼不一致，不可還車");
        }
        if (request.getOrderStatus() == null) {
            request.setOrderStatus(ContractStatus.COMPLETE.getCode());
        }
        if (request.getOrderStatus().equals(ContractStatus.GOING.getCode()) || request.getOrderStatus().equals(ContractStatus.COMPLETE.getCode())) {
            if (request.getSubscriptionInfo().getReturnStation() == null) {
                throw new BadRequestException(String.format("已還車訂單異動，%s不可為空", DealerOrderExcelColumn.RETURN_STATION.getDescription()));
            }
            if (request.getSubscriptionInfo().getReturnDate() == null) {
                throw new BadRequestException(String.format("已還車訂單異動，%s不可為空", DealerOrderExcelColumn.RETURN_DATE.getDescription()));
            }
        } else {
            throw new BadRequestException("訂單狀態參數不為出車、還車狀態，不可執行還車API");
        }
    }

    /**
     * 取消訂單驗證API
     */
    private void validateCancelUpdate(DealerOrderCancelRequest request, DealerOrder dealerOrder) {

        if (!dealerOrder.getOrderStatus().equals(ContractStatus.CREATE.getCode())) {
            throw new BadRequestException("訂單不為建立中狀態，不可取消");
        }
        if (hasPlateNoChanged(request.getPlateNo(), dealerOrder)) {
            throw new BadRequestException("車牌號碼與訂單車牌號碼不一致，不可取消訂單");
        }
        if (request.getOrderStatus() == null) {
            request.setOrderStatus(ContractStatus.CANCEL.getCode());
        }
        if (!request.getOrderStatus().equals(ContractStatus.CANCEL.getCode())) {
            throw new BadRequestException("訂單狀態參數不為取消訂單狀態，不可執行取消API");
        }
    }

    /**
     * 驗證預定出/還車站點資料
     */
    private void validateExpectStation(DealerSubscriptionInfoInterface subscriptionInfo) {

        Map<String, Stations> stationsMap = stationService.getStationsMap();
        
        if (subscriptionInfo instanceof DealerSubscriptionInfo) {
            // 建立經銷商訂單：expectDepartStation 和 expectReturnStation 必填
            validateStationCode(stationsMap, subscriptionInfo.getExpectDepartStation(), true);
            validateStationCode(stationsMap, subscriptionInfo.getExpectReturnStation(), true);
        } else {
            // 編輯、出車、結案、取消經銷商訂單：expectDepartStation 和 expectReturnStation 選填
            validateStationCode(stationsMap, subscriptionInfo.getExpectDepartStation(), false);
            validateStationCode(stationsMap, subscriptionInfo.getExpectReturnStation(), false);
        }
    }

    /**
     * 驗證訂車人之城市、區域資料
     */
    private void validateCustomerInfoCityAndArea(BaseDealerCustomerInfo customerInfo) {

        Integer cityCode = customerInfo.getCity();
        Integer areaCode = customerInfo.getArea();

        if (cityCode != null && areaCode != null) {
            Map<Integer, Map<Integer, Area>> cityMap = goSmartServer.getCityArea().stream().collect(Collectors.toMap(City::getCityId, city ->
                city.getArea().stream().collect(Collectors.toMap(Area::getAreaId, a -> a))
            ));
            if (!cityMap.isEmpty()) {
                Map<Integer, Area> areaMap = cityMap.get(cityCode);
                if (areaMap == null) {
                    throw new BadRequestException(String.format("查無 %d 此城市編號", cityCode));
                }
                Area area = areaMap.get(areaCode);
                if (area == null) {
                    throw new BadRequestException(String.format("查無 %d 此地區編號", areaCode));
                }
            }
        }
    }

    private void validateStationCode(Map<String, Stations> stationsMap, String stationCode) {
        validateStationCode(stationsMap, stationCode, true);
    }

    private void validateStationCode(Map<String, Stations> stationsMap, String stationCode, boolean required) {
        if (StringUtils.isBlank(stationCode)) {
            if (required) {
                throw new BadRequestException("站點編號不可為空");
            }
            return; // 選填欄位若為空則跳過驗證
        }
        if (!stationsMap.containsKey(stationCode)) {
            throw new BadRequestException(String.format("查無 %s 此站點編號", stationCode));
        }
    }

    /**
     * 將經銷商用戶註冊至格上會員
     */
    private void registerUser(DealerOrderCreateRequest request) {

        DealerCustomerInfoForCreate customerInfo = request.getCustomerInfo();
        List<Integer> acctIds = authServer.getUsers(null, customerInfo.getMainCell(), customerInfo.getIdNo());
        if (acctIds.isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.setTimeZone(TimeZone.getTimeZone(DateUtils.ZONE_TPE));
            DriverDto dto = new DriverDto();
            dto.setCityId(customerInfo.getCity());
            dto.setAreaId(customerInfo.getArea());
            dto.setNationalCode(customerInfo.getNationalCode());
            dto.setAddress(customerInfo.getAddress());
            dto.setId(customerInfo.getIdNo());
            dto.setEmail(customerInfo.getEmail());
            dto.setPhone(customerInfo.getMainCell());
            dto.setIsForeigner(customerInfo.getIsForeigner());
            dto.setIndex(0);
            dto.setName(customerInfo.getUserName());
            try {
                dto.setBirthday(sdf.parse(customerInfo.getBirthDay()));
            } catch (Exception e) {
                log.error("轉換生日失敗", e);
            }
            CustomerDriverPostReq req = new CustomerDriverPostReq();
            req.setDriverDtoList(new ArrayList<>());
            req.getDriverDtoList().add(dto);
            authServer.driverToUsers(req);
        }
    }

    private <T> void filterByUserInfo(DealerOrderCriteria criteria, List<T> responses,
                                      Function<T, String> getOrderNo,
                                      Function<T, String> getIdNo,
                                      Function<T, String> getUserName,
                                      Function<T, String> getNationalCode,
                                      Function<T, String> getPhone) {

        List<String> reservedOrderNos = responses.stream()
            .filter(response -> isMatchingCriteria(criteria,
                getIdNo.apply(response),
                getUserName.apply(response),
                getNationalCode.apply(response),
                getPhone.apply(response)))
            .map(getOrderNo)
            .collect(Collectors.toList());

        responses.removeIf(response -> !reservedOrderNos.contains(getOrderNo.apply(response)));
    }

    private boolean isMatchingCriteria(DealerOrderCriteria criteria, String idNo, String userName, String nationalCode, String phone) {
        return (StringUtils.isBlank(criteria.getIdNo()) || Objects.equals(criteria.getIdNo(), idNo))
            && (StringUtils.isBlank(criteria.getUserName()) || Objects.equals(criteria.getUserName(), userName))
            && (StringUtils.isBlank(criteria.getNationalCode()) || Objects.equals(criteria.getNationalCode(), nationalCode))
            && (StringUtils.isBlank(criteria.getPhone()) || Objects.equals(criteria.getPhone(), phone));
    }

    private void filteredByUserInfoForExcel(DealerOrderCriteria criteria, List<DealerOrderExcel> responses) {
        filterByUserInfo(criteria, responses,
            DealerOrderExcel::getOrderNo,
            DealerOrderExcel::getIdNo,
            DealerOrderExcel::getUserName,
            DealerOrderExcel::getNationalCode,
            DealerOrderExcel::getMainCell);
    }

    private void filteredByUserInfo(DealerOrderCriteria criteria, List<DealerOrderQueryResponse> responses) {
        filterByUserInfo(criteria, responses,
            DealerOrderQueryResponse::getOrderNo,
            response -> response.getCustomerInfo().getIdNo(),
            response -> response.getCustomerInfo().getUserName(),
            response -> response.getCustomerInfo().getNationalCode(),
            response -> response.getCustomerInfo().getMainCell());
    }

    /**
     * 查詢資料
     *
     * @param queryRequest 查詢相關參數
     * @param limit        限制回傳筆數
     * @param offset       略過前N筆資料
     */
    public List<DealerOrderQueryResponse> searchPage(DealerOrderCriteria queryRequest, Integer limit, Integer offset) {

        Map<String, Stations> stationsMap = stationService.getStationsMap();
        if (StringUtils.isNotBlank(queryRequest.getIdNo())) {
            AuthDealerUserResponse authDealerUserResponse = authServer.getDealerUser(queryRequest.getIdNo());
            if (authDealerUserResponse != null) {
                queryRequest.setAcctId(Collections.singletonList(authDealerUserResponse.getId().intValue()));
            } else {
                return new ArrayList<>();
            }
        }
        List<DealerOrderQueryResponse> responses = dealerOrderRepository.findBySearch(queryRequest, limit, offset)
            .stream()
            .map(o -> {
                DealerOrder dealerOrder = (DealerOrder) o[0];
                DealerOrderQueryResponse response = new DealerOrderQueryResponse(
                    dealerOrder,
                    (CarModel) Optional.ofNullable(o[1]).orElseGet(CarModel::new),
                    (CarBrand) Optional.ofNullable(o[2]).orElseGet(CarBrand::new),
                    dealerOrderPriceInfoService.getDealerOrderPriceInfoByOrderNo(dealerOrder.getOrderNo()),
                    dealerOrderRepository.getRelatedDealerOrdersByParentOrderNo(dealerOrder.getOrderNo(), dealerOrder.getParentOrderNo())
                );

                // 站點相關資訊
                mapStationNamesToDealerUser(stationsMap, response.getSubscriptionInfo());

                return response;
            }).collect(Collectors.toList());

        prepareAndFilterDealerUsers(queryRequest, responses);

        return responses;
    }

    /**
     * 首先填充經銷商客戶資訊，然後根據給定的條件進行過濾
     */
    private void prepareAndFilterDealerUsersForExcel(DealerOrderCriteria queryRequest, List<DealerOrderExcel> responses) {
        // 填充經銷商客戶資訊
        setDealerUserInfo(responses);

        filteredByUserInfoForExcel(queryRequest, responses);
    }

    /**
     * 首先填充經銷商客戶資訊，然後根據給定的條件進行過濾
     */
    private void prepareAndFilterDealerUsers(DealerOrderCriteria queryRequest, List<DealerOrderQueryResponse> responses) {
        // 填充經銷商客戶資訊
        setDealerUserInfo(responses.stream().map(DealerOrderQueryResponse::getCustomerInfo).collect(Collectors.toList()));

        filteredByUserInfo(queryRequest, responses);
    }

    /**
     * 將站點名稱設置至 DealerUser
     */
    private void mapStationNamesToDealerUser(Map<String, Stations> stationsMap, SubscriptionInfo subscriptionInfo) {
        Optional.ofNullable(stationsMap.get(subscriptionInfo.getExpectDepartStation())).map(Stations::getStationName).ifPresent(subscriptionInfo::setExpectDepartStationName);
        Optional.ofNullable(stationsMap.get(subscriptionInfo.getExpectReturnStation())).map(Stations::getStationName).ifPresent(subscriptionInfo::setExpectReturnStationName);
        Optional.ofNullable(stationsMap.get(subscriptionInfo.getDepartStation())).map(Stations::getStationName).ifPresent(subscriptionInfo::setDepartStationName);
        Optional.ofNullable(stationsMap.get(subscriptionInfo.getReturnStation())).map(Stations::getStationName).ifPresent(subscriptionInfo::setReturnStationName);
    }

    /**
     * 經銷商訂單查詢
     *
     * @param pageRequest  頁面相關參數
     * @param queryRequest 查詢相關參數
     */
    public Page<DealerOrderQueryResponse> searchByPage(PageRequest pageRequest, DealerOrderCriteria queryRequest) {
        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();

        AuthDealerUserUnionQuery query = null;
        if (StringUtils.isNotBlank(queryRequest.getIdNo())) {
            query = Optional.ofNullable(query).orElse(new AuthDealerUserUnionQuery());
            query.setIdNo(Collections.singletonList(queryRequest.getIdNo()));
        }

        if (StringUtils.isNotBlank(queryRequest.getUserName())) {
            query = Optional.ofNullable(query).orElse(new AuthDealerUserUnionQuery());
            query.setUserName(Collections.singletonList(queryRequest.getUserName()));
        }

        if (StringUtils.isNotBlank(queryRequest.getNationalCode()) && StringUtils.isNotBlank(queryRequest.getPhone())) {
            query = Optional.ofNullable(query).orElse(new AuthDealerUserUnionQuery());
            query.setPhone(Collections.singletonList(new Phone(queryRequest.getNationalCode(), queryRequest.getPhone())));
        }
        if (query != null) {
            List<AuthDealerUserResponse> list = authServer.getDealerUsers(query);
            if (list.isEmpty()) {
                return Page.of(0, Collections.emptyList(), offset, limit);
            }
            queryRequest.setAcctId(list.stream().map(AuthDealerUserResponse::getId).map(Long::intValue).collect(Collectors.toList()));
        }

        long total = dealerOrderRepository.count(queryRequest);
        if (0 == total) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }

        List<DealerOrderQueryResponse> list = searchPage(queryRequest, limit, offset);

        return Page.of(total, list, offset, limit);
    }

    /**
     * 經銷商訂單明細查詢
     *
     * @param orderNo 訂單編號
     */
    public DealerOrderResponse queryDealerOrderDetail(String orderNo) {

        Map<String, Stations> stationsMap = stationService.getStationsMap();

        DealerOrderResponse dealerOrderResponse = dealerOrderRepository.findById(orderNo)
            .map(dealerOrder -> {
                CarResponse cars;
                ContractSearchRep contractInfo = null;

                cars = carsService.getCarInfo(dealerOrder.getPlateNo());
                if (StringUtils.isNotBlank(dealerOrder.getLrentalContractNo())) {
                    contractInfo = lrentalServer.getContractInfo(dealerOrder.getLrentalContractNo());
                }
                CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(cars.getPlateNo());
                if (carBaseInfoSearchResponse == null && cars.getCrsNo() != null && cars.getCrsNo() > 0) {
                    carBaseInfoSearchResponse = crsService.getCarsByCrsCarNo(Collections.singletonList(cars.getCrsNo())).get(cars.getCrsNo());
                }
                List<DealerOrderPriceInfo> dealerOrderPriceInfos = dealerOrderPriceInfoService.getDealerOrderPriceInfoByOrderNo(orderNo);
                List<DealerOrder> relatedDealerOrders = dealerOrderRepository.getRelatedDealerOrdersByParentOrderNo(dealerOrder.getOrderNo(), dealerOrder.getParentOrderNo());
                DealerOrderResponse orderResponse = new DealerOrderResponse(dealerOrder, cars, carBaseInfoSearchResponse, contractInfo, dealerOrderPriceInfos, relatedDealerOrders);
                mapStationNamesToDealerUser(stationsMap, orderResponse.getSubscriptionInfo());
                return orderResponse;
            }).orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));

        // 設定經銷商客戶資訊
        setDealerUserInfo(dealerOrderResponse.getCustomerInfo());
        dealerOrderResponse.setBuChangeLog(buChangeService.getLastOrderNoLog(dealerOrderResponse.getOrderNo()));
        return dealerOrderResponse;
    }

    /**
     * 取得經銷商訂單
     */
    public DealerOrder getOrder(String orderNo) {
        return dealerOrderRepository.findById(orderNo).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
    }

    /**
     * 取得經銷商訂單
     */
    public List<DealerOrderQueryResponse> getOrders(List<String> orderNos) {
        return createDealerOrderQueryResponsesWithUserInfo(dealerOrderRepository.findAllById(orderNos));
    }

    /**
     * 取得經銷商訂單清單
     */
    public List<DealerOrder> getOrdersByOrderNos(List<String> orderNos) {
        return dealerOrderRepository.findAllById(orderNos);
    }

    /**
     * 匯出經銷商訂單CSV
     *
     * @param queryRequest 查詢相關參數
     */
    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateDealerOrderCsv(DealerOrderCriteria queryRequest) {

        Map<String, Stations> stationsMap = stationService.getStationsMap();

        List<DealerOrderExcel> responses = dealerOrderRepository.findBySearch(queryRequest, null, null)
            .stream()
            .map(o -> {
                DealerOrderExcel csv = new DealerOrderExcel((DealerOrder) o[0]);

                // 站點相關資訊
                mapStationNamesToDealerUser(stationsMap, csv);

                return csv;
            }).collect(Collectors.toList());

        prepareAndFilterDealerUsersForExcel(queryRequest, responses);

        // 縣市相關資訊
        List<City> cities = goSmartServer.getCityArea();
        responses.forEach(o -> setCityArea(o, cities));

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            responses,
            new String[]{"經銷商訂單編號", "經銷商名稱", "訂單狀態", "是否新訂單", "是否通過授信",
                "是否支付保證金", "保證金支付時間", "母約編號", "期數", "車牌號碼",
                "保險ID", "訂車人姓名", "訂車人身分ID", "訂車人國碼", "訂車人電話",
                "訂車人生日", "訂車人信箱", "訂車人戶籍縣市", "訂車人戶籍區域", "訂車人戶籍地址",
                "法人共同承租人統一編號", "法人共同承租人公司抬頭", "法人共同承租人公司地址",
                "預定出車站點名稱", "預定還車站點名稱", "實際出車站點名稱", "實際還車站點名稱",
                "預定出車時間", "預定還車時間", "實際出車時間", "實際還車時間", "保證金",
                "月費租金", "里程費率(實際)", "訂閱租期", "款項明細", "訂單應收總金額",
                "訂單實收總金額", "是否取消", "取消備註", "是否同意建立格上會員"},
            true,
            ',',
            out,
            Charset.forName("big5"),
            DealerOrderExcel.class
        );
        return out;
    }

    /**
     * 匯出經銷商訂單Excel
     *
     * @param queryRequest 查詢相關參數
     */
    public byte[] generateDealerOrderExcel(DealerOrderCriteria queryRequest) {

        Map<String, Stations> stationsMap = stationService.getStationsMap();

        List<DealerOrderExcel> responses = dealerOrderRepository.findBySearch(queryRequest, null, null)
            .stream()
            .map(o -> {
                DealerOrderExcel excel = new DealerOrderExcel((DealerOrder) o[0]);

                // 站點相關資訊
                mapStationNamesToDealerUser(stationsMap, excel);

                return excel;
            }).collect(Collectors.toList());

        prepareAndFilterDealerUsersForExcel(queryRequest, responses);

        // 縣市相關資訊
        List<City> cities = goSmartServer.getCityArea();
        responses.forEach(o -> setCityArea(o, cities));

        List<Map<String, Object>> sheetData = mapDealerOrderExcelListToSheetData(responses);

        String fileName = "SUB_dealerOrder_" + DateUtils.toDateString("yyyyMMdd") + ".xlsx";

        return new ExcelUtil().builder()
            .setfileName(fileName)
            .addSheetData("匯出", DealerOrderExcelColumn.getDescriptions(), sheetData)
            .build()
            .toByte();
    }

    private List<Map<String, Object>> mapDealerOrderExcelListToSheetData(List<DealerOrderExcel> responses) {
        return responses.stream().map(dealerOrderExcel -> {
            Map<String, Object> map = new HashMap<>();
            map.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), dealerOrderExcel.getOrderNo());
            map.put(DealerOrderExcelColumn.DEALER_NAME.getDescription(), dealerOrderExcel.getDealerName());
            map.put(DealerOrderExcelColumn.SECURITY_DEPOSIT_DATE.getDescription(), formatDate(dealerOrderExcel.getSecurityDepositDate()));
            map.put(DealerOrderExcelColumn.PARENT_ORDER_NO.getDescription(), dealerOrderExcel.getParentOrderNo());
            map.put(DealerOrderExcelColumn.PREVIOUS_ORDER_NO.getDescription(), dealerOrderExcel.getPreviousOrderNo());
            map.put(DealerOrderExcelColumn.STAGE.getDescription(), dealerOrderExcel.getStage());
            map.put(DealerOrderExcelColumn.PLATE_NO.getDescription(), dealerOrderExcel.getPlateNo());
            map.put(DealerOrderExcelColumn.USER_NAME.getDescription(), dealerOrderExcel.getUserName());
            map.put(DealerOrderExcelColumn.ID_NO.getDescription(), dealerOrderExcel.getIdNo());
            map.put(DealerOrderExcelColumn.NATIONAL_CODE.getDescription(), dealerOrderExcel.getNationalCode());
            map.put(DealerOrderExcelColumn.MAIN_CELL.getDescription(), dealerOrderExcel.getMainCell());
            map.put(DealerOrderExcelColumn.BIRTHDAY.getDescription(), dealerOrderExcel.getBirthDay());
            map.put(DealerOrderExcelColumn.EMAIL.getDescription(), dealerOrderExcel.getEmail());
            map.put(DealerOrderExcelColumn.POSTAL_CODE.getDescription(), goSmartServer.getAreaCodeByCityIdAndAreaId(dealerOrderExcel.getCity(), dealerOrderExcel.getArea()));
            map.put(DealerOrderExcelColumn.ADDRESS.getDescription(), dealerOrderExcel.getAddress());
            map.put(DealerOrderExcelColumn.VAT_NUMBER.getDescription(), dealerOrderExcel.getVatNumber());
            map.put(DealerOrderExcelColumn.COMPANY_NAME.getDescription(), dealerOrderExcel.getCompanyName());
            map.put(DealerOrderExcelColumn.EXPECT_DEPART_STATION.getDescription(), dealerOrderExcel.getExpectDepartStationName());
            map.put(DealerOrderExcelColumn.EXPECT_RETURN_STATION.getDescription(), dealerOrderExcel.getExpectReturnStationName());
            map.put(DealerOrderExcelColumn.EXPECT_DEPART_DATE.getDescription(), formatDate(dealerOrderExcel.getExpectDepartDate()));
            map.put(DealerOrderExcelColumn.EXPECT_RETURN_DATE.getDescription(), formatDate(dealerOrderExcel.getExpectReturnDate()));
            map.put(DealerOrderExcelColumn.SECURITY_DEPOSIT.getDescription(), dealerOrderExcel.getSecurityDeposit());
            map.put(DealerOrderExcelColumn.MONTHLY_FEE.getDescription(), dealerOrderExcel.getMonthlyFee());
            map.put(DealerOrderExcelColumn.ACTUAL_MILEAGE_RATE.getDescription(), dealerOrderExcel.getActualMileageRate());
            map.put(DealerOrderExcelColumn.ORIGINAL_MILEAGE_RATE.getDescription(), dealerOrderExcel.getOriginalMileageRate());
            map.put(DealerOrderExcelColumn.SUBSCRIBE_MONTH.getDescription(), dealerOrderExcel.getSubscribeMonth());
            map.put(DealerOrderExcelColumn.PREPAID_MONTHS.getDescription(), dealerOrderExcel.getPrepaidMonths());
            map.put(DealerOrderExcelColumn.DEPART_STATION.getDescription(), dealerOrderExcel.getDepartStationName());
            map.put(DealerOrderExcelColumn.DEPART_DATE.getDescription(), formatDate(dealerOrderExcel.getDepartDate()));
            map.put(DealerOrderExcelColumn.BEGIN_AMT.getDescription(), dealerOrderExcel.getBeginAmt());
            map.put(DealerOrderExcelColumn.RETURN_STATION.getDescription(), dealerOrderExcel.getReturnStationName());
            map.put(DealerOrderExcelColumn.RETURN_DATE.getDescription(), formatDate(dealerOrderExcel.getReturnDate()));
            map.put(DealerOrderExcelColumn.CLOSE_AMT.getDescription(), dealerOrderExcel.getCloseAmt());
            map.put(DealerOrderExcelColumn.IS_RETURNED.getDescription(), dealerOrderExcel.getIsReturned() != null && dealerOrderExcel.getIsReturned() ? "Y" : null);
            map.put(DealerOrderExcelColumn.IS_CANCEL.getDescription(), dealerOrderExcel.getIsCancel() ? "Y" : null);
            map.put(DealerOrderExcelColumn.CANCEL_DATE.getDescription(), formatDate(dealerOrderExcel.getCancelDate()));
            map.put("錯誤訊息", dealerOrderExcel.getErrorMessages());
            return map;
        }).collect(Collectors.toList());
    }

    private void setCityArea(DealerUserCityArea dealerUser, List<City> cities) {

        if (dealerUser.getCity() != null && dealerUser.getArea() != null) {
            City city = cities.stream().filter(c -> c.getCityId() == dealerUser.getCity()).findAny().orElse(null);
            if (city != null) {
                dealerUser.setCityName(city.getCityName());
                if (city.getArea() != null) {
                    Optional.of(city.getArea()).orElse(Collections.emptyList()).stream()
                        .filter(area -> area.getAreaId() == dealerUser.getArea())
                        .findAny().map(Area::getAreaName).ifPresent(dealerUser::setAreaName);
                }
            }
        }
    }

    /**
     * 用戶資料寫入Da71
     */
    private void addUserToDA71(String orderNo, List<City> cities, DealerCustomerInfoForCreate customerInfo) {

        ContractCustomerCreateRequest request = new ContractCustomerCreateRequest(orderNo, cities, customerInfo);
        lrentalServer.createUserToDA71(request, configService.getSubscribeConfig().getSubscribeDefaultMemberId());
    }


    /**
     * 建立長租契約
     */
    public DealerOrder createLrentalContract(LrentalContractRequest request, String memberId) {
        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));
        return createLrentalContract(dealerOrder, request, memberId);
    }

    public DealerOrder createLrentalContract(DealerOrder dealerOrder, LrentalContractRequest request, String memberId) {
        DealerOrderQueryResponse dealerOrderQueryResponse = createDealerOrderQueryResponseWithUserInfo(dealerOrder);
        String lrentalContractNo = lrentalContractService.createContract(dealerOrderQueryResponse, request, memberId);
        dealerOrder.setLrentalContractNo(lrentalContractNo);
        dealerOrderQueryResponse.setLrentalContractNo(lrentalContractNo);
        insuranceService.createBatchInsurance(dealerOrderQueryResponse, request.getMemo(), Optional.ofNullable(request.getLicenseExpDate()).map(Date::toInstant).orElse(null), memberId, CARPLUS_COMPANY_CODE);
        return dealerOrderRepository.save(dealerOrder);
    }

    /**
     * 建立部門約
     */
    public void generateCenterContract(DealerOrder dealerOrder) {
        try {
            Cars cars = carsService.findByPlateNo(dealerOrder.getPlateNo());
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(cars.getPlateNo());
            // 沒有長租契約、虛擬車、有撥車編號、CRS為空、CRS的BUID不為訂閱，則不發動建立部門約
            if (StringUtils.isBlank(dealerOrder.getLrentalContractNo())
                || cars.isVirtualCar()
                || carBaseInfoSearchResponse == null
                || !Objects.equals(BuIdEnum.subscribe.getCode(), carBaseInfoSearchResponse.getBuId())) {
                log.info("不需建立部門約 - 經銷商訂單無長租契約編號: {}, 是虛擬車: {}, CRS為空: {}, CRS庫位不為訂閱: {}",
                    StringUtils.isBlank(dealerOrder.getLrentalContractNo()),
                    cars.isVirtualCar(),
                    carBaseInfoSearchResponse == null,
                    carBaseInfoSearchResponse != null && !Objects.equals(BuIdEnum.subscribe.getCode(), carBaseInfoSearchResponse.getBuId()));
                return;
            }
            ContractAddReq req = new ContractAddReq();
            req.setContractType(ContractEnum.ContractType.add_assign_carCenter);
            req.setPlateNo(cars.getPlateNo());
            req.setContractStartDate(DateUtil.transferADDateToMinguoDate(DateUtil.convertToStartOfInstant(Instant.now().plus(1, DAYS))));
            req.setUserId(configService.getSubscribeConfig().getSubscribeDefaultMemberId());
            req.setBuID(BuIdEnum.subscribe.getCode());
            req.setOrderNo(dealerOrder.getOrderNo());
            PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse = crsService.searchProjectCar(carBaseInfoSearchResponse.getCarNo());
            if (purchaseProjectCarSearchResponse != null) {
                req.validateProjectCar(purchaseProjectCarSearchResponse, dealerOrder.getExpectReturnDate());
                if (!req.getIsUseCarAgeResidualValue()) {
                    req.setContractType(ContractEnum.ContractType.add_assign_carCenter_project);
                }
            }
            String daNo = lrentalServer.addContract(req);
            dealerOrder.setLrentalContractNo(daNo);
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", dealerOrder.getOrderNo());
            map.put("errMsg", e.getMessage());
            mattermostServer.notify("訂單自動建立長租契約展期失敗", map, e);
        }
    }

    /**
     * 續約後建立長租契約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void createLrentalContractAfterRenew(String memberId, DealerOrder previousOrder, DealerOrder renewOrder) {
        try {
            if (StringUtils.isNotBlank(previousOrder.getLrentalContractNo()) && renewOrder.getOrderStatus() >= ContractStatus.CREATE.getCode()) {
                LrentalContractRequest request = new LrentalContractRequest();
                request.setOrderNo(renewOrder.getOrderNo());
                ContractSearchRep contractSearchRep = lrentalServer.getContractInfo(previousOrder.getLrentalContractNo());
                List<String> replaceCode = new ArrayList<>();
                for (char c : Optional.ofNullable(contractSearchRep).map(ContractSearchRep::getDachang).orElse("0").toCharArray()) {
                    replaceCode.add(String.valueOf(c));
                }
                request.setReplaceCodes(replaceCode);
                request.setMemo("續約");
                createLrentalContract(request, StringUtils.isBlank(memberId) ? configService.getSubscribeConfig().getSubscribeDefaultMemberId() : memberId);
            }
        } catch (Exception e) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("orderNo", renewOrder.getOrderNo());
            map.put("errMsg", e.getMessage());
            mattermostServer.notify("訂單自動建立續約合約失敗", map, e);
        }
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void changeCar(DealerOrder dealerOrder, Cars newCar, Cars oriCar, String memberId) {
        carsService.updateCrsCarNo(newCar);
        carsService.updateCrsCarNo(oriCar);

        if (StringUtils.isBlank(newCar.getPlateNo()) || Objects.equals(oriCar.getPlateNo(), newCar.getPlateNo())) {
            return;
        }

        // 檢查新車的車輛狀態和上架狀態
        AuthDealerUserResponse dealerUser = authServer.getDealerUser(dealerOrder.getDealerUserId());
        // 車輛須為空車 或 非空車但有同客戶進行中經銷商訂單
        if (!CarDefine.CarStatus.Free.getCode().equals(newCar.getCarStatus()) && hasProcessingOrdersNotAllFromSameUser(dealerUser, newCar)) {
            throwExceptionAndNotify(CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER.getMsg(), dealerOrder.getOrderNo());
        }

        // 當車號異動時，將原車號進行下架(因車可能有異味或車況有問題)，再由營業判定是否上架
        if (!oriCar.isVirtualCar() && oriCar.getCrsCarNo() != null) {
            carsService.launchClose(oriCar.getPlateNo());
            // 撥車處理
            buChange(dealerOrder, newCar, oriCar, memberId);
        }

        dealerOrder.setPlateNo(newCar.getPlateNo());
        dealerOrderRepository.saveAndFlush(dealerOrder);
        // 原車依據其他訂單狀態異動車輛狀態
        updateCarStatusBasedOnOrders(oriCar, dealerOrder.getOrderNo());
        // 新車從空車00狀態變更為鎖車20狀態，還需要將訂單編號回寫入鎖車訂單編號
        lockCarToOrder(newCar, dealerOrder.getOrderNo());
    }

    /**
     * 車籍異動，撥車處理
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void buChange(DealerOrder dealerOrder, Cars useCar, Cars oriCar, String memberId) {
        carsService.updateCrsCarNo(oriCar);
        carsService.updateCrsCarNo(useCar);
        if (oriCar.getCrsCarNo() != null && oriCar.getCrsCarNo().equals(useCar.getCrsCarNo())) {
            carsService.changeSameCar(oriCar, useCar, dealerOrder.getOrderNo());
            if (StringUtils.isNotBlank(dealerOrder.getLrentalContractNo())) {
                notifyService.notifyContractChange(dealerOrder);
            }
        } else {
            CarBaseInfoSearchResponse carBase = crsService.getCar(oriCar.getPlateNo());
            handleGenerateCenterContract(dealerOrder, oriCar, useCar, carBase);

            if (oriCar.getBuChangeMasterId() != null) {
                buChangeService.changeReturn(dealerOrder, carBase, memberId, oriCar);
                carsService.update(oriCar);
            }
            dealerOrder.setLrentalContractNo(null);
            dealerOrderRepository.save(dealerOrder);
        }
    }

    /**
     * 使用匯入的經銷商訂單 Excel row data 填入 DealerOrderExcel
     */
    public DealerOrderExcel convertRowToDealerOrderExcel(Row row, int cellOffset, Map<String, Map<City, Area>> postalMap) {
        DealerOrderExcel dealerOrderExcel = new DealerOrderExcel();
        List<String> errorMessages = new ArrayList<>();

        setCellValueToField(row, DealerOrderExcelColumn.ORDER_NO, cellOffset, errorMessages, dealerOrderExcel::setOrderNo);
        setCellValueToField(row, DealerOrderExcelColumn.DEALER_NAME, cellOffset, errorMessages, dealerOrderExcel::setDealerName);
        setCellValueToField(row, DealerOrderExcelColumn.SECURITY_DEPOSIT_DATE, cellOffset, errorMessages, dealerOrderExcel::setSecurityDepositDate);

        setCellValueToField(row, DealerOrderExcelColumn.PARENT_ORDER_NO, cellOffset, errorMessages, dealerOrderExcel::setParentOrderNo);
        setCellValueToField(row, DealerOrderExcelColumn.PREVIOUS_ORDER_NO, cellOffset, errorMessages, dealerOrderExcel::setPreviousOrderNo);
        setCellValueToField(row, DealerOrderExcelColumn.STAGE, cellOffset, errorMessages, dealerOrderExcel::setStage);
        setCellValueToField(row, DealerOrderExcelColumn.PLATE_NO, cellOffset, errorMessages, dealerOrderExcel::setPlateNo);
        setCellValueToField(row, DealerOrderExcelColumn.USER_NAME, cellOffset, errorMessages, dealerOrderExcel::setUserName);
        setCellValueToField(row, DealerOrderExcelColumn.ID_NO, cellOffset, errorMessages, dealerOrderExcel::setIdNo);
        setCellValueToField(row, DealerOrderExcelColumn.NATIONAL_CODE, cellOffset, errorMessages, dealerOrderExcel::setNationalCode);
        setCellValueToField(row, DealerOrderExcelColumn.MAIN_CELL, cellOffset, errorMessages, dealerOrderExcel::setMainCell);
        setCellValueToField(row, DealerOrderExcelColumn.BIRTHDAY, cellOffset, errorMessages, dealerOrderExcel::setBirthDay);
        setCellValueToField(row, DealerOrderExcelColumn.EMAIL, cellOffset, errorMessages, dealerOrderExcel::setEmail);

        handlePostalCode(dealerOrderExcel, row.getCell(DealerOrderExcelColumn.POSTAL_CODE.getIndex(cellOffset)), errorMessages, postalMap);

        setCellValueToField(row, DealerOrderExcelColumn.ADDRESS, cellOffset, errorMessages, dealerOrderExcel::setAddress);
        setCellValueToField(row, DealerOrderExcelColumn.VAT_NUMBER, cellOffset, errorMessages, dealerOrderExcel::setVatNumber);
        setCellValueToField(row, DealerOrderExcelColumn.COMPANY_NAME, cellOffset, errorMessages, dealerOrderExcel::setCompanyName);
        setCellValueToField(row, DealerOrderExcelColumn.EXPECT_DEPART_STATION, cellOffset, errorMessages, dealerOrderExcel::setExpectDepartStationName);
        setCellValueToField(row, DealerOrderExcelColumn.EXPECT_RETURN_STATION, cellOffset, errorMessages, dealerOrderExcel::setExpectReturnStationName);
        setCellValueToField(row, DealerOrderExcelColumn.EXPECT_DEPART_DATE, cellOffset, errorMessages, dealerOrderExcel::setExpectDepartDate);
        setCellValueToField(row, DealerOrderExcelColumn.EXPECT_RETURN_DATE, cellOffset, errorMessages, dealerOrderExcel::setExpectReturnDate);
        setCellValueToField(row, DealerOrderExcelColumn.SECURITY_DEPOSIT, cellOffset, errorMessages, dealerOrderExcel::setSecurityDeposit);
        setCellValueToField(row, DealerOrderExcelColumn.MONTHLY_FEE, cellOffset, errorMessages, dealerOrderExcel::setMonthlyFee);
        setCellValueToField(row, DealerOrderExcelColumn.ACTUAL_MILEAGE_RATE, cellOffset, errorMessages, dealerOrderExcel::setActualMileageRate);
        setCellValueToField(row, DealerOrderExcelColumn.ORIGINAL_MILEAGE_RATE, cellOffset, errorMessages, dealerOrderExcel::setOriginalMileageRate);
        setCellValueToField(row, DealerOrderExcelColumn.SUBSCRIBE_MONTH, cellOffset, errorMessages, dealerOrderExcel::setSubscribeMonth);
        setCellValueToField(row, DealerOrderExcelColumn.PREPAID_MONTHS, cellOffset, errorMessages, dealerOrderExcel::setPrepaidMonths);
        setCellValueToField(row, DealerOrderExcelColumn.DEPART_STATION, cellOffset, errorMessages, dealerOrderExcel::setDepartStationName);
        setCellValueToField(row, DealerOrderExcelColumn.DEPART_DATE, cellOffset, errorMessages, dealerOrderExcel::setDepartDate);
        setCellValueToField(row, DealerOrderExcelColumn.BEGIN_AMT, cellOffset, errorMessages, dealerOrderExcel::setBeginAmt);
        setCellValueToField(row, DealerOrderExcelColumn.RETURN_STATION, cellOffset, errorMessages, dealerOrderExcel::setReturnStationName);
        setCellValueToField(row, DealerOrderExcelColumn.RETURN_DATE, cellOffset, errorMessages, dealerOrderExcel::setReturnDate);
        setCellValueToField(row, DealerOrderExcelColumn.CLOSE_AMT, cellOffset, errorMessages, dealerOrderExcel::setCloseAmt);
        setCellValueToField(row, DealerOrderExcelColumn.IS_RETURNED, cellOffset, errorMessages, dealerOrderExcel::setIsReturned);
        setCellValueToField(row, DealerOrderExcelColumn.IS_CANCEL, cellOffset, errorMessages, dealerOrderExcel::setIsCancel);
        setCellValueToField(row, DealerOrderExcelColumn.CANCEL_DATE, cellOffset, errorMessages, dealerOrderExcel::setCancelDate);

        if (!errorMessages.isEmpty()) {
            dealerOrderExcel.setErrorMessages(String.join(";", errorMessages));
        }

        return dealerOrderExcel;
    }

    private <T> void setCellValueToField(Row row, DealerOrderExcelColumn column, int cellOffset,
                                         @NonNull List<String> errorMessages, Consumer<T> setter) {
        T value = null;
        try {
            Cell cell = row.getCell(column.getIndex(cellOffset));
            T cellValue = ExcelUtil.getCellValue(cell, (Class<T>) column.getTargetType());
            // 額外檢查訂單編號須必填和時間欄位格式
            if (cellValue == null && column == DealerOrderExcelColumn.ORDER_NO) {
                errorMessages.add(DEALER_ORDER_NO_REQUIRED.getMsg());
            }
            if (Date.class == column.getTargetType() && !ExcelUtil.isCellNullOrBlank(cell)
                && (CellType.STRING != cell.getCellType() || !cell.getStringCellValue().matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"))) {
                errorMessages.add(column.getDescription() + DEALER_ORDER_SHEET_DATE_FORMAT_ERROR.getMsg());
            }
            value = cellValue;
        } catch (Exception e) {
            errorMessages.add(column.getDescription() + e.getMessage());
        }
        if (value != null) {
            setter.accept(value);
        }
    }

    private void handlePostalCode(DealerOrderExcel dealerOrderExcel, Cell cell, List<String> errorMessages, Map<String, Map<City, Area>> postalMap) {
        if (!isCellNullOrBlank(cell)) {
            String postalCode;
            try {
                postalCode = ExcelUtil.getCellValue(cell, String.class);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
            Map<City, Area> cityAreaMap = postalMap.get(postalCode);
            if (MapUtils.isNotEmpty(cityAreaMap)) {
                cityAreaMap.keySet().stream().findFirst().ifPresent(city -> dealerOrderExcel.setCity(city.getCityId()));
                cityAreaMap.values().stream().findFirst().ifPresent(area -> dealerOrderExcel.setArea(area.getAreaId()));
            } else {
                errorMessages.add(DEALER_USER_POSTAL_CODE_ERROR.getMsg());
            }
        }
    }

    @Get(group = DealerOrderValidate.class, key = "#uuid", ttl = 60 * 60 * 24)
    @Transactional(transactionManager = "mysqlTransactionManager")
    public DealerOrderValidate validateDealerOrderWorkbookAndSave(Workbook workbook, String uuid, String headerMemberId) {

        List<String> existingOrderNosInExcel = new ArrayList<>();
        List<DealerOrderExcel> successOrders = new ArrayList<>();
        List<DealerOrderExcel> errorOrders = new ArrayList<>();
        List<DealerOrderValidateError> errorList = new ArrayList<>();

        Sheet sheet = workbook.getSheetAt(0); // 取第一個工作頁分表
        int cellOffset = ExcelUtil.calculateCellOffset(sheet);

        Map<String, String> stationsNameCodeMap = stationService.getStationsNameCodeMap();
        Map<String, Map<City, Area>> postalMap = goSmartServer.getCityAreaMapByPostalCode();

        // 取得匯入經銷商訂單 Excel 所有訂單編號
        List<String> importedOrderNos = new ArrayList<>();
        // 取得匯入經銷商訂單 Excel 所有身分證號
        List<String> importedIdNos = new ArrayList<>();
        // 取得匯入經銷商訂單 Excel 所有車牌號碼
        Set<String> importedPlateNos = new HashSet<>();

        // 預設跳過標題列
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            try {
                Optional.ofNullable(ExcelUtil.getCellValue(row.getCell(DealerOrderExcelColumn.ORDER_NO.getIndex(cellOffset)), String.class)).ifPresent(importedOrderNos::add);
                Optional.ofNullable(ExcelUtil.getCellValue(row.getCell(DealerOrderExcelColumn.ID_NO.getIndex(cellOffset)), String.class)).ifPresent(importedIdNos::add);
                Optional.ofNullable(ExcelUtil.getCellValue(row.getCell(DealerOrderExcelColumn.PLATE_NO.getIndex(cellOffset)), String.class))
                    .filter(StringUtils::isNotBlank)
                    .ifPresent(importedPlateNos::add);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }

        // 獲取已存在的訂單資料
        Map<String, DealerOrder> dbDealerOrderMap = dealerOrderRepository.findAllById(importedOrderNos).stream()
            .collect(Collectors.toMap(DealerOrder::getOrderNo, Function.identity()));

        // 獲取用戶資料
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        List<Integer> allDealerUserIds = dealerOrderRepository.getAllDealerUserIds();
        query.setIds(allDealerUserIds);
        query.setIdNo(importedIdNos);
        List<AuthDealerUserResponse> authDealerUsers = authServer.getDealerUsers(query);

        Map<Long, AuthDealerUserResponse> dealerUserById = new HashMap<>();
        Map<String, AuthDealerUserResponse> dealerUserByIdNo = new HashMap<>();
        authDealerUsers.forEach(user -> {
            dealerUserById.put(user.getId(), user);
            dealerUserByIdNo.put(user.getIdNo(), user);
        });

        // 獲取車輛資料
        Map<String, Cars> carsMap = carsService.findByPlateNoList(new ArrayList<>(importedPlateNos)).stream()
            .collect(Collectors.toMap(Cars::getPlateNo, Function.identity()));

        // 預設跳過標題列
        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (ExcelUtil.isEntireRowEmpty(row, cellOffset)) {
                continue;
            }

            List<String> errorMessages = new ArrayList<>();
            DealerOrderExcel dealerOrderExcel = convertRowToDealerOrderExcel(row, cellOffset, postalMap);
            if (StringUtils.isNotBlank(dealerOrderExcel.getErrorMessages())) {
                errorMessages.add(dealerOrderExcel.getErrorMessages());
            }

            // 檢查訂單編號在 Excel 是否重複
            if (existingOrderNosInExcel.contains(dealerOrderExcel.getOrderNo())) {
                errorMessages.add(DEALER_ORDER_DUPLICATE.getMsg());
            }

            // 獲取 DB 中的訂單 by 訂單編號
            DealerOrder dbDealerOrder = dbDealerOrderMap.get(dealerOrderExcel.getOrderNo());

            // 使用策略工廠獲取適合的處理策略
            DealerOrderExcelProcessStrategy strategy = dealerOrderExcelProcessStrategyFactory.getStrategy(dbDealerOrder, dealerOrderExcel);

            if (strategy != null && errorMessages.isEmpty()) {
                strategy.process(new DealerOrderExcelProcessContext(
                    dealerOrderExcel,
                    dbDealerOrder,
                    errorMessages,
                    stationsNameCodeMap,
                    dealerUserById,
                    dealerUserByIdNo,
                    carsMap.get(dealerOrderExcel.getPlateNo()),
                    successOrders,
                    existingOrderNosInExcel,
                    headerMemberId
                ));
            } else if (dbDealerOrder != null && strategy == null) {
                // 策略未找到但訂單存在，可能是訂單狀態不支持當前操作
                errorMessages.add(DEALER_ORDER_STATUS_ERROR.getMsg());
            }

            // 若有錯誤訊息，添加到錯誤列表
            if (!errorMessages.isEmpty()) {
                errorList.add(DealerOrderValidateError.builder()
                    .orderNo(dealerOrderExcel.getOrderNo())
                    .errorMessage(errorMessages)
                    .build());
                dealerOrderExcel.setErrorMessages(String.join(";", errorMessages));
                errorOrders.add(dealerOrderExcel);
            }
        }

        return DealerOrderValidate.builder()
            .rows(successOrders)
            .errorRows(errorOrders)
            .errorList(errorList)
            .build();
    }

    /**
     * 驗證預定出車、還車站點是否存在
     */
    public void validateExpectDepartReturnStationFields(DealerOrderExcel dealerOrderExcel, List<String> errorMessages, Map<String, String> stationsNameCodeMap) {
        String expectDepartStationName = dealerOrderExcel.getExpectDepartStationName();
        String expectReturnStationName = dealerOrderExcel.getExpectReturnStationName();
        if (expectDepartStationName != null && !stationsNameCodeMap.containsKey(expectDepartStationName)) {
            errorMessages.add(DEALER_EXPECT_DEPART_STATION_NOT_EXIST.getMsg());
        }
        if (expectReturnStationName != null && !stationsNameCodeMap.containsKey(expectReturnStationName)) {
            errorMessages.add(DEALER_EXPECT_RETURN_STATION_NOT_EXIST.getMsg());
        }
    }

    public void validateDepartDealerOrderExcelFields(DealerOrderExcel dealerOrderExcel, DealerOrder dbDealerOrder, Cars car, List<String> errorMessages, Map<String, String> stationsNameCodeMap) {
        if (StringUtils.isBlank(dealerOrderExcel.getDepartStationName())
            || dealerOrderExcel.getDepartDate() == null || dealerOrderExcel.getBeginAmt() == null) {
            errorMessages.add(String.format("出車須%s、%s、%s皆為必填",
                DealerOrderExcelColumn.DEPART_STATION.getDescription(),
                DealerOrderExcelColumn.DEPART_DATE.getDescription(),
                DealerOrderExcelColumn.BEGIN_AMT.getDescription()));
        }
        if (!stationsNameCodeMap.containsKey(dealerOrderExcel.getDepartStationName())) {
            errorMessages.add(DEALER_DEPART_STATION_NOT_EXIST.getMsg());
        }
        // 檢查車牌是否為 SEALAND 虛擬車號
        if (SEALAND_VIRTUAL_PLATE_NO.equals(dealerOrderExcel.getPlateNo())) {
            errorMessages.add(DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART.getMsg());
        }
        DealerOrder dealerOrder = Optional.ofNullable(dbDealerOrder)
            .orElse(dealerOrderRepository.findById(dealerOrderExcel.getOrderNo()).orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND)));
        Cars carToDepart = car != null && StringUtils.isBlank(dealerOrderExcel.getPlateNo())
            ? car
            : Optional.ofNullable(carsService.findByPlateNo(Optional.ofNullable(dealerOrderExcel.getPlateNo()).orElse(dealerOrder.getPlateNo())))
            .orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));
        // 檢查是否為 SeaLand 虛擬車牌
        if (SEALAND_VIRTUAL_PLATE_NO.equals(carToDepart.getPlateNo())) {
            errorMessages.add(DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART.getMsg());
        }
        // validation for changeCar
        if (dealerOrderExcel.getPlateNo() != null && !dealerOrderExcel.getPlateNo().equals(dealerOrder.getPlateNo())) {
            AuthDealerUserResponse dealerUser = authServer.getDealerUser(dealerOrder.getDealerUserId());
            // 車輛須為空車 或 有進行中經銷商訂單但同客戶
            if (!CarDefine.CarStatus.Free.getCode().equals(carToDepart.getCarStatus()) && hasProcessingOrdersNotAllFromSameUser(dealerUser, carToDepart)) {
                errorMessages.add(CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER.getMsg());
            }
        }
        try {
            validateCarBuId(carToDepart.getPlateNo(), dealerOrder);
        } catch (SubscribeException e) {
            errorMessages.add(e.getReason());
        }
    }

    public CarValidationResult validateAndRetrieveCar(String plateNo, AuthDealerUserResponse dealerUser, List<String> errorMessages) {
        // 檢查車輛資訊
        Cars cars = carsService.findByPlateNo(plateNo);
        // 若車輛不存在訂閱
        if (cars == null) {
            // 檢查 CRS 是否存在該車號
            CarBaseInfoSearchResponse carBaseInfo = crsService.getCar(plateNo);
            if (carBaseInfo != null) {
                // 存在於 CRS - 新增車籍
                carsService.addCarByCrs(carBaseInfo);
            } else {
                // 不存在於 CRS - 關聯指定 SeaLand 虛擬車號
                plateNo = SEALAND_VIRTUAL_PLATE_NO;
            }
            return new CarValidationResult(plateNo, errorMessages);
        }
        // 若 車輛存在 -> 車輛須為空車 或 有進行中經銷商訂單但同客戶
        if (!CarDefine.CarStatus.Free.getCode().equals(cars.getCarStatus()) && hasProcessingOrdersNotAllFromSameUser(dealerUser, cars)) {
            errorMessages.add(CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER.getMsg());
        }
        return new CarValidationResult(plateNo, errorMessages);
    }

    /**
     * 檢查指定車輛是否有其他使用者的進行中經銷商訂單
     * 此方法用於確認車輛是否可以被新的使用者使用。當車輛有進行中經銷商訂單時,
     * 需確認這些經銷商訂單是否都屬於同一位使用者,以避免車輛重複被預訂。
     *
     * @param dealerUser 經銷商使用者資訊，若為 null 表示新客戶
     * @param cars       欲檢查的車輛資訊
     * @return true:
     *         - 車輛有進行中經銷商訂單 且
     *         - 這些經銷商訂單不是全部來自同一個使用者
     *         false:
     *         - 車輛沒有進行中經銷商訂單 或
     *         - 車輛的所有進行中經銷商訂單都來自同一個使用者
     */
    public boolean hasProcessingOrdersNotAllFromSameUser(AuthDealerUserResponse dealerUser, Cars cars) {
        // 獲取指定車牌號碼的所有處理中訂單
        List<DealerOrderQueryResponse> processingOrders = getProcessingDealerOrdersByPlateNo(cars.getPlateNo());

        // 如果沒有訂單，條件無法成立
        if (processingOrders.isEmpty()) {
            return false;
        }

        // 如果 dealerUser 為 null(新客戶)，任何現有訂單必然來自其他客戶
        if (dealerUser == null) {
            return true;
        }

        // 檢查是否每個訂單都屬於同一個客戶
        boolean allOrdersFromSameUser = processingOrders.stream()
            .allMatch(order -> order.getDealerUserId().equals(dealerUser.getId()));

        // 如果有訂單且並非全都來自同一個客戶，返回 true
        return !allOrdersFromSameUser;
    }

    public ExcelUtil.SheetData generateErrorDealerOrderExport(DealerOrderValidate dealerOrderValidate) {

        List<DealerOrderExcel> errorRows = dealerOrderValidate.getErrorRows();
        if (CollectionUtils.isEmpty(errorRows)) {
            throw new BadRequestException("沒有錯誤資料可供下載");
        }

        List<String> headers = new ArrayList<>(DealerOrderExcelColumn.getDescriptions().size() + 1);
        headers.addAll(DealerOrderExcelColumn.getDescriptions());
        headers.add("錯誤訊息");

        List<Map<String, Object>> sheetData = mapDealerOrderExcelListToSheetData(errorRows);

        return new ExcelUtil.SheetData("error", headers, sheetData);
    }

    private String formatDate(Date date) {
        return Optional.ofNullable(date).map(d -> DateUtils.toDateString(d, "yyyy-MM-dd HH:mm:ss")).orElse(null);
    }

    // 返回 List<DealerOrder> 符合以下提醒投保條件 orderStatus = 0(create) & expectDepartDate = 3天後 & lrentalContractNo is null & 車輛庫位 ≠ 長租 (CRS)
    public List<String> getDealerOrderNoForRemindInsurance() {
        List<DealerOrder> dealerOrders = dealerOrderRepository.getDealerOrderForRemindInsurance();
        // 另須符合車輛庫位不等於長租(CRS)
        Map<String, CarBaseInfoQueryResponse> cars = crsService.getCarBaseInfoQueryResponses(dealerOrders.stream().map(DealerOrder::getPlateNo).collect(Collectors.toList()));
        return dealerOrders.stream()
            .filter(dealerOrder -> !Objects.equals(cars.get(dealerOrder.getPlateNo()).getBuId(), BuIdEnum.lRental.getCode()))
            .map(DealerOrder::getOrderNo)
            .collect(Collectors.toList());
    }

    // 返回 List<String> 符合以下出車異常條件 orderStatus = 1(going) & lrentalContractNo is null
    public List<String> getDealerOrderNoForDepartAbnormal() {
        List<DealerOrder> dealerOrders = dealerOrderRepository.getDealerOrderNoForDepartAbnormal();
        // 另須符合車輛庫位不等於長租(CRS)
        Map<String, CarBaseInfoQueryResponse> cars = crsService.getCarBaseInfoQueryResponses(dealerOrders.stream().map(DealerOrder::getPlateNo).collect(Collectors.toList()));
        return dealerOrders.stream()
            .filter(dealerOrder -> {
                CarBaseInfoQueryResponse car = cars.get(dealerOrder.getPlateNo());
                return car != null && !Objects.equals(car.getBuId(), BuIdEnum.lRental.getCode());
            })
            .map(DealerOrder::getOrderNo)
            .collect(Collectors.toList());
    }

    private List<String> getSpecHeads() {
        List<String> headers = new ArrayList<>(DealerOrderExcelColumn.getDescriptions().size() + 1);
        headers.add("標題");
        headers.addAll(DealerOrderExcelColumn.getDescriptions());
        return Collections.unmodifiableList(headers);
    }

    private List<Map<String, Object>> getSpecRows() {
        List<Map<String, Object>> specRows = new ArrayList<>();
        Map<String, Object> specExampleRow = new LinkedHashMap<>();
        specExampleRow.put("標題", "範例值");
        fillCreateDealerOrderData(specExampleRow);
        fillDepartDealerOrderData(specExampleRow);
        fillCloseDealerOrderData(specExampleRow);
        fillReturnDealerOrderData(specExampleRow);
        fillCancelDealerOrderData(specExampleRow);
        specRows.add(specExampleRow);
        specRows.add(new HashMap<String, Object>() {
            {
                put(DealerOrderExcelColumn.DEALER_NAME.getDescription(), "SR");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "共通必填");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "建單必填");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "起租必填");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "迄租必填");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "還車必填");
            }
        });
        specRows.add(new HashMap<String, Object>() {
            {
                put("標題", "取消必填");
            }
        });

        return specRows;
    }

    private List<Map<String, Object>> getExampleRows() {
        List<Map<String, Object>> exampleRows = new ArrayList<>();
        Map<String, Object> createDealerOrderRow = new LinkedHashMap<>();
        fillCreateDealerOrderData(createDealerOrderRow);
        Map<String, Object> departDealerOrderRow = new LinkedHashMap<>();
        departDealerOrderRow.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), "24063012346");
        fillDepartDealerOrderData(departDealerOrderRow);
        Map<String, Object> closeDealerOrderRow = new LinkedHashMap<>();
        closeDealerOrderRow.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), "24063012347");
        fillCloseDealerOrderData(closeDealerOrderRow);
        Map<String, Object> returnDealerOrderRow = new LinkedHashMap<>();
        returnDealerOrderRow.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), "24063012348");
        fillReturnDealerOrderData(returnDealerOrderRow);
        Map<String, Object> cancelDealerOrderRow = new LinkedHashMap<>();
        cancelDealerOrderRow.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), "24063012349");
        fillCancelDealerOrderData(cancelDealerOrderRow);
        exampleRows.add(createDealerOrderRow);
        exampleRows.add(departDealerOrderRow);
        exampleRows.add(closeDealerOrderRow);
        exampleRows.add(returnDealerOrderRow);
        exampleRows.add(cancelDealerOrderRow);

        return exampleRows;
    }

    private List<Map<String, Object>> getStationRows() {
        List<Map<String, Object>> stationRows = new ArrayList<>();

        for (DealerStation station : DealerStation.values()) {
            Map<String, Object> stationMap = new LinkedHashMap<>();
            stationMap.put("ID", station.getCode());
            stationMap.put("Name", station.getName());
            stationRows.add(stationMap);
        }

        return stationRows;
    }

    private void fillCreateDealerOrderData(Map<String, Object> data) {
        data.put(DealerOrderExcelColumn.ORDER_NO.getDescription(), "24063012345");
        data.put(DealerOrderExcelColumn.DEALER_NAME.getDescription(), "SEALAND");
        data.put(DealerOrderExcelColumn.SECURITY_DEPOSIT_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
        data.put(DealerOrderExcelColumn.PARENT_ORDER_NO.getDescription(), "24063012345");
        data.put(DealerOrderExcelColumn.PREVIOUS_ORDER_NO.getDescription(), "24063012346");
        data.put(DealerOrderExcelColumn.STAGE.getDescription(), "2-3");
        data.put(DealerOrderExcelColumn.PLATE_NO.getDescription(), "ABC-1234");
        data.put(DealerOrderExcelColumn.USER_NAME.getDescription(), "王曉明");
        data.put(DealerOrderExcelColumn.ID_NO.getDescription(), "A123456789");
        data.put(DealerOrderExcelColumn.NATIONAL_CODE.getDescription(), "886");
        data.put(DealerOrderExcelColumn.MAIN_CELL.getDescription(), "0927225123");
        data.put(DealerOrderExcelColumn.BIRTHDAY.getDescription(), "yyyy-mm-dd");
        data.put(DealerOrderExcelColumn.EMAIL.getDescription(), "<EMAIL>");
        data.put(DealerOrderExcelColumn.POSTAL_CODE.getDescription(), "123");
        data.put(DealerOrderExcelColumn.ADDRESS.getDescription(), "XX市XX區XX路XX段XX號");
        data.put(DealerOrderExcelColumn.VAT_NUMBER.getDescription(), "12345678");
        data.put(DealerOrderExcelColumn.COMPANY_NAME.getDescription(), "XX有限公司");
        data.put(DealerOrderExcelColumn.EXPECT_DEPART_STATION.getDescription(), DealerStation.XITUN_USED_CAR.getName());
        data.put(DealerOrderExcelColumn.EXPECT_RETURN_STATION.getDescription(), DealerStation.XITUN_USED_CAR.getName());
        data.put(DealerOrderExcelColumn.EXPECT_DEPART_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
        data.put(DealerOrderExcelColumn.EXPECT_RETURN_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
        data.put(DealerOrderExcelColumn.SECURITY_DEPOSIT.getDescription(), 10000);
        data.put(DealerOrderExcelColumn.MONTHLY_FEE.getDescription(), 16800);
        data.put(DealerOrderExcelColumn.ACTUAL_MILEAGE_RATE.getDescription(), 7);
        data.put(DealerOrderExcelColumn.ORIGINAL_MILEAGE_RATE.getDescription(), 14);
        data.put(DealerOrderExcelColumn.SUBSCRIBE_MONTH.getDescription(), 3);
        data.put(DealerOrderExcelColumn.PREPAID_MONTHS.getDescription(), 3);
    }

    private void fillDepartDealerOrderData(Map<String, Object> data) {
        data.put(DealerOrderExcelColumn.DEPART_STATION.getDescription(), DealerStation.XITUN_USED_CAR.getName());
        data.put(DealerOrderExcelColumn.DEPART_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
        data.put(DealerOrderExcelColumn.BEGIN_AMT.getDescription(), 12345);
    }

    private void fillCloseDealerOrderData(Map<String, Object> data) {
        data.put(DealerOrderExcelColumn.RETURN_STATION.getDescription(), DealerStation.XITUN_USED_CAR.getName());
        data.put(DealerOrderExcelColumn.RETURN_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
        data.put(DealerOrderExcelColumn.CLOSE_AMT.getDescription(), 12345);
    }

    private void fillReturnDealerOrderData(Map<String, Object> data) {
        data.put(DealerOrderExcelColumn.IS_RETURNED.getDescription(), "Y");
    }

    private void fillCancelDealerOrderData(Map<String, Object> data) {
        data.put(DealerOrderExcelColumn.IS_CANCEL.getDescription(), "Y");
        data.put(DealerOrderExcelColumn.CANCEL_DATE.getDescription(), "yyyy-mm-dd HH:MM:SS");
    }

    public byte[] generateImportedXlsxExample() {

        return new ExcelUtil().builder()
            .setfileName("匯入Sealand訂單資料_範例檔.xlsx")
            .addSheetData("說明", getSpecHeads(), getSpecRows())
            .setForegroundColor(0, new int[][]{{0, 0}, {1, 0}}, IndexedColors.GREY_25_PERCENT)
            .setForegroundColor(0, new int[][]{{3, 0}, {0, DealerOrderExcelColumn.ORDER_NO.getExampleIndex()}}, IndexedColors.PALE_BLUE)
            .setForegroundColor(0, new int[][]{{4, 0}, {0, DealerOrderExcelColumn.DEALER_NAME.getExampleIndex()},
                {0, DealerOrderExcelColumn.SECURITY_DEPOSIT_DATE.getExampleIndex()},
                {0, DealerOrderExcelColumn.PARENT_ORDER_NO.getExampleIndex()},
                {0, DealerOrderExcelColumn.STAGE.getExampleIndex()},
                {0, DealerOrderExcelColumn.PLATE_NO.getExampleIndex()},
                {0, DealerOrderExcelColumn.USER_NAME.getExampleIndex()},
                {0, DealerOrderExcelColumn.ID_NO.getExampleIndex()},
                {0, DealerOrderExcelColumn.NATIONAL_CODE.getExampleIndex()},
                {0, DealerOrderExcelColumn.MAIN_CELL.getExampleIndex()},
                {0, DealerOrderExcelColumn.BIRTHDAY.getExampleIndex()},
                {0, DealerOrderExcelColumn.EMAIL.getExampleIndex()},
                {0, DealerOrderExcelColumn.POSTAL_CODE.getExampleIndex()},
                {0, DealerOrderExcelColumn.ADDRESS.getExampleIndex()},
                {0, DealerOrderExcelColumn.EXPECT_DEPART_STATION.getExampleIndex()},
                {0, DealerOrderExcelColumn.EXPECT_RETURN_STATION.getExampleIndex()},
                {0, DealerOrderExcelColumn.EXPECT_DEPART_DATE.getExampleIndex()},
                {0, DealerOrderExcelColumn.EXPECT_RETURN_DATE.getExampleIndex()},
                {0, DealerOrderExcelColumn.SECURITY_DEPOSIT.getExampleIndex()},
                {0, DealerOrderExcelColumn.MONTHLY_FEE.getExampleIndex()},
                {0, DealerOrderExcelColumn.ACTUAL_MILEAGE_RATE.getExampleIndex()},
                {0, DealerOrderExcelColumn.SUBSCRIBE_MONTH.getExampleIndex()}}, IndexedColors.CORAL)
            .setForegroundColor(0, new int[][]{{5, 0}, {0, DealerOrderExcelColumn.DEPART_STATION.getExampleIndex()},
                {0, DealerOrderExcelColumn.DEPART_DATE.getExampleIndex()}, {0, DealerOrderExcelColumn.BEGIN_AMT.getExampleIndex()}}, IndexedColors.LIGHT_GREEN)
            .setForegroundColor(0, new int[][]{{6, 0}, {7, 0}, {0, DealerOrderExcelColumn.RETURN_STATION.getExampleIndex()},
                {0, DealerOrderExcelColumn.RETURN_DATE.getExampleIndex()}, {0, DealerOrderExcelColumn.CLOSE_AMT.getExampleIndex()},
                {0, DealerOrderExcelColumn.IS_RETURNED.getExampleIndex()}}, IndexedColors.ROSE)
            .setBorders(0, new int[][]{{6, 0}, {0, DealerOrderExcelColumn.RETURN_STATION.getExampleIndex()},
                {0, DealerOrderExcelColumn.RETURN_DATE.getExampleIndex()},
                {0, DealerOrderExcelColumn.CLOSE_AMT.getExampleIndex()}}, BorderStyle.MEDIUM, IndexedColors.VIOLET)
            .setForegroundColor(0, new int[][]{{8, 0}, {0, DealerOrderExcelColumn.IS_CANCEL.getExampleIndex()},
                {0, DealerOrderExcelColumn.CANCEL_DATE.getExampleIndex()}}, IndexedColors.RED)
            .addComment(0, new int[]{0, DealerOrderExcelColumn.PREVIOUS_ORDER_NO.getExampleIndex()}, "需要輸入期數為1-1的訂單")
            .addComment(0, new int[]{0, DealerOrderExcelColumn.STAGE.getExampleIndex()}, "需要輸入該訂單的前一筆訂單，以利續約建立契約時反查原始契約")
            .addComment(0, new int[]{0, DealerOrderExcelColumn.EXPECT_RETURN_STATION.getExampleIndex()}, "預定/實際出還車站點，皆需輸入站點ID，對照表可查分頁3")
            .addSheetData("範例", new ArrayList<>(getSpecHeads().subList(1, getSpecHeads().size())), getExampleRows())
            .addSheetData("站點ID對照表", Arrays.asList("ID", "Name"), getStationRows())
            .build()
            .toByte();
    }

    public List<DealerOrderQueryResponse> getProcessingDealerOrdersByPlateNosExcludingCurrentOrder(String plateNo, String orderNo) {
        return getProcessingDealerOrdersByPlateNos(Collections.singletonList(plateNo)).stream()
            .filter(dealerOrder -> !dealerOrder.getOrderNo().equals(orderNo))
            .collect(Collectors.toList());
    }

    public List<DealerOrderQueryResponse> getProcessingDealerOrdersByPlateNos(List<String> plateNos) {
        return getDealerOrdersByPlateNosAndStatus(plateNos, Arrays.asList(ContractStatus.CREATE, ContractStatus.GOING));
    }

    public List<DealerOrderQueryResponse> getProcessingDealerOrdersByPlateNo(String plateNo) {
        return getProcessingDealerOrdersByPlateNos(Collections.singletonList(plateNo));
    }

    public List<DealerOrderQueryResponse> getDealerOrdersByPlateNosAndStatus(List<String> plateNos, List<ContractStatus> statusList) {
        if (plateNos == null || plateNos.isEmpty()) {
            return new ArrayList<>();
        }
        DealerOrderCriteria dealerOrderCriteria = new DealerOrderCriteria();
        dealerOrderCriteria.setOrderStatus(statusList.stream().map(ContractStatus::getCode).collect(Collectors.toList()));
        dealerOrderCriteria.setPlateNo(plateNos);
        List<DealerOrderQueryResponse> responses = dealerOrderRepository.findBySearch(dealerOrderCriteria, Integer.MAX_VALUE, 0)
            .stream().map(o -> new DealerOrderQueryResponse((DealerOrder) o[0]))
            .collect(Collectors.toList());

        // 設定經銷商客戶資訊
        setDealerUserInfo(responses.stream().map(DealerOrderQueryResponse::getCustomerInfo).collect(Collectors.toList()));
        return responses;
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public void handleGenerateCenterContract(DealerOrder dealerOrder, @NonNull Cars oriCar, @Nullable Cars newCar, CarBaseInfoSearchResponse carBase) {
        if (carBase == null || !BuIdEnum.subscribe.getCode().equals(carBase.getBuId())) {
            return;
        }
        // 只要車輛庫位為訂閱，當車籍狀態為空車或非空車但有其他進行中訂單皆無長租契約編號
        if (shouldGenerateCenterContract(dealerOrder, oriCar)) {
            generateCenterContract(dealerOrder);
            notifyService.notifyContractCancel(dealerOrder, oriCar.getPlateNo(),
                Optional.ofNullable(newCar).map(Cars::getPlateNo).orElse(oriCar.getPlateNo()), dealerOrder.getLrentalContractNo());
        }
    }

    /**
     * 判斷是否需要建立部門約
     * 當符合以下條件時需建立:
     * 1. 車籍狀態為空車 或
     * 2. 所有進行中訂單皆無長租契約編號且當前訂單有長租契約編號
     */
    private boolean shouldGenerateCenterContract(DealerOrder dealerOrder, Cars cars) {
        // 如果車籍狀態為空車則建立部門約
        if (CarDefine.CarStatus.Free.getCode().equals(cars.getCarStatus())) {
            return true;
        }

        String plateNo = cars.getPlateNo();
        // 檢查其他進行中訂單是否皆無長租契約編號
        boolean noOtherOrdersHaveContract = orderService.getProcessingOrdersByPlateNo(plateNo)
            .stream()
            .allMatch(order -> StringUtils.isBlank(order.getLrentalContractNo()));

        boolean noOtherDealerOrdersHaveContract = getProcessingDealerOrdersByPlateNosExcludingCurrentOrder(plateNo, dealerOrder.getOrderNo())
            .stream()
            .allMatch(order -> StringUtils.isBlank(order.getLrentalContractNo()));

        // 當前訂單必須有長租契約編號
        boolean currentOrderHasContract = StringUtils.isNotBlank(dealerOrder.getLrentalContractNo());

        boolean shouldGenerate = noOtherOrdersHaveContract && noOtherDealerOrdersHaveContract && currentOrderHasContract;
        if (!shouldGenerate) {
            log.info("不滿足建立部門約條件 - 其他進行中格上訂單皆無長租契約編號: {}, 其他進行中經銷商訂單皆無長租契約編號: {}, 當前經銷商訂單有長租契約編號: {}",
                noOtherOrdersHaveContract, noOtherDealerOrdersHaveContract, currentOrderHasContract);
        }
        return shouldGenerate;
    }

    /**
     * 異動經銷商訂單客戶與長租契約編號
     */
    public DealerOrder updateUserIdAndLrentalContractNo(DealerOrderUpdateUserAndLrentalContractNoRequest request) {

        DealerOrder dealerOrder = dealerOrderRepository.findById(request.getOrderNo())
            .orElseThrow(() -> new SubscribeException(DEALER_ORDER_NOT_FOUND));

        // 驗證新的 dealerUserId 是否存在
        AuthDealerUserResponse dealerUser = authServer.getDealerUser(request.getDealerUserId());
        if (dealerUser == null) {
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }

        dealerOrder.setDealerUserId(request.getDealerUserId());

        // 驗證指定 lrentalContractNo 是否存在，否則拋出錯誤
        if (StringUtils.isNotBlank(request.getLrentalContractNo())) {
            Optional.ofNullable(lrentalServer.getContractInfo(request.getLrentalContractNo()))
                .orElseThrow(() -> new SubscribeException(LRENTAL_CONTRACT_NOT_FOUND));
            dealerOrder.setLrentalContractNo(request.getLrentalContractNo());
        } else {
            // 若請求的 lrentalContractNo 為空，則清空經銷商訂單的 lrentalContractNo
            dealerOrder.setLrentalContractNo(null);
        }

        return dealerOrderRepository.save(dealerOrder);
    }

    public void updatePlateNoForOrders(String newPlateNo, List<DealerOrderQueryResponse> processingDealerOrders) {
        List<DealerOrder> dealerOrders = getOrdersByOrderNos(processingDealerOrders.stream().map(DealerOrderQueryResponse::getOrderNo).collect(Collectors.toList()));
        dealerOrders.forEach(dealerOrder -> dealerOrder.setPlateNo(newPlateNo));
        dealerOrderRepository.saveAll(dealerOrders);
    }

    public void update(DealerOrder dealerOrder) {
        dealerOrderRepository.save(dealerOrder);
    }
}
