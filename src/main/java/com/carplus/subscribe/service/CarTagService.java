package com.carplus.subscribe.service;

import carplus.common.redis.cache.Get;
import com.carplus.subscribe.db.mysql.dao.CarTagRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.cars.req.CommonCarCriteria;
import com.carplus.subscribe.model.cars.resp.CarCommonResponse;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.carplus.subscribe.model.request.car.CarTagCreateRequest;
import com.carplus.subscribe.model.request.car.CarTagUpdateRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_TAG_NAME_DUPLICATE;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_TAG_NOT_FOUND;

@Service
public class CarTagService {

    @Autowired
    private CarTagRepository carTagRepository;

    @Autowired
    private CarsService carsService;

    /**
     * 取得所有標籤
     **/
    @NonNull
    @Get(group = CarTag.class, key = "'list'", ttl = 60 * 60 * 24)
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarTag> findAllCarTags(@Nullable Boolean isShow) {
        return carTagRepository.findAll()
            .stream()
            .filter(tag -> Optional.ofNullable(isShow).orElse(true).equals(tag.getIsShow()))
            .sorted(Comparator.comparingInt(CarTag::getSeqNo))
            .collect(Collectors.toList());
    }

    /**
     * 根據車輛查詢條件取得所有標籤
     */
    @NonNull
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarTag> findAllCarTags(@Nullable Boolean isShow, CarsCondition carsCondition) {
        List<CarTag> carTagList = findAllCarTags(isShow);

        if (!carsCondition.hasAnyFieldSet()) {
            return carTagList;
        }

        CommonCarCriteria criteria = new CommonCarCriteria();
        BeanUtils.copyProperties(carsCondition, criteria, carplus.common.utils.BeanUtils.ignorePropertyNames(carsCondition));
        carsService.setSearchPrerequisites(criteria);

        Set<Integer> carTagSet = carsService.getCarResponse(criteria, Integer.MAX_VALUE, 0).stream()
            .filter(car -> CollectionUtils.isNotEmpty(car.getTagIds()) && carsService.isCarMatchingCommonCriteria((CarCommonResponse) car, criteria))
            .map(CarResponse::getTagIds)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());

        return carTagList.stream().filter(tag -> carTagSet.contains(tag.getTagId())).collect(Collectors.toList());
    }

    public CarTag findCarTagById(Integer tagId) {
        return carTagRepository.findById(tagId).orElseThrow(() -> new SubscribeException(CAR_TAG_NOT_FOUND));
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void createCarTag(CarTagCreateRequest createRequest, String memberId) {
        CarTag carTag = createRequest.toEntity();
        carTag.setMemberId(memberId);
        Integer seqNo = carTagRepository.getMaxSeqNo() + 1;
        carTag.setSeqNo(seqNo);
        if (carTagRepository.isDuplicateName(carTag.getCnName(), null)) {
            throw new SubscribeException(CAR_TAG_NAME_DUPLICATE);
        }
        carTagRepository.save(carTag);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateCarTag(CarTagUpdateRequest updateRequest, String memberId) {
        CarTag carTag = findCarTagById(updateRequest.getId());
        updateRequest.toEntity(carTag);
        carTag.setMemberId(memberId);
        if (carTagRepository.isDuplicateName(carTag.getCnName(), updateRequest.getId())) {
            throw new SubscribeException(CAR_TAG_NAME_DUPLICATE);
        }
        carTagRepository.save(carTag);
    }


    /**
     * 更新排序
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateSort(List<Integer> tagIds, String memberId) {
        List<CarTag> carTags = carTagRepository.findAll();
        Map<Integer, CarTag> carTagMap = carTags.stream().collect(Collectors.toMap(CarTag::getTagId, Function.identity()));
        carTags.sort(Comparator.comparingInt(CarTag::getSeqNo));
        for (int i = 0; i < tagIds.size(); i++) {
            CarTag carTag = carTagMap.get(tagIds.get(i));
            if (Objects.equals(carTag.getSeqNo(), i)) {
                continue;
            }
            carTag.setSeqNo(i);
            carTag.setMemberId(memberId);
        }
        carTagRepository.saveAll(carTags);
    }
}
