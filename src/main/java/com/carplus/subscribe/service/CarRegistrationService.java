package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.dao.CarRegistrationRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarRegistration;
import com.carplus.subscribe.exception.SubscribeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_REGISTRATION_ALREADY_EXIST;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_REGISTRATION_NOT_FOUND;

@Service
public class CarRegistrationService {

    @Autowired
    private CarRegistrationRepository carRegistrationRepository;

    public void addCarRegistration(String vatNo, String name, String shortName) {
        if (carRegistrationRepository.existsById(vatNo)) {
            throw new SubscribeException(CAR_REGISTRATION_ALREADY_EXIST);
        } else {

            CarRegistration carRegistration = new CarRegistration(vatNo, name, shortName);
            carRegistrationRepository.save(carRegistration);
        }
    }

    public void updateCarRegistration(String vatNo, String name, String shortName) {
        CarRegistration carRegistration = carRegistrationRepository.findById(vatNo).orElse(null);

        if (carRegistration == null) {
            throw new SubscribeException(CAR_REGISTRATION_NOT_FOUND);
        } else {
            carRegistration.setName(name);
            carRegistration.setShortName(shortName);
            carRegistrationRepository.save(carRegistration);
        }
    }

    public void deleteCarRegistration(String vatNo) {
        CarRegistration carRegistration = carRegistrationRepository.findById(vatNo).orElse(null);

        if (carRegistration == null) {
            throw new SubscribeException(CAR_REGISTRATION_NOT_FOUND);
        } else {
            carRegistrationRepository.delete(carRegistration);
        }
    }

    public CarRegistration getCarRegistration(String vatNo) {
        CarRegistration carRegistration = carRegistrationRepository.findById(vatNo).orElse(null);

        if (carRegistration == null) {
            throw new SubscribeException(CAR_REGISTRATION_NOT_FOUND);
        } else {
            return carRegistration;
        }
    }

    public List<CarRegistration> getAllCarRegistrations() {
        return carRegistrationRepository.findAll();
    }

    public boolean isCarRegistrationExist(String vatNo) {
        return carRegistrationRepository.existsById(vatNo);
    }

}
