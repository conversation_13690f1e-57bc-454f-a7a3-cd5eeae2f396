package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dao.SkuShipmentHistoryRepository;
import com.carplus.subscribe.db.mysql.dao.SkuShipmentRepository;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.enums.ShipmentStatus;
import com.carplus.subscribe.event.priceinfo.MerchandisePriceInfoCreatedEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.shipment.SkuShipmentCSV;
import com.carplus.subscribe.model.shipment.SkuShipmentCriteria;
import com.carplus.subscribe.model.shipment.SkuShipmentDetailResponse;
import com.carplus.subscribe.model.shipment.SkuShipmentResponse;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.utils.CsvUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SkuShipmentService {

    @Autowired
    private SkuShipmentRepository skuShipmentRepository;
    @Autowired
    private SkuShipmentHistoryRepository shipmentHistoryRepository;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private SkuService skuService;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private OrderService orderService;

    /**
     * 取得出貨清單的分頁結果
     */
    public Page<SkuShipmentResponse> getShipmentList(SkuShipmentCriteria criteria, PageRequest pageRequest) {
        // 檢查是否需要在應用層過濾 (有 payStatus 條件時)
        boolean needsApplicationFiltering = CollectionUtils.isNotEmpty(criteria.getPayStatus());

        if (needsApplicationFiltering) {
            // 需要在應用層過濾時，先獲取所有過濾後的結果，然後手動分頁
            return getShipmentListWithApplicationFiltering(criteria, pageRequest);
        } else {
            // 沒有 payStatus 過濾時，可以使用資料庫分頁
            return getShipmentListWithDatabasePagination(criteria, pageRequest);
        }
    }

    /**
     * 使用在應用層過濾和分頁的查詢方法 (當有 payStatus 過濾時)
     */
    private Page<SkuShipmentResponse> getShipmentListWithApplicationFiltering(SkuShipmentCriteria criteria, PageRequest pageRequest) {
        // 獲取所有原始資料
        List<SkuShipment> rawEntities = skuShipmentRepository.search(criteria, 0, Integer.MAX_VALUE);

        if (rawEntities.isEmpty()) {
            return Page.of(0, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }

        // 聚合並過濾
        List<SkuShipmentResponse> filtered = aggregateAndFilterShipments(rawEntities, criteria.getPayStatus());

        // 手動分頁
        int skip = pageRequest.getSkip();
        int limit = pageRequest.getLimit();
        int totalCount = filtered.size();

        List<SkuShipmentResponse> pagedResults = filtered.stream()
            .skip(skip)
            .limit(limit)
            .collect(Collectors.toList());

        return processAndReturnResults(pagedResults, totalCount, pageRequest);
    }

    /**
     * 使用資料庫分頁的查詢方法 (當沒有 payStatus 過濾時)
     */
    private Page<SkuShipmentResponse> getShipmentListWithDatabasePagination(SkuShipmentCriteria criteria, PageRequest pageRequest) {
        // 先計算符合條件的資料總數
        long totalCount = count(criteria);
        if (totalCount == 0) {
            return Page.of(0, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }

        // 查詢原始資料
        List<SkuShipment> rawEntities = skuShipmentRepository.search(criteria, pageRequest.getSkip(), pageRequest.getLimit());

        if (rawEntities.isEmpty()) {
            return Page.of(totalCount, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }

        // 資料庫分頁時不需要 payStatus 過濾
        List<SkuShipmentResponse> aggregatedResults = aggregateAndFilterShipments(rawEntities, null);

        return processAndReturnResults(aggregatedResults, totalCount, pageRequest);
    }

    /**
     * 聚合和過濾方法：一次性完成 Entity -> DTO 轉換、聚合和過濾
     *
     * @param rawEntities 原始 SkuShipment 列表
     * @param payStatusList 付款狀態過濾條件，null 表示不過濾
     * @return 處理後的 SkuShipmentResponse 列表
     */
    private List<SkuShipmentResponse> aggregateAndFilterShipments(List<SkuShipment> rawEntities, List<PayStatus> payStatusList) {
        if (rawEntities.isEmpty()) {
            return Collections.emptyList();
        }

        // 批量查詢所有相關資料，避免 N+1 查詢
        Map<String, Orders> orderMap = batchLoadOrders(rawEntities);
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = batchLoadOrderPriceInfos(rawEntities);

        // 按訂單號分組原始數據
        Map<String, List<SkuShipment>> groupedByOrder = rawEntities.stream()
            .collect(Collectors.groupingBy(SkuShipment::getOrderNo));

        return groupedByOrder.entrySet().stream()
            .map(entry -> buildAggregatedResponse(
                entry.getKey(),
                entry.getValue(),
                orderMap,
                orderPriceInfoMap,
                payStatusList
            ))
            .filter(Objects::nonNull) // 過濾掉因 payStatus 過濾而被排除的結果
            .collect(Collectors.toList());
    }

    /**
     * 建構聚合後的 SkuShipmentResponse，同時處理過濾邏輯
     */
    private SkuShipmentResponse buildAggregatedResponse(String orderNo,
                                                        List<SkuShipment> shipments,
                                                        Map<String, Orders> orderMap,
                                                        Map<Integer, OrderPriceInfo> orderPriceInfoMap,
                                                        List<PayStatus> payStatusList) {

        // 按 orderPriceInfoId 分組出貨記錄
        Map<Integer, List<SkuShipment>> groupedByPriceInfo = shipments.stream()
            .collect(Collectors.groupingBy(SkuShipment::getOrderPriceInfoId));

        // 建構出貨清單，同時進行 payStatus 過濾
        List<SkuShipmentResponse.SkuOrderPriceInfo> shipmentList = groupedByPriceInfo.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(e -> buildSkuOrderPriceInfo(e.getKey(), e.getValue(), orderPriceInfoMap))
            .filter(sku -> payStatusList == null || payStatusList.contains(sku.getPayStatus()))
            .collect(Collectors.toList());

        // 如果經過 payStatus 過濾後沒有任何出貨資料，返回 null 表示該訂單應被排除
        if (shipmentList.isEmpty() && payStatusList != null) {
            return null;
        }

        // 建構訂單層級的回應
        SkuShipmentResponse response = new SkuShipmentResponse();
        response.setOrderNo(orderNo);
        response.setSkuList(shipmentList);

        // 設定訂單狀態和開始日期 (從任一出貨記錄獲取)
        Orders order = orderMap.get(orderNo);
        if (order != null) {
            response.setOrderStatus(order.getStatus());
            response.setOrderStatusName(OrderStatus.of(order.getStatus()).getName());
            response.setOrderStartDate(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()));
        }

        return response;
    }

    /**
     * 建構單一汽車用品費用資訊
     */
    private SkuShipmentResponse.SkuOrderPriceInfo buildSkuOrderPriceInfo(Integer orderPriceInfoId,
                                                                         List<SkuShipment> shipments,
                                                                         Map<Integer, OrderPriceInfo> orderPriceInfoMap) {

        SkuShipment firstShipment = shipments.get(0);
        OrderPriceInfo orderPriceInfo = orderPriceInfoMap.get(orderPriceInfoId);

        SkuShipmentResponse.SkuOrderPriceInfo sku = new SkuShipmentResponse.SkuOrderPriceInfo();
        sku.setOrderPriceInfoId(orderPriceInfoId);

        if (orderPriceInfo != null) {
            sku.setAmount(orderPriceInfo.getActualPrice());
            sku.setQuantity(orderPriceInfo.getInfoDetail().getQuantity());
            sku.setPayStatus(orderPriceInfo.getPayStatus());
            sku.setPayStatusName(orderPriceInfo.getPayStatusName());
            sku.setSkuCode(orderPriceInfo.getSkuCode() != null ? orderPriceInfo.getSkuCode() : firstShipment.getSkuCode());
        } else {
            // orderPriceInfo 被刪除時，從 SkuShipment 實體獲取汽車用品代碼，其他資訊設為 null
            sku.setSkuCode(firstShipment.getSkuCode());
            sku.setAmount(null);
            sku.setQuantity(null);
            sku.setPayStatus(null);
            sku.setPayStatusName(null);
        }

        // 建構出貨資訊清單
        List<SkuShipmentResponse.ShipmentInfo> shipmentInfoList = shipments.stream()
            .sorted(Comparator.comparing(SkuShipment::getId))
            .map(this::buildShipmentInfo)
            .collect(Collectors.toList());

        sku.setSkuShipmentList(shipmentInfoList);
        return sku;
    }

    /**
     * 建構出貨資訊
     */
    private SkuShipmentResponse.ShipmentInfo buildShipmentInfo(SkuShipment skuShipment) {
        SkuShipmentResponse.ShipmentInfo shipmentInfo = new SkuShipmentResponse.ShipmentInfo();
        shipmentInfo.setId(skuShipment.getId());
        shipmentInfo.setStatus(skuShipment.getStatus());
        shipmentInfo.setStatusName(skuShipment.getStatus().getDescription());
        shipmentInfo.setCreateDate(skuShipment.getInstantCreateDate());
        shipmentInfo.setUpdateDate(skuShipment.getInstantUpdateDate());
        shipmentInfo.setUpdater(skuShipment.getUpdater());
        return shipmentInfo;
    }

    /**
     * 處理結果並返回分頁資料 (補充額外資訊)
     */
    private Page<SkuShipmentResponse> processAndReturnResults(List<SkuShipmentResponse> results, long totalCount, PageRequest pageRequest) {
        if (results.isEmpty()) {
            return Page.of(totalCount, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }

        // 使用共用方法建立折扣費用對應表
        Map<Integer, List<OrderPriceInfo>> refOrderPriceInfoMap = buildRefOrderPriceInfoMap(results);

        Map<String, Sku> skuMap = skuService.getMapByCodes(results.stream()
            .map(SkuShipmentResponse::getSkuList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.SkuOrderPriceInfo::getSkuCode)
            .collect(Collectors.toSet()));

        Set<String> memberIds = results.stream()
            .map(SkuShipmentResponse::getSkuList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.SkuOrderPriceInfo::getSkuShipmentList)
            .flatMap(List::stream)
            .map(SkuShipmentResponse.ShipmentInfo::getUpdater)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<String, MemberInfo> memberInfoMap = authorityServer.getMemberInfosByMemberIds(memberIds);

        // 每一筆 SkuShipmentResponse (補 skuName、updaterName、amount 修正)
        results.forEach(shipment -> shipment.getSkuList().forEach(skuOrderPriceInfo -> {
            skuOrderPriceInfo.setSkuName(skuMap.getOrDefault(skuOrderPriceInfo.getSkuCode(), new Sku()).getName());

            Optional.ofNullable(skuOrderPriceInfo.getAmount()).ifPresent(amount ->
                skuOrderPriceInfo.setAmount(calculateAmountWithNegative(refOrderPriceInfoMap, skuOrderPriceInfo.getOrderPriceInfoId(), amount)));

            skuOrderPriceInfo.getSkuShipmentList().forEach(shipmentInfo -> {
                shipmentInfo.setUpdaterName(
                    Optional.ofNullable(memberInfoMap.get(shipmentInfo.getUpdater()))
                        .map(MemberInfo::getMemberName)
                        .orElse(null)
                );
            });
        }));

        return Page.of(
            totalCount,
            results,
            pageRequest.getSkip(),
            pageRequest.getLimit()
        );
    }

    /**
     * 共用方法：建立折扣費用對應表
     */
    private Map<Integer, List<OrderPriceInfo>> buildRefOrderPriceInfoMap(List<SkuShipmentResponse> responses) {
        List<String> orderNos = responses.stream()
            .map(SkuShipmentResponse::getOrderNo)
            .collect(Collectors.toList());

        return buildRefOrderPriceInfoMap(orderNos);
    }

    /**
     * 共用方法：建立折扣費用對應表
     */
    private Map<Integer, List<OrderPriceInfo>> buildRefOrderPriceInfoMap(Collection<String> orderNos) {
        return priceInfoService.getUnPaidPriceInfoByOrders(new ArrayList<>(orderNos), true)
            .stream()
            .filter(orderPriceInfo -> orderPriceInfo.getRefPriceInfoNo() != null)
            .collect(Collectors.groupingBy(OrderPriceInfo::getRefPriceInfoNo));
    }

    /**
     * 計算汽車用品費用的實際金額 (含負項)
     */
    private int calculateAmountWithNegative(Map<Integer, List<OrderPriceInfo>> refOrderPriceInfoMap, Integer orderPriceInfoId, int amount) {
        int negativeAmount = refOrderPriceInfoMap.getOrDefault(orderPriceInfoId, Collections.emptyList())
            .stream()
            .mapToInt(OrderPriceInfo::getActualPrice)
            .sum();
        return amount + negativeAmount;
    }

    public long count(SkuShipmentCriteria criteria) {
        // 沒有 payStatus 條件時，可直接在資料庫層統計
        if (CollectionUtils.isEmpty(criteria.getPayStatus())) {
            return skuShipmentRepository.countDistinctOrdersInDatabase(criteria);
        }

        // 有 payStatus 時，需在應用層聚合與過濾
        List<SkuShipment> rawEntities = skuShipmentRepository.search(criteria, 0, Integer.MAX_VALUE);

        if (rawEntities.isEmpty()) {
            return 0;
        }

        // 使用聚合和過濾方法
        List<SkuShipmentResponse> filtered = aggregateAndFilterShipments(rawEntities, criteria.getPayStatus());
        return filtered.size();
    }

    /**
     * 批量載入訂單資料，避免 N+1 查詢問題
     */
    private Map<String, Orders> batchLoadOrders(List<SkuShipment> shipments) {
        Set<String> orderNos = shipments.stream()
            .map(SkuShipment::getOrderNo)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (orderNos.isEmpty()) {
            return Collections.emptyMap();
        }

        return orderService.getOrders(new ArrayList<>(orderNos))
            .stream()
            .collect(Collectors.toMap(Orders::getOrderNo, order -> order));
    }

    /**
     * 批量載入費用資訊，避免 N+1 查詢問題
     */
    private Map<Integer, OrderPriceInfo> batchLoadOrderPriceInfos(List<SkuShipment> shipments) {
        Set<Integer> orderPriceInfoIds = shipments.stream()
            .map(SkuShipment::getOrderPriceInfoId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (orderPriceInfoIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 使用 getByIds 方法批量載入 OrderPriceInfo
        return priceInfoService.getByIds(new ArrayList<>(orderPriceInfoIds))
            .stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, priceInfo -> priceInfo));
    }

    /**
     * Get single shipment details
     */
    public SkuShipmentDetailResponse getShipmentDetail(Integer shipmentId) {
        SkuShipment shipment = skuShipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SHIPMENT_NOT_FOUND));

        return convertToShipmentDetailResponse(shipment);
    }

    /**
     * 共用邏輯：為汽車用品費用清單建立出貨資料和歷程
     *
     * @param merchandisePriceInfos 汽車用品費用清單
     * @param memberId              建立者工號
     */
    private void createShipmentsForPriceInfos(Orders order, List<OrderPriceInfo> merchandisePriceInfos, String memberId) {
        if (CollectionUtils.isEmpty(merchandisePriceInfos)) {
            return;
        }
        if (!OrderStatus.of(order.getStatus()).isSkuShipmentAvailable()) {
            // 訂單狀態不適用於建立出貨資料
            throw new SubscribeException(SubscribeHttpExceptionCode.ORDER_STATUS_NOT_APPLICABLE_FOR_CREATING_SHIPMENT);
        }

        List<SkuShipment> shipments = new ArrayList<>();
        List<SkuShipmentHistory> shipmentHistories = new ArrayList<>();

        // 獨立處理每個 OrderPriceInfo，不合併相同 skuCode 的記錄
        for (OrderPriceInfo priceInfo : merchandisePriceInfos) {
            // 使用 orderPriceInfoId 作為主要參考，確保每筆汽車用品費用獨立處理
            Integer priceInfoId = priceInfo.getId();

            // 檢查已存在的出貨記錄數量，僅計算與當前 orderPriceInfoId 關聯的記錄
            int existingShipments = skuShipmentRepository.countByOrderPriceInfoId(priceInfoId);
            int requestedQuantity = priceInfo.getInfoDetail().getQuantity();
            int remainingQuantity = Math.max(0, requestedQuantity - existingShipments);

            // 只為差異數量建立新的出貨記錄，每個記錄都與特定的 orderPriceInfoId 關聯
            for (int i = 0; i < remainingQuantity; i++) {
                SkuShipment shipment = SkuShipment.builder()
                    .orderNo(priceInfo.getOrderNo())
                    .orderPriceInfoId(priceInfoId)
                    .skuCode(priceInfo.getSkuCode())
                    .status(ShipmentStatus.PENDING)
                    .updater(memberId)
                    .build();

                shipments.add(shipment);
            }
        }

        // 如果沒有需要建立的出貨記錄，直接返回
        if (shipments.isEmpty()) {
            return;
        }

        // 批量新增出貨資料
        List<SkuShipment> savedShipments = skuShipmentRepository.saveAll(shipments);

        // 為所有出貨資料建立初始歷程記錄
        for (SkuShipment shipment : savedShipments) {
            SkuShipmentHistory history = SkuShipmentHistory.builder()
                .shipmentId(shipment.getId())
                .status(ShipmentStatus.PENDING)
                .operator(memberId)
                .build();

            shipmentHistories.add(history);
        }

        // 批量新增出貨歷程記錄
        shipmentHistoryRepository.saveAll(shipmentHistories);
    }

    /**
     * 為訂單建立出貨資料
     */
    public void createShipments(String orderNo, String memberId) {
        Orders order = orderService.getOrder(orderNo);
        createShipments(order, memberId);
    }

    /**
     * 為訂單建立出貨資料
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void createShipments(Orders order, String memberId) {
        List<OrderPriceInfo> merchandisePriceInfos = priceInfoService.getPriceInfosByOrder(
            order.getOrderNo(),
            PriceInfoDefinition.PriceInfoCategory.Merchandise,
            PriceInfoDefinition.PriceInfoType.Pay
        );

        createShipmentsForPriceInfos(order, merchandisePriceInfos, memberId);
    }

    /**
     * 更新出貨狀態
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateShipmentStatus(Integer shipmentId, ShipmentStatus newStatus, String memberId) {
        SkuShipment shipment = skuShipmentRepository.findById(shipmentId)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SHIPMENT_NOT_FOUND));

        // 狀態沒改變，則不更新
        if (shipment.getStatus() == newStatus) {
            return;
        }

        shipment.setStatus(newStatus);
        shipment.setUpdater(memberId);
        skuShipmentRepository.save(shipment);

        // 新增出貨歷程
        SkuShipmentHistory history = SkuShipmentHistory.builder()
            .shipmentId(shipment.getId())
            .status(newStatus)
            .operator(memberId)
            .build();

        shipmentHistoryRepository.save(history);
    }

    private SkuShipmentDetailResponse convertToShipmentDetailResponse(SkuShipment shipment) {
        OrderPriceInfo orderPriceInfo = null;
        Orders order = null;

        if (shipment.getOrderPriceInfoId() != null) {
            orderPriceInfo = priceInfoService.getPriceInfoWrapper(shipment.getOrderNo()).getById(shipment.getOrderPriceInfoId());
        }
        if (shipment.getOrderNo() != null) {
            order = orderService.getOrder(shipment.getOrderNo());
        }

        SkuShipmentDetailResponse response = new SkuShipmentDetailResponse();
        response.setOrderNo(shipment.getOrderNo());
        if (order != null) {
            response.setOrderStatus(order.getStatus());
            response.setOrderStartDate(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()));
        }
        response.setOrderPriceInfoId(shipment.getOrderPriceInfoId());

        Map<Integer, List<OrderPriceInfo>> refOrderPriceInfoMap = Optional.ofNullable(order)
            .map(o -> priceInfoService.getUnPaidPriceInfoByOrder(shipment.getOrderNo(), true)
                .stream()
                .filter(opi -> opi.getRefPriceInfoNo() != null)
                .collect(Collectors.groupingBy(OrderPriceInfo::getRefPriceInfoNo)))
            .orElse(Collections.emptyMap());

        Optional.ofNullable(orderPriceInfo).ifPresent(opi ->
            response.setAmount(calculateAmountWithNegative(refOrderPriceInfoMap, opi.getId(), opi.getAmount())));

        List<SkuShipmentHistory> shipmentHistoryList = shipment.getHistoryList();

        Set<String> memberIds = shipmentHistoryList.stream()
            .map(SkuShipmentHistory::getOperator)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        Map<String, MemberInfo> memberInfoMap = authorityServer.getMemberInfosByMemberIds(memberIds);

        List<SkuShipmentDetailResponse.ShipmentHistoryInfo> historyList = shipmentHistoryList
            .stream()
            .map(history -> {
                SkuShipmentDetailResponse.ShipmentHistoryInfo historyInfo = new SkuShipmentDetailResponse.ShipmentHistoryInfo();
                historyInfo.setId(history.getId());
                historyInfo.setStatus(history.getStatus());
                historyInfo.setStatusName(history.getStatus().getDescription());
                historyInfo.setUpdateDate(history.getInstantCreateDate());
                historyInfo.setOperator(history.getOperator());
                Optional.ofNullable(historyInfo.getOperator())
                    .ifPresent(memberId -> historyInfo.setOperatorName(memberInfoMap.get(memberId).getMemberName()));
                return historyInfo;
            })
            .collect(Collectors.toList());
        response.setHistoryList(historyList);

        return response;
    }

    /**
     * 處理汽車用品費用建立事件，自動建立出貨資料及歷程
     * 每個 OrderPriceInfo 獨立處理，即使多個記錄共享相同的 skuCode 和 orderNo
     */
    @Async
    @TransactionalEventListener
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void handleMerchandisePriceInfoCreated(MerchandisePriceInfoCreatedEvent event) {
        createShipmentsForPriceInfos(event.getOrder(), event.getMerchandisePriceInfos(), event.getMemberId());
    }

    /**
     * 產生 SKU 出貨 CSV 匯出資料
     * 使用與列表查詢相同的過濾邏輯確保一致性，包含付款狀態過濾
     */
    public CsvUtil.ByteArrayOutputStream2ByteBuffer generateSkuShipmentCsv(SkuShipmentCriteria criteria) {
        // 檢查是否需要在應用層過濾 (有 payStatus 條件時)
        boolean needsApplicationFiltering = CollectionUtils.isNotEmpty(criteria.getPayStatus());

        if (needsApplicationFiltering) {
            // 需要在應用層過濾時，使用與列表查詢相同的邏輯
            return generateCsvWithApplicationFiltering(criteria);
        } else {
            // 沒有 payStatus 過濾時，直接查詢並產生 CSV
            return generateCsvWithoutFiltering(criteria);
        }
    }

    /**
     * 產生 CSV (無需在應用層過濾)
     */
    private CsvUtil.ByteArrayOutputStream2ByteBuffer generateCsvWithoutFiltering(SkuShipmentCriteria criteria) {
        // 查詢所有出貨資料
        List<SkuShipment> shipments = skuShipmentRepository.search(criteria, 0, Integer.MAX_VALUE);
        return generateCsvFromShipments(shipments);
    }

    /**
     * 產生 CSV (需要在應用層過濾)
     */
    private CsvUtil.ByteArrayOutputStream2ByteBuffer generateCsvWithApplicationFiltering(SkuShipmentCriteria criteria) {
        // 使用與列表查詢相同的邏輯進行過濾
        List<SkuShipment> rawEntities = skuShipmentRepository.search(criteria, 0, Integer.MAX_VALUE);

        if (rawEntities.isEmpty()) {
            return createCsvOutput(Collections.emptyList());
        }

        List<SkuShipment> filteredShipments = filterShipmentsByPayStatus(rawEntities, criteria.getPayStatus());

        return generateCsvFromShipments(filteredShipments);
    }

    /**
     * 從 SkuShipment 列表產生 CSV
     */
    private CsvUtil.ByteArrayOutputStream2ByteBuffer generateCsvFromShipments(List<SkuShipment> shipments) {
        if (shipments.isEmpty()) {
            return createCsvOutput(Collections.emptyList());
        }

        // 批量查詢所有相關資料，避免 N+1 查詢
        Map<String, Orders> orderMap = batchLoadOrders(shipments);
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = batchLoadOrderPriceInfos(shipments);

        // 收集所有需要查詢的 memberId
        Set<String> memberIds = shipments.stream()
            .map(SkuShipment::getUpdater)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 批量查詢人員資訊
        Map<String, MemberInfo> memberInfoMap = authorityServer.getMemberInfosByMemberIds(memberIds);

        Map<String, Sku> skuMap = skuService.getMapByCodes(shipments.stream()
            .map(SkuShipment::getSkuCode)
            .collect(Collectors.toSet()));

        Set<String> orderNos = shipments.stream()
            .map(SkuShipment::getOrderNo)
            .collect(Collectors.toSet());

        // 使用共用方法建立折扣費用對應表
        Map<Integer, List<OrderPriceInfo>> refOrderPriceInfoMap = buildRefOrderPriceInfoMap(orderNos);

        // 直接從 SkuShipment 轉換為 CSV，使用預載入的資料避免 N+1 查詢
        List<SkuShipmentCSV> csvList = shipments.stream()
            .map(shipment -> {
                // 使用預載入的資料，避免 N+1 查詢
                OrderPriceInfo orderPriceInfo = orderPriceInfoMap.get(shipment.getOrderPriceInfoId());
                Orders order = orderMap.get(shipment.getOrderNo());

                String updaterName = Optional.ofNullable(shipment.getUpdater())
                    .map(memberInfoMap::get)
                    .map(MemberInfo::getMemberName)
                    .orElse(null);

                return new SkuShipmentCSV(shipment, orderPriceInfo, order, updaterName, skuMap, refOrderPriceInfoMap);
            })
            .collect(Collectors.toList());

        return createCsvOutput(csvList);
    }

    /**
     * 在 SkuShipment 層級過濾 payStatus，保持原始排序
     *
     * @param shipments 原始 SkuShipment 列表
     * @param payStatusList 付款狀態過濾條件
     * @return 過濾後的 SkuShipment 列表，保持原始排序
     */
    private List<SkuShipment> filterShipmentsByPayStatus(List<SkuShipment> shipments, List<PayStatus> payStatusList) {
        if (CollectionUtils.isEmpty(payStatusList)) {
            return shipments;
        }

        // 批量查詢 OrderPriceInfo 以獲取 PayStatus
        Map<Integer, OrderPriceInfo> orderPriceInfoMap = batchLoadOrderPriceInfos(shipments);

        return shipments.stream()
            .filter(shipment -> {
                OrderPriceInfo priceInfo = orderPriceInfoMap.get(shipment.getOrderPriceInfoId());
                return priceInfo != null && payStatusList.contains(priceInfo.getPayStatus());
            })
            .collect(Collectors.toList());
    }

    /**
     * 建立 CSV 輸出
     */
    private CsvUtil.ByteArrayOutputStream2ByteBuffer createCsvOutput(List<SkuShipmentCSV> csvList) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            csvList,
            SkuShipmentCSV.HEADS,
            true,
            ',',
            out,
            Charset.forName("big5"),
            SkuShipmentCSV.class
        );
        return out;
    }
}