package com.carplus.subscribe.service;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.dao.CarBrandRepository;
import com.carplus.subscribe.db.mysql.dao.CarModelImageRepository;
import com.carplus.subscribe.db.mysql.dao.CarModelRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.CarModelAddRequest;
import com.carplus.subscribe.model.request.CarModelImageRequest;
import com.carplus.subscribe.model.request.CarModelQueryRequest;
import com.carplus.subscribe.model.request.CarModelUpdateRequest;
import com.carplus.subscribe.model.response.CarModelResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CarModelService {

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private CarModelRepository carModelRepository;

    @Autowired
    private CarModelImageRepository carModelImageRepository;

    @Autowired
    private CarBrandRepository carBrandRepository;

    @Value("${gcs.url}")
    private String gcsUrl;
    @Autowired
    private CarBrandService carBrandService;

    @Transactional(transactionManager = "mysqlTransactionManager")
    public CarModelResponse addCardModel(CarModelAddRequest request) {
        carBrandService.findByBrandCode(request.getBrandCode());
        if (carModelRepository.getCarModelByName(request.getCarModelName()).size() > 1) {
            throw new SubscribeException(SubscribeHttpExceptionCode.CAR_MODEL_NAME_DUPLICATE);
        }
        CarDefine.CarKind carKind = Optional.ofNullable(CarDefine.CarKind.of(request.getCarKind())).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_MODEL_CAR_KIND_IS_NOT_FIND));
        // 新增車型
        CarModel entity = new CarModel();
        entity.setCarModelCode(carModelRepository.nextSeq());
        entity.setBrandCode(request.getBrandCode());
        entity.setCarModelName(request.getCarModelName());
        entity.setCarKind(carKind);
        entity.setDeleted(false);
        carModelRepository.save(entity);
        // 新增車型圖片
        saveAllImages(entity.getCarModelCode(), request.getImages());
        return getInfo(entity.getCarModelCode());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public CarModelResponse updateCarModel(CarModelUpdateRequest request) {
        carBrandService.findByBrandCode(request.getBrandCode());
        CarModel entity = findByCarModelCode(request.getCarModelCode());
        if (Objects.isNull(entity)) {
            throw new BadRequestException(String.format("[車型不存在, 車型代碼: %s]", request.getCarModelCode()));
        }
        if (!entity.getCarModelName().equalsIgnoreCase(request.getCarModelName()) && carModelRepository.getCarModelByName(request.getCarModelName()).size() > 1) {
            throw new SubscribeException(SubscribeHttpExceptionCode.CAR_MODEL_NAME_DUPLICATE);
        }
        CarDefine.CarKind carKind = Optional.ofNullable(CarDefine.CarKind.of(request.getCarKind())).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_MODEL_CAR_KIND_IS_NOT_FIND));

        List<CarModelImage> oldImages = carModelImageRepository.findByCarModelCode(request.getCarModelCode());
        // 刪除舊車型圖
        carModelImageRepository.deleteAll(oldImages);
        // 更新車型
        entity.setBrandCode(request.getBrandCode());
        entity.setCarModelName(request.getCarModelName());
        entity.setCarKind(carKind);
        carModelRepository.save(entity);
        // 更新車型圖
        saveAllImages(request.getCarModelCode(), request.getImages());
        return getInfo(entity.getCarModelCode());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateIsDeleted(String carModelCode, Integer paramDeleted) {
        boolean isDeleted = paramDeleted == 1;
        carModelRepository.updateIsDeleted(carModelCode, isDeleted);
    }

    public CarModel findByCarModelCode(String carModelCode) {
        return carModelRepository.findByCarModelCode(carModelCode);
    }

    /**
     * 透過一組carModelCode找尋CarModel清單
     * 會透過Partition一次500筆
     */
    public List<CarModel> findByCarModelCodes(List<String> carModelCodes) {
        return carModelRepository.findByCarModelCodes(carModelCodes);
    }

    /**
     * 透過一組carModelCode找尋CarModelImage清單
     * 會透過Partition一次500筆
     */
    public List<CarModelImage> findByCarModelImages(List<String> carModelCodes) {
        return carModelImageRepository.findByCarModelImages(carModelCodes);
    }

    public CarModel findByBrandCode(String brandCode) {
        return carModelRepository.findByBrandCode(brandCode);
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public CarModel findByOriginalCarModelCode(String originalCarModelCode) {
        return carModelRepository.findByOriginalCarModelCode(originalCarModelCode);
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public CarModelResponse getInfo(String carModeCode) {
        CarModel carModel = findByCarModelCode(carModeCode);
        List<CarModelImage> images = carModelImageRepository.findByCarModelCode(carModeCode);
        CarBrand carBrand = carBrandService.findByBrandCode(carModel.getBrandCode());
        return new CarModelResponse(carModel, images, gcsUrl, carBrand.getBrandName());
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarModelResponse> findBySearch(CarModelQueryRequest request) {
        return carModelRepository.findBySearch(request, null, null)
            .stream()
            .map(tuple -> new CarModelResponse(tuple, mapper, gcsUrl))
            .collect(Collectors.toList());
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarModel> findAll() {
        return carModelRepository.findAll();
    }

    private void saveAllImages(String carModelCode, List<CarModelImageRequest> imageList) {
        if (imageList != null && !imageList.isEmpty()) {
            List<CarModelImage> entityImages = new ArrayList<CarModelImage>();
            imageList.forEach(m -> {
                CarModelImage carModelImage = new CarModelImage();
                carModelImage.setCarModelCode(carModelCode);
                carModelImage.setYear(m.getYear());
                carModelImage.setPaths(m.getPaths());
                entityImages.add(carModelImage);
            });
            carModelImageRepository.saveAll(entityImages);
        }
    }

    /**
     * 確認車型代號存在與否
     */
    public String checkCarModelCodeAndGet(String carModelCode) {
        return Optional.ofNullable(findByCarModelCode(carModelCode))
            .map(CarModel::getCarModelCode)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_MODEL_NOT_FOUND));
    }
}
