package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import com.carplus.subscribe.constant.GoSmartConstant;
import com.carplus.subscribe.db.mysql.dao.CarModelImageRepository;
import com.carplus.subscribe.db.mysql.dao.UploadFileRepository;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.enums.UploadFileKindEnum;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.presign.GcsGetDownloadUrlReq;
import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq;
import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq.TypeCount;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.presign.UploadFilePresignedRes;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.utils.DownloadUtils;
import com.carplus.subscribe.utils.HttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.util.*;

import static com.carplus.subscribe.enums.FileTypeEnum.JPG;

@Slf4j
@Service
public class GcsService {

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private CarModelImageRepository carModelImageRepository;

    @Autowired
    private UploadFileRepository uploadFileRepository;

    /**
     * 將指定網址下載後轉傳GCS
     */
    public String gcsPublicUpload(String url) throws Exception {
        byte[] bytes = DownloadUtils.downloadFileAsByteArray(url);

        GcsGetUploadUrlReq req = goSmartServer.createGcsGetUploadUrlReq(JPG);
        req.setIsTemp(Boolean.FALSE);
        GcsUrlRes gcsUrlRes = goSmartServer.getGcsPublicUploadUrl(req);
        GcsUrlRes.TypeResponse typeResponse = gcsUrlRes.getSignedUrls().get(0);
        String uploadUrl = typeResponse.getSignedUrl();
        String mediaType = typeResponse.getMediaType();
        // 上傳 excel
        HttpRequestUtils.upload(uploadUrl, bytes, mediaType);
        System.out.println(uploadUrl);
        URI uri = URI.create(uploadUrl);
        return uri.getScheme() + "://" + uri.getHost() + uri.getPath();
    }


    /**
     * 將車圖從阿里雲轉到GCS
     */
    public void convertCarModelImg2Gcs() {
        Map<String, String> convertedMap = new HashMap<>();
        List<CarModelImage> carModelImageList = carModelImageRepository.findAll();
        String ossUrl = goSmartServer.getOSSPresign(GoSmartConstant.FILE_PATH_SECURITY_DEPOSIT_REFUND, GoSmartConstant.FILE_TYPE_JPG).getOssUrl();
        List<CarModelImage> updateList = new ArrayList<>();
        for (CarModelImage carModelImage : carModelImageList) {
            boolean isUpdate = false;
            List<String> pathes = new ArrayList<>();
            for (String path : carModelImage.getPaths()) {
                if (convertedMap.containsKey(path)) {
                    pathes.add(convertedMap.get(path));
                    continue;
                }
                if (!path.startsWith("/SUB/SUB/")) {
                    isUpdate = true;
                    String url = path;
                    if (!path.startsWith("http")) {
                        url = ossUrl + path;
                    }
                    try {
                        String newUrl = URI.create(gcsPublicUpload(url)).getPath();
                        convertedMap.put(path, newUrl);
                        pathes.add(newUrl);
                    } catch (Exception e) {
                        convertedMap.put(path, path);
                        pathes.add(path);
                    }
                } else {
                    pathes.add(path);
                }
            }
            carModelImage.setPaths(pathes);
            if (isUpdate) {
                updateList.add(carModelImage);
            }
        }
        carModelImageRepository.saveAll(updateList);
    }

    /**
     * 取得上傳憑証
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public UploadFilePresignedRes getUploadPresigned(Integer acctId, UploadFileKindEnum kind, Boolean isTemp, String filename, String operator) {

        String filePath = kind.buildFilePath(acctId);

        // 取得上傳憑証
        GcsGetUploadUrlReq uploadUrlReq = new GcsGetUploadUrlReq();
        uploadUrlReq.setIsTemp(isTemp);
        uploadUrlReq.setSource(HeaderDefine.SystemKind.SUB);
        uploadUrlReq.setFilePath(filePath);
        uploadUrlReq.setRequestList(Collections.singletonList(TypeCount.builder().mediaType(kind.getMediaType().toString()).quantity(1).build()));

        GcsUrlRes gcsUploadUrl = goSmartServer.getGcsUploadUrl(uploadUrlReq);
        if (gcsUploadUrl.getSignedUrls().isEmpty()) {
            log.error("[Upload File] 取不到上傳憑証");
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_SIGN_NOT_EXISTS);
        }

        UploadFile uploadFile = new UploadFile();
        uploadFile.setType(kind.name());
        uploadFile.setFilename(gcsUploadUrl.getSignedUrls().get(0).getFileName());
        uploadFile.setPath(filePath);
        uploadFile.setMediaType(kind.getMediaType().toString());
        uploadFile.setAcctId(acctId);
        uploadFile.setDisplayFilename(filename);
        if (!isTemp) {
            uploadFile.setStatus(1);
        }
        uploadFile.setCreateUser(operator);
        uploadFile.setUpdateUser(operator);
        uploadFileRepository.save(uploadFile);

        UploadFilePresignedRes result = new UploadFilePresignedRes();
        result.setId(uploadFile.getId());
        result.setFileName(gcsUploadUrl.getSignedUrls().get(0).getFileName());
        result.setMediaType(gcsUploadUrl.getSignedUrls().get(0).getMediaType());
        result.setSignedUrl(gcsUploadUrl.getSignedUrls().get(0).getSignedUrl());

        return result;
    }

    /**
     * 取得下載憑証
     */
    public GcsUrlRes getDownloadPresigned(Integer acctId, UploadFileKindEnum kind, Integer fileId) {
        return this.getDownloadPresigned(acctId, kind, fileId, 1);
    }

    /**
     * 取得下載憑証-指定到期時間
     */
    public GcsUrlRes getDownloadPresigned(Integer acctId, UploadFileKindEnum kind, Integer fileId, Integer minutes) {
        UploadFile uploadFile = uploadFileRepository.findById(fileId).orElse(null);
        if (uploadFile == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_FILE_NOT_EXISTS);
        }

        if (kind != UploadFileKindEnum.CONTRACT_TEMPLATE && uploadFile.getAcctId() != null && !uploadFile.getAcctId().equals(acctId)) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ECONTRACT_TEMPLATE_NOT_EXISTS);
        }

        // 取得合約PDF
        GcsGetDownloadUrlReq pdfDownloadReq = GcsGetDownloadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(kind.buildFilePath(acctId))
            .isTemp(kind == UploadFileKindEnum.SIGN)
            .fileNames(Collections.singletonList(uploadFile.getFilename()))
            .minutes(minutes)
            .build();

        GcsUrlRes result = goSmartServer.getGcsDownloadUrl(pdfDownloadReq);
        if (result.getSignedUrls().isEmpty()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.GENERATE_DOWNLOAD_PRESIGNED_FAILED);
        }
        return result;
    }
}
