package com.carplus.subscribe.service;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.config.AppProperties;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.notify.MaacSubscribeNotify;
import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.carplus.subscribe.model.notify.maac.Maac;
import com.carplus.subscribe.model.notify.maac.MaacCreatorFunction;
import com.carplus.subscribe.model.notify.maac.data.*;
import com.carplus.subscribe.utils.PriceUtils;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class MaacCreatorService {

    private final Map<Class<? extends BaseDataDTO>, MaacCreatorFunction<? extends BaseDataDTO>> creatorFunctions;

    public MaacCreatorService() {
        this.creatorFunctions = new HashMap<>();
        this.creatorFunctions.put(SecurityDepositPaidDataDTO.class, this::createSecurityDepositPaidMaac);
        this.creatorFunctions.put(OpenForPayStageFeeDataDTO.class, this::createStageFeeMaac);
        this.creatorFunctions.put(CancelOrderDataDTO.class, this::createCancelOrderMaac);
        this.creatorFunctions.put(RenewCallOrderDataDTO.class, this::createRenewCallOrderMaac);
        this.creatorFunctions.put(ModifyOrderDataDTO.class, this::createModifyOrderMaac);
        this.creatorFunctions.put(RefundSuccessDataDTO.class, this::createRefundSuccessMaac);
    }

    /**
     * {@link #createSecurityDepositPaidMaac}
     * {@link #createStageFeeMaac}
     * {@link #createCancelOrderMaac}
     * {@link #createRenewCallOrderMaac}
     * {@link #createModifyOrderMaac}
     * {@link #createRefundSuccessMaac}
     */
    @SuppressWarnings("unchecked")
    public <T extends BaseDataDTO> Maac<T> createMaac(Class<T> dtoClass, Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        MaacCreatorFunction<?> maacCreatorFunction = creatorFunctions.get(dtoClass);
        if (maacCreatorFunction == null) {
            throw new UnsupportedOperationException("不支援的通知種類: " + dtoClass.getSimpleName());
        }
        return (Maac<T>) maacCreatorFunction.createMaac(order, user, subNotify);
    }

    /**
     * 訂單保證金繳付成功 : 暫時不接此PNP，直接用簡訊發送
     */
    private Maac<SecurityDepositPaidDataDTO> createSecurityDepositPaidMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        int securityDepositAmount = order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getPaidSecurityDeposit();
        String formattedAmount = PriceUtils.formatWithThousandsSeparator(securityDepositAmount);

        SecurityDepositPaidDataDTO.LinePushDTO linePushDTO = SecurityDepositPaidDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .orderId(order.getOrderNo())
                .paymentAmount(formattedAmount)
                .build();
        SecurityDepositPaidDataDTO.PnpDTO pnpDTO = SecurityDepositPaidDataDTO.PnpDTO.builder()
                .paymentAmount(formattedAmount)
                .paymentName("訂閱車保證金")
                .paymentDate(order.getSecurityDepositDate().atZone(DateUtils.ZONE_TPE).format(DateTimeFormatter.ofPattern("yyyy/MM/dd")))
                .orderId(order.getOrderNo())
                .url(AppProperties.getFullShortOfficialUrl())
                .build();
        BaseDataDTO.SmsDTO smsDTO = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        SecurityDepositPaidDataDTO data = SecurityDepositPaidDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePushDTO)
                .pnp(pnpDTO)
                .sms(smsDTO)
                .build();

        return Maac.<SecurityDepositPaidDataDTO>builder()
            .pnpSettingId(subNotify.getPnpSettingId())
            .data(data)
            .build();
    }

    /**
     * 當期款項繳費提醒
     */
    private Maac<OpenForPayStageFeeDataDTO> createStageFeeMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        String contractDate = DateUtils.toDateString(Date.from(order.getContract().getExpectEndDate()), "yyyy/MM/dd");

        OpenForPayStageFeeDataDTO.LinePushDTO linePushDTO = OpenForPayStageFeeDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .orderId(order.getOrderNo())
                .paymentExpiredDate(subNotify.getLastPayDate())
                .build();
        OpenForPayStageFeeDataDTO.PnpDTO pnpDTO = OpenForPayStageFeeDataDTO.PnpDTO.builder()
                .lastPayDate(subNotify.getLastPayDate())
                .contractDate(contractDate)
                .url(AppProperties.getFullShortOfficialUrl())
                .build();
        BaseDataDTO.SmsDTO smsDTO = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        OpenForPayStageFeeDataDTO data = OpenForPayStageFeeDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePushDTO)
                .pnp(pnpDTO)
                .sms(smsDTO)
                .build();

        return Maac.<OpenForPayStageFeeDataDTO>builder()
                .pnpSettingId(subNotify.getPnpSettingId())
                .data(data)
                .build();
    }

    /**
     * 訂單取消通知
     */
    private Maac<CancelOrderDataDTO> createCancelOrderMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        CancelOrderDataDTO.LinePushDTO linePushDTO = CancelOrderDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .orderId(order.getOrderNo())
                .build();
        CancelOrderDataDTO.PnpDTO pnpDTO = CancelOrderDataDTO.PnpDTO.builder()
                .orderId(order.getOrderNo())
                .url(AppProperties.getFullShortOfficialUrl())
                .build();
        BaseDataDTO.SmsDTO smsDTO = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        CancelOrderDataDTO data = CancelOrderDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePushDTO)
                .pnp(pnpDTO)
                .sms(smsDTO)
                .build();
        return Maac.<CancelOrderDataDTO>builder()
                .pnpSettingId(subNotify.getPnpSettingId())
                .data(data)
                .build();
    }

    /**
     * 續約提醒
     */
    private Maac<RenewCallOrderDataDTO> createRenewCallOrderMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        RenewCallOrderDataDTO.LinePushDTO linePushDTO = RenewCallOrderDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .contractExpiredDate(subNotify.getContractExpiredDate())
                .renewUrl(subNotify.getRenewUrl())
                .build();
        RenewCallOrderDataDTO.PnpDTO pnpDTO = RenewCallOrderDataDTO.PnpDTO.builder()
                .contractTime(subNotify.getContractExpiredDate())
                .url(subNotify.getRenewUrl())
                .build();
        BaseDataDTO.SmsDTO smsDTO = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        RenewCallOrderDataDTO data = RenewCallOrderDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePushDTO)
                .pnp(pnpDTO)
                .sms(smsDTO)
                .build();

        return Maac.<RenewCallOrderDataDTO>builder()
                .pnpSettingId(subNotify.getPnpSettingId())
                .data(data)
                .build();
    }

    /**
     * 訂單資訊更新
     */
    private Maac<ModifyOrderDataDTO> createModifyOrderMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {
        ModifyOrderDataDTO.LinePushDTO linePush = ModifyOrderDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .orderId(order.getOrderNo())
                .build();
        ModifyOrderDataDTO.PnpDTO pnp = ModifyOrderDataDTO.PnpDTO.builder()
                .orderId(order.getOrderNo())
                .carInfo(subNotify.getStartDate())
                .url(AppProperties.getFullShortOfficialUrl())
                .build();
        BaseDataDTO.SmsDTO base = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        ModifyOrderDataDTO data = ModifyOrderDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePush)
                .pnp(pnp)
                .sms(base)
                .build();

        return Maac.<ModifyOrderDataDTO>builder()
                .pnpSettingId(subNotify.getPnpSettingId())
                .data(data)
                .build();
    }

    /**
     * TapPay退款成功通知
     */
    private Maac<RefundSuccessDataDTO> createRefundSuccessMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify) {

        String formattedAmount = PriceUtils.formatWithThousandsSeparator(subNotify.getRefundAmount());

        RefundSuccessDataDTO.LinePushDTO linePushDTO = RefundSuccessDataDTO.LinePushDTO.builder()
                .stage(AppProperties.getStage())
                .customerName(user.getCustomerName())
                .orderId(order.getOrderNo())
                .refundAmount(formattedAmount)
                .build();
        BaseDataDTO.SmsDTO smsDTO = BaseDataDTO.SmsDTO.builder()
                .content(subNotify.getSmsContent2C())
                .build();
        RefundSuccessDataDTO data = RefundSuccessDataDTO.builder()
                .phoneNumber(user.combineNationalCodeAndPhoneNumber())
                .linePushTemplateId(subNotify.getLinePushTemplateId())
                .linePush(linePushDTO)
                .pnp(new RefundSuccessDataDTO.PnpDTO())
                .sms(smsDTO)
                .build();

        return Maac.<RefundSuccessDataDTO>builder()
                .pnpSettingId(subNotify.getPnpSettingId())
                .data(data)
                .build();
    }

}