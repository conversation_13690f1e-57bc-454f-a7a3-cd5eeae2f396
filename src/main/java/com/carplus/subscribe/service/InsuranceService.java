package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CompulsoryInsurance;
import com.carplus.subscribe.enums.ConditionOrderSource;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.feign.InsuranceClient;
import com.carplus.subscribe.model.CustomizedSearchPage;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.authority.AuthorityProperties;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.insurance.*;
import com.carplus.subscribe.model.insurance.policy.PolicyDetailResponse;
import com.carplus.subscribe.model.insurance.policy.PolicyEnum;
import com.carplus.subscribe.model.insurance.policy.PolicyListRequest;
import com.carplus.subscribe.model.insurance.policy.PolicyListResponse;
import com.carplus.subscribe.model.lrental.CarplusCompanyResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.LRENTAL_RELATIVE_COMPANY_NOT_FOUND;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.MEMBER_INFO_NOT_FOUND;

@Slf4j
@Service
public class InsuranceService {

    @Autowired
    private InsuranceClient insuranceClient;
    @Autowired
    private AuthorityProperties authorityProperties;
    @Autowired
    private CrsService crsService;
    @Autowired
    private AuthorityServer authorityServer;
    @Autowired
    private LrentalServer lrentalServer;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 建立暫存保險
     */
    public void createBatchInsurance(Orders orders, String memo, Instant licenseExpDate, String memberId, String companyId, String docNo) {
        if (hasValidArbitraryPolicy(orders.getPlateNo())) {
            return;
        }
        SubscribeInsurance subscribeInsurance = generateSubscribeInsurance(orders, memo, CompulsoryInsurance.subscribe, licenseExpDate, docNo);
        RequisitionCreateRequest request = generateInsuranceRequest(subscribeInsurance, memberId, ConditionOrderSource.Carplus);

        createBatchInsuranceInternal(licenseExpDate, memberId, companyId, request, subscribeInsurance);
    }

    /**
     * 建立暫存保險
     */
    public void createBatchInsurance(DealerOrderQueryResponse dealerOrder, String memo, Instant licenseExpDate, String memberId, String companyId) {
        if (hasValidArbitraryPolicy(dealerOrder.getPlateNo())) {
            return;
        }
        SubscribeInsurance subscribeInsurance = generateSubscribeInsurance(dealerOrder, memo, CompulsoryInsurance.subscribe, null, dealerOrder.getLrentalContractNo());
        RequisitionCreateRequest request = generateInsuranceRequest(subscribeInsurance, memberId, ConditionOrderSource.SeaLand);

        createBatchInsuranceInternal(licenseExpDate, memberId, companyId, request, subscribeInsurance);
    }

    public SubscribeInsurance generateSubscribeInsurance(Orders orders, String memo, CompulsoryInsurance ci, Instant licenseExpDate, String docNo) {
        docNo = CompulsoryInsurance.subscribe.equals(ci) ? orders.getLrentalContractNo() : Optional.ofNullable(docNo).orElse("");
        AuthUser authUser = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        Instant startDate = Optional.ofNullable(orders.getStartDate()).orElseGet(orders::getExpectStartDate);
        return generateSubscribeInsurance(orders.getPlateNo(), memo, ci, licenseExpDate, docNo, authUser, startDate);
    }

    public SubscribeInsurance generateSubscribeInsurance(DealerOrderQueryResponse dealerOrderQueryResponse, String memo, CompulsoryInsurance ci, Instant licenseExpDate, String docNo) {
        AuthUser authUser = new AuthUser();
        authUser.setLoginId(dealerOrderQueryResponse.getCustomerInfo().getIdNo());
        authUser.setAcctName(dealerOrderQueryResponse.getCustomerInfo().getUserName());
        Instant startDate = Optional.ofNullable(dealerOrderQueryResponse.getSubscriptionInfo().getDepartDate()).orElse(dealerOrderQueryResponse.getSubscriptionInfo().getExpectDepartDate());
        return generateSubscribeInsurance(dealerOrderQueryResponse.getPlateNo(), memo, ci, licenseExpDate, docNo, authUser, startDate);
    }

    private SubscribeInsurance generateSubscribeInsurance(String plateNo, String memo, CompulsoryInsurance ci, Instant licenseExpDate, String docNo, AuthUser authUser, Instant startDate) {
        SubscribeInsurance subscribeInsurance = new SubscribeInsurance();
        subscribeInsurance.setPlateNo(plateNo);
        subscribeInsurance.setDepenceDocMemo(memo);
        subscribeInsurance.setDepenceMemo(ci.getDepenceMemo());
        subscribeInsurance.setLicenseExpDate(licenseExpDate);
        subscribeInsurance.setCompulsoryInsurance(ci);
        subscribeInsurance.setDepenceDocNo(docNo);
        subscribeInsurance.setAuthUser(authUser);
        subscribeInsurance.setStartDate(startDate);
        subscribeInsurance.setDepenceDocType(ci.name());
        return subscribeInsurance;
    }

    public RequisitionCreateRequest generateInsuranceRequest(SubscribeInsurance subscribeInsurance, String memberId, ConditionOrderSource orderSource) {
        AuthorityProperties.Department authorityDepartmentConfig = authorityProperties.getDepartmentCode();
        CarBaseInfoSearchResponse carBase = crsService.getCar(subscribeInsurance.getPlateNo());
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream().findAny().orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));

        CarplusCompanyResponse carplusCompanyResponse = lrentalServer.getCompanyInfo(carBase.getCarBase().getCompanyNo()).stream()
            .findFirst().orElseThrow(() -> new SubscribeException(LRENTAL_RELATIVE_COMPANY_NOT_FOUND));

        CarplusCompanyResponse carplusHeadquartersCompanyResponse = lrentalServer.getHeadquartersCompanyInfo(carBase.getCarBase().getCompanyNo());
        if (carplusHeadquartersCompanyResponse == null) {
            throw new SubscribeException(LRENTAL_RELATIVE_COMPANY_NOT_FOUND);
        }

        ApplyInfo applyInfo = ApplyInfo.builder()
            .policyType(2)
            .policyNew(1)
            .insureFreeYear1(0)
            .requisitionStartDate(subscribeInsurance.getStartDate())
            .insureCarPrice(carBase.getCarBase().getGetPrice())
            .isTaxi(false)
            .build();

        CustomerInfo customerInfo = CustomerInfo.builder()
            .insuredId(carplusCompanyResponse.getRent())
            .insuredName(carplusCompanyResponse.getCompShortName())
            .proposerId(carplusHeadquartersCompanyResponse.getRent())
            .proposerName(carplusHeadquartersCompanyResponse.getCompShortName())
            .insureUserId(subscribeInsurance.getAuthUser().getLoginId())
            .insureUserName(subscribeInsurance.getAuthUser().getAcctName())
            .build();

        CarInfo carInfo = CarInfo.builder()
            .carStandardCode(carBase.getCarBase().getBrandCode() + carBase.getCarBase().getClassCode() + carBase.getCarBase().getStdPriceCode() + carBase.getCarBase().getCode4())
            .brandCode(carBase.getCarBase().getBrandCode())
            .classCode(carBase.getCarBase().getClassCode())
            .stdPriceCode(carBase.getCarBase().getStdPriceCode())
            .code4(carBase.getCarBase().getCode4())
            .stdPrice(carBase.getCarBase().getStdPrice())
            .classDetail(carBase.getCarSpecInfoResponse().getClassDetail())
            .carType(carBase.getCarSpecInfoResponse().getCarPlusCarType())
            .cylinder(carBase.getCarSpecInfoResponse().getCylinder())
            .seats(carBase.getCarSpecInfoResponse().getSeats())
            .carNo(carBase.getCarNo())
            .plateNo(carBase.getPlateNo())
            .publishDate(carBase.getCarBase().getPublishDate())
            .engineNo(carBase.getCarBase().getEngineNo())
            .insureAccessoriesAmount(0)
            .bodyNo(carBase.getCarBase().getBodyNo())
            .reserveLicenseDate(subscribeInsurance.getLicenseExpDate())
            .getPrice(carBase.getCarBase().getGetPrice())
            .licenseType("1")
            .carSource("1")
            .build();

        PaymentInfo paymentInfo = PaymentInfo.builder()
            .paymentDepartmentName("訂閱車業務管理課")
            .paymentDepartmentCode(authorityDepartmentConfig.getSCARM())
            .build();

        DependencyInfo dependencyInfo = DependencyInfo.builder()
            .depenceDocSystemCode("subscribe")
            .depenceDocType(subscribeInsurance.getDepenceDocType())
            .depenceDocNo(subscribeInsurance.getDepenceDocNo())
            .depenceDocMemo(subscribeInsurance.getDepenceDocMemo())
            .depenceMemo(subscribeInsurance.getDepenceMemo())
            .depenceDocUpDepartmentCode(authorityDepartmentConfig.getSCARD())
            .depenceDocUpDepartmentName("訂閱車業務部")
            .depenceDocDepartmentCode(authorityDepartmentConfig.getSCARM())
            .depenceDocDepartmentName("訂閱車業務管理課")
            .depenceDocUserID(memberInfo.getMemberId())
            .depenceDocUserName(memberInfo.getMemberName())
            .build();

        long itemPlanId = ConditionOrderSource.Carplus == orderSource
            ? crsService.searchInsurePlan(carBase)
            : "1".equals(carBase.getCarSpecInfoResponse().getCarPlusCarSeriesCode()) ? 19L : 20L;

        RequisitionCreateRequest requisitionCreateRequest = RequisitionCreateRequest.builder().applyInfo(applyInfo)
            .customerInfo(customerInfo)
            .carInfo(carInfo)
            .paymentInfo(paymentInfo)
            .depenceInfo(dependencyInfo)
            .itemPlanId(itemPlanId)
            .itemInfoList(new ArrayList<>())
            .build();

        // 設定強制險
        // 若有預計領牌日，則表示為強制險
        if (subscribeInsurance.getLicenseExpDate() != null) {
            requisitionCreateRequest.convertToCompulsoryInsurance(subscribeInsurance.getLicenseExpDate());
        }
        return requisitionCreateRequest;
    }

    private void createBatchInsuranceInternal(Instant licenseExpDate, String memberId, String companyId, RequisitionCreateRequest request, SubscribeInsurance subscribeInsurance) {
        List<RequisitionCreateRequest> requisitionCreateRequestList = new ArrayList<>();
        // 如有預計領牌日要投保強制險
        if (licenseExpDate != null) {
            request.getCarInfo().setReserveLicenseDate(licenseExpDate);
            ApplyInfo applyInfo = ApplyInfo.builder()
                .policyType(1)
                .policyNew(1)
                .insureFreeYear1(0)
                .requisitionStartDate(licenseExpDate)
                .insureCarPrice(request.getApplyInfo().getInsureCarPrice())
                .isTaxi(false)
                .build();
            RequisitionCreateRequest requisitionCreateRequest = RequisitionCreateRequest.builder().applyInfo(applyInfo)
                .customerInfo(request.getCustomerInfo())
                .carInfo(request.getCarInfo())
                .paymentInfo(request.getPaymentInfo())
                .depenceInfo(request.getDepenceInfo())
                .itemInfoList(new ArrayList<>())
                .build();
            // 設定強制險
            requisitionCreateRequest.convertToCompulsoryInsurance(licenseExpDate);
            requisitionCreateRequestList.add(requisitionCreateRequest);
        } else {
            // 如預計領牌日為空值，則 reserveLicenseDate 為車籍領牌日
            request.getCarInfo().setReserveLicenseDate(crsService.getCar(subscribeInsurance.getPlateNo()).getCarLicense().getLicenseDate().toInstant());
        }
        requisitionCreateRequestList.add(request);
        addBatchInsurance(companyId, memberId, requisitionCreateRequestList);
    }

    public void addBatchInsurance(String companyId, String memberId, List<RequisitionCreateRequest> requisitionCreateRequestList) {
        insuranceClient.addBatchInsurance(companyId, memberId, requisitionCreateRequestList);
    }

    /**
     * 以車牌號碼查詢該車輛是否已存在有效任意險保單 (不存在有效任意險保單才需要新增暫存投保資料)
     */
    public boolean hasValidArbitraryPolicy(String plateNo) {
        PolicyListRequest request = new PolicyListRequest();
        request.setIsRejectQuery(false);
        request.setPlateNo(Sets.newHashSet(plateNo));
        request.setStatus(Sets.newHashSet(PolicyEnum.Status.PAYING.getCode(), PolicyEnum.Status.VAILD.getCode()));
        request.setPolicyType(ApplyInfoEnum.PolicyType.ARBITRARILY.getCode());

        Result<CustomizedSearchPage<PolicyListResponse>> result = insuranceClient.queryPolicy(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, request);

        return result.getData().getPage().getList()
            .stream()
            .map(PolicyListResponse::getPolicyId)
            .anyMatch(policyId -> {
                Result<PolicyDetailResponse> policyDetailResult = insuranceClient.queryPolicyDetail(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, policyId);

                return Optional.ofNullable(policyDetailResult.getData())
                    .map(PolicyDetailResponse::getPaymentInfo)
                    .map(PaymentInfo::getPaymentDepartmentCode)
                    .filter(paymentDepartmentCode -> Arrays.asList(
                        CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE,
                        CarPlusConstant.SUBSCRIBE_BUSINESS_DEPT_CODE,
                        CarPlusConstant.departmentMasterCodeOld,
                        CarPlusConstant.departmentMasterCode
                    ).contains(paymentDepartmentCode))
                    .isPresent();
            });
    }
}
