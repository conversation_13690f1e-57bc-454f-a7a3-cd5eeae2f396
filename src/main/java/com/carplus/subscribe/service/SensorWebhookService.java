package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CreditRemarkType;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.credit.AutoCreditInfo;
import com.carplus.subscribe.model.sensor.SensorEvent;
import com.carplus.subscribe.model.sensor.SensorEventReq;
import com.carplus.subscribe.server.SensorWebhookServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.carplus.subscribe.enums.CreditRemarkType.MANUAL_PASS;
import static com.carplus.subscribe.enums.SensorEvent.CREDIT_RESULT;

@Service
public class SensorWebhookService {

    @Autowired
    private SensorWebhookServer sensorWebhookServer;

    @Autowired
    private CarsService carsService;

    /**
     * 傳遞授信事件至神策
     */
    @Async
    public void sendCreditEvent(Orders orders, AuthUser user) {
        SensorEvent sensorEvent = new SensorEvent();
        MainContract mainContract = orders.getContract().getMainContract();
        CarResponse carInfo = carsService.getCarInfo(mainContract.getPlateNo());
        sensorEvent.setSensorId(user.getMainCell());
        sensorEvent.setEventName(CREDIT_RESULT.getEventName());
        boolean isCreditPass = false;
        if (orders.getCreditInfo().getManualCreditInfo() != null) {
            isCreditPass = MANUAL_PASS.equals(orders.getCreditInfo().getManualCreditInfo().getCreditRemarkType());
        } else {
            AutoCreditInfo creditInfo = orders.getCreditInfo().getAutoCreditInfo().get(orders.getCreditInfo().getAutoCreditInfo().size() - 1);
            isCreditPass = CreditRemarkType.isAutoCreditPass(creditInfo.getCreditRemarkType());
        }

        Map<String, Object> params = new HashMap<>();
        params.put("order_no", orders.getOrderNo());
        params.put("parent_order_no", mainContract.getMainContractNo());
        params.put("pickup_station_id", mainContract.getDepartStationCode());
        params.put("return_station_id", mainContract.getReturnStationCode());
        params.put("sub_order_type", orders.getIsNewOrder() ? "新訂單" : "續約");
        params.put("sub_price", mainContract.getOriginalPriceInfo().getUseMonthlyFee());
        params.put("car_model", String.format("%s %s", carInfo.getCarBrand().getBrandNameEn().toLowerCase(), carInfo.getCarModel().getCarModelName().toLowerCase()));
        params.put("$is_first_time", true);
        params.put("sub_contract_period", orders.getMonth());
        params.put("is_success", isCreditPass);
        params.put("platform_name", "收銀台");
        params.put("evt_name", "訂閱車_審核結果");
        params.put("is_login", false);

        sensorEvent.setParams(params);
        sensorWebhookServer.sendEventTrackToSensor(new SensorEventReq(sensorEvent));
    }

}
