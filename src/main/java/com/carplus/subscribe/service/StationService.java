package com.carplus.subscribe.service;

import carplus.common.redis.cache.Get;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.BeanUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.StationsRepository;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.enums.StationDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.authority.*;
import com.carplus.subscribe.model.config.AdminRoleConfig;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.station.StationQueryRequest;
import com.carplus.subscribe.model.request.station.StationUpdateRequest;
import com.carplus.subscribe.model.station.*;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StationService {

    @Autowired
    private StationsRepository stationsRepository;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private ConfigService configService;

    @Autowired
    private CarsService carsService;

    @Autowired
    private EntityChangeLogService entityChangeLogService;

    @Autowired
    private GoSmartServer goSmartServer;

    public Stations findByStationCode(@NonNull String stationCode) {
        return stationsRepository.findById(stationCode).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.STATION_NOT_FOUND, stationCode));
    }

    public StationResponseWithChangeLogs findByStationCode(@Nullable AdminUser adminUser, String stationCode) {
        StationResponse response = getAllSubscribeAvailableStations(adminUser).stream()
            .filter(station -> station.getStationCode().equals(stationCode)).findFirst()
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.STATION_NOT_FOUND, stationCode));
        List<EntityChangeLog> changeLogs = entityChangeLogService.getByMainEntityAndPrimaryKey(Stations.class, stationCode);
        return new StationResponseWithChangeLogs(response, changeLogs);
    }

    public List<Stations> findAll() {
        return stationsRepository.findAll();
    }

    @NonNull
    public Map<String, Stations> getAllVisibleStation(@Nullable AdminUser admin) {
        return getAllStation(admin).stream().filter(Stations::isVisible).collect(Collectors.toMap(Stations::getStationCode, Function.identity()));
    }

    @NonNull
    public Collection<Stations> getAllStation(@Nullable AdminUser admin) {
        List<AdminRoleConfig> roles = Optional.ofNullable(admin).map(AdminUser::getRoles).orElseGet(Lists::newArrayList);
        Collection<Stations> stationList = stationsRepository.findAll();
        if (admin != null && !roles.isEmpty()) {
            boolean doCheck = roles.stream().allMatch(AdminRoleConfig::isCheckStation);
            if (doCheck) {
                // 人員所屬組織
                List<String> sourceDepartmentCodes = Lists.newArrayList();
                // 可視組織
                Set<String> visibleList = Sets.newHashSet();

                // 若該組織為站級，向上找組級單位
                for (MemberInfo memberInfo : admin.getMemberInfos()) {
                    DepartmentInfo departmentInfo = authorityServer.getDepartmentByCode(memberInfo.getCompanyCode(), memberInfo.getDepartmentCode());
                    if (LevelCode.L07 == departmentInfo.getLevelCode()) {
                        DepartmentInfo parentDepartmentInfo = authorityServer.getDepartmentByCode(memberInfo.getCompanyCode(), departmentInfo.getParentDepartmentCode());
                        if (LevelCode.L06 == parentDepartmentInfo.getLevelCode()) {
                            sourceDepartmentCodes.add(parentDepartmentInfo.getDepartmentCode());
                            continue;
                        }
                    }

                    sourceDepartmentCodes.add(memberInfo.getDepartmentCode());
                }

                for (String sourceDepartmentCode : sourceDepartmentCodes) {
                    if (visibleList.contains(sourceDepartmentCode)) {
                        continue;
                    }
                    boolean obtained = false;
                    for (DepartmentInfo departmentInfo : authorityServer.getAllDirectlyDepartments(admin.getCompanyCode(), sourceDepartmentCode)) {
                        if (!obtained) {
                            obtained = sourceDepartmentCode.equals(departmentInfo.getDepartmentCode());
                        }
                        if (obtained) {
                            visibleList.add(departmentInfo.getDepartmentCode());
                        }
                    }
                }

                for (Stations station : stationList) {
                    if (visibleList.contains(station.getNewLevelCode())) {
                        station.setVisible(true);
                        if (station.getStationCategory() != StationDefine.StationCategory.DEALER) {
                            for (Stations child : stationList) {
                                if (station.getStationCode().equals(child.getParentStationCode())) {
                                    child.setVisible(true);
                                }
                            }
                        }
                    }
                }
            } else {
                stationList.forEach(station -> station.setVisible(true));
            }
        }

        return stationList;
    }

    @Get(group = StationResponse.class, key = "'list'", ttl = 60 * 60 * 24)
    public List<StationResponse> getAllSubscribeAvailableStations() {
        return stationsRepository.findAll().stream()
            .filter(stations -> !stations.isDeleted())
            .map(stations -> BeanUtils.copyProperties(stations, new StationResponse()))
            .sorted(getComparator())
            .collect(Collectors.toList());
    }

    public List<StationResponse> getAllSubscribeAvailableStations(@Nullable AdminUser admin) {
        List<String> pausedEContractStationCodes = configService.getPauseEContractStationList();
        return getAllStation(admin).stream().filter(stations -> !stations.isDeleted())
            .map(stations -> BeanUtils.copyProperties(stations, new StationResponse()))
            .peek(s -> s.setForceOnlineRForm(!pausedEContractStationCodes.contains(s.getStationCode())))
            .sorted(getComparator())
            .collect(Collectors.toList());
    }

    public List<StationResponse> getAllSubscribeAvailableStations(AdminUser adminUser, StationQueryRequest request) {
        List<StationResponse> list = getAllSubscribeAvailableStations(adminUser);
        return list.stream().filter(s -> (request.getStationName() == null || s.getStationName().contains(request.getStationName()))
            && (request.getGeoRegion() == null || request.getGeoRegion().contains(s.getGeoRegion()))
            && (request.getLocateGeoRegion() == null || request.getLocateGeoRegion().contains(s.getLocateGeoRegion()))
            && (request.getForceOnlineRForm() == null || request.getForceOnlineRForm().contains(s.isForceOnlineRForm()))
            && (request.getCarplusService() == null || request.getCarplusService().contains(s.getCarplusService()))
            && (request.getVisible() == null || request.getVisible().contains(s.isVisible()))).collect(Collectors.toList());
    }

    /**
     * 排序 1 依照 goSmart cityId 排序
     * 排序 2 依照 carplusService 先中古後短租
     */
    private Comparator<StationResponse> getComparator() {
        List<City> cityArea = goSmartServer.getCityArea();
        Map<Integer, Integer> cityOrderMap = new HashMap<>();
        for (int index = 0; index < cityArea.size(); index++) {
            cityOrderMap.put(cityArea.get(index).getCityId(), index);
        }

        return Comparator.comparing((StationResponse station) -> cityOrderMap.get(station.getCityId()), Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(StationResponse::getCarplusService, Comparator.comparing(service -> StationDefine.CarplusService.PREOWNED == service ? 0 : 1));
    }

    @Get(group = SealandStationResponse.class, key = "'list'", ttl = 60 * 60 * 24)
    public List<SealandStationResponse> getSealandStations() {
        List<String> activeStations = configService.getSeaLandStationList();
        return stationsRepository.findAll().stream().filter(stations -> !Objects.equals(stations.getStationCategory(), StationDefine.StationCategory.DEALER))
            .map(stations -> new SealandStationResponse(stations.getStationCode(), stations.getStationName(), stations.getStatus(), activeStations.contains(stations.getStationCode()))).collect(Collectors.toList());
    }

    /**
     * 修改站點資訊
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateStation(StationUpdateRequest request, String memberId) {

        Stations station = findByStationCode(request.getStationCode());

        if (Objects.isNull(station)) {
            throw new BadRequestException(String.format("[站點不存在, 代碼: {%s}]", request.getStationCode()));
        }

        if (request.getForceOnlineRForm() != null) {
            configService.setPauseEContractStation(request.getStationCode(), !request.getForceOnlineRForm());
        }

        Optional.ofNullable(request.getStationCategory()).ifPresent(station::setStationCategory);
        Optional.ofNullable(request.getIsSubscribe()).ifPresent(station::setIsSubscribe);
        Optional.ofNullable(request.getStatus()).ifPresent(station::setStatus);
        stationsRepository.save(station);
    }

    public Map<String, Stations> getStationsMap() {
        return findAll().stream().collect(Collectors.toMap(Stations::getStationCode, station -> station));
    }

    public Map<String, String> getStationsNameCodeMap() {
        return findAll().stream().collect(Collectors.toMap(Stations::getStationName, Stations::getStationCode));
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public Map<String, List<Stations>> getActiveStationMap() {
        Set<StationDefine.StationCategory> excludeCategories = Sets.newHashSet(
            StationDefine.StationCategory.VIRTUAL,
            StationDefine.StationCategory.COURIER,
            StationDefine.StationCategory.DEALER
        );

        return findAll().stream()
            .filter(stations -> !stations.isDeleted()
                && !excludeCategories.contains(stations.getStationCategory())
                && !"D".equals(stations.getStatus()))
            .collect(Collectors.groupingBy(Stations::getGeoRegion));
    }

    private CarAreaInfo buildCarAreaInfo(Map.Entry<String, List<Stations>> regionStationsEntry, Function<Stations, CarLocationInfo> carLocationInfoMapper) {
        CarAreaInfo carAreaInfo = new CarAreaInfo();
        GeoDefine.GeoRegion geoRegion = GeoDefine.GeoRegion.valueOf(regionStationsEntry.getKey());
        carAreaInfo.setName(geoRegion.getName());
        carAreaInfo.setCode(geoRegion.name());
        carAreaInfo.setCodeNew(geoRegion.getAreaIdNew());
        carAreaInfo.setUnderCarInfo(regionStationsEntry.getValue().stream()
            .map(carLocationInfoMapper)
            .collect(Collectors.toList()));
        return carAreaInfo;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public List<CarAreaInfo> getCarAreaInfosForCrs() {
        return getActiveStationMap().entrySet().stream()
            .map(regionStationsEntry -> buildCarAreaInfo(regionStationsEntry, this::mapToCarLocationInfo))
            .collect(Collectors.toList());
    }

    private CarLocationInfo mapToCarLocationInfo(Stations station) {
        CarLocationInfo carLocationInfo = new CarLocationInfo();
        carLocationInfo.setCode(station.getStationCode());
        carLocationInfo.setName(station.getStationName());
        carLocationInfo.setCodeNew(CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE);
        carLocationInfo.setVatNo(CarPlusConstant.SUB_VIRTUAL_VAT_NO);
        return carLocationInfo;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public CarAreaInfoListWithCount getCarCountForCrs() {
        Map<String, List<Stations>> stationMap = getActiveStationMap();
        List<String> stationCodeList = stationMap.values().stream()
            .flatMap(List::stream)
            .map(Stations::getStationCode)
            .collect(Collectors.toList());
        Map<String, Integer> carCountMap = carsService.countByStationCode(stationCodeList);

        List<CarAreaInfo> carAreaInfoList = stationMap.entrySet().stream()
            .map(entry -> buildCarAreaInfo(entry, station -> mapToCarLocationInfoWithCount(station, carCountMap)))
            .peek(carAreaInfo -> carAreaInfo.setCount(carAreaInfo.getUnderCarInfo().stream().mapToInt(CarLocationInfo::getCount).sum()))
            .collect(Collectors.toList());

        int totalCount = carAreaInfoList.stream().mapToInt(CarAreaInfo::getCount).sum();
        return new CarAreaInfoListWithCount(totalCount, carAreaInfoList);
    }

    private CarLocationInfo mapToCarLocationInfoWithCount(Stations station, Map<String, Integer> carCountMap) {
        CarLocationInfo carLocationInfo = mapToCarLocationInfo(station);
        carLocationInfo.setCount(carCountMap.getOrDefault(station.getStationCode(), 0));
        return carLocationInfo;
    }
}
