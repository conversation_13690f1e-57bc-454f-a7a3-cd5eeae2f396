package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.dao.ContractRepository;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.dto.MainContractOrderInfoDTO;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.CompanyDriver;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.order.*;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.request.contract.CarReadyUpdateRequest;
import com.carplus.subscribe.model.request.contract.ContractCreateReq;
import com.carplus.subscribe.model.request.contract.ContractCriteria;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.utils.OrderUtils;
import com.google.common.collect.Lists;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.OrderStatus.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.DEPART_ORDER_NOT_FOUND;

@Service
public class ContractService {

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private MainContractRepository mainContractRepository;
    @Lazy
    @Autowired
    private EContractService econtractService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    @Lazy
    private PriceInfoService priceInfoService;
    @Autowired
    private CarModelService carModelService;
    @Autowired
    private CarBrandService carBrandService;
    @Autowired
    private StationService stationService;
    @Autowired
    private ContractLogic contractLogic;

    @Value("${gcs.url}")
    private String gcsUrl;

    public List<Contract> findAll() {
        return contractRepository.findAll();
    }

    /**
     * 建立合約與訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Contract createContractAndOrders(Contract contract, ContractCreateReq req, Integer acctId, @Nullable MemberInfo memberInfo) {
        mainContractRepository.saveAndFlush(contract.getMainContract());
        contract = contractRepository.saveAndFlush(contract);
        contract = contractRepository.getContractOrders(contract.getContractNo());
        orderService.createOrderFromContract(contract, req.getInvoice(), req.getRemark(), req.getMonth(), false,
            acctId, memberInfo, req.getYesChargingPoint(), req.getMerchList(), req.getCustRemark(), req.getOrderPlatform());
        return contract;
    }

    /**
     * 產生主合約號碼
     */
    @Lock(LockModeType.READ)
    public String generateMContractNo() {
        String lastNo = mainContractRepository.getLastMainContractNo();
        String date = DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE);
        String no = "";
        if (lastNo == null || lastNo.substring(1, 9).compareTo(date) < 0) {
            no = "U" + date + "00001";
        } else {
            no = "U" + (Long.parseLong(lastNo.substring(1)) + 1);
        }
        return no;
    }

    /**
     * 產生合約號碼
     */
    @Lock(LockModeType.READ)
    public String generateContractNo() {
        String lastNo = contractRepository.getLastContractNo();
        String date = DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE);
        String no = "";
        if (lastNo == null || lastNo.substring(1, 9).compareTo(date) < 0) {
            no = "C" + date + "00001";
        } else {
            no = "C" + (Long.parseLong(lastNo.substring(1)) + 1);
        }
        return no;
    }

    /**
     * 查詢使用者主合約
     */
    public MainContract getMainContractById(int acctId, @NonNull String mainContractNo) {
        return mainContractRepository.getMainContractById(acctId, mainContractNo);
    }

    /**
     * 查詢使用者所有主合約
     */
    public List<MainContract> getMainContractsByAcctId(int acctId) {
        return mainContractRepository.getMainContractByAcctId(acctId);
    }

    /**
     * 查詢使用者所有主合約
     */
    public MainContract getUserMainContractById(int acctId, String mainContractNo) {
        return mainContractRepository.getMainContractByAcctId(acctId, mainContractNo);
    }

    /**
     * 編號查詢主合約
     */
    public MainContract getMainContractByNo(@NonNull String mainContractNo) {
        return mainContractRepository.getMainContractByNo(mainContractNo, false);
    }

    public Page<MainContract> searchPages(PageRequest pageRequest, ContractCriteria contractCriteria) {
        long total = mainContractRepository.count(contractCriteria);
        if (total == 0) {
            return Page.of(total, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }
        List<MainContract> mainContractList = mainContractRepository.search(contractCriteria, pageRequest.getLimit(), pageRequest.getSkip());
        return Page.of(total, mainContractList, pageRequest.getSkip(), pageRequest.getLimit());
    }

    /**
     * 官網主約訂單搜尋
     */
    public Page<MainContractResponse> commonSearchPage(PageRequest pageRequest, ContractCriteria contractCriteria) {
        long total = mainContractRepository.count(contractCriteria);
        if (total == 0) {
            return Page.of(total, Collections.emptyList(), pageRequest.getSkip(), pageRequest.getLimit());
        }
        List<MainContract> mainContractList = mainContractRepository.search(contractCriteria, pageRequest.getLimit(), pageRequest.getSkip());
        Map<String, CarBrand> brandMap = carBrandService.getAllCarBrands().stream().collect(Collectors.toMap(CarBrand::getBrandCode, carBrand -> carBrand));
        Map<String, CarModel> carModels = carModelService.findByCarModelCodes(mainContractList.stream().map(MainContract::getCarModelCode).collect(Collectors.toList()))
            .stream().peek(carModel -> carModel.setCarBrand(brandMap.get(carModel.getBrandCode()))).collect(Collectors.toMap(CarModel::getCarModelCode, model -> model));
        Map<String, List<CarModelImage>> carModelImages = carModelService.findByCarModelImages(new ArrayList<>(carModels.keySet()))
            .stream().peek(carModelImage -> carModelImage.setPaths(carModelImage.getPaths().stream()
                .map(path -> {
                    if (!path.startsWith("http")) {
                        path = gcsUrl + path;
                    }
                    return path;
                }).collect(Collectors.toList()))
            ).collect(Collectors.groupingBy(CarModelImage::getCarModelCode));
        Map<String, Stations> stationsMap = stationService.getStationsMap();
        boolean isBlacklisted = authServer.isBlacklistedOrSubSuspended(contractCriteria.getAcctId());
        List<MainContractResponse> mainContractResponses = mainContractList.stream().map(mainContract -> {
            MainContractResponse mainContractResponse = new MainContractResponse();
            BeanUtils.copyProperties(mainContract, mainContractResponse);
            mainContractResponse.setCarModel(carModels.get(mainContractResponse.getCarModelCode()));
            mainContractResponse.setCarModelImages(carModelImages.get(mainContractResponse.getCarModelCode()));
            mainContractResponse.setDepartStationName(Optional.ofNullable(stationsMap.get(mainContractResponse.getDepartStationCode())).map(Stations::getStationName).orElse(null));
            mainContractResponse.setReturnStationName(Optional.ofNullable(stationsMap.get(mainContractResponse.getReturnStationCode())).map(Stations::getStationName).orElse(null));
            mainContractResponse.setIsRenewable(contractLogic.publicIsRenewable(mainContract, isBlacklisted));
            if (contractCriteria.isDetail()) {
                mainContractResponse.setContracts(mainContract.getContracts().stream().map(contract -> {
                    ContractInfo contractInfo = new ContractInfo();
                    BeanUtils.copyProperties(contract, contractInfo);
                    contractInfo.setMainContract(null);
                    contractInfo.setOrders(null);

                    contractInfo.setOrderResponses(contract.getOrders().stream().map(orders -> {
                        OrderResponse orderResponse = new OrderResponse();
                        BeanUtils.copyProperties(orders, orderResponse);
                        CommonOrderPriceInfoResponse priceInfoResponse = priceInfoService.generatePriceInfoResponse(orders);
                        if (priceInfoResponse.getAmount() > 0 && OrderStatus.of(orders.getStatus()).isNeedPay()) {
                            mainContractResponse.setNeedPaid(true);
                        }
                        orderResponse.setPriceInfoResponse(priceInfoResponse);
                        orderResponse.setContract(null);
                        orderResponse.setIsRenewable(contractLogic.publicIsRenewable(orders, isBlacklisted));
                        return orderResponse;
                    }).collect(Collectors.toList()));
                    contractInfo.getOrderResponses().sort(Comparator.comparing(OrderResponse::getExpectStartDate).thenComparing(OrderResponse::getInstantCreateDate).reversed());
                    return contractInfo;
                }).collect(Collectors.toList()));
                mainContractResponse.getContracts().sort(Comparator.comparing(Contract::getContractNo).reversed());
            } else {
                mainContractResponse.setNeedPaid(priceInfoService.getUserUnPaidPriceInfo(mainContract.getAcctId(), mainContract.getMainContractNo()).stream().mapToInt(PriceInfoInterface::getActualPrice).sum() > 0);
            }
            return mainContractResponse;
        }).collect(Collectors.toList());
        return Page.of(total, mainContractResponses, pageRequest.getSkip(), pageRequest.getLimit());

    }

    public Page<MainContractV2Response> userSearchPage(PageRequest pageRequest, Integer acctId, List<ContractStatus> contractStatuses) {
        ContractCriteria contractCriteria = new ContractCriteria();
        contractCriteria.setContractStatuses(contractStatuses);
        contractCriteria.setAcctId(acctId);
        contractCriteria.setDetail(true);
        Page<MainContractResponse> page = commonSearchPage(pageRequest, contractCriteria);
        List<MainContractV2Response> result = page.getList().stream().map(MainContractV2Response::new).collect(Collectors.toList());
        return Page.of(page.getTotal(), result, page.getSkip(), page.getLimit());
    }

    public MainContractV2Response getUserMainContract(String mainContract, Integer acctId) {
        ContractCriteria contractCriteria = new ContractCriteria();
        contractCriteria.setMainContractNo(mainContract);
        contractCriteria.setAcctId(acctId);
        contractCriteria.setDetail(true);
        Page<MainContractResponse> page = commonSearchPage(new PageRequest(1, 0), contractCriteria);
        List<MainContractV2Response> result = page.getList().stream().map(MainContractV2Response::new).collect(Collectors.toList());
        return result.isEmpty() ? null : result.get(0);
    }

    /**
     * 更新主合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public MainContract updateMainContract(MainContract mainContract) {
        return mainContractRepository.save(mainContract);
    }

    /**
     * 保證金付款過期
     */
    @Transactional(transactionManager = "mysqlTransactionManager", propagation = Propagation.REQUIRES_NEW)
    public void paySecurityDepositTimeout(MainContract mainContract) {
        Contract contract = mainContract.getContracts().get(0);
        contract.setStatus(ContractStatus.CANCEL.getCode());
        cancelContract(contract);
        List<Orders> ordersList = orderService.getOrdersByContractNo(contract.getContractNo());
        Orders orders = ordersList.get(0);
        orders.setCancelDate(Instant.now());
        orders.setCancelMemo(SubscribeHttpExceptionCode.SECURITY_DEPOSIT_PAY_TIMEOUT.getMsg());
        orderService.cancelOrder(orders);
        cancelMainContract(orders.getContract().getMainContract());
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(ordersList.get(0).getOrderNo());
        orderPriceInfoList.forEach(orderPriceInfo -> priceInfoService.refundAll(orderPriceInfo.getId(), PriceInfoDefinition.PriceInfoCategory.CancelBooking));
    }

    /**
     * 取消主合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public MainContract cancelMainContract(MainContract mainContract) {

        if (mainContract.getStatus() == ContractStatus.GOING.getCode()) {
            List<Contract> contractList = new ArrayList<>();
            if (!Hibernate.isInitialized(mainContract.getContracts())) {
                contractList = contractRepository.getContractsByMainContractNo(mainContract.getMainContractNo());
            } else {
                contractList = mainContract.getContracts();
            }
            Contract contract = contractList.stream().filter(c -> c.getStatus() == ContractStatus.GOING.getCode()).findAny().orElse(null);
            if (contract != null) {
                mainContract.setExpectEndDate(contract.getExpectEndDate());
            } else {
                mainContract.setStatus(ContractStatus.COMPLETE.getCode());
            }
        } else {
            mainContract.setStatus(ContractStatus.CANCEL.getCode());
        }
        return mainContractRepository.save(mainContract);
    }


    /**
     * 更新合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Contract updateContract(Contract contract) {
        return contractRepository.save(contract);
    }

    /**
     * 取消合約
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Contract cancelContract(Contract contract) {

        if (contract.getStatus() == ContractStatus.CREATE.getCode()) {
            contract.setStage(Math.abs(contract.getStage()) * -1);
            contract.setStatus(ContractStatus.CANCEL.getCode());
        } else if (contract.getStatus() == ContractStatus.GOING.getCode()) {
            List<Orders> ordersList = orderService.getOrdersByContractNo(contract.getContractNo());
            Orders order = ordersList.stream().filter(orders -> orders.getStartDate() != null).max(Comparator.comparing(Orders::getStartDate))
                .orElseThrow(() -> new SubscribeException(DEPART_ORDER_NOT_FOUND, contract.getContractNo()));
            contract.setExpectEndDate(order.getExpectEndDate());
            if (ordersList.stream().noneMatch(o -> o.getStatus() < ARRIVE_NO_CLOSE.getStatus())) {
                contract.setStatus(ContractStatus.COMPLETE.getCode());
            }
        }
        return contractRepository.save(contract);
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateCarReady(String mainContractNo, CarReadyUpdateRequest request) {
        MainContract mainContract = Optional.ofNullable(getMainContractByNo(mainContractNo)).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.MAIN_CONTRACT_IS_EMPTY));
        mainContract.setCarReady(request.getCarReady());
        updateMainContract(mainContract);
    }

    /**
     * 設定法人承租人
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateCompanyDriver(String mainContractNo, CompanyDriver companyDriver) {
        MainContract mainContract = Optional.ofNullable(getMainContractByNo(mainContractNo)).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.MAIN_CONTRACT_IS_EMPTY));
        mainContract.setCompanyDriver(companyDriver);
        updateMainContract(mainContract);
    }

    public Contract getContractByContractNo(String contractNo) {
        Contract queryContract = new Contract();
        queryContract.setContractNo(contractNo);
        queryContract.setDisclaimer(null);
        queryContract.setPremium(null);
        queryContract.setReplacement(null);
        Example example = Example.of(queryContract);
        Optional optional = contractRepository.findOne(example);
        if (optional.isPresent()) {
            return (Contract) optional.get();
        }
        return null;
    }

    /**
     * 拿取合約與其底
     */
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public Contract getContractAndOrdersByContractNo(String contractNo) {
        Contract contract = contractRepository.getContractOrders(contractNo);
        if (contract == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.CONTRACT_NOT_FOUND, contractNo);
        }
        return contract;
    }


    /**
     * 拿取主約與合約與訂單
     */
    public MainContract getMainContractAndContractAndOrdersByMainContractNo(String mainContractNo) {
        return mainContractRepository.getMainContractByNo(mainContractNo, true);
    }

    /**
     * 拿取主約與合約與訂單並排序
     */
    public MainContract getMainContractAndContractAndOrdersByMainContractNoAndSort(String mainContractNo) {
        MainContract mainContract = mainContractRepository.getMainContractByNo(mainContractNo, true);
        mainContract.getContracts().stream()
            .map(Contract::getOrders)
            .forEach(orders -> {
                orders.forEach(OrderUtils::sortRemarks);
                orders.sort(Comparator.comparing(Orders::getExpectStartDate).thenComparing(Orders::getInstantCreateDate).reversed());
            });
        mainContract.getContracts().sort(Comparator.comparing(Contract::getExpectStartDate).thenComparing(Contract::getInstantCreateDate).reversed());
        return mainContract;
    }

    /**
     * 拿取收銀台主約與合約與訂單與電子合約
     */
    public MainContractCashierResponse getCashierMainContract(String mainContractNo) {
        MainContract mainContract = mainContractRepository.getMainContractByNo(mainContractNo, true);
        AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());
        MainContractCashierResponse mv2 = BeanUtils.copyProperties(mainContract, new MainContractCashierResponse());
        HashMap<String, MemberInfo> userMap = new HashMap<>();
        mainContract.getContracts().forEach(c -> c.getOrders().sort(Comparator.comparing(Orders::getExpectStartDate).thenComparing(Orders::getInstantCreateDate).reversed()));
        mainContract.getContracts().sort(Comparator.comparing(Contract::getExpectStartDate).thenComparing(Contract::getInstantCreateDate).reversed());
        mv2.setContractList(mainContract.getContracts().stream().map(contract -> {
            ContractResponse contractResponse = BeanUtils.copyProperties(contract, new ContractResponse());
            List<EContractResponse> eContractList = new ArrayList<>();
            List<UploadFile> uploadFileList = econtractService.getEContractFiles(contract.getContractNo());
            uploadFileList.forEach(uploadFile -> {
                EContractResponse eContract = new EContractResponse();
                eContract.setId(uploadFile.getId());
                eContract.setName(uploadFile.getDisplayFilename());
                eContract.setCreateDate(uploadFile.getInstantCreateDate());
                eContract.setUpdateDate(uploadFile.getInstantUpdateDate());
                eContract.setUrlPath(uploadFile.getPath());
                eContract.setUserName(authUser.getAcctName());
                if (Optional.ofNullable(uploadFile.getCreateUser()).filter(user -> user.startsWith("K")).isPresent()) {
                    eContract.setMemberName(Optional.ofNullable(getMember(uploadFile.getCreateUser(), userMap)).map(MemberInfo::getMemberName).orElse(""));
                }

                eContractList.add(eContract);
            });
            contractResponse.setEcontractList(eContractList);
            return contractResponse;
        }).collect(Collectors.toList()));
        return mv2;
    }

    private MemberInfo getMember(String memberId, HashMap<String, MemberInfo> userMap) {
        MemberInfo user = userMap.get(memberId);
        if (user == null) {
            Optional<MemberInfo> optional = authorityServer.getMemberInfos(memberId).stream().findAny();
            if (optional.isPresent()) {
                user = optional.get();
                userMap.put(memberId, user);
            }
        }
        return user;
    }

    /**
     * 取得主合約下所有合約
     */
    public List<Contract> getContractByMainContract(String mainContractNo) {
        return contractRepository.getContractsByMainContractNo(mainContractNo);
    }

    /**
     * 取得多個主約
     */
    public List<MainContract> getMainContracts(List<String> mainContractNos) {
        return mainContractRepository.findAllById(mainContractNos);
    }

    /**
     * 取得該車牌過往所有主約、Contract、Order部分資訊 <br/>
     * 無車牌，則取得所有主約及主約對應所有Contract、Order部分資訊 <br/>
     * Default: 取得 MainContract Status NOT IN (99)/IN (0,1,10) && Order Status IN (80,89,90) 的 所有主約及主約對應所有Contract、Order部分資訊
     */
    public List<MainContractOrderInfoDTO> getMainContractOrderInfoByPlateNo(@Nullable String plateNo) {
        return getMainContractOrderInfoByPlateNo(plateNo,
            Lists.newArrayList(PROCESSING.getStatus(), CREDIT_PENDING.getStatus(), BOOKING.getStatus()),
            Lists.newArrayList(ARRIVE_NO_CLOSE.getStatus(), CLOSE_WITH_SUB.getStatus(), CLOSE.getStatus()));
    }

    public List<MainContractOrderInfoDTO> getMainContractOrderInfoByPlateNo(@Nullable String plateNo,
                                                                            @Nullable List<Integer> mainContractStatus,
                                                                            @Nullable List<Integer> orderStatus) {
        return mainContractRepository.getMainContractByPlateNo(plateNo, mainContractStatus, orderStatus);
    }
}
