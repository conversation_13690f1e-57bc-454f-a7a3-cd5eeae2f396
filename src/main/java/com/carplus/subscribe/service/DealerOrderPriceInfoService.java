package com.carplus.subscribe.service;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.dao.DealerOrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.TransactionItem;
import com.carplus.subscribe.model.order.DealerOrderPriceInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.springframework.beans.BeanUtils.copyProperties;

@Service
@Slf4j
public class DealerOrderPriceInfoService {

    @Autowired
    private DealerOrderPriceInfoRepository dealerOrderPriceInfoRepository;

    /**
     * 新增經銷商訂單費用資訊
     */
    public DealerOrderPriceInfo createDealerOrderPriceInfo(DealerOrderPriceInfoRequest request) {
        validateDate(request);
        DealerOrderPriceInfo dealerOrderPriceInfo = new DealerOrderPriceInfo();
        copyProperties(request, dealerOrderPriceInfo, carplus.common.utils.BeanUtils.ignorePropertyNames(request));
        return dealerOrderPriceInfoRepository.save(dealerOrderPriceInfo);
    }

    /**
     * 驗證資料
     */
    private void validateDate(DealerOrderPriceInfoRequest request) {
        try {
            PayFor.valueOf(request.getPayFor());
        } catch (IllegalArgumentException e) {
            throw new BadRequestException("費用目的資料錯誤!");
        }

        boolean isTransactionItem = Arrays.stream(TransactionItem.values())
                .map(TransactionItem::getCode)
                .anyMatch(value -> value.equals(request.getTransactionItem()));

        if (!isTransactionItem) {
            throw new BadRequestException("交易項目資料錯誤!");
        }

    }

    public List<DealerOrderPriceInfo> getDealerOrderPriceInfoByOrderNo(String orderNo) {
        return dealerOrderPriceInfoRepository.findByOrderNo(orderNo);
    }

    Optional<DealerOrderPriceInfo> getEtagPriceInfo(String orderNo) {
        return getDealerOrderPriceInfoByOrderNo(orderNo).stream()
            .filter(dealerOrderPriceInfo -> PayFor.ETag.getName().equals(dealerOrderPriceInfo.getPayFor()))
            .findFirst();
    }

    public void addOrUpdate(DealerOrderPriceInfo dealerOrderPriceInfo) {
        dealerOrderPriceInfoRepository.save(dealerOrderPriceInfo);
    }
}
