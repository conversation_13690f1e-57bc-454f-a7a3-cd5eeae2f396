package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.dao.EntityChangeLogRepository;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.change.EntityBase;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.db.mysql.entity.change.RequestInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class EntityLogger {

    @Autowired
    private EntityChangeLogRepository entityChangeLogRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Async
    public void logEntities(RequestInfo requestInfo, Class<?> mainEntity, Map<String, List<EntityBase>> entities) {
        String mainEntityName = mainEntity.getSimpleName();

        // 檢查是否只有 Cars 實體類型的資料
        String carsName = Cars.class.getSimpleName();
        boolean onlyCarsEntities = entities.size() == 1 && entities.containsKey(carsName);

        if (onlyCarsEntities) {
            List<? extends EntityBase> carsEntities = entities.get(carsName);
            if (carsEntities != null && !carsEntities.isEmpty()) {
                // 收集所有需要插入的 EntityChangeLog
                List<EntityChangeLog> logs = carsEntities.stream().map(carEntity -> {
                    String mainEntityId = carEntity.getEntityId();
                    List<? extends EntityBase> changes = Collections.singletonList(carEntity);

                    EntityChangeLog entityChangeLog = createEntityLog(requestInfo, mainEntityName, mainEntityId);
                    entityChangeLog.setChanges(changes);
                    return entityChangeLog;
                }).collect(Collectors.toList());

                // 批量保存
                entityChangeLogRepository.saveAll(logs);
            }
        } else {
            // 原本的邏輯，處理多實體類型的情況
            List<? extends EntityBase> mainEntityChanges = entities.get(mainEntityName);
            // mainEntityChanges == null 代表 mainEntity 沒有異動，須嘗試從 requestInfo 中取得 mainEntityId (備案)
            String mainEntityId = mainEntityChanges == null && requestInfo.getMainEntityId() != null ? requestInfo.getMainEntityId() : Objects.requireNonNull(mainEntityChanges).get(0).getEntityId();
            EntityChangeLog entityChangeLog = createEntityLog(requestInfo, mainEntityName, mainEntityId);

            List<? extends EntityBase> allChanges = entities.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());

            entityChangeLog.setChanges(allChanges);

            entityChangeLogRepository.save(entityChangeLog);
        }
    }

    private EntityChangeLog createEntityLog(RequestInfo requestInfo, String mainEntityName, String mainEntityId) {
        EntityChangeLog log = new EntityChangeLog();
        log.setRequestUrl(requestInfo.getRequestUrl());
        log.setRequestMethod(requestInfo.getRequestMethod());
        log.setChangedBy(requestInfo.getChangedBy());
        log.setRequestBody(Optional.ofNullable(requestInfo.getRequestBody())
            .map(obj -> {
                try {
                    return objectMapper.writeValueAsString(obj);
                } catch (JsonProcessingException e) {
                    return null;
                }
            })
            .orElse(null));
        log.setMainEntityName(mainEntityName);
        log.setMainEntityId(mainEntityId);
        return log;
    }
}
