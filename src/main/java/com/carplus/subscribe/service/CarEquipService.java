package com.carplus.subscribe.service;

import carplus.common.redis.cache.Get;
import com.carplus.subscribe.db.mysql.dao.CarEquipRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarEquip;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CarEquipService {

    @Autowired
    private CarEquipRepository carEquipRepository;

    @NonNull
    @Get(group = CarEquip.class, key = "'list'", ttl = 60 * 60 * 24)
    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarEquip> findAllCarEquips(@Nullable Boolean isShow) {
        return carEquipRepository.findAll()
            .stream()
            .filter(carEquip -> Optional.ofNullable(isShow)
                .map(b -> b ? carEquip.getIsShow() == 1 : carEquip.getIsShow() == 0)
                .orElse(true))
            .collect(Collectors.toList());
    }
}
