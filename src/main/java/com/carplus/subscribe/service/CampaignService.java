package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.CampaignRepository;
import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarRegistration;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.gosmart.BannerCategory;
import com.carplus.subscribe.model.request.campaign.CampaignCriteria;
import com.carplus.subscribe.model.request.campaign.CampaignRequest;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.carplus.subscribe.model.request.campaign.CommonCampaignCriteria;
import com.carplus.subscribe.model.response.campaign.CampaignCommonBaseResponse;
import com.carplus.subscribe.model.response.campaign.CampaignCommonDetailResponse;
import com.carplus.subscribe.model.response.campaign.CampaignInternalBaseResponse;
import com.carplus.subscribe.model.response.campaign.CampaignInternalDetailResponse;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.GoSmartServer;
import org.apache.commons.collections4.CollectionUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
public class CampaignService {

    @Autowired
    private CampaignRepository campaignRepository;

    @Autowired
    private CarBrandService carBrandService;

    @Autowired
    private CarModelService carModelService;

    @Autowired
    private CarTagService carTagService;

    @Autowired
    private CarRegistrationService carRegistrationService;

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private AuthorityServer authorityServer;

    public Campaign create(CampaignRequest request, String memberId) {
        Campaign campaign = new Campaign();
        buildCampaign(campaign, request, memberId);
        return campaignRepository.save(campaign);
    }

    private void buildCampaign(Campaign campaign, CampaignRequest request, String memberId) {
        campaign.setTitle(request.getTitle());
        campaign.setDescription(request.getDescription());

        // 驗證車輛顯示條件
        CarsCondition carsCondition = request.getCarsCondition();
        validateCarsCondition(carsCondition);
        campaign.setCarsCondition(carsCondition);

        // 驗證橫幅類別並取得
        Integer bannerCategoryId = request.getBannerCategoryId();
        BannerCategory bannerCategory = Optional.ofNullable(goSmartServer.getBannerCategory(bannerCategoryId))
            .orElseThrow(() -> new SubscribeException(HttpStatus.OK, BANNER_CATEGORY_NOT_FOUND, String.format("%s bannerCategoryId: %d", BANNER_CATEGORY_NOT_FOUND.getMsg(), bannerCategoryId)));
        campaign.setBannerCategoryId(bannerCategory.getBannerCategoryId());

        campaign.setStartDate(request.getStartDate());
        campaign.setEndDate(request.getEndDate());

        setUpdater(campaign, memberId);
    }

    private void buildCampaign(Campaign campaign, CampaignRequest request) {
        campaign.setTitle(request.getTitle());
        String cleanDescription = Jsoup.clean(request.getDescription(), Safelist.basic());
        campaign.setDescription(cleanDescription);

        // 驗證車輛顯示條件
        CarsCondition carsCondition = request.getCarsCondition();
        validateCarsCondition(carsCondition);
        campaign.setCarsCondition(carsCondition);

        // 驗證橫幅類別並取得
        Integer bannerCategoryId = request.getBannerCategoryId();
        BannerCategory bannerCategory = Optional.ofNullable(goSmartServer.getBannerCategory(bannerCategoryId))
            .orElseThrow(() -> new SubscribeException(HttpStatus.OK, BANNER_CATEGORY_NOT_FOUND, String.format("%s bannerCategoryId: %d", BANNER_CATEGORY_NOT_FOUND.getMsg(), bannerCategoryId)));
        campaign.setBannerCategoryId(bannerCategory.getBannerCategoryId());

        campaign.setStartDate(request.getStartDate());
        campaign.setEndDate(request.getEndDate());
    }

    private void validateCarsCondition(CarsCondition carsCondition) {
        if (StringUtils.isNotBlank(carsCondition.getGeoRegion()) && GeoDefine.GeoRegion.stringToEnum(carsCondition.getGeoRegion()) == null) {
            throw new SubscribeException(INVALID_GEO_REGION);
        }
        if (CollectionUtils.isNotEmpty(carsCondition.getBrandCode())) {
            carBrandService.validateBrandCodes(carsCondition.getBrandCode());
        }
        if (CollectionUtils.isNotEmpty(carsCondition.getCarModelCode())) {
            List<String> distinctCarModelCodes = carsCondition.getCarModelCode().stream().distinct().collect(Collectors.toList());
            List<String> dbCarModelCodes = carModelService.findByCarModelCodes(distinctCarModelCodes).stream()
                .map(CarModel::getCarModelCode).collect(Collectors.toList());
            List<String> notFoundCarModelCodes = distinctCarModelCodes.stream().filter(code -> !dbCarModelCodes.contains(code)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFoundCarModelCodes)) {
                throw new SubscribeException(HttpStatus.OK, CAR_MODEL_NOT_FOUND, String.format("車型代碼不存在, 車型: %s", notFoundCarModelCodes));
            }
            carsCondition.setCarModelCode(distinctCarModelCodes);
        }
        if (CollectionUtils.isNotEmpty(carsCondition.getTagIds())) {
            List<Integer> distinctCarTagIds = carsCondition.getTagIds().stream().distinct().collect(Collectors.toList());
            List<Integer> dbCarTagIds = carTagService.findAllCarTags(true).stream()
                .map(CarTag::getTagId).collect(Collectors.toList());
            List<Integer> notFoundCarTagIds = distinctCarTagIds.stream().filter(tag -> !dbCarTagIds.contains(tag)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFoundCarTagIds)) {
                throw new SubscribeException(HttpStatus.OK, CAR_TAG_NOT_FOUND, String.format("標籤代碼不存在, 標籤: %s", notFoundCarTagIds));
            }
            carsCondition.setTagIds(distinctCarTagIds);
        }
        if (CollectionUtils.isNotEmpty(carsCondition.getVatNo())) {
            List<String> distinctVatNos = carsCondition.getVatNo().stream().distinct().collect(Collectors.toList());
            List<String> dbVatNos = carRegistrationService.getAllCarRegistrations().stream().map(CarRegistration::getVatNo).collect(Collectors.toList());
            List<String> notFoundVatNos = distinctVatNos.stream().filter(vatNo -> !dbVatNos.contains(vatNo)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFoundVatNos)) {
                throw new SubscribeException(HttpStatus.OK, CAR_REGISTRATION_NOT_FOUND, String.format("車籍統編不存在, 車籍統編: %s", notFoundVatNos));
            }
            carsCondition.setVatNo(distinctVatNos);
        }
    }

    private void setUpdater(Campaign campaign, String memberId) {
        MemberInfo memberInfo = authorityServer.getMemberInfos(memberId).stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        campaign.setUpdater(memberInfo.getMemberId());
    }

    public <T extends CommonCampaignCriteria> Page<? extends CampaignCommonBaseResponse> searchByPage(PageRequest pageRequest, T criteria) {
        int offset = pageRequest.getSkip();
        int limit = pageRequest.getLimit();

        long count = campaignRepository.count(criteria);
        if (count == 0) {
            return Page.of(0, Collections.emptyList(), offset, limit);
        }

        List<? extends CampaignCommonBaseResponse> list = searchPage(criteria, offset, limit);

        return Page.of(count, list, offset, limit);
    }

    private <T extends CommonCampaignCriteria> List<? extends CampaignCommonBaseResponse> searchPage(T criteria, Integer offset, Integer limit) {
        return campaignRepository.findBySearch(criteria, offset, limit).stream()
            .map(campaign -> {
                if (criteria instanceof CampaignCriteria) {
                    return new CampaignInternalBaseResponse(campaign, authorityServer.getMemberName(campaign.getUpdater()));
                } else {
                    return new CampaignCommonBaseResponse(campaign);
                }
            })
            .collect(Collectors.toList());
    }

    public <T extends CampaignCommonBaseResponse> T get(Integer id, boolean fromInternal) {
        return campaignRepository.findById(id)
            .filter(campaign -> fromInternal || (campaign.isValid()))
            .map(campaign -> {
                BannerCategory bannerCategory = goSmartServer.getBannerCategory(campaign.getBannerCategoryId());
                return (T) (fromInternal
                    ? new CampaignInternalDetailResponse(campaign, bannerCategory, authorityServer.getMemberName(campaign.getUpdater()))
                    : new CampaignCommonDetailResponse(campaign, bannerCategory));
            })
            .orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
    }

    public CampaignInternalDetailResponse update(Integer id, CampaignRequest request, String memberId) {
        Campaign campaign = campaignRepository.findById(id)
            .orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
        buildCampaign(campaign, request, memberId);
        return new CampaignInternalDetailResponse(campaignRepository.save(campaign), goSmartServer.getBannerCategory(campaign.getBannerCategoryId()), authorityServer.getMemberName(campaign.getUpdater()));
    }

    public void delete(Integer id, String memberId) {
        Campaign campaign = campaignRepository.findById(id)
            .filter(c -> !c.isDeleted())
            .orElseThrow(() -> new SubscribeException(HttpStatus.OK, CAMPAIGN_NOT_FOUND, String.format("活動刪除失敗, 活動編號 %d 已刪除", id)));
        campaign.setDeleted(true);
        setUpdater(campaign, memberId);
        campaignRepository.save(campaign);
    }
}
