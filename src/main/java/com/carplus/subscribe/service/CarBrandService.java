package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dao.CarBrandRepository;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.mapper.cars.CarBrandMapper;
import com.carplus.subscribe.model.cars.resp.CarBrandResponse;
import com.carplus.subscribe.model.request.CarBrandRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Service
public class CarBrandService {

    @Autowired
    private CarBrandRepository carBrandRepository;

    @Transactional(transactionManager = "mysqlTransactionManager")
    public CarBrandResponse add(CarBrandRequest request) {
        findCarBrandExist(request);
        CarBrand carBrand = CarBrandMapper.reqToCarBrand(request);
        return CarBrandMapper.toCarBrandResp(carBrandRepository.save(carBrand));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public void findCarBrandExist(CarBrandRequest request) {
        carBrandRepository.findByBrandCode(request.getBrandCode())
            .ifPresent(cb -> {
                throw new SubscribeException(CAR_BRAND_ALREADY_EXIST);
            });
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public CarBrandResponse update(CarBrandRequest request) {
        CarBrand carBrand = findByBrandCode(request.getBrandCode());
        CarBrandMapper.toCarBrandForUpdate(request, carBrand);
        return CarBrandMapper.toCarBrandResp(carBrandRepository.save(carBrand));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public CarBrand findByBrandCode(String brandCode) {
        return carBrandRepository.findByBrandCode(brandCode).orElseThrow(() -> new SubscribeException(CAR_BRAND_NOT_FOUND));
    }

    @Transactional(transactionManager = "mysqlTransactionManager", readOnly = true)
    public List<CarBrand> getAllCarBrands() {
        return carBrandRepository.findAll();
    }

    public Page<CarBrandResponse> searchByPage(PageRequest pageRequest, List<String> brands) {
        int limit = pageRequest.getLimit();
        int offset = pageRequest.getSkip();
        long count;

        if (brands == null || brands.isEmpty()) {
            count = carBrandRepository.count();
            List<CarBrandResponse> all = carBrandRepository.findByPage(limit, offset, null).stream()
                .map(CarBrandMapper::toCarBrandResp)
                .collect(Collectors.toList());
            return Page.of(count, all, offset, limit);
        }

        count = carBrandRepository.count(brands);
        List<CarBrandResponse> results = carBrandRepository.findByPage(limit, offset, brands).stream()
            .map(CarBrandMapper::toCarBrandResp)
            .collect(Collectors.toList());

        return Page.of(count, results, offset, limit);
    }

    public List<CarBrandResponse> getDescribeCarBrand() {
        return carBrandRepository.findAll().stream()
            .filter(CarBrand::isAppearOnOfficial)
            .map(CarBrandMapper::toCarBrandResp)
            .sorted(Comparator.comparing(
                CarBrandResponse::getSeqNo,
                Comparator.nullsLast(Comparator.naturalOrder())
            ))
            .collect(Collectors.toList());
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateSeq(List<String> brandCodes) {

        List<CarBrand> carBrands = validateBrandCodes(brandCodes);

        // 使用 brandCodes 的順序來設定 seqNo
        Map<String, Integer> brandCodeSeqMap = brandCodes.stream()
            .collect(Collectors.toMap(brandCode -> brandCode, brandCodes::indexOf));
        for (CarBrand carBrand : carBrands) {
            Integer seqNo = brandCodeSeqMap.get(carBrand.getBrandCode());
            if (Objects.equals(carBrand.getSeqNo(), seqNo)) {
                continue;
            }
            carBrand.setSeqNo(seqNo);
        }
        carBrandRepository.saveAll(carBrands);
    }

    public List<CarBrand> validateBrandCodes(List<String> brandCodes) {
        // 檢查請求中是否有重複的 brandCode
        Set<String> uniqueBrandCodes = new LinkedHashSet<>(brandCodes);
        if (uniqueBrandCodes.size() != brandCodes.size()) {
            throw new SubscribeException(CAR_BRAND_CODES_DUPLICATE);
        }

        // 檢查提供的 brandCode 是否都存在於資料庫中
        List<CarBrand> carBrands = carBrandRepository.findByBrandCodeIn(uniqueBrandCodes);
        if (carBrands.size() != uniqueBrandCodes.size()) {
            List<String> dbCarBrandCodes = carBrands.stream()
                .map(CarBrand::getBrandCode)
                .collect(Collectors.toList());

            List<String> notFoundBrandCodes = uniqueBrandCodes.stream()
                .filter(code -> !dbCarBrandCodes.contains(code))
                .collect(Collectors.toList());

            throw new SubscribeException(HttpStatus.OK, CAR_BRAND_NOT_FOUND, String.format("[廠牌代碼不存在, 廠牌: %s]", notFoundBrandCodes));
        }
        return carBrands;
    }
}
