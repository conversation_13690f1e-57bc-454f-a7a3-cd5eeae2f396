package com.carplus.subscribe.service.helpers;

import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.CarBaseInfoToAddResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
public class AddCarBuilderDataPreSetup {
    private final CarBaseInfoToAddResponse carBaseInfoToAdd;
    private final MemberInfo memberInfo;
    private final List<Cars> cars;
    private final Map<Integer, Cars> latestExistingCarsInfo;
    private final boolean isCrsCarNoExist;
    private final List<String> orderNos;
    private final String CheckedCarModelCode;
    private final boolean isNotVirtualAndIsCarPlusCar;
}
