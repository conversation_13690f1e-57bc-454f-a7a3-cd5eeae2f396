package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.TaskType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.TaskClient;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBase;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.presign.GcsGetDownloadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.request.task.TaskAddRequest;
import com.carplus.subscribe.model.request.task.UpdateTaskRequest;
import com.carplus.subscribe.model.response.task.*;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.strategy.econtract.impl.EContractRefEntityFetcher;
import com.carplus.subscribe.utils.DateUtil;
import com.carplus.subscribe.utils.OrderUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRMapCollectionDataSource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static java.time.temporal.ChronoField.HOUR_OF_DAY;
import static java.time.temporal.ChronoField.MINUTE_OF_HOUR;

@Service
@Slf4j
public class RentalTaskService {

    private static final String datePattern = "yyyy/MM/dd";
    private static final String dateTimePattern = "yyyy/MM/dd HH:mm";
    // 保持 200*128的比例
    private static final double rate = 7.5;
    private static final int maxWidth = (int) (128 * rate); // 最大寬度
    private static final int maxHeight = (int) (200 * rate); // 最大高度

    @Autowired
    ResourceLoader resourceLoader;
    @Autowired
    private CarsService carsService;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private StationService stationService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private TaskClient taskClient;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private GoSmartServer goSmartServer;
    @Value("${gcs.url}")
    private String gcsUrl;
    @Autowired
    private OrderService orderService;
    @Autowired
    private EContractRefEntityFetcher eContractRefEntityFetcher;

    public byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096];
        int n;
        while ((n = input.read(buffer)) != -1) {
            output.write(buffer, 0, n);
        }
        input.close();
        byte[] bytes = output.toByteArray();
        output.close();
        return bytes;
    }

    private byte[] resize(InputStream input) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 先將 InputStream 轉換為 byte[]
            byte[] originalBytes = toByteArray(input);

            // 使用 ByteArrayInputStream 創建新的輸入流
            ByteArrayInputStream bais = new ByteArrayInputStream(originalBytes);
            // 讀取原始圖片
            BufferedImage originalImage = ImageIO.read(bais);

            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            // 計算縮放比例
            double scaleWidth = (double) maxWidth / originalWidth;
            double scaleHeight = (double) maxHeight / originalHeight;
            double scale = Math.min(scaleWidth, scaleHeight);
            // 如果圖片小於最大寬度和最大高度，則不縮放
            if (scale >= 1) {
                log.info("圖片尺寸小於最大限制，不進行縮放。{}", scale);
                return originalBytes;
            }
            input.close();


            int targetWidth = (int) (originalWidth * scale);
            int targetHeight = (int) (originalHeight * scale);
            Image scaledImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_SMOOTH);
            BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());
            Graphics2D g2d = resizedImage.createGraphics();

            // 設置渲染提示以提升圖片品質
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            g2d.drawImage(scaledImage, 0, 0, targetWidth, targetHeight, null);
            g2d.dispose();
            // 保存修改後的圖片
            ImageIO.write(resizedImage, "jpeg", baos);
            byte[] imageBytes = baos.toByteArray();
            log.info("圖片Resize已成功修改並保存。{}", scale);
            baos.close();
            return imageBytes;
        } catch (Exception e) {
            log.error("Error reading image", e);
            return null;
        }
    }

    public void resetTask(Integer taskId) {
        taskClient.resetRentalTask(taskId);
    }

    public Integer generateTask(Orders order, TaskType taskType) {
        Integer taskId = 0;
        try {
            Contract contract = order.getContract();
            MainContract mainContract = contract.getMainContract();
            CarResponse car = carsService.getCarInfo(mainContract.getPlateNo());
            String plateNo = mainContract.getPlateNo();
            AuthUser authUser = authServer.getUserWithRetry(mainContract.getAcctId());
            Instant startDate = Optional.ofNullable(order.getStartDate()).orElseGet(order::getExpectStartDate);
            ZonedDateTime originalExpectEndDate = Optional.ofNullable(order.getEndDate()).orElseGet(order::getExpectEndDate).atZone(DateUtils.ZONE_TPE);
            Instant endDate = startDate.atZone(DateUtils.ZONE_TPE).plus(order.getMonth(), ChronoUnit.MONTHS).minus(1, ChronoUnit.DAYS)
                .with(HOUR_OF_DAY, originalExpectEndDate.get(HOUR_OF_DAY))
                .with(MINUTE_OF_HOUR, originalExpectEndDate.get(MINUTE_OF_HOUR)).toInstant();

            TaskAddRequest taskAddRequest = TaskAddRequest.builder()
                .orderNo(contract.getContractNo()) // 訂閱車使用 contractNo
                .systemKind(HeaderDefine.SystemKind.SUB)
                .driver(TaskAddRequest.Driver.builder()
                    .id(authUser.getLoginId())
                    .name(authUser.getAcctName())
                    .licenseNo(authUser.getJurisdictionNum())
                    .acctId(mainContract.getAcctId())
                    .build())
                .carInfo(TaskAddRequest.CarInfo.builder()
                    .externalName(String.format("%s %s", car.getCarBrand().getBrandNameEn(), car.getCarModel().getCarModelName()))
                    .plateNo(plateNo)
                    .color(car.getColorDesc())
                    .mileageFee(mainContract.getOriginalPriceInfo().getMileageFee())
                    .build())
                .departExecuteDate(startDate)
                .returnExecuteDate(endDate)
                .departStationId(mainContract.getDepartStationCode())
                .departStationName(stationService.findByStationCode(mainContract.getDepartStationCode()).getStationName())
                .returnStationId(mainContract.getReturnStationCode())
                .returnStationName(stationService.findByStationCode(mainContract.getReturnStationCode()).getStationName())
                .eContractType(taskType)
                .eContractCarKind(CarDefine.CarKind.sedan)
                .remark(taskType.equals(TaskType.DEPART) ? order.getDepartRemark() : order.getReturnRemark())
                .build();

            Result<AddRentalTaskResponse> result = taskClient.addRentalTask(taskAddRequest);

            if (result.getStatusCode() == 0) {
                taskId = result.getData().getTaskId();
                switch (taskType) {
                    case DEPART:
                        contract.setDepartTaskId(String.valueOf(taskId));
                        break;
                    case RETURN:
                        contract.setReturnTaskId(String.valueOf(taskId));
                        break;
                    default:
                        break;
                }
            } else {
                log.error("task service addRentalTask error:{}", result.getMessage());
                throw new SubscribeException(ADD_TASK_FAIL);
            }
        } catch (Exception e) {
            log.error("task service addRentalTask error", e);
            throw e;
        }
        return taskId;
    }

    /**
     * 刪除出租單任務
     */
    public void deleteTask(Orders orders, TaskType taskType) {
        Contract contract = orders.getContract();
        if (taskType.equals(TaskType.DEPART) && StringUtils.isNotBlank(contract.getDepartTaskId())) {
            taskClient.deleteRentalTask(contract.getDepartTaskId());
        } else if (taskType.equals(TaskType.RETURN) && StringUtils.isNotBlank(contract.getReturnTaskId())) {
            // 使之可以正常續約，故不拋出Exception
            try {
                taskClient.deleteRentalTask(contract.getReturnTaskId());
            } catch (Exception e) {
                mattermostServer.notify("刪除還車任務失敗", Collections.singletonMap(orders.getOrderNo(), contract.getReturnTaskId()), e);
            }
        }
    }

    public TaskDetailResponse getTaskDetail(String taskId) {
        Result<TaskDetailResponse> result = taskClient.getRentalTask(taskId);
        if (result.getStatusCode() == 0 && result.getData() != null) {
            return result.getData();
        }
        throw new SubscribeException(GET_TASK_DETAIL_FAIL);
    }

    public byte[] generatePdf(String econtractRefEntityNo) throws IOException, JRException {
        EContractReferencable entity = eContractRefEntityFetcher.fetch(econtractRefEntityNo);
        return generatePdf(entity, authServer.getAuthUser(entity));
    }


    public byte[] generatePdf(EContractReferencable econtractRefEntity, AuthUser authUser) throws IOException, JRException {
        String departTaskId = econtractRefEntity.getDepartTaskId();
        String returnTaskId = econtractRefEntity.getReturnTaskId();
        String taskId = StringUtils.isBlank(returnTaskId) ? departTaskId : returnTaskId;
        if (taskId == null) {
            throw new SubscribeException(TASK_ID_EMPTY);
        }

        Map<String, Object> parameters = new HashMap<>();
        TaskDetailResponse taskDetailResponse = null;
        TaskDetailResponse departTaskDetailResponse = null;
        List<Orders> orders = null;

        // 獲取合約編號
        String entityNo = econtractRefEntity.getEntityNo();
        parameters.put("contractNo", entityNo);

        // 對於 Contract 類型，獲取相關的 Orders
        if (econtractRefEntity instanceof Contract) {
            orders = orderService.getOrdersByContractNo(entityNo);
        }

        JasperReport jrGenerateStream = prepareReports(parameters);
        prepareCustomInfo(parameters, econtractRefEntity, authUser);

        if (StringUtils.isNotBlank(departTaskId)) {
            departTaskDetailResponse = getDepartTaskDetailResponse(departTaskId);
            if (departTaskDetailResponse.getStatus() != 7) {
                throw new SubscribeException(TASK_IS_NOT_DONE);
            }
            if (CollectionUtils.isEmpty(departTaskDetailResponse.getDriverSignModels())) {
                throw new SubscribeException(TASK_IS_NOT_SIGN);
            }
            prepareDepartInfo(parameters, econtractRefEntity, departTaskDetailResponse, orders);
            taskDetailResponse = departTaskDetailResponse;
        }

        if (StringUtils.isNotBlank(returnTaskId)) {
            TaskDetailResponse returnTaskDetailResponse = getTaskDetail(returnTaskId);
            taskDetailResponse = returnTaskDetailResponse;
            if (CollectionUtils.isEmpty(returnTaskDetailResponse.getDriverSignModels())) {
                throw new SubscribeException(TASK_IS_NOT_SIGN);
            }
            prepareReturnInfo(parameters, econtractRefEntity, returnTaskDetailResponse, orders);
        }

        if (departTaskDetailResponse == null) {
            departTaskDetailResponse = taskDetailResponse;
        }

        if (taskDetailResponse != null) {
            prepareVehicleConditionPhotos(parameters, departTaskDetailResponse);
            List<AbnormalAreas> departAreas = getAbnormalAreas(taskDetailResponse.getDepartCarAbnormalAreas());
            List<AbnormalAreas> returnAreas = getAbnormalAreas(taskDetailResponse.getReturnCarAbnormalAreas());
            prepareDepartAndReturnPointImageParameters(parameters, departAreas, returnAreas);
            prepareDepartAndReturnImageParameters(parameters, departAreas, returnAreas);
        }

        prepareRepresentativeParameters(parameters, econtractRefEntity);

        JasperPrint jasperPrint = JasperFillManager.fillReport(jrGenerateStream, parameters, new JREmptyDataSource());

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);
            return outputStream.toByteArray();
        }
    }

    private TaskDetailResponse getDepartTaskDetailResponse(String departTaskId) {
        TaskDetailResponse departTaskDetailResponse = getTaskDetail(departTaskId);
        int count = 0;
        while (departTaskDetailResponse.getAssignMemberSignModel() == null && count < 3) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            departTaskDetailResponse = getTaskDetail(departTaskId);
            count++;
        }

        return departTaskDetailResponse;
    }

    /**
     * 準備出租單資料
     */
    private JasperReport prepareReports(Map<String, Object> parameters) throws IOException, JRException {
        Resource generateReportStream = resourceLoader.getResource("classpath:jrxml/GenericLayout.jrxml");
        JasperReport jrGenerateStream = JasperCompileManager.compileReport(generateReportStream.getInputStream());
        Resource firstPageReportStream = resourceLoader.getResource("classpath:jrxml/basic.jrxml");
        JasperReport firstPageStream = JasperCompileManager.compileReport(firstPageReportStream.getInputStream());

        Resource custInfoReportStream = resourceLoader.getResource("classpath:jrxml/subscription_info.jrxml");
        JasperReport custReport = JasperCompileManager.compileReport(custInfoReportStream.getInputStream());
        Resource departInfoReportStream = resourceLoader.getResource("classpath:jrxml/depart_info.jrxml");
        JasperReport departReport = JasperCompileManager.compileReport(departInfoReportStream.getInputStream());
        Resource returnInfoReportStream = resourceLoader.getResource("classpath:jrxml/return_info.jrxml");
        JasperReport returnReport = JasperCompileManager.compileReport(returnInfoReportStream.getInputStream());

        Resource departAndReturnImageReportStream = resourceLoader.getResource("classpath:jrxml/departDeturnImage.jrxml");
        JasperReport departAndReturnImageReport = JasperCompileManager.compileReport(departAndReturnImageReportStream.getInputStream());

        Resource exteriorPhotosReportStream = resourceLoader.getResource("classpath:jrxml/exteriorPhotos.jrxml");
        JasperReport exteriorPhotosReport = JasperCompileManager.compileReport(exteriorPhotosReportStream.getInputStream());

        Resource coordinateReportStream = resourceLoader.getResource("classpath:jrxml/coordinate.jrxml");
        JasperReport coordinateReport = JasperCompileManager.compileReport(coordinateReportStream.getInputStream());

        Resource representativeReportStream = resourceLoader.getResource("classpath:jrxml/representative.jrxml");
        JasperReport representativeReport = JasperCompileManager.compileReport(representativeReportStream.getInputStream());

        String stampPath = "classpath:jrxml/img/logo.png";
        InputStream stampImg = resourceLoader.getResource(stampPath).getInputStream();
        parameters.put("logo", toByteArray(stampImg));
        parameters.put("tempReport", firstPageStream);
        parameters.put("custInfoReport", custReport);
        parameters.put("departInfo", departReport);
        parameters.put("returnInfo", returnReport);
        parameters.put("departReturnImageReport", departAndReturnImageReport);
        parameters.put("representativeReport", representativeReport);
        parameters.put("exteriorPhotosReport", exteriorPhotosReport);
        parameters.put("coordinateReport", coordinateReport);
        return jrGenerateStream;
    }

    private void prepareCustomInfo(Map<String, Object> parameters, EContractReferencable econtractRefEntity, AuthUser authUser) {
        if (!(econtractRefEntity instanceof Contract) && !(econtractRefEntity instanceof DealerOrder)) {
            throw new IllegalArgumentException("不支援的實體類型: " + econtractRefEntity.getClass().getName());
        }

        Map<String, Stations> stationMap = stationService.getStationsMap();

        String plateNo = econtractRefEntity.getPlateNo();
        String departStationCode = econtractRefEntity.getDepartStationCode();
        String returnStationCode = econtractRefEntity.getReturnStationCode();
        Instant startDate = Optional.ofNullable(econtractRefEntity.getStartDate()).orElse(econtractRefEntity.getExpectStartDate());
        Instant expectEndDate = econtractRefEntity.getExpectEndDate();

        CarResponse car = carsService.getCarInfo(plateNo);
        CarBaseInfoSearchResponse carBaseInfo = crsService.getCar(plateNo);

        parameters.put("acctName", authUser.getAcctName());
        parameters.put("birthday", authUser.getBirthday());
        parameters.put("idNo", authUser.getLoginId());
        parameters.put("mainCell", authUser.getMainCell());
        parameters.put("email", authUser.getEmail());
        parameters.put("address", OrderUtils.getFullHHAddress(authUser, goSmartServer.getCityArea()));
        parameters.put("plateNo", plateNo);
        parameters.put("engineNo", Optional.ofNullable(carBaseInfo).map(CarBaseInfoSearchResponse::getCarBase).map(CarBase::getEngineNo).orElse(""));
        parameters.put("carColor", car.getColorDesc());
        parameters.put("carModelName", car.getCarBrand().getBrandNameEn() + " " + car.getCarModel().getCarModelName());
        parameters.put("securityDeposit", String.format("%,d", econtractRefEntity.getSecurityDeposit()));
        parameters.put("monthlyFee", String.format("%,d", econtractRefEntity.getMonthlyFee()));
        parameters.put("mileageFee", String.format("%,.2f", econtractRefEntity.getMileageFee()));
        parameters.put("expectDepartDate", startDate != null ? DateUtils.toDateString(Date.from(startDate), dateTimePattern) : "");
        parameters.put("expectReturnDate", expectEndDate != null ? DateUtils.toDateString(Date.from(expectEndDate), dateTimePattern) : "");
        parameters.put("expectDepartStation", Optional.ofNullable(stationMap.get(departStationCode)).map(Stations::getStationName).orElse(""));
        parameters.put("expectReturnStation", Optional.ofNullable(stationMap.get(returnStationCode)).map(Stations::getStationName).orElse(""));
    }

    private void prepareDepartInfo(Map<String, Object> parameters, EContractReferencable econtractRefEntity, TaskDetailResponse taskDetailResponse, @Nullable List<Orders> orders) {

        Integer departMileage = orders != null ? orders.stream()
            .filter(o -> o.getStage() > 0)
            .min(Comparator.comparing(Orders::getStage))
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND)).getDepartMileage()
            : taskDetailResponse.getDepartMileage();
        parameters.put("startDate", Optional.ofNullable(econtractRefEntity.getStartDate()).map(date -> DateUtils.toDateString(Date.from(date), dateTimePattern)).orElse(""));
        parameters.put("mileageOut", Optional.ofNullable(departMileage).map(mileage -> String.format("%,d", mileage)).orElse(""));
        parameters.put("fuelOut", Optional.ofNullable(taskDetailResponse.getFuel()).map(f -> (int) (f * 10000) / 100).map(Object::toString).orElse("") + " %");
        parameters.put("tireDepthDepart", Optional.ofNullable(taskDetailResponse.getTireTreadDepth()).map(Object::toString).orElse(""));
        parameters.put("warningLightDepart", getCheckMark(taskDetailResponse.getDashboardAlert()));
        parameters.put("odorDepart", getCheckMark(taskDetailResponse.getScent()));
        parameters.put("seatAndTrimDepart", getCheckMark(taskDetailResponse.getSeat()));
        parameters.put("carpetAndGloveboxDepart", getCheckMark(taskDetailResponse.getBlanket()));
        parameters.put("engineDepart", getCheckMark(taskDetailResponse.getEngine()));
        parameters.put("acDepart", getCheckMark(taskDetailResponse.getAirCondition()));
        parameters.put("windowAndDoorMirrorDepart", getCheckMark(taskDetailResponse.getWindows()));
        parameters.put("audioAndMultimediaDepart", getCheckMark(taskDetailResponse.getMedia()));
        parameters.put("vehicleLicenseDepart", getCheckMark(taskDetailResponse.getCarLicense()));
        parameters.put("hasKeyDepart", getCheckMark(taskDetailResponse.getHasKey()));
        parameters.put("keyCountDepart", Optional.ofNullable(taskDetailResponse.getKeys()).map(Object::toString).orElse("  "));
        parameters.put("dashCamsDepart", getCheckMark(taskDetailResponse.getDashCams()));
        parameters.put("trunkLidDepart", getCheckMark(taskDetailResponse.getTrunkLid()));
        parameters.put("departRemark", getNote(taskDetailResponse.getOutsideNote()) + getNote(taskDetailResponse.getDashboardNote()) + getNote(taskDetailResponse.getInsideNote())
            + getNote(taskDetailResponse.getPartsNote()) + getNote(taskDetailResponse.getEquipsNote()));
        parameters.put("departMemberSignImage", Optional.ofNullable(taskDetailResponse.getAssignMemberSignModel()).map(SignModel::getUrl).map(this::getSignImage).orElse(null));
        parameters.put("departCustSignImage", CollectionUtils.isNotEmpty(taskDetailResponse.getDriverSignModels())
            ? Optional.ofNullable(taskDetailResponse.getDriverSignModels().get(0)).map(SignModel::getUrl).map(this::getSignImage).orElse(null) : null);
        parameters.put("departMemberSignDate", Optional.ofNullable(taskDetailResponse.getAssignMemberSignModel()).map(SignModel::getSignDate).map(date -> DateUtils.toDateString(date, datePattern)).orElse(null));
        parameters.put("departCustSignDate", CollectionUtils.isNotEmpty(taskDetailResponse.getDriverSignModels())
            ? Optional.ofNullable(taskDetailResponse.getDriverSignModels().get(0)).map(SignModel::getSignDate).map(date -> DateUtils.toDateString(date, datePattern)).orElse(null) : null);
    }

    private void prepareReturnInfo(Map<String, Object> parameters, EContractReferencable econtractRefEntity, TaskDetailResponse taskDetailResponse, @Nullable List<Orders> orders) {
        Integer returnMileage = orders != null ? orders.stream()
            .max(Comparator.comparing(Orders::getStage))
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_NOT_FOUND)).getReturnMileage()
            : taskDetailResponse.getReturnMileage();
        parameters.put("endDate", Optional.ofNullable(econtractRefEntity.getEndDate()).map(date -> DateUtils.toDateString(Date.from(date), dateTimePattern)).orElse(""));
        parameters.put("mileageIn", Optional.ofNullable(returnMileage).map(mileage -> String.format("%,d", mileage)).orElse(""));
        parameters.put("fuelIn", Optional.ofNullable(taskDetailResponse.getFuel()).map(f -> (int) (f * 10000) / 100).map(Object::toString).orElse("") + " %");
        parameters.put("tireDepth", Optional.ofNullable(taskDetailResponse.getTireTreadDepth()).map(Object::toString).orElse(""));
        parameters.put("warningLight", getCheckMark(taskDetailResponse.getDashboardAlert()));
        parameters.put("odor", getCheckMark(taskDetailResponse.getScent()));
        parameters.put("seatAndTrim", getCheckMark(taskDetailResponse.getSeat()));
        parameters.put("carpetAndGlovebox", getCheckMark(taskDetailResponse.getBlanket()));
        parameters.put("engine", getCheckMark(taskDetailResponse.getEngine()));
        parameters.put("ac", getCheckMark(taskDetailResponse.getAirCondition()));
        parameters.put("windowAndDoorMirror", getCheckMark(taskDetailResponse.getWindows()));
        parameters.put("audioAndMultimedia", getCheckMark(taskDetailResponse.getMedia()));
        parameters.put("vehicleLicense", getCheckMark(taskDetailResponse.getCarLicense()));
        parameters.put("hasKey", getCheckMark(taskDetailResponse.getHasKey()));
        parameters.put("keyCount", Optional.ofNullable(taskDetailResponse.getKeys()).map(Object::toString).orElse("  "));
        parameters.put("dashCams", getCheckMark(taskDetailResponse.getDashCams()));
        parameters.put("trunkLid", getCheckMark(taskDetailResponse.getTrunkLid()));
        parameters.put("returnRemark", getNote(taskDetailResponse.getOutsideNote()) + getNote(taskDetailResponse.getDashboardNote()) + getNote(taskDetailResponse.getInsideNote())
            + getNote(taskDetailResponse.getPartsNote()) + getNote(taskDetailResponse.getEquipsNote()));
        parameters.put("returnMemberSignImage", Optional.ofNullable(taskDetailResponse.getAssignMemberSignModel()).map(SignModel::getUrl).map(this::getSignImage).orElse(null));
        parameters.put("returnCustSignImage", CollectionUtils.isNotEmpty(taskDetailResponse.getDriverSignModels())
            ? Optional.ofNullable(taskDetailResponse.getDriverSignModels().get(0)).map(SignModel::getUrl).map(this::getSignImage).orElse(null) : null);
        parameters.put("returnMemberSignDate", Optional.ofNullable(taskDetailResponse.getAssignMemberSignModel()).map(SignModel::getSignDate).map(date -> DateUtils.toDateString(date, datePattern)).orElse(null));
        parameters.put("returnCustSignDate", CollectionUtils.isNotEmpty(taskDetailResponse.getDriverSignModels())
            ? Optional.ofNullable(taskDetailResponse.getDriverSignModels().get(0)).map(SignModel::getSignDate).map(date -> DateUtils.toDateString(date, datePattern)).orElse(null) : null);
    }

    private String getCheckMark(Boolean value) {
        return Optional.ofNullable(value).map(b -> b ? "■" : "□").orElse("□");
    }

    private String getNote(String note) {
        if (StringUtils.isNotBlank(note)) {
            return Optional.of(note).map(s -> s + "; ").orElse("");
        }
        return "";
    }

    private byte[] getSignImage(String privateUrl) {
        try {
            String filePath = "";
            String[] parts = privateUrl.split("/");
            if (parts.length >= 4) {
                filePath = parts[2] + "/" + parts[3] + "/" + parts[4];
            }

            GcsGetDownloadUrlReq gcsGetDownloadUrlReq = GcsGetDownloadUrlReq.builder()
                .source("taskService")
                .filePath(filePath)
                .isTemp(Boolean.FALSE)
                .fileNames(Collections.singletonList(parts[5]))
                .build();
            GcsUrlRes urlRes = goSmartServer.getGcsDownloadUrl(gcsGetDownloadUrlReq);
            Map<String, byte[]> imageMap = new HashMap<>();
            for (GcsUrlRes.TypeResponse o : urlRes.getSignedUrls()) {
                return toByteArray(new URL(o.getSignedUrl()).openStream());
            }
        } catch (IOException e) {
            log.error("拿取簽名圖失敗", e);
            return null;
        }
        return null;
    }

    private void prepareVehicleConditionPhotos(Map<String, Object> parameters, TaskDetailResponse taskDetailResponse) throws IOException {
        if (CollectionUtils.isNotEmpty(taskDetailResponse.getSurroundingsVehicleImages())) {
            for (int i = 0; i < taskDetailResponse.getSurroundingsVehicleImages().size(); i++) {
                parameters.put("surround" + (1 + i), resize(new URL(gcsUrl + taskDetailResponse.getSurroundingsVehicleImages().get(i)).openStream()));
            }
        }
        if (CollectionUtils.isNotEmpty(taskDetailResponse.getInteriorPhotos())) {
            for (int i = 0; i < taskDetailResponse.getInteriorPhotos().size(); i++) {
                parameters.put("insight" + (1 + i), resize(new URL(gcsUrl + taskDetailResponse.getInteriorPhotos().get(i)).openStream()));
            }
        }
        if (StringUtils.isNotBlank(taskDetailResponse.getEtagNo())) {
            parameters.put("etag", resize(new URL(gcsUrl + taskDetailResponse.getEtagNo()).openStream()));
        }
    }

    private List<AbnormalAreas> getAbnormalAreas(CarAbnormalAreas carAbnormalAreas) {
        List<AbnormalAreas> areas = new ArrayList<>();
        if (carAbnormalAreas == null) {
            return areas;
        }
        Arrays.asList(
            carAbnormalAreas.getFront(),
            carAbnormalAreas.getBack(),
            carAbnormalAreas.getRight(),
            carAbnormalAreas.getLeft(),
            carAbnormalAreas.getTop(),
            carAbnormalAreas.getInside()
        ).forEach(list -> {
            if (CollectionUtils.isNotEmpty(list)) {
                areas.addAll(list);
            }
        });
        return areas;
    }

    private void prepareDepartAndReturnPointImageParameters(Map<String, Object> parameters, List<AbnormalAreas> departAreas, List<AbnormalAreas> returnAreas) throws IOException {
        String frontPath = "classpath:jrxml/img/img_car_view_1.png";
        String backPath = "classpath:jrxml/img/img_car_view_2.png";
        String rightPath = "classpath:jrxml/img/img_car_view_3.png";
        String leftPath = "classpath:jrxml/img/img_car_view_4.png";
        String topPath = "classpath:jrxml/img/img_car_view_5.png";
        String insightPath = "classpath:jrxml/img/img_car_view_inside_3.png";

        AtomicInteger count = new AtomicInteger(0);
        Map<AreaEnum, Map<Integer, AbnormalAreas>> groupMap = new LinkedHashMap<>();
        groupAreas(groupMap, departAreas, count);
        groupAreas(groupMap, returnAreas, count);
        parameters.put("front", drawImage(resourceLoader.getResource(frontPath).getInputStream(), groupMap.get(AreaEnum.FRONT)));
        parameters.put("back", drawImage(resourceLoader.getResource(backPath).getInputStream(), groupMap.get(AreaEnum.BACK)));
        parameters.put("right", drawImage(resourceLoader.getResource(rightPath).getInputStream(), groupMap.get(AreaEnum.RIGHT)));
        parameters.put("left", drawImage(resourceLoader.getResource(leftPath).getInputStream(), groupMap.get(AreaEnum.LEFT)));
        parameters.put("top", drawImage(resourceLoader.getResource(topPath).getInputStream(), groupMap.get(AreaEnum.TOP)));
        parameters.put("insight", drawImage(resourceLoader.getResource(insightPath).getInputStream(), groupMap.get(AreaEnum.INSIDE)));
    }

    private void groupAreas(Map<AreaEnum, Map<Integer, AbnormalAreas>> groupMap, List<AbnormalAreas> areas, AtomicInteger startNum) {

        for (AbnormalAreas abnormalAreas : areas) {
            AreaEnum area = abnormalAreas.getArea();
            startNum.getAndIncrement();
            if (groupMap.containsKey(area)) {
                groupMap.get(area).put(startNum.get(), abnormalAreas);
            } else {
                Map<Integer, AbnormalAreas> map = new LinkedHashMap<>();
                map.put(startNum.get(), abnormalAreas);
                groupMap.put(area, map);
            }
        }
    }

    private byte[] drawImage(InputStream inputStream, Map<Integer, AbnormalAreas> abnormalAreasMap) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        if (abnormalAreasMap != null && !abnormalAreasMap.isEmpty()) {
            try {
                // 讀取原始圖片
                BufferedImage image = ImageIO.read(inputStream);

                int imageWidth = image.getWidth();
                int imageHeight = image.getHeight();

                // 創建Graphics2D對象
                Graphics2D g2d = image.createGraphics();

                // 設置圓點的屬性
                int radius = 10; // 圓點的半徑
                int fontSize = 18; // 字體大小
                Color circleColor = Color.BLACK; // 圓點的顏色
                Color textColor = Color.WHITE; // 數字的顏色
                Font font = new Font("Arial", Font.BOLD, fontSize); // 數字的字體

                // 繪製圓點並寫入數字
                for (Map.Entry<Integer, AbnormalAreas> entry : abnormalAreasMap.entrySet()) {
                    int x = (int) (entry.getValue().getX() * imageWidth);
                    int y = (int) (entry.getValue().getY() * imageHeight);

                    // 繪製圓點
                    g2d.setColor(circleColor);
                    g2d.fillOval(x - radius, y - radius, 2 * radius, 2 * radius);

                    // 寫入數字
                    g2d.setColor(textColor);
                    g2d.setFont(font);
                    String text = String.valueOf(entry.getKey());
                    FontMetrics metrics = g2d.getFontMetrics(font);
                    int textWidth = metrics.stringWidth(text);
                    int textHeight = metrics.getAscent() - metrics.getDescent();
                    int textX = x - textWidth / 2;
                    int textY = y + textHeight / 2; // 調整文字的垂直位置
                    g2d.drawString(text, textX, textY);
                }

                // 釋放Graphics2D資源
                g2d.dispose();

                // 保存修改後的圖片
                ImageIO.write(image, "png", baos);
                byte[] imageBytes = baos.toByteArray();

                System.out.println("圖片已成功修改並保存。");
                return imageBytes;

            } catch (IOException e) {
                log.error("處理圖片時發生錯誤: " + e.getMessage(), e);
            }
        }
        return toByteArray(inputStream);

    }

    private void prepareDepartAndReturnImageParameters(Map<String, Object> parameters, List<AbnormalAreas> departAreas, List<AbnormalAreas> returnAreas) throws IOException {
        AtomicInteger num = new AtomicInteger(0);
        parameters.put("isDepartEmpty", departAreas.isEmpty());
        if (!departAreas.isEmpty()) {
            parameters.put("departDataSet", new JRMapCollectionDataSource(generateDepartReturnImageList(departAreas, num)));
        }
        parameters.put("isReturnEmpty", returnAreas.isEmpty());
        if (!returnAreas.isEmpty()) {
            parameters.put("returnDataSet", new JRMapCollectionDataSource(generateDepartReturnImageList(returnAreas, num)));
        }

    }

    private Collection<Map<String, ?>> generateDepartReturnImageList(List<AbnormalAreas> departCarAbnormalAreas, AtomicInteger num) throws IOException {
        Collection<Map<String, ?>> imageList = new ArrayList<Map<String, ?>>();
        for (List<AbnormalAreas> subList : Lists.partition(departCarAbnormalAreas, 4)) {

            Map<String, Object> service = new LinkedHashMap<>();
            int count = 0;
            for (AbnormalAreas abnormalAreas : subList) {
                count++;
                num.getAndIncrement();
                service.put("Field_" + (count), num.get() + "");
                service.put("Field_" + (count + 4), resize(new URL(gcsUrl + abnormalAreas.getImage()).openStream()));
            }
            imageList.add(service);
        }
        return imageList;
    }

    private void prepareRepresentativeParameters(Map<String, Object> parameters, EContractReferencable econtractRefEntity) throws IOException {
        parameters.put("stamp", toByteArray(resourceLoader.getResource("classpath:jrxml/img/company_stamp.png").getInputStream()));
        parameters.put("stamp1", toByteArray(resourceLoader.getResource("classpath:jrxml/img/manager_stamp_new.png").getInputStream()));
        parameters.put("mingouYear", DateUtil.transferADDateToMinguoDate(econtractRefEntity.getStartDate(), "yyy"));
        parameters.put("mingouMonth", DateUtil.transferADDateToMinguoDate(econtractRefEntity.getStartDate(), "MM"));
        parameters.put("mingouDate", DateUtil.transferADDateToMinguoDate(econtractRefEntity.getStartDate(), "dd"));
    }


    public void updateDepartMileage(Contract contract, Integer mileage) {
        if (StringUtils.isNotBlank(contract.getDepartTaskId())) {
            UpdateTaskRequest updateTaskRequest = new UpdateTaskRequest();
            updateTaskRequest.setId(Integer.parseInt(contract.getDepartTaskId()));
            updateTaskRequest.setMileage(mileage);
            Result<TaskDetailResponse> result = taskClient.updateRentalTask(updateTaskRequest);
            if (result.getStatusCode() != 0) {
                throw new SubscribeException(SubscribeHttpExceptionCode.UPDATE_TASK_FAIL);
            }
        }
    }

}
