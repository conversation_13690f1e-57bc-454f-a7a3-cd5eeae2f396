package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.SkuRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.sku.SkuAddRequest;
import com.carplus.subscribe.model.request.sku.SkuCriteria;
import com.carplus.subscribe.model.request.sku.SkuUpdateRequest;
import com.carplus.subscribe.model.sku.SkuCommonResponse;
import com.carplus.subscribe.model.sku.SkuResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SkuService {

    @Autowired
    private SkuRepository skuRepository;

    public void addSku(SkuAddRequest skuAddRequest) {
        // 驗證 SKU 名稱是否唯一
        if (skuRepository.existsByName(skuAddRequest.getName())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.SKU_NAME_DUPLICATE);
        }

        Sku sku = new Sku();
        // 根據 SKU 類型自動產生新的 SKU 代碼
        sku.setCode(generateSkuCode(skuAddRequest.getType()));
        sku.setType(skuAddRequest.getType());
        sku.setName(skuAddRequest.getName());
        sku.setUnitPrice(skuAddRequest.getUnitPrice());
        if (StringUtils.isNotBlank(skuAddRequest.getDescription())) {
            sku.setDescription(skuAddRequest.getDescription());
        }
        Optional.ofNullable(skuAddRequest.getImgPath()).ifPresent(sku::setImgPath);
        sku.setOfficial(Boolean.TRUE.equals(skuAddRequest.getIsOfficial()));
        sku.setCashier(Optional.ofNullable(skuAddRequest.getIsCashier()).orElse(true)); // 預設顯示於收銀台
        skuRepository.save(sku);
    }

    public SkuResponse getByCode(String code) {
        Sku sku = skuRepository.findById(code)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SKU_NOT_FOUND));
        return new SkuResponse(sku);
    }

    public <T extends SkuCommonResponse> Page<T> searchByPage(PageRequest pageRequest, SkuCriteria criteria, Function<Sku, T> mapper) {
        // 取得總筆數
        long total = skuRepository.count(criteria);
        
        // 取得分頁資料
        List<T> list = skuRepository.search(criteria, pageRequest.getLimit(), pageRequest.getSkip()).stream()
            .map(mapper).collect(Collectors.toList());
        
        return Page.of(total, list, pageRequest.getSkip(), pageRequest.getLimit());
    }

    public void updateSku(String code, SkuUpdateRequest skuUpdateRequest) {
        Sku sku = skuRepository.findById(code)
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.SKU_NOT_FOUND));

        Optional.ofNullable(skuUpdateRequest.getUnitPrice()).ifPresent(sku::setUnitPrice);
        if (StringUtils.isNotBlank(skuUpdateRequest.getDescription())) {
            sku.setDescription(skuUpdateRequest.getDescription());
        }
        Optional.ofNullable(skuUpdateRequest.getImgPath()).ifPresent(sku::setImgPath);
        Optional.ofNullable(skuUpdateRequest.getIsOfficial()).ifPresent(sku::setOfficial);
        Optional.ofNullable(skuUpdateRequest.getIsCashier()).ifPresent(sku::setCashier);

        skuRepository.save(sku);
    }

    /**
     * 檢查所有傳入的汽車用品代碼是否都存在
     */
    public void validateAllCodesExist(Set<String> skuCodeSet) {
        skuRepository.validateAllCodesExist(skuCodeSet);
    }

    public Map<String, Sku> getAllSkus() {
        return skuRepository.findAll().stream().collect(Collectors.toMap(Sku::getCode, Function.identity()));
    }

    public Map<String, Sku> getMapByCodes(Set<String> skuCodes) {
        return skuRepository.findAllById(skuCodes).stream().collect(Collectors.toMap(Sku::getCode, Function.identity()));
    }

    /**
     * 根據 SKU 類型產生新的 SKU 代碼
     */
    public String generateSkuCode(String skuType) {
        // 找出所有相同類型的現有 SKU
        List<Sku> skusWithSameType = skuRepository.findByType(skuType);

        if (skusWithSameType.isEmpty()) {
            // 如果沒有相同類型的 SKU 存在，則從 001 開始
            return skuType + "001";
        }

        // 在這些 SKU 中找出最大的數字代碼值
        int maxNumericValue = 0;

        for (Sku sku : skusWithSameType) {
            String code = sku.getCode();
            if (code.startsWith(skuType)) {
                String numericPart = code.substring(skuType.length());
                try {
                    int numericValue = Integer.parseInt(numericPart);
                    if (numericValue > maxNumericValue) {
                        maxNumericValue = numericValue;
                    }
                } catch (NumberFormatException e) {
                    // 跳過非數字部分
                }
            }
        }

        // 將最大值加一並格式化，前面補零
        return skuType + String.format("%03d", maxNumericValue + 1);
    }
}