package com.carplus.subscribe.service.factory;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.service.strategy.dealerorder.DealerOrderExcelProcessStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 經銷商訂單處理策略工廠
 */
@Component
public class DealerOrderExcelProcessStrategyFactory {

    private final List<DealerOrderExcelProcessStrategy> strategies;

    @Autowired
    public DealerOrderExcelProcessStrategyFactory(List<DealerOrderExcelProcessStrategy> strategies) {
        this.strategies = strategies;
    }

    /**
     * 根據訂單狀態和 Excel 資料選擇適合的策略
     *
     * @param dbDealerOrder 資料庫中的訂單資料，為 null 表示新建訂單
     * @param dealerOrderExcel Excel中的訂單資料
     * @return 適合的策略，若無適合策略返回 null
     */
    public DealerOrderExcelProcessStrategy getStrategy(DealerOrder dbDealerOrder, DealerOrderExcel dealerOrderExcel) {
        return strategies.stream()
                .filter(strategy -> strategy.isApplicable(dbDealerOrder, dealerOrderExcel))
                .findFirst()
                .orElse(null);
    }
} 