package com.carplus.subscribe.server;

import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.utils.MarkdownUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MattermostServer {

    @Value("${env}")
    private String env;
    @Value("${mattermost.enabled}")
    private boolean enabled;
    @Value("${mattermost.webhook}")
    private String host;
    @Autowired
    private ObjectMapper objectMapper;


    /**
     * Common Msg Alert
     */
    public void notify(@NonNull String title, @Nullable Map<String, Object> msg, @Nullable Throwable ex) {

        try {
            Map<String, Object> body = Maps.newHashMap();

            String text = MarkdownUtils.header(4, title + " [`" + env.toUpperCase() + "` Subscribe Server]");
            text += "@chunkuan.liu @chihhung.li\n";

            String card = "";
            if (msg != null) {
                card += MarkdownUtils.code(
                    "json",
                    objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(msg));
            }
            if (ex != null) {
                card += MarkdownUtils.code(
                    "text",
                    ex.getMessage()
                );
                card += MarkdownUtils.code(
                    "text",
                    Arrays.stream(ex.getStackTrace()).map(StackTraceElement::toString).filter(s -> s.startsWith("com.carplus")).collect(Collectors.joining("\n")));
            }

            body.put("text", text + "\n" + card);

            send(body);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * send to Subscribe-Alert Channel
     */
    private void send(@NonNull Map<String, Object> body) {
        if (!enabled) {
            log.error("info:{}", body);
            return;
        }
        try {
            String result = HttpUtils.post(
                    host,
                    HttpUtils.Options.custom()
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> EntityUtils.toString(res.getEntity()))
                .fetch();
            if (!"ok".equals(result)) {
                log.error("call mattermost webhook error");
            }
        } catch (Exception e) {
            log.error("call mattermost webhook error: ", e);
        }
    }
}

