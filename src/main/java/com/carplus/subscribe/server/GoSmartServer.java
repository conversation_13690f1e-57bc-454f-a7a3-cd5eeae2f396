package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.redis.cache.Get;
import carplus.common.response.Result;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.enums.FileTypeEnum;
import com.carplus.subscribe.model.gosmart.BannerCategory;
import com.carplus.subscribe.model.presign.*;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GoSmartServer {

    @Value("${carplus.service.gosmart}")
    private String gosmartUri;
    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 取得OSS的TOKEN相關資訊
     */
    @NonNull
    public UserFilePresigned getOSSPresign(String filePath, String fileType) {
        PresignRequest request = new PresignRequest();
        request.setFilePath(filePath);
        request.setSource(HeaderDefine.SystemKind.SUB);
        request.setFileType(fileType);
        return getOSSPresign(request);
    }

    /**
     * 取得OSS的TOKEN相關資訊
     */
    @NonNull
    private UserFilePresigned getOSSPresign(PresignRequest request) {
        try {
            return HttpUtils.post(gosmartUri + "/internal/gosmart/v1/presigned/oss",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON))
                )
                .then(res -> {
                    Map<String, Object> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class).getData());

                    return BeanUtils.toBean(result, UserFilePresigned.class);
                })
                .fetch();
        } catch (Exception e) {
            log.error("call gosmart-service for get user file presign error：", e);
        }

        return null;
    }

    /**
     * 取得 City,Area
     */
    @Get(group = City.class, key = "'city'", ttl = -1L)
    @NonNull
    public List<City> getCityArea() {
        try {
            return HttpUtils.get(
                    gosmartUri + "/common/gosmart/v1/nationalCode/getAreaCity")
                .then(res -> {
                    List<Map<String, Object>> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class).getData());

                    return BeanUtils.toBean(result, City.class);
                })
                .fetch();
        } catch (Exception e) {
            log.error("call gosmart-service for get city/area error：", e);
        }

        return Lists.newArrayList();
    }

    public Map<String, Map<City, Area>> getCityAreaMapByPostalCode() {
        return getCityArea().stream()
                .flatMap(city -> city.getArea().stream().map(area -> new AbstractMap.SimpleEntry<>(area.getAreaCode(), new AbstractMap.SimpleEntry<>(city, area))))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            Map<City, Area> map = new HashMap<>();
                            map.put(entry.getValue().getKey(), entry.getValue().getValue());
                            return map;
                        },
                        (map1, map2) -> {
                            map1.putAll(map2);
                            return map1;
                        }
                ));
    }

    public String getAreaCodeByCityIdAndAreaId(Integer cityId, Integer areaId) {
        return cityId != null && areaId != null ? getCityArea().stream()
                .filter(city -> city.getCityId() == cityId)
                .flatMap(city -> city.getArea().stream().filter(area -> area.getAreaId() == areaId).map(Area::getAreaCode))
                .findFirst()
                .orElse(null) : null;
    }

    public GcsGetUploadUrlReq createGcsGetUploadUrlReq(FileTypeEnum fileTypeEnum) {
        GcsGetUploadUrlReq.TypeCount typeCount = GcsGetUploadUrlReq.TypeCount.builder()
            .mediaType(fileTypeEnum.getMediaType())
            .quantity(1)
            .build();
        return GcsGetUploadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(HeaderDefine.SystemKind.SUB)
            .isTemp(Boolean.TRUE)
            .requestList(Collections.singletonList(typeCount))
            .build();
    }

    public GcsUrlRes getGcsUploadUrl(GcsGetUploadUrlReq req) {
        String url = gosmartUri + "/internal/gosmart/v1/gcs/signedUrls/upload";
        log.info("getGcsUploadUrl, url={}, req={}", url, req);
        GcsUrlRes res = callGoSmartApi(url, req, GcsUrlRes.class);
        log.info("getGcsUploadUrl, res={}", res);
        return res;
    }

    public GcsUrlRes getGcsPublicUploadUrl(GcsGetUploadUrlReq req) {
        String url = gosmartUri + "/internal/gosmart/v1/gcs/signedUrls/publicUpload";
        log.info("getGcsPublicUploadUrl, url={}, req={}", url, req);
        GcsUrlRes res = callGoSmartApi(url, req, GcsUrlRes.class);
        log.info("getGcsPublicUploadUrl, res={}", res);
        return res;
    }

    private <T> T callGoSmartApi(String url, Object req, Class<T> resType) {
        try {
            String jsonStringReq = objectMapper.writeValueAsString(req);
            HttpUtils.Options options = HttpUtils.Options.custom()
                .entity(() -> new StringEntity(jsonStringReq, ContentType.APPLICATION_JSON));


            return HttpUtils.post(url, options)
                .then(res -> {
                    String jsonStringRes = EntityUtils.toString(res.getEntity());
                    log.info("callGoSmartApi, url={}, jsonStringRes={}", url, jsonStringRes);
                    return new ObjectMapper().readValue(jsonStringRes, resType);
                })
                .fetch();
        } catch (Exception e) {
            log.error("callGoSmartApi fail", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 建立 GCS 檔案下載 url 請求
     */
    public GcsGetDownloadUrlReq createGcsGetDownloadUrlReq(String filename) {
        return GcsGetDownloadUrlReq.builder()
            .source(HeaderDefine.SystemKind.SUB)
            .filePath(HeaderDefine.SystemKind.SUB)
            .isTemp(Boolean.TRUE)
            .fileNames(Collections.singletonList(filename))
            .build();
    }

    /**
     * 取得 GCS 檔案下載 url
     */
    public GcsUrlRes getGcsDownloadUrl(GcsGetDownloadUrlReq req) {
        String url = gosmartUri + "/internal/gosmart/v1/gcs/signedUrls/download";
        log.info("getGcsDownloadUrl, url={}, req={}", url, req);
        GcsUrlRes res = callGoSmartApi(url, req, GcsUrlRes.class);
        log.info("getGcsDownloadUrl, res={}", res);
        return res;
    }

    /**
     * 上傳GCS
     */
    public static void putFileToGCS(byte[] fileByte, String uploadUrl, String contentType) throws Exception {
        carplus.common.utils.HttpUtils.Options options = carplus.common.utils.HttpUtils.Options.custom().entity(() -> new ByteArrayEntity(fileByte));
        options.header("Content-Type", contentType);
        HttpUtils.put(uploadUrl, options)
            .then(res -> {
                log.info("{}", res);
                return null;
            }).fetch();
    }

    /**
     * 取得橫幅類別
     */
    public BannerCategory getBannerCategory(Integer bannerCategoryId) {
        try {
            return HttpUtils.get(String.format("%s/internal/gosmart/v3/bannerCategory/%d", gosmartUri, bannerCategoryId))
                .then(res -> {
                    Map<String, Object> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class).getData());
                    return BeanUtils.toBean(result, BannerCategory.class);
                })
                .fetch();
        } catch (Exception e) {
            log.error("call gosmart-service for get bannerCategory error：", e);
        }
        return null;
    }
}
