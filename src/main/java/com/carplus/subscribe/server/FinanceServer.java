package com.carplus.subscribe.server;

import carplus.common.response.CarPlusCode;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.StationsRepository;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.model.etag.ETagResponse;
import com.carplus.subscribe.model.etag.EtagValidResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class FinanceServer {

    @Value("${station.subscribe}")
    private String subscribeStationCode;
    @Value("${carplus.service.finance}")
    private String uri;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private StationsRepository stationsRepository;


    /**
     * 發票檢核
     */
    public void checkInvoice(Invoice invoice) {
        Map<String, Object> body = Maps.newHashMap();
        InvoiceDefine.InvCategory category = Objects.requireNonNull(InvoiceDefine.InvCategory.of(invoice.getCategory()));
        body.put("carrierType", category.getCarrierType());
        body.put("carrierID1", StringUtils.trim(invoice.getCarrierID()));
        body.put("donateCode", StringUtils.trim(invoice.getId()));

        try {
            HttpUtils.post(
                    uri + "/internal/finance/v1/invoice/invoiceCarrier",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> responseObj = objectMapper.readValue(EntityUtils.toString(res.getEntity()), Result.class);

                    if (responseObj.getStatusCode() != 0) {
                        throw new BadRequestException("發票檢核失敗 : " + StringUtils.trim(category.getTitle()));
                    }

                    return responseObj;
                })
                .fetch();
        } catch (Exception e) {
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            log.error("call finance-service invoiceCarrier error：", e);
            throw new ServerException("發票檢查服務異常");
        }
    }

    /**
     * etag 車號判斷是否為合法通路
     */
    public boolean isValidWithETag(@NonNull String plateNo) {
        try {
            EtagValidResponse rs = HttpUtils.get(
                    uri + "/internal/finance/v2/etag/rentId",
                    HttpUtils.Options.custom().queryString("plateNo", plateNo))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), EtagValidResponse.class))
                .fetch();

            return rs.getStatusCode() == 0 && StringUtils.isNotBlank(rs.getData());
        } catch (Exception e) {
            log.error("call finance-service etag rentId error：", e);
        }

        return false;
    }

    /**
     * etag 出車
     */
    @NonNull
    public ETagResponse rentWithETag(@NonNull ETagInfo etagInfo, @NonNull String plateNo, @NonNull String funId, String adminId) {
        String contractNo = Optional.ofNullable(etagInfo.getSrentalContractNo()).orElse(etagInfo.getOrderNo() + "-" + etagInfo.getStage());
        Map<String, Object> body = Maps.newHashMap();
        body.put("plateNo", plateNo);
        body.put("contractNo", contractNo);
        body.put("channelId", subscribeStationCode);
        body.put("funId", funId); // 承租行為: 01 新增租車資料 02 修改租車資料(只能於還車前執行) 03 租車契約作廢(還車後不得作廢)
        body.put("time", etagInfo.getDepartDate().toEpochMilli());

        try {
            return HttpUtils.post(
                    uri + "/internal/finance/v1/etag/rent",
                    HttpUtils.Options.custom()
                        .header("X-EmpId", adminId)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), ETagResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("call finance-service etag rent error：", e);

            ETagResponse errorResponse = new ETagResponse();
            errorResponse.setStatusCode(CarPlusCode.INTERNAL_SERVER_ERROR.getCode());
            errorResponse.setMessage("etag 出車服務異常：" + e.getMessage());

            return errorResponse;
        }
    }

    /**
     * etag 還車
     */
    @NonNull
    public ETagResponse returnWithETag(@NonNull ETagInfo etagInfo, @NonNull String funId, String plateNo, String adminId, String returnStationCode) {
        String contractNo = Optional.ofNullable(etagInfo.getSrentalContractNo()).orElse(etagInfo.getOrderNo() + "-" + etagInfo.getStage());
        Instant returnDate = Instant.now().minus(3, ChronoUnit.SECONDS);
        if (etagInfo.getReturnDate() != null) {
            returnDate = etagInfo.getReturnDate().minus(3, ChronoUnit.SECONDS);;
        } else {
            etagInfo.setReturnDate(returnDate);
        }
        Map<String, Object> body = Maps.newHashMap();
        body.put("plateNo", plateNo);
        body.put("contractNo", contractNo);
        body.put("channelId", subscribeStationCode);
        body.put("funId", funId); // 還車行為: 01 新增還車資料 02 修改還車資料(已有繳費記錄則不得修改) 03 還車且繳費
        body.put("time", returnDate.toEpochMilli());
        // 繳費狀態: 還車時, 當 FundID=03 還車且繳款, 則 PaymentStatus 必填
        // 01 信用卡(業者刷卡) 02 信用卡(遠通系統刷卡) 03 現金
        body.put("paymentStatus", "03");

        Stations station = stationsRepository.findById(returnStationCode).get();
        if (StringUtils.isNotBlank(station.getEmail())) {
            // 避免用分號傳送多組 email
            body.put("email", station.getEmail().split(";")[0]);
        }

        try {
            return HttpUtils.post(
                    uri + "/internal/finance/v1/etag/return",
                    HttpUtils.Options.custom()
                        .header("X-EmpId", adminId)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), ETagResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("call finance-service etag return error：", e);

            ETagResponse errorResponse = new ETagResponse();
            errorResponse.setStatusCode(CarPlusCode.INTERNAL_SERVER_ERROR.getCode());
            errorResponse.setMessage("etag 還車服務異常：" + e.getMessage());

            return errorResponse;
        }
    }

    /**
     * etag 查詢
     */
    @NonNull
    public ETagResponse queryWithETag(@NonNull ETagInfo etagInfo, String plateNo, String adminId) {
        String contractNo = Optional.ofNullable(etagInfo.getSrentalContractNo()).orElse(etagInfo.getOrderNo() + "-" + etagInfo.getStage());
        Map<String, Object> body = Maps.newHashMap();
        body.put("plateNo", plateNo);
        body.put("contractNo", contractNo);
        body.put("channelId", "2310"); // 固定丟 2310

        try {
            return HttpUtils.post(
                    uri + "/internal/finance/v1/etag/query",
                    HttpUtils.Options.custom()
                        .header("X-EmpId", adminId)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), ETagResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("call finance-service etag query error：", e);
            throw new BadRequestException("etag 查詢服務異常");
        }
    }
}
