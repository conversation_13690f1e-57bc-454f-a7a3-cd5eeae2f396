package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.LogicException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import carplus.common.utils.TaiwanIDUtils;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.CreditMechanismType;
import com.carplus.subscribe.enums.CreditRemarkType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.audit.BlackListQueryRes;
import com.carplus.subscribe.model.audit.UserDocumentsReviewByCashierPUTV2Req;
import com.carplus.subscribe.model.audit.UserDocumentsReviewInternalRes;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.auth.req.CustomerDriverPostReq;
import com.carplus.subscribe.model.auth.req.MultiQueryRequest;
import com.carplus.subscribe.model.auth.resp.*;
import com.carplus.subscribe.model.credit.CreditCheckFullResponse;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.credit.req.CreditCheckRequest;
import com.carplus.subscribe.model.credit.resp.CreditCheckFullRes;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Slf4j
@Component
public class AuthServer {

    @Value("${carplus.service.auth}")
    private String authUri;
    @Value("${carplus.service.audit}")
    private String auditUri;
    @Autowired
    private ObjectMapper objectMapper;
    @Value("${audit.timeout}")
    private Integer auditTimeout;

    private static final int BATCH_SIZE = 10;
    // 查詢超時時間 (秒)
    private static final int USER_QUERY_TIMEOUT_SECONDS = 30;
    private static final int SHUTDOWN_TIMEOUT_SECONDS = 10;
    private static final int FORCE_SHUTDOWN_TIMEOUT_SECONDS = 5;
    private static final int CLEANUP_INTERVAL_MINUTES = 5;

    // 專用執行緒池，用於 auth 相關的併發操作
    private final ExecutorService authOperationExecutor;

    // 定期清理執行緒池
    private final ScheduledExecutorService cleanupExecutor;

    // 用於追蹤正在進行的使用者查詢，避免對相同 acctId 的重複查詢
    private final ConcurrentHashMap<Integer, CompletableFuture<AuthUser>> ongoingUserRequests = new ConcurrentHashMap<>();

    public AuthServer() {
        // 優化執行緒池大小計算
        int processors = Runtime.getRuntime().availableProcessors();
        int threadPoolSize = Math.min(processors * 2, 16);

        this.authOperationExecutor = Executors.newFixedThreadPool(
            threadPoolSize,
            new ThreadFactoryBuilder()
                .setNameFormat("auth-operation-%d")
                .setDaemon(true)
                .setUncaughtExceptionHandler((thread, exception) -> log.error("執行緒 {} 發生未捕捉異常", thread.getName(), exception))
                .build()
        );

        // 提取清理執行緒池為成員變數，確保可以正確關閉
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor(
            new ThreadFactoryBuilder()
                .setNameFormat("user-request-cleanup-%d")
                .setDaemon(true)
                .build()
        );

        // 啟動定期清理任務
        this.cleanupExecutor.scheduleAtFixedRate(
            this::cleanupCompletedRequests,
            CLEANUP_INTERVAL_MINUTES,
            CLEANUP_INTERVAL_MINUTES,
            TimeUnit.MINUTES
        );
    }

    /**
     * 定期清理已完成的使用者查詢請求，防止記憶體洩漏
     */
    private void cleanupCompletedRequests() {
        try {
            int removedCount = 0;
            Iterator<Map.Entry<Integer, CompletableFuture<AuthUser>>> iterator = ongoingUserRequests.entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<Integer, CompletableFuture<AuthUser>> entry = iterator.next();
                CompletableFuture<AuthUser> future = entry.getValue();

                if (future.isDone() || future.isCancelled() || future.isCompletedExceptionally()) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                log.debug("清理了 {} 個已完成的使用者查詢請求", removedCount);
            }
        } catch (Exception e) {
            log.warn("清理使用者查詢請求時發生錯誤", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("開始關閉 AuthServer...");

        // 1. 先關閉清理執行緒池
        shutdownExecutorService(cleanupExecutor, "cleanup");

        // 2. 再關閉主要執行緒池
        shutdownExecutorService(authOperationExecutor, "auth-operation");

        // 3. 清理所有待處理的使用者查詢請求
        int cancelledCount = 0;
        for (CompletableFuture<AuthUser> future : ongoingUserRequests.values()) {
            if (future.cancel(true)) {
                cancelledCount++;
            }
        }
        ongoingUserRequests.clear();

        if (cancelledCount > 0) {
            log.info("取消了 {} 個待處理的使用者查詢請求", cancelledCount);
        }

        log.info("AuthServer 已完成關閉程序");
    }

    /**
     * 統一的執行緒池關閉邏輯
     */
    private void shutdownExecutorService(ExecutorService executor, String name) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            log.info("正在關閉 {} 執行緒池...", name);
            executor.shutdown();

            if (!executor.awaitTermination(SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                log.warn("{} 執行緒池未能在 {} 秒內正常關閉，強制關閉", name, SHUTDOWN_TIMEOUT_SECONDS);
                executor.shutdownNow();

                if (!executor.awaitTermination(FORCE_SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    log.error("{} 執行緒池未能在 {} 秒內強制關閉", name, FORCE_SHUTDOWN_TIMEOUT_SECONDS);
                } else {
                    log.info("{} 執行緒池已強制關閉", name);
                }
            } else {
                log.info("{} 執行緒池已正常關閉", name);
            }
        } catch (InterruptedException e) {
            log.warn("{} 執行緒池關閉過程被中斷", name);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 紀錄自動授信資訊
     */
    private void recordAutoCreditResult(Orders order, CreditCheckFullResponse response, boolean isCredit, CreditCheckFullRes check) {
        if (isCredit) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.AUTO_CREDIT_SUCCESS, CreditMechanismType.CRIF, null));
        } else {
            List<String> failMessage = null;
            CreditRemarkType autoCreditRemarkType = (response == null || response.getData() == null || response.getData().getPass() == CreditCheckFullRes.Pass.FAIL)
                ? CreditRemarkType.SYSTEM_CREDIT_FAIL :
                CreditRemarkType.AUTO_CREDIT_FAIL;
            if (check != null) {
                if (check.getPass() == CreditCheckFullRes.Pass.FAIL) {
                    autoCreditRemarkType = CreditRemarkType.CREDIT_FAIL;
                } else if (check.getPass() == CreditCheckFullRes.Pass.MANUALLY) {
                    autoCreditRemarkType = CreditRemarkType.AUTO_CREDIT_FAIL;
                }
                if (!check.getDetails().isEmpty()) {
                    failMessage = check.getDetails().stream()
                        .filter(detail -> !detail.getIsValided())
                        .map(CreditCheckFullRes.DetailRes::getMessage)
                        .collect(Collectors.toList());
                }
            } else {
                failMessage = Optional.ofNullable(response)
                    .filter(resp -> StringUtils.isNotEmpty(resp.getMessage()))
                    .map(resp -> Lists.newArrayList(resp.getMessage()))
                    .orElse(null);
            }
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(autoCreditRemarkType, CreditMechanismType.CRIF, failMessage));
        }
    }

    /**
     * 自動授信
     */
    public boolean doAutoCredit(@NonNull Orders order, AuthUser user) {
        if (Objects.equals(1, user.getIsForeigner()) || !TaiwanIDUtils.validateTaiwanID(user.getLoginId())) {
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.ID_VALIDATE_FAIL, CreditMechanismType.CRIF,
                    Collections.singletonList(CreditRemarkType.ID_VALIDATE_FAIL.getDescription())));
            return false;
        }

        CreditCheckRequest request = new CreditCheckRequest(order.getOrderNo(), user);
        log.info("訂單編號: {}, 請求授信: {}", order.getOrderNo(), request);
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v2/users/ccis/creditCheck",
                    HttpUtils.Options.custom()
                        .requestConfig(builder -> builder.setSocketTimeout(auditTimeout))
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    CreditCheckFullResponse response = objectMapper.readValue(res.getEntity().getContent(), CreditCheckFullResponse.class);

                    boolean isCredit = false;
                    CreditCheckFullRes check = null;
                    if (response != null && response.getStatusCode() == 0) {
                        check = response.getData();

                        log.info("訂單編號: {}, 授信結果：{}", order.getOrderNo(), check);
                        if (check != null && check.getPass() == CreditCheckFullRes.Pass.PASS) {
                            isCredit = true;
                        }
                    }
                    // 紀錄自動授信資訊
                    recordAutoCreditResult(order, response, isCredit, check);
                    return isCredit;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for CRIF check error：", e);
            order.setCreditInfo(Optional.ofNullable(order.getCreditInfo()).orElseGet(CreditInfo::new)
                .updateAutoCredit(CreditRemarkType.SYSTEM_CREDIT_FAIL, CreditMechanismType.CRIF, Lists.newArrayList(e.getMessage())));
        }

        return false;
    }

    /**
     * 取得會員資料 with retry - 防止對相同 acctId 的重複併發查詢
     */
    public AuthUser getUserWithRetry(int acctId) {
        log.debug("開始查詢使用者資料，acctId: {}", acctId);

        try {
            // 使用 computeIfAbsent 來簡化併發控制邏輯
            CompletableFuture<AuthUser> future = ongoingUserRequests.computeIfAbsent(acctId,
                id -> CompletableFuture
                    .supplyAsync(() -> getUserWithRetryInternal(id, 0), authOperationExecutor)
                    .whenComplete((result, throwable) -> {
                        // 查詢完成後自動清理
                        ongoingUserRequests.remove(id);
                        if (throwable == null) {
                            log.debug("成功查詢使用者資料，acctId: {}", id);
                        } else {
                            log.warn("查詢使用者資料失敗，acctId: {}", id, throwable);
                        }
                    })
            );

            // 使用 get(timeout, timeUnit) 來實現超時控制
            return future.get(USER_QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("查詢使用者資料逾時，acctId: {}", acctId);
            ongoingUserRequests.remove(acctId); // 清理逾時的請求
            throw new ServerException(String.format("查詢使用者資料逾時，acctId: %d", acctId), e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("查詢使用者資料被中斷，acctId: {}", acctId);
            throw new ServerException(String.format("查詢使用者資料被中斷，acctId: %d", acctId), e);
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            log.error("查詢使用者資料執行失敗，acctId: {}", acctId, cause);

            if (cause instanceof ServerException) {
                throw (ServerException) cause;
            } else if (cause instanceof CarPlusException) {
                throw (CarPlusException) cause;
            } else {
                throw new ServerException(String.format("查詢使用者資料失敗，acctId: %d", acctId), cause);
            }
        }
    }

    private AuthUser getUserWithRetryInternal(int acctId, int attempt) {
        final int maxAttempts = 3;

        try {
            log.debug("執行使用者查詢，acctId: {}，嘗試次數: {}", acctId, attempt + 1);
            return getUser(acctId);

        } catch (Exception e) {
            if (attempt >= maxAttempts) {
                log.error("使用者查詢已達最大重試次數 {} 次，查詢失敗，acctId: {}", maxAttempts, acctId, e);

                // 統一異常處理，保持原始異常類型
                if (e instanceof ServerException) {
                    throw (ServerException) e;
                } else if (e instanceof CarPlusException) {
                    throw (CarPlusException) e;
                } else {
                    throw new ServerException(String.format("使用者查詢重試 %d 次後仍然失敗，acctId: %d", maxAttempts, acctId), e);
                }
            }

            // 使用指數退避策略：500ms, 1s, 2s
            long waitTime = (long) Math.pow(2, attempt) * 500;
            log.info("使用者查詢失敗，將在 {}ms 後進行第 {} 次重試，acctId: {}", waitTime, attempt + 2, acctId);

            try {
                Thread.sleep(waitTime);
            } catch (InterruptedException ex) {
                Thread.currentThread().interrupt();
                log.error("使用者查詢重試等待被中斷，acctId: {}", acctId);
                throw new ServerException(String.format("使用者查詢重試等待被中斷，acctId: %d", acctId), ex);
            }

            return getUserWithRetryInternal(acctId, attempt + 1);
        }
    }

    /**
     * 取得會員資料 by account id
     */
    @NonNull
    public AuthUser getUser(int acctId) {
        try {
            return HttpUtils.post(
                auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctId)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    if (response.getData() != null && response.getData().getUsers() != null && !response.getData().getUsers().isEmpty()) {
                        return response.getData().getUsers().get(0);
                    }

                    throw new ServerException(String.format("找不到該ACCTID[%d]使用者", acctId));
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 身分證+電話
     */
    @Nullable
    public AuthUser getUser(@Nullable String cid, @Nullable String phone) {
        if (StringUtils.isBlank(cid) && StringUtils.isBlank(phone)) {
            return null;
        }

        try {
            return HttpUtils.get(
                    auditUri + "/internal/audit/v1/users/query",
                    HttpUtils.Options.custom().queryString("loginId", StringUtils.trim(cid)).queryString("mainCell", StringUtils.trim(phone)))
                .then(res -> {
                    AuthUserResponse response = objectMapper.readValue(res.getEntity().getContent(), AuthUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return response.getData();
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<Integer> getUsers(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        return getUserAcctIds(acctName, phone, idNo).stream().map(AuthUser::getAcctId).collect(Collectors.toList());
    }

    /**
     * 取得多筆會員資料 by accountId ignore Exception
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(Integer... acctIds) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/multiQuery",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(new MultiQueryRequest(acctIds)), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    MultiQueryResponse response = objectMapper.readValue(res.getEntity().getContent(), MultiQueryResponse.class);

                    return Optional.ofNullable(response)
                        .filter(r -> r.getStatusCode() == 0)
                        .map(Result::getData)
                        .map(com.carplus.subscribe.model.auth.resp.MultiQueryRes::getUsers)
                        .orElseGet(Lists::newArrayList);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return Lists.newArrayList();
    }

    /**
     * 取得會員資料 by 姓名/電話
     */
    @NonNull
    public List<AuthUser> getUserAcctIds(@Nullable String acctName, @Nullable String phone, @Nullable String idNo) {
        if (StringUtils.isBlank(acctName) && StringUtils.isBlank(phone) && StringUtils.isBlank(idNo)) {
            return Lists.newArrayList();
        }

        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(acctName)) {
                options.queryString("acctName", acctName);
            }
            if (StringUtils.isNotBlank(phone)) {
                options.queryString("mainCell", phone);
            }
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            }

            return HttpUtils.get(auditUri + "/internal/audit/v1/users/queries", options)
                .then(res -> {
                    QueriesResponse response = objectMapper.readValue(res.getEntity().getContent(), QueriesResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return Optional.ofNullable(response.getData())
                        .map(QueriesRes::getList)
                        .orElseGet(Lists::newArrayList);
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("call audit-service for get user error：{}", e.getMessage());
            throw new ServerException(e);
        }
    }

    /**
     * IVR需求單 會員中心註冊戳記
     **/
    public void ivrSignUp(@NonNull String ivrId, @NonNull String phone) {
        try {
            Map<String, Object> body = Maps.newHashMap();
            body.put("ivrId", ivrId);
            body.put("mainCell", phone);
            HttpUtils.post(authUri + "/internal/auth/v1/users/ivr/fastPass",
                    HttpUtils.Options
                        .custom()
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() != 0) {
                        log.error("ivr會員註冊戳記失敗：{}, {}", result.getMessage(), result.getData());
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("ivr會員註冊戳記失敗：", e);
        }
    }

    /**
     * 帳號狀態查詢，黑名單 & 各服務停權狀態
     */
    private BlackListQueryRes queryAccountStatus(@NonNull Integer acctId) {
        try {
            return HttpUtils.get(String.format("%s/internal/audit/v1/users/%d/account/status", auditUri, acctId))
                .then(res -> {
                    Result<BlackListQueryRes> blackListQueryResResult = objectMapper.readValue(res.getEntity().getContent(), new TypeReference<Result<BlackListQueryRes>>() {});
                    if (blackListQueryResResult.getStatusCode() != 0) {
                        throw new ServerException(blackListQueryResResult.getMessage());
                    }
                    return blackListQueryResResult.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for query account status error:", e);
            throw new ServerException(e);
        }
    }

    /**
     * 檢查帳號狀態，若在黑名單或訂閱停權則拋出 SubscribeException
     * 返回給前端 reason pattern: {"inBlackList": Boolean, "subSuspended": Boolean}
     */
    public void verifyAccountStatus(@NonNull Integer acctId) {
        BlackListQueryRes res = queryAccountStatus(acctId);

        // 若在黑名單或訂閱停權有任一狀態為 true 則拋出異常
        if (Boolean.TRUE.equals(res.getInBlackList()) || Boolean.TRUE.equals(res.getSubSuspended())) {
            Map<String, Object> blackListQueryResMap = new LinkedHashMap<>();
            blackListQueryResMap.put(BlackListQueryRes.Fields.inBlackList, res.getInBlackList());
            blackListQueryResMap.put(BlackListQueryRes.Fields.subSuspended, res.getSubSuspended());

            throw new SubscribeException(BACK_LIST, blackListQueryResMap);
        }
    }

    /**
     * 檢查帳號狀態，若在黑名單或訂閱停權則返回 true，其餘情況返回 false
     */
    public boolean isBlacklistedOrSubSuspended(@NonNull Integer acctId) {
        BlackListQueryRes res = queryAccountStatus(acctId);
        return Boolean.TRUE.equals(res.getInBlackList()) || Boolean.TRUE.equals(res.getSubSuspended());
    }

    /**
     * 檢查黑名單
     */
    @Nullable
    private Boolean checkBlackList(@Nullable String idNo, @Nullable Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (StringUtils.isNotBlank(idNo)) {
                options.queryString("loginId", idNo);
            } else if (Objects.nonNull(acctId)) {
                options.queryString("acctId", String.valueOf(acctId));
            }
            return HttpUtils.get(auditUri + "/internal/audit/v1/users/blacklist/verify", options)
                .then(res -> {
                    Result<List<Map<Object, Object>>> result = objectMapper.readValue(res.getEntity().getContent(), new TypeReference<Result<List<Map<Object, Object>>>>() {});
                    if (result.getStatusCode() == 0 && result.getData().isEmpty()) {
                        return true;
                    }
                    if (result.getStatusCode() == 0 && !result.getData().isEmpty()) {
                        log.warn("身份證字號或會員帳號為黑名單, pid={}, acctId={}", idNo, acctId);
                        return false;
                    }
                    log.info("身份證字號或會員帳號黑名單檢查, pid:{}, acctId:{}, 結果={}", idNo, acctId, result);
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for check blacklist error:", e);
        }
        return null;
    }

    /**
     * 檢查黑名單 by pId身分證
     */
    @NonNull
    public List<String> checkPidBlackList(@NonNull List<String> idNos) {
        if (idNos.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> blacklistedPids = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < idNos.size(); i += BATCH_SIZE) {
            List<String> batch = idNos.subList(i, Math.min(i + BATCH_SIZE, idNos.size()));

            CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() ->
                CompletableFuture.allOf(batch.stream()
                        .map(pid -> CompletableFuture.runAsync(() -> {
                            try {
                                Boolean result = checkBlackList(pid, null);
                                if (result != null && !result) {
                                    blacklistedPids.add(pid);
                                }
                            } catch (Exception ex) {
                                log.error("Error checking blacklist for pid: {}", pid, ex);
                            }
                        }, authOperationExecutor))
                        .toArray(CompletableFuture[]::new))
                    .join(), authOperationExecutor);

            futures.add(batchFuture);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("批次處理黑名單檢查時發生錯誤", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return blacklistedPids;
    }

    /**
     * 檢查黑名單 by acctId會員
     */
    @NonNull
    public List<Integer> checkAcctBlackList(@NonNull List<Integer> acctIds) {
        if (acctIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<Integer> blacklistedAccounts = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < acctIds.size(); i += BATCH_SIZE) {
            List<Integer> batch = acctIds.subList(i, Math.min(i + BATCH_SIZE, acctIds.size()));

            CompletableFuture<Void> batchFuture = CompletableFuture.runAsync(() ->
                CompletableFuture.allOf(batch.stream()
                        .map(acctId -> CompletableFuture.runAsync(() -> {
                            try {
                                Boolean result = checkBlackList(null, acctId);
                                if (result != null && !result) {
                                    blacklistedAccounts.add(acctId);
                                }
                            } catch (Exception ex) {
                                log.error("Error checking blacklist for acctId: {}", acctId, ex);
                            }
                        }, authOperationExecutor))
                        .toArray(CompletableFuture[]::new))
                    .join(), authOperationExecutor);

            futures.add(batchFuture);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("批次處理黑名單檢查時發生錯誤", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
        }

        return blacklistedAccounts;
    }

    /**
     * 駕駛轉換成會員
     */
    @NonNull
    public DriverDTOResponse driverToUsers(CustomerDriverPostReq customerDriverPostReq) {
        try {
            return HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/driver/convert",
                    HttpUtils.Options.custom()
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", "SUB")
                        .entity(() -> {
                            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(customerDriverPostReq), StandardCharsets.UTF_8);
                            stringEntity.setContentType("application/json");
                            return stringEntity;
                        }))
                .then(res -> objectMapper.readValue(res.getEntity().getContent(), DriverDTOResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("getUsers error：", e);
        }

        return null;
    }

    /**
     * 經銷商客戶資料查詢
     */
    @NonNull
    public List<AuthDealerUserResponse> getDealerUsers(@NonNull AuthDealerUserUnionQuery query) {
        HttpUtils.Options options = HttpUtils.Options.custom();
        options.header("X-MemberId", "SUB")
            .header("X-Platform", HeaderDefine.Platform.SERVER)
            .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
            .entity(() -> {
                StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(query), StandardCharsets.UTF_8);
                stringEntity.setContentType("application/json");
                return stringEntity;
            });

        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users/unionQuery", options)
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    return Optional.ofNullable(response.getData()).map(QueryAuthDealerUserRes::getUsers).orElseGet(ArrayList::new);
                })
                .fetch();
        } catch (Exception e) {
            log.error("getDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
    }

    @NonNull
    public AuthUser getAuthUser(DealerOrder dealerOrder) {
        AuthDealerUserResponse dealerUser = getDealerUser(dealerOrder.getDealerUserId());
        if (dealerUser == null) {
            throw new SubscribeException(DEALER_USER_NOT_FOUND);
        }
        List<AuthUser> authUsers = getUserAcctIds(dealerUser.getUserName(), dealerUser.getMainCell(), dealerUser.getIdNo());
        if (authUsers.isEmpty()) {
            throw new SubscribeException(ECONTRACT_ACCOUNT_NOT_EXISTS);
        }
        return authUsers.get(0);
    }

    @NonNull
    public AuthUser getAuthUser(EContractReferencable entity) {
        Integer acctId = entity.getAcctId();

        if (acctId != null) {
            return getUser(acctId);
        } else {
            return getAuthUser((DealerOrder) entity);
        }
    }

    public <T> AuthDealerUserResponse getDealerUser(@NonNull T idNoOrId) throws SubscribeException {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        if (idNoOrId instanceof String) {
            query.setIdNo(Collections.singletonList((String) idNoOrId));
        } else if (idNoOrId instanceof Long) {
            query.setIds(Collections.singletonList(((Long) idNoOrId).intValue()));
        } else {
            throw new SubscribeException(DEALER_USER_QUERY_PARAMETER_ERROR);
        }
        List<AuthDealerUserResponse> dealerUsers = getDealerUsers(query);
        if (dealerUsers.isEmpty()) {
            return null;
        }

        return dealerUsers.get(0);
    }

    /**
     * 經銷商客戶資訊從 AuthServer 取得
     * Generate DealerUserMap
     */
    public Map<Long, AuthDealerUserResponse> generateDealerUserMap(Set<Long> dealerUserIds) {
        AuthDealerUserUnionQuery query = new AuthDealerUserUnionQuery();
        query.setIds(dealerUserIds.stream().map(Long::intValue).collect(Collectors.toList()));
        return getDealerUsers(query).stream().collect(Collectors.toMap(AuthDealerUserResponse::getId, Function.identity()));
    }

    /**
     * 經銷商客戶資料新增/更新
     */
    @NonNull
    public List<AuthDealerUserResponse> saveDealerUsers(@NonNull AuthDealerUserSaveRequest request) {
        try {
            return HttpUtils.post(auditUri + "/internal/audit/v1/seaLand/users",
                    HttpUtils.Options.custom()
                        .header("X-MemberId", "SUB")
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.SUB)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    QueryAuthDealerUserResponse response = objectMapper.readValue(res.getEntity().getContent(), QueryAuthDealerUserResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }
                    if (response.getData() != null) {
                        if (CollectionUtils.isNotEmpty(response.getData().getErrorInfos())) {
                            throw new ServerException(response.getData().getErrorInfos().stream()
                                .map(error -> String.format("錯誤訊息: %s", error.getErrorMsg()))
                                .collect(Collectors.joining(",")));
                        } else {
                            return response.getData().getUsers();
                        }
                    }
                    throw new SubscribeException(DEALER_USER_CREATE_FAIL);
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call audit-service for save dealer user error：{}", e.getMessage());
            throw LogicException.of(DEALER_USER_CREATE_FAIL, Optional.ofNullable(e.getReason()).orElse("Unknown reason"));
        } catch (Exception e) {
            log.error("saveDealerUsers error：", e);
            throw new SubscribeException(DEALER_USER_CREATE_FAIL);
        }
    }

    /**
     * 使用者簽署最新使用者條款
     */
    @Async
    public void privacyPolicyAccept(String memberId, IOrder order) {
        if (StringUtils.isBlank(order.getDepartTaskId())) {
            return;
        }
        Map<String, String> parameter = new HashMap<>();
        parameter.put("corporation", "CARPLUS");
        parameter.put("userOrderNo", order.getOrderNo());
        try {
            Integer acctId = order instanceof Orders
                ? ((Orders) order).getContract().getMainContract().getAcctId()
                : getAuthUser((DealerOrder) order).getAcctId();
            parameter.put("acctId", acctId.toString());
            HttpUtils.post(
                    String.format("%s/internal/auth/v3/agreements/users/accept", authUri),
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-MemberId", memberId)
                        .header("X-System-Kind", HeaderDefine.SystemKind.CASHIER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(parameter), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    log.info("approval Policy response: {}", result);
                    return result;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approval Policy error：{}", e.getMessage(), e);
        }
    }

    /**
     * 拿取使用者證件照
     */
    @Nullable
    public UserDocumentsReviewInternalRes getAuditPreSignedReview(Integer acctId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.queryString("acctId", String.valueOf(acctId))
                .queryString("systemKind", HeaderDefine.SystemKind.CASHIER);
            return HttpUtils.get(auditUri + "/internal/audit/v2/users/documents/review", options)
                .then(res -> {
                    TypeReference<Result<UserDocumentsReviewInternalRes>> typeReference = new TypeReference<Result<UserDocumentsReviewInternalRes>>() {};
                    Result<UserDocumentsReviewInternalRes> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    if (result.getStatusCode() == 0 && result.getData() != null) {
                        return result.getData();
                    }
                    // 查無審核文件當作狀態-1
                    if (Objects.equals(result.getStatusCode(), 1308)) {
                        UserDocumentsReviewInternalRes resp = new UserDocumentsReviewInternalRes();
                        resp.setVerifyStatus(-1);
                        return resp;
                    }
                    log.info("拿取證件結果 fail.{},acctId:{}", result, acctId);
                    throw new BadRequestException("拿取證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for document review error:", e);
        }
        return null;
    }

    /**
     * 通過使用者證件照審核
     */
    @Nullable
    public void approveAuditPreSignedReview(UserDocumentsReviewByCashierPUTV2Req req, String memberId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            options.header("X-Platform", HeaderDefine.Platform.SERVER);
            options.header("X-System-Kind", HeaderDefine.SystemKind.CASHIER);
            options.header("X-MemberId", memberId);
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.acctId, req.getAcctId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardFrontId, req.getIdCardFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.idCardBackId, req.getIdCardBackId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.driverLicenseFrontId, req.getDriverLicenseFrontId().toString());
            options.queryString(UserDocumentsReviewByCashierPUTV2Req.Fields.reviewStatus, req.getReviewStatus().name());
            HttpUtils.put(auditUri + "/internal/audit/v2/users/documents/review/cashier", options)
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    // statusCode=1316, message=審核已通過，不可重複送審
                    if (result.getStatusCode() == 0 || result.getStatusCode() == 1316) {
                        return result.getData();
                    }
                    log.info("通過證件 fail.{},req:{}", result, req);
                    throw new BadRequestException("通過證件結果 fail");
                })
                .fetch();
        } catch (Exception e) {
            log.error("call audit-service for approve document review error:", e);
        }
    }

    /**
     * 通過使用者證件照審核
     */
    @Nullable
    @Async
    public void approveAuditPreSignedReview(Integer acctId, String memberId) {
        UserDocumentsReviewInternalRes reviewInternalRes = getAuditPreSignedReview(acctId);
        if (reviewInternalRes != null
            && reviewInternalRes.getVerifyStatus() != -1
            && reviewInternalRes.getIdCardFrontId() != null
            && reviewInternalRes.getIdCardBackId() != null
            && reviewInternalRes.getDriverLicenceFrontId() != null) {
            UserDocumentsReviewByCashierPUTV2Req req = new UserDocumentsReviewByCashierPUTV2Req();
            req.setAcctId(acctId);
            req.setIdCardFrontId(reviewInternalRes.getIdCardFrontId());
            req.setIdCardBackId(reviewInternalRes.getIdCardBackId());
            req.setDriverLicenseFrontId(reviewInternalRes.getDriverLicenceFrontId());
            req.setReviewStatus(UserDocumentsReviewByCashierPUTV2Req.CashierReviewStatus.PASS);
            approveAuditPreSignedReview(req, memberId);
        }
    }
}
