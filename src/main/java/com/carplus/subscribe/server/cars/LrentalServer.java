package com.carplus.subscribe.server.cars;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.lrental.*;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.server.cars.model.*;
import com.carplus.subscribe.server.cars.model.request.CarHistoryRequest;
import com.carplus.subscribe.server.cars.model.request.CarLocationRequest;
import com.carplus.subscribe.server.cars.model.request.CarPropertyRequest;
import com.carplus.subscribe.server.cars.model.request.Da41SearchReq;
import com.carplus.subscribe.service.ConfigService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.SingletonMap;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LrentalServer {

    @Value("${carplus.service.lrental}")
    private String lrentalUrl;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MattermostServer mattermostServer;

    @Autowired
    private ConfigService configService;

    /**
     * 長租車籍資料
     */
    @NonNull
    public List<Da41Entity> getLrentalCars(@NonNull List<String> plateNos) {

        try {
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/carLicense/findCarLicense", lrentalUrl),
                    HttpUtils.Options.custom().header(CarPlusConstant.HEADER_COMPANY_CODE, CarPlusConstant.CARPLUS_COMPANY_CODE)
                        .entity(() -> {
                            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(
                                Da41SearchReq.builder().daMaknoList(plateNos).build()), StandardCharsets.UTF_8);
                            stringEntity.setContentType("application/json");
                            return stringEntity;
                        })
                )
                .then(res -> {
                    CarsResponse result = objectMapper.readValue(res.getEntity().getContent(), CarsResponse.class);
                    if (result.getStatusCode() == 0) {
                        return Optional.ofNullable(result.getData()).orElseGet(Lists::newArrayList);
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call lrental-service for get car infos error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 新增契約
     *
     * @return 契約編號
     */
    public String addContract(ContractAddReq contractAddReq) {
        try {
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/contractModify/addContract", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(contractAddReq), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));

                    log.info("add Contract no : {}", result);
                    if (result.getData() == null) {
                        throw new ServerException(result.getMessage());
                    }
                    return result.getData().toString();
                }).fetch();
        } catch (Exception e) {
            log.error("call addContract fail: {}", e.toString());
            throw new ServerException(e);
        }
    }

    public ContractSearchRep getContractInfo(String daNo) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("dano", daNo);
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/contractSearch/contractInfo", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(map), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    JavaType subType = objectMapper.getTypeFactory().constructParametricType(List.class, ContractSearchRep.class);
                    JavaType javaType = objectMapper.getTypeFactory().constructParametricType(Result.class, subType);

                    Result<List<ContractSearchRep>> result = objectMapper.readValue(res.getEntity().getContent(), javaType);

                    log.info("get Contract Info no : {}", result);
                    if (result.getData() == null || result.getData().isEmpty()) {
                        return null;
                    }
                    return result.getData().get(0);
                }).fetch();
        } catch (Exception e) {
            log.error("call Contract Info fail: {}", e.toString());
            throw new ServerException(e);
        }
    }

    public ContractSearchRep getSeaLandContractInfoByPlatNo(String plateNo) {
        try {
            Map<String, String> map = new HashMap<>();
            map.put("daMakno", plateNo);
            map.put("daStat", "0");
            map.put("darent", configService.getSubscribeConfig().getSeaLandCompanyCode());
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/contractSearch/contractInfo", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(map), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    JavaType subType = objectMapper.getTypeFactory().constructParametricType(List.class, ContractSearchRep.class);
                    JavaType javaType = objectMapper.getTypeFactory().constructParametricType(Result.class, subType);

                    Result<List<ContractSearchRep>> result = objectMapper.readValue(res.getEntity().getContent(), javaType);

                    log.info("get Contract Info no : {}", result);
                    if (result.getData() == null || result.getData().isEmpty()) {
                        return null;
                    }
                    return result.getData().get(0);
                }).fetch();
        } catch (Exception e) {
            log.error("call Contract Info fail: {}", e.toString());
            throw new ServerException(e);
        }
    }


    /**
     * 查詢格上相關公司
     */
    public List<CarplusCompanyResponse> getCompanyInfo(String daRent) {
        try {
            return HttpUtils.get(
                    String.format("%s/common/lrentald/v1/carplus/company", lrentalUrl),
                    HttpUtils.Options.custom().queryString("daRentList", daRent))
                .then(res -> {
                    CarplusCompanyListResponse result = objectMapper.readValue(res.getEntity().getContent(), CarplusCompanyListResponse.class);

                    log.info("get CompanyInfo no : {}", result);
                    if (result.getData() == null) {
                        return null;
                    }
                    return result.getData();
                }).fetch();
        } catch (Exception e) {
            log.error("call getCompanyInfo fail: {}", e.toString());
            throw new ServerException(e);
        }
    }

    /**
     * 查詢總公司
     */
    public CarplusCompanyResponse getHeadquartersCompanyInfo(String daRent) {
        try {
            return HttpUtils.get(
                    String.format("%s/common/lrentald/v1/carplus/company/Headquarters", lrentalUrl),
                    HttpUtils.Options.custom().queryString("daRentList", daRent))
                .then(res -> {
                    CarplusCompanyMapResponse result = objectMapper.readValue(res.getEntity().getContent(), CarplusCompanyMapResponse.class);

                    log.info("get Headquarters CompanyInfo no : {}", result);
                    if (result.getData() == null) {
                        return null;
                    }
                    return result.getData().get(daRent);
                }).fetch();
        } catch (Exception e) {
            log.error("call Headquarters getCompanyInfo fail: {}", e.toString());
            throw new ServerException(e);
        }
    }

    public void createUserToDA71(ContractCustomerCreateRequest createRequest, String memberId) {
        try {
            HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/contractModify/customer", lrentalUrl),
                    HttpUtils.Options.custom().header(CarPlusConstant.AUTH_HEADER_MEMBER, memberId)
                        .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, HeaderDefine.SystemKind.SUB)
                        .header(CarPlusConstant.AUTH_HEADER_PLATFORM, CarPlusConstant.PLATE_FORM_CODE)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(createRequest), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));

                    log.info("add Contract no : {}", result);
                    if (result.getData() == null || result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }
                    return result.getData();
                }).fetch();
        } catch (Exception e) {
            log.error("call addContract fail: {}", e.toString());
            mattermostServer.notify("寫入長租DA71失敗", new SingletonMap<>("request", createRequest), e);
            throw new ServerException(e);
        }

    }

    /**
     * 更新中古車調度系統狀態
     */
    public void updateCarProperty(UpdateCarProperty updateCarProperty) {
        try {
            HttpUtils.patch(
                    String.format("%s/internal/lrentald/v1/crs/updateProperty", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(updateCarProperty), ContentType.APPLICATION_JSON)))
                .then(result -> result).fetch();
        } catch (Exception e) {
            log.error("call addContract fail: {}", e.toString());
            mattermostServer.notify("更新中古車調度系統狀態失敗", new SingletonMap<>("request", updateCarProperty), e);
            throw new ServerException(e);
        }

    }

    public CarInventoryInfo getCarLocation(String plateNo) {
        CarLocationRequest request = new CarLocationRequest();
        request.setMakNoList(Collections.singletonList(plateNo));
        try {
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/crs/carLocation", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<CarInventoryInfo>> typeReference = new TypeReference<Result<CarInventoryInfo>>() {
                    };
                    Result<CarInventoryInfo> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), typeReference));

                    if (result.getData() == null || result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }
                    return result.getData();
                }).fetch();
        } catch (Exception e) {
            log.error("call get CarLocation fail: {}", e.toString());
            mattermostServer.notify("查詢中古車調度系統狀態失敗", new SingletonMap<>("plateNo", plateNo), e);
            throw new ServerException(e);
        }
    }

    public CarPropertyResp getCarProperty(String plateNo) {
        CarPropertyRequest request = new CarPropertyRequest();
        request.setPlateNoSet(Collections.singletonList(plateNo));
        try {
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/crs/property", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<List<CarPropertyResp>>> typeReference = new TypeReference<Result<List<CarPropertyResp>>>() {
                    };
                    Result<List<CarPropertyResp>> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), typeReference));

                    if (result.getData() == null || result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }
                    if (CollectionUtils.isNotEmpty(result.getData())) {
                        return result.getData().get(0);
                    }
                    return null;
                }).fetch();
        } catch (Exception e) {
            log.error("call get CarProperty fail: {}", e.toString());
            mattermostServer.notify("查詢中古車調度系統狀態失敗", new SingletonMap<>("plateNo", plateNo), e);
            throw new ServerException(e);
        }
    }


    public List<CarHistorySearchRep> getCarHistory(List<Integer> crsNos) {
        CarHistoryRequest request = new CarHistoryRequest();
        request.setCarNoSet(crsNos);
        try {
            return HttpUtils.post(
                    String.format("%s/internal/lrentald/v1/repair/carHistory", lrentalUrl),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(request), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<List<CarHistorySearchRep>>> typeReference = new TypeReference<Result<List<CarHistorySearchRep>>>() {
                    };
                    Result<List<CarHistorySearchRep>> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), typeReference));

                    if (result.getData() == null || result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }
                    return result.getData();
                }).fetch();
        } catch (Exception e) {
            log.error("call get CarHistory fail: {}", e.toString());
            mattermostServer.notify("查詢中古車入庫狀態失敗", new SingletonMap<>("crsNo", crsNos), e);
            throw new ServerException(e);
        }
    }

    public CarHistorySearchRep getCarLastHistory(Integer crsNo) {
        List<CarHistorySearchRep> list = getCarHistory(Collections.singletonList(crsNo)).stream().sorted(Comparator.comparing(CarHistorySearchRep::getSgdt).reversed()).collect(Collectors.toList());
        return list.isEmpty() ? null : list.get(0);
    }

    public Map<Integer, CarHistorySearchRep> getCarLastHistoryMap(List<Integer> crsNos) {
        List<CarHistorySearchRep> list = getCarHistory(crsNos).stream().sorted(Comparator.comparing(CarHistorySearchRep::getSgdt).reversed()).collect(Collectors.toList());
        return list.stream().collect(Collectors.toMap(CarHistorySearchRep::getCarNo, carHistorySearchRep -> carHistorySearchRep, (k1, k2) -> k1));
    }


}
