package com.carplus.subscribe.server.cars.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CarPropertyResp {

    @Schema(description = "舊車籍序號")
    private Integer pyAuto;
    @Schema(description = "調度車態，CarItem.Type=1250")
    private Integer status;
    @Schema(description = "調度車態名稱")
    private String statusName;
    @Schema(description = "用途分配，CarItem.Type=415")
    private Integer purposeCode;
    @Schema(description = "用途分配名稱")
    private String purposeCodeName;
}
