package com.carplus.subscribe.server.cars.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class CarHistorySearchRep {
    /**
     * 出入庫歷史資料編號
     */
    @Schema(description = "出入庫歷史資料編號")
    private Integer carhistoryAuto;

    /**
     * 舊車籍編號
     */
    @Schema(description = "舊車籍編號")
    private Integer pyAuto;

    /**
     * 車籍編號
     */
    @Schema(description = "車籍編號")
    private Integer carNo;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    public String makNo;

    /**
     * 入庫倉庫
     */
    @Schema(description = "入庫倉庫")
    private Integer sgAuto;

    /**
     * 異動別，1：入庫、其餘：出庫
     */
    @Schema(description = "異動別，1：入庫、其餘：出庫")
    private Integer sgType;

    /**
     * 里程數
     */
    @Schema(description = "里程數")
    private Integer km;

    /**
     * 處理人
     */
    @Schema(description = "處理人")
    private String linceInUser;

    /**
     * 異動日
     */
    @Schema(description = "異動日")
    private Date sgdt;

    /**
     * 用途
     */
    @Schema(description = "用途")
    private Integer mission;

    /**
     * 建立日期
     */
    @Schema(description = "建立日期")
    private Date cdt;

    /**
     * 建立人
     */
    @Schema(description = "建立人")
    private String cUser;

    /**
     * 出庫倉庫
     */
    @Schema(description = "出庫倉庫")
    private Integer sgFauto;

    /**
     * 入庫倉庫名稱
     */
    @Schema(description = "入庫倉庫名稱")
    private String sgName;

    /**
     * 出庫倉庫名稱
     */
    @Schema(description = "出庫倉庫名稱")
    private String sourceSG;

    /**
     * 異動別名稱
     */
    @Schema(description = "異動別名稱")
    private String sgStatus;

    /**
     * 建立人名稱
     */
    @Schema(description = "建立人名稱")
    private String cuserName;

    /**
     * 用途名稱
     */
    @Schema(description = "用途名稱")
    private String missionName;

    /**
     * 是否為入庫
     */
    public boolean isCarInStock() {
        return Objects.equals(getSgType(), 1);
    }

}
