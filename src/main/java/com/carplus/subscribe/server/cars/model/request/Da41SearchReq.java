package com.carplus.subscribe.server.cars.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "車籍資料查詢")
public class Da41SearchReq {
    /**
     * 牌照號碼List
     */
    @Schema(description = "牌照號碼List")
    private List<String> daMaknoList;
    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private String daTrDt;
    /**
     * 異動狀況
     */
    @Schema(description = "異動狀況")
    private String daTrCd;
    /**
     * 排序方式(預設：da_carno 引擎號碼)
     */
    @Schema(description = "排序方式(預設：da_carno 引擎號碼)")
    private List<String> sortBy = Collections.singletonList("dacarno");
    /**
     * 引擎號碼List
     */
    @Schema(description = "引擎號碼List")
    private List<String> engineNoList;
    /**
     * PY_AUTO List
     */
    @Schema(description = "PY_AUTO List")
    private List<Integer> pyAutoList;
}
