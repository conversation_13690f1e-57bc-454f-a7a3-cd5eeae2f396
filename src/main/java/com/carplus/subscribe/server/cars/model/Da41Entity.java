package com.carplus.subscribe.server.cars.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 長租車籍資料
 */
@Data
public class Da41Entity {
    @Schema(description = "牌照號碼")
    private String damakno;
    @Schema(description = "引擎號碼")
    private String dacarno;
    @Schema(description = "車身號碼")
    private String dacarno2;
    @Schema(description = "公司別")
    private String dacomp;
    @Schema(description = "訂單編號")
    private String daodno;
    @Schema(description = "0舊名稱 9新名稱")
    private String dabyno;
    @Schema(description = "原始車號(950424確定)")
    private String daphno;
    @Schema(description = "廠牌代碼")
    private String daspec;
    @Schema(description = "行照車型")
    private String datype;
    @Schema(description = "車型代碼")
    private String daclas;
    @Schema(description = "車型英碼")
    private String daclasen;
    @Schema(description = "年份")
    private String dayear;
    @Schema(description = "能源種類")
    private String daoil;
    @Schema(description = "車種")
    private String dacust;
    @Schema(description = "顏色")
    private String dacolor;
    @Schema(description = "排氣量")
    private BigDecimal dacc;
    @Schema(description = "輪胎數")
    private BigDecimal dasercl;
    @Schema(description = "幾人座")
    private BigDecimal dapercnt;
    @Schema(description = "發票號碼")
    private String dainvno;
    @Schema(description = "取得金額")
    private BigDecimal dagetamt;
    @Schema(description = "原始領牌日(950424確定)")
    private String dagetdt;
    @Schema(description = "領照日期")
    private String damakdt;
    @Schema(description = "行照有效日")
    private String daenddt;
    @Schema(description = "建檔日期")
    private String dacrdt;
    @Schema(description = "期滿預估價")
    private BigDecimal daamtl;
    @Schema(description = "牌照類別")
    private String daco;
    @Schema(description = "保修單位")
    private String daunit;
    @Schema(description = "新舊車")
    private String danewold;
    @Schema(description = "異動日期")
    private String datrdt;
    @Schema(description = "異動狀況")
    private String datrcd;
    @Schema(description = "車種區隔")
    private String daCarSeg;
    @Schema(description = "汽車產地")
    private String daCarOrigin;
    @Schema(description = "車歷狀態")
    private String daCarStatus;
    @Schema(description = "原始領照日")
    private String daLinceDt;
    @Schema(description = "廠牌")
    private String brandCode;
    @Schema(description = "車型")
    private String classCode;
    @Schema(description = "車型明細")
    private String stdPriceCode;
    @Schema(description = "資產序號")
    private Integer pYauto;
    @Schema(description = "目前契約編號")
    private String da21no;
    @Schema(description = "保固年限")
    private Integer bodyKeepYear;
    @Schema(description = "保固公里數")
    private Integer bodyKeepKm;
    @Schema(description = "監理牌照號碼")
    private String stdMakno;
    @Schema(description = "繳銷日期")
    private String statDdt;
    @Schema(description = "出售日期")
    private String stat5Dt;
    @Schema(description = "保有終止日")
    private String quitDt;
    @Schema(description = "原始牌照號碼")
    private String srcMakno;
    @Schema(description = "丙種認證到期日")
    private String proveDueDt;
    @Schema(description = "專案碼")
    private Integer projectCode;
    @Schema(description = "領牌費用基準日")
    private String taxFeeDt;
    @Schema(description = "SuspendEndDT")
    private Date suspendEndDT;
    @Schema(description = "eTag註記")
    private Integer eTagFlag;
    @Schema(description = "eTag登錄狀態:0正常1移除")
    private Integer eTagStatus;
    @Schema(description = "下次驗車日期")
    private String nextCheckDt;
    @Schema(description = "貨車式樣")
    private Integer vanType;
    @Schema(description = "(罰單)丙種電子轉責")
    private Integer polECFlag;
}
