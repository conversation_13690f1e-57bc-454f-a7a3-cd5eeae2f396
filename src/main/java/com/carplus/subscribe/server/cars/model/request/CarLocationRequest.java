package com.carplus.subscribe.server.cars.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class CarLocationRequest {
    private boolean carInStockOnly = true;

    @Schema(description = "車輛流水號List")
    private List<Integer> carNoList;

    @Schema(description = "倉庫代碼List")
    private List<Integer> locationIdList;

    @Schema(description = "牌照號碼List")
    private List<String> makNoList;

    @Schema(description = "車輛財產序號List")
    private List<Integer> pyautoList;
}
