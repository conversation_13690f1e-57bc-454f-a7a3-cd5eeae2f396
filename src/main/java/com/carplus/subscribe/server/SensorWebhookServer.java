package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.model.sensor.SensorEventReq;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class SensorWebhookServer {

    @Value("${carplus.service.sensor}")
    private String sensorUrl;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 傳送事件到神策
     */
    @NonNull
    public void sendEventTrackToSensor(SensorEventReq eventReq) {
        try {
            HttpUtils.post(
                    sensorUrl + "/internal/sensorwebhook/v1/sensor/event/track",
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.GOSMART)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(eventReq), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<Map<String, Object>> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));

                    if (result.getStatusCode() != 0) {
                        log.error("傳遞事件至神策失敗:{}", result.getMessage());
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call sensorWebhook-service for send event track error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }
}
