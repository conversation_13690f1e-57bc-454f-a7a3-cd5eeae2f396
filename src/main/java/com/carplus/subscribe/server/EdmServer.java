package com.carplus.subscribe.server;

import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.model.edm.EdmRequest;
import com.carplus.subscribe.model.edm.EdmResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EdmServer {

    @Value("${carplus.service.edm}")
    private String uri;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 取得訂閱 mail-template
     */
    public EdmResponse subscribe(@NonNull EdmRequest edmSubscribeRequest) {
        try {
            String payload = objectMapper.writeValueAsString(edmSubscribeRequest);

            log.info("call edm-service for mail-template payload: {}", payload);
            return HttpUtils.post(uri + "/internal/v1/mailTemplate",
                    HttpUtils.Options.custom()
                        .entity(() -> new StringEntity(payload, ContentType.APPLICATION_JSON)))
                .then(res -> {
                    EdmResponse edmResponse = objectMapper.readValue(EntityUtils.toString(res.getEntity()), EdmResponse.class);
                    if (edmResponse.getStatusCode() != 0) {
                        throw new ServerException(edmResponse.getMessage());
                    }

                    return edmResponse;
                })
                .fetch();
        } catch (Exception e) {
            String msg = String.format("訂單: %s, edm-type: %s, call edm-service for mail-template", edmSubscribeRequest, edmSubscribeRequest.getType());
            log.error("{}", msg, e);
            throw new ServerException(msg, e);
        }
    }
}
