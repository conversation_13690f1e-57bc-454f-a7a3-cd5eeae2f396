package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.enums.coupon.SourceItem;
import com.carplus.subscribe.model.coupon.Coupon;
import com.carplus.subscribe.model.coupon.SearchMemberCouponResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CouponServer {

    @Value("${carplus.service.coupon}")
    private String couponUri;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 會員輸入序號領取優惠券
     *
     * @param eventCode 優惠券序號
     * @param acctId 會員ID
     */
    public void receiveCouponByCode(@NonNull String eventCode, int acctId) {
        try {
            Map<String, String> requestBody = Collections.singletonMap("eventCode", eventCode);

            HttpUtils.post(
                    couponUri + "/coupon/v1/receiveCouponByCode",
                    HttpUtils.Options.custom()
                        .header(CarPlusConstant.AUTH_HEADER_ACCT, String.valueOf(acctId))
                        .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, HeaderDefine.SystemKind.SUB)
                        .header(CarPlusConstant.AUTH_HEADER_PLATFORM, HeaderDefine.Platform.SERVER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(requestBody), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);

                    if (result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }

                    return result.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error("領取優惠券失敗，eventCode: {}, acctId: {}", eventCode, acctId, e);
            throw new ServerException(e);
        }
    }

    /**
     * 使用優惠券
     *
     * @param sequenceId 優惠券序號
     * @param acctId 會員ID
     * @param orderId 訂單ID
     * @param orderTime 訂單時間
     * @param phone 手機號碼
     * @param totalAmount 總金額
     * @param discountAmount 折扣金額
     */
    public void useCoupon(@NonNull String sequenceId, int acctId, @NonNull String orderId, long orderTime,
                          @NonNull String phone, int totalAmount, int discountAmount) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("acctId", acctId);
            requestBody.put("assignTime", null);
            requestBody.put("discountAmount", discountAmount);
            requestBody.put("orderId", orderId);
            requestBody.put("orderTime", orderTime);
            requestBody.put("phone", phone);
            requestBody.put("sequenceId", sequenceId);
            requestBody.put("source", SourceItem.SUBSCRIBE.getCode());
            requestBody.put("totalAmount", totalAmount);

            HttpUtils.post(
                    couponUri + "/internal/coupon/v3/coupon/use",
                    HttpUtils.Options.custom()
                        .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, HeaderDefine.SystemKind.SUB)
                        .header(CarPlusConstant.AUTH_HEADER_PLATFORM, HeaderDefine.Platform.SERVER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(requestBody), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);

                    if (result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }

                    return result.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error("使用優惠券失敗，sequenceId: {}, acctId: {}, orderId: {}", sequenceId, acctId, orderId, e);
            throw new ServerException(e);
        }
    }

    /**
     * 取消優惠券使用
     *
     * @param sequenceId 優惠券序號
     * @param acctId 會員ID
     * @param orderId 訂單ID
     */
    public void cancelCouponUsed(@NonNull String sequenceId, int acctId, @NonNull String orderId) {
        try {
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("acctId", acctId);
            requestBody.put("orderId", orderId);
            requestBody.put("sequenceId", sequenceId);
            requestBody.put("source", SourceItem.SUBSCRIBE.getCode());

            HttpUtils.post(
                    couponUri + "/internal/coupon/v2/cancelCouponUsed",
                    HttpUtils.Options.custom()
                        .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, HeaderDefine.SystemKind.SUB)
                        .header(CarPlusConstant.AUTH_HEADER_PLATFORM, HeaderDefine.Platform.SERVER)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(requestBody), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);

                    if (result.getStatusCode() != 0) {
                        throw new ServerException(result.getMessage());
                    }

                    return result.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error("取消優惠券使用失敗，sequenceId: {}, acctId: {}, orderId: {}", sequenceId, acctId, orderId, e);
            throw new ServerException(e);
        }
    }

    /**
     * 獲取會員可用優惠券
     *
     * @param acctId 會員ID
     * @return 優惠券資訊，如果不存在或無效則返回 null
     */
    @NonNull
    public List<Coupon> findValidCoupons(int acctId) {
        try {
            return HttpUtils.get(
                    couponUri + "/internal/coupon/v3/member/coupons",
                    HttpUtils.Options.custom()
                        .queryString("acctId", String.valueOf(acctId))
                        .queryString("isExpired", "false")
                        .queryString("readyToTake", "false")
                        .queryString("source", SourceItem.SUBSCRIBE.getCode()))
                .then(res -> {
                    TypeReference<Result<List<SearchMemberCouponResponse>>> typeReference = new TypeReference<Result<List<SearchMemberCouponResponse>>>() {};
                    Result<List<SearchMemberCouponResponse>> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);

                    if (result.getStatusCode() == 0 && CollectionUtils.isNotEmpty(result.getData())) {
                        return result.getData().stream()
                            .map(memberCoupon -> {
                                Coupon coupon = new Coupon();
                                coupon.setSequenceId(memberCoupon.getSequenceId());
                                coupon.setCouponId(memberCoupon.getCouponId());
                                coupon.setCouponName(memberCoupon.getCouponName());
                                coupon.setDescription(memberCoupon.getDescription());
                                coupon.setScope(memberCoupon.getScope());
                                coupon.setCalendarTypes(memberCoupon.getCalendarTypes());
                                coupon.setDiscountType(memberCoupon.getDiscountType());
                                coupon.setOverTotalPrice(Integer.parseInt(memberCoupon.getOverTotalPrice()));
                                coupon.setDiscount(Integer.parseInt(memberCoupon.getDiscount()));
                                coupon.setCarSeries(memberCoupon.getCarSeries());
                                coupon.setStartTime(memberCoupon.getStartTime());
                                coupon.setEndTime(memberCoupon.getEndTime());
                                return coupon;
                            })
                            .collect(Collectors.toList());
                    }
                    return Collections.<Coupon>emptyList();
                })
                .fetch();
        } catch (Exception e) {
            log.error("獲取優惠券列表失敗，acctId: {}", acctId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 根據序號獲取指定的會員優惠券
     *
     * @param sequenceId 優惠券序號
     * @param acctId 會員ID
     * @return 優惠券資訊，如果不存在或無效則返回 null
     */
    @Nullable
    public Coupon findCouponBySequenceId(@NonNull String sequenceId, int acctId) {
        List<Coupon> validCoupons = findValidCoupons(acctId);
        return validCoupons.stream()
            .filter(coupon -> Objects.equals(coupon.getSequenceId(), sequenceId))
            .findFirst()
            .orElse(null);
    }
}
