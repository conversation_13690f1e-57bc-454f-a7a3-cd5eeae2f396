package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.BadRequestException;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.NotifyRepository;
import com.carplus.subscribe.db.mysql.entity.Notify;
import com.carplus.subscribe.enums.NotifyStatus;
import com.carplus.subscribe.model.notify.Email;
import com.carplus.subscribe.model.notify.Push;
import com.carplus.subscribe.model.notify.Sms;
import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.carplus.subscribe.model.notify.maac.Maac;
import com.carplus.subscribe.model.push.req.MailSendV3Request;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.util.CastUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Component
public class PushServer {

    @Value("${carplus.service.push}")
    private String pushUri;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private NotifyRepository notifyRepository;

    /**
     * 發送簡訊
     */
    public void notify(@NonNull Sms sms) {
        Map<String, Object> body = Maps.newHashMap();
        body.put("nationalCode", sms.getNationalCode());
        body.put("msgmemobile", sms.getMsgmemobile());
        body.put("msgcontent", sms.getMsgcontent());
        body.put("msgtype", sms.getMsgtype());
        body.put("sender", sms.getSender());
        body.put("callback", sms.getCallback());

        try {
            HttpUtils.post(
                    pushUri + "/internal/notification/v2/SmsSend",
                    HttpUtils.Options.custom().header("X-System-Kind", HeaderDefine.SystemKind.GOSMART).entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));
                    if (result.getStatusCode() == 0) {
                        log.info("call push service send Sms：{}", result);
                    } else {
                        throw new ServerException(StringUtils.trim(result.getStatusCode() + ":" + result.getMessage()));
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call push-service for send sms error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }

    /**
     * 發送簡訊 (漸強 MAAC)
     */
    public void notify(@NonNull Maac maac) {
        try {
            HttpUtils.post(
                    pushUri + "/internal/maac/v3/pnp/push",
                    HttpUtils.Options.custom().header("X-System-Kind", HeaderDefine.SystemKind.GOSMART).entity(() -> new StringEntity(objectMapper.writeValueAsString(maac), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() == 0) {
                        log.info("call push service send Sms MAAC：{}", result);
                    } else {
                        throw new ServerException(StringUtils.trim(result.getStatusCode() + ":" + result.getMessage()));
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call push-service for send sms MAAC error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }

    /**
     * 手機推播
     */
    public void notify(@NonNull Push push) {
        try {
            HttpUtils.post(
                    pushUri + "/internal/notification/v1/PushNotification",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(push), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Map<String, Object> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Map.class));
                    Object statusCode = result.getOrDefault("statusCode", null);
                    if (statusCode != null && (int) statusCode == 131) {
                        log.error("call push-service push notification error : 該用戶未啟用推播 acctId: {}", push.getAcctid());
                        return null;
                    }
                    Object returnCode = result.getOrDefault("ReturnCode", null);
                    if (returnCode != null && (int) returnCode == 0) {
                        log.info("call push-service push notification：{}", result);
                    } else {
                        throw new ServerException(StringUtils.trim(result.get("RtnMessage")));
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call push-service for push notification error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }

    /**
     * 發送Email V2版本
     */
    public void notify(@NonNull Email email) {
        try {
            final ContentType TEXT_PLAIN = ContentType.create("text/plain", StandardCharsets.UTF_8);
            //  替換Html內容
            Resource htmlTemplate = new ClassPathResource(email.getHtmlTemplate());
            String content = getHtmlContent(htmlTemplate, email.getHtmlParam());
            MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                .addTextBody("receive", email.getReceive(), TEXT_PLAIN)
                .addTextBody("subject", email.getSubject(), TEXT_PLAIN)
                .addTextBody("content", content, TEXT_PLAIN)
                .addTextBody("systemKind", email.getSystemKind(), TEXT_PLAIN)
                .addTextBody("cc", Optional.ofNullable(email.getCc()).orElse(""), TEXT_PLAIN)
                .addTextBody("bcc", Optional.ofNullable(email.getBcc()).orElse(""), TEXT_PLAIN);
            HttpUtils.post(pushUri + "/internal/notification/v2/mailSend",
                    HttpUtils.Options
                        .custom()
                        .entity(builder::build))
                .then(res -> {
                    Result result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));
                    if (result.getStatusCode() == 0) {
                        log.info("call push service send Email：{}", result);
                    } else {
                        throw new ServerException(StringUtils.trim(result.getStatusCode() + ":" + result.getMessage()));
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call push-service for send email error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }

    /**
     * 發送 Email V3 版本
     */
    public void notifyV3(@NonNull MailSendV3Request mailSendRequest) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom();
            if (mailSendRequest.getAcctId() > 0) {
                options.header("X-AcctId", String.valueOf(mailSendRequest.getAcctId()));
            }
            HttpUtils.post(
                    pushUri + "/internal/notification/v3/mailSend",
                    options
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.GOSMART)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(mailSendRequest), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<?> result = objectMapper.readValue(res.getEntity().getContent(), Result.class);
                    if (result.getStatusCode() == 0) {
                        log.info("call push-service for send email v3：{}", result);
                    } else {
                        throw new ServerException(StringUtils.trim(result.getStatusCode() + ":" + result.getMessage()));
                    }
                    return null;
                })
                .fetch();
        } catch (Exception e) {
            log.error("call push-service for send email v3 error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }


    /**
     * 直接發送通知 並記錄結果
     */
    @Nullable
    public Notify notifyAndSave(@NonNull Notify notify) {
        String msg = null;
        NotifyStatus status = NotifyStatus.success;
        try {
            String notifyContent = notify.getNotifyContent();
            switch (notify.getNotifyType()) {
                case PUSH:
                    Push push = objectMapper.readValue(notifyContent, Push.class);
                    notify(push);
                    break;
                case SMS:
                    Sms sms = objectMapper.readValue(notifyContent, Sms.class);
                    notify(sms);
                    break;
                case MAAC:
                    JavaType type = objectMapper.getTypeFactory().constructParametricType(Maac.class, notify.getCategory().getBaseDataDtoType());
                    Maac<? extends BaseDataDTO> maac = objectMapper.readValue(notifyContent, type);
                    notify(maac);
                    break;
                case EMAIL:
                    Email email = objectMapper.readValue(notifyContent, Email.class);
                    if (email.isWithoutHtmlParams()) {
                        //只有V3可以寄附件
                        email.setAttachmentUrl(notify.getAttachmentUrl());
                        email.setDisplayFilename(notify.getDisplayFilename());
                        notifyV3(new MailSendV3Request(email));
                    } else {
                        notify(email);
                    }
                    break;
                default:
                    return null;
            }
        } catch (Exception e) {
            status = NotifyStatus.fail;
            msg = e.getMessage();
        }

        notify.setExeDate(new Date());
        notify.setStatus(status);
        notify.setMsg(msg);

        return notify;
    }

    /**
     * 重新發送通知 並記錄結果
     */
    @Nullable
    public Notify retryNotifyAndSave(@NonNull Notify retry) {
        retry.setNotifyId(null);
        retry.setExeDate(null);
        retry.setStatus(NotifyStatus.pending);
        retry.setMsg(null);
        retry.setCreateDate(null);
        retry.setDefDate(new Date(System.currentTimeMillis()));
        return this.notifyAndSave(retry);
    }

    /**
     * 重新發送通知 並記錄結果
     */
    @Nullable
    public Notify retryNotifyAndSave(long notifyId) {
        Notify retry = notifyRepository.findById(notifyId)
            .orElseThrow(() -> new BadRequestException("查無此Id:" + notifyId));

        return this.retryNotifyAndSave(retry);
    }


    /**
     * gen html內容
     */
    @NonNull
    private String getHtmlContent(@NonNull Resource htmlTemplate, @Nullable Map<String, Object> htmlParam) throws IOException {
        byte[] bytes;
        try (InputStream in = htmlTemplate.getInputStream()) {
            bytes = new byte[in.available()];
            in.read(bytes);
        }
        String content = StringUtils.byte2String(bytes, Charsets.UTF_8.toString());
        Document html = Jsoup.parse(content);
        // 替換html內容
        if (htmlParam != null) {
            htmlParam.forEach((key, val) -> {
                if (Objects.isNull(val)) {
                    return;
                }
                // 多筆處理
                if (val instanceof List) {
                    List<Map> rows = (List<Map>) val;
                    Element first = html.getElementById(key);
                    Element current = html.getElementById(key);
                    if (Objects.isNull(first)) {
                        return;
                    }
                    // 產生列
                    Element clone = first.clone();
                    for (int i = 1; i < rows.size(); i++) {
                        first.after(clone);
                        clone = clone.clone();
                    }
                    // 塞值
                    for (Map<String, Object> row : rows) {
                        for (String k : row.keySet()) {
                            if (k.startsWith("STYLE_")) {
                                Optional.ofNullable(current.getElementById(k))
                                    .ifPresent(e -> e.attr("style", String.valueOf(row.get(k))));
                            } else if (k.startsWith("HREF_")) {
                                Optional.ofNullable(current.getElementById(k))
                                    .ifPresent(e -> e.attr("href", String.valueOf(row.get(k))));
                            } else {
                                Optional.ofNullable(current.getElementById(k))
                                    .ifPresent(e -> e.text(String.valueOf(row.get(k))));
                            }
                        }
                        current = current.nextElementSibling();
                    }
                    return;
                }
                // 單筆處理
                if (key.startsWith("HREF_")) {
                    Optional.ofNullable(html.getElementById(key)).ifPresent(e -> e.attr("href", String.valueOf(val)));
                } else {
                    Optional.ofNullable(html.getElementById(key)).ifPresent(e -> e.text(String.valueOf(val)));
                }
            });
        }

        return html.toString();
    }
}
