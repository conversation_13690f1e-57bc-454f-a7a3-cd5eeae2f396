package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine.Platform;
import carplus.common.enums.HeaderDefine.SystemKind;
import carplus.common.enums.srental.GeoDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.StationsRepository;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.enums.AuditorLevel;
import com.carplus.subscribe.enums.JobTitle;
import com.carplus.subscribe.enums.StationDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.authority.*;
import com.carplus.subscribe.model.config.AdminRoleConfig;
import com.carplus.subscribe.service.ConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.MEMBER_INFO_NOT_FOUND;

@Slf4j
@Component
public class AuthorityServer {

    private static final String CARPLUS_COMPANY_CODE = "carplus";
    @Autowired
    private AuthorityProperties authorityProperties;
    @Value("${carplus.service.authority}")
    private String authorityUri;
    @Value("${carplus.service.audit}")
    private String auditUri;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private StationsRepository stationsRepository;
    @Autowired
    private ConfigService configService;

    /**
     * 依員編取得後台人員資料
     */
    @NonNull
    public List<MemberInfo> getMemberInfos(@Nullable String memberId) {
        if (StringUtils.isBlank(memberId)) {
            return Lists.newArrayList();
        }

        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/org/memberInfo", authorityUri),
                    HttpUtils.Options.custom().header(CarPlusConstant.AUTH_HEADER_MEMBER, memberId)
                )
                .then(res -> {
                    MemberInfoListResponse result = objectMapper.readValue(res.getEntity().getContent(), MemberInfoListResponse.class);
                    if (result.getStatusCode() == 0) {
                        return Optional.ofNullable(result.getData()).orElseGet(Lists::newArrayList);
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get member infos error：", e);
            throw new ServerException(e);
        }
    }

    public String getMemberName(String memberId) {
        return Optional.ofNullable(memberId)
            .map(this::getMemberInfos)
            .map(infos -> infos.stream()
                .findFirst()
                .orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND)))
            .map(MemberInfo::getMemberName)
            .orElse(null);
    }

    /**
     * 批量依員編取得後台人員資料
     *
     * @param memberIds 人員編號列表
     * @return Map key 為 memberId, value 為對應的人員資料
     */
    @NonNull
    public Map<String, MemberInfo> getMemberInfosByMemberIds(@Nullable Set<String> memberIds) {
        if (memberIds == null || memberIds.isEmpty()) {
            return Maps.newHashMap();
        }

        // 過濾空白的 memberId
        List<String> validMemberIds = memberIds.stream()
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());

        if (validMemberIds.isEmpty()) {
            return Maps.newHashMap();
        }

        try {
            // 建立請求 body
            Map<String, Object> requestBody = Maps.newHashMap();
            requestBody.put("memberIds", validMemberIds);

            String requestJson = objectMapper.writeValueAsString(requestBody);

            return HttpUtils.post(
                    String.format("%s/internal/authority/v1/org/memberInfoByMemberIds", authorityUri),
                    HttpUtils.Options.custom().entity(() -> new StringEntity(requestJson, ContentType.APPLICATION_JSON))
                )
                .then(res -> {
                    MemberInfoListResponse response = objectMapper.readValue(res.getEntity().getContent(), MemberInfoListResponse.class);

                    if (response.getStatusCode() != 0) {
                        throw new ServerException(response.getMessage());
                    }

                    List<MemberInfo> memberInfoList = Optional.ofNullable(response.getData()).orElseGet(Lists::newArrayList);

                    // 處理重複的 memberId，策略：保留第一筆資料
                    return memberInfoList.stream()
                        .collect(Collectors.toMap(
                            MemberInfo::getMemberId,
                            Function.identity(),
                            (existing, replacement) -> existing, // 保留第一筆，忽略後續重複的
                            LinkedHashMap::new
                        ));
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get member infos by member ids error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 依員編取得短租後台/收銀台使用角色
     */
    @NonNull
    public List<AdminRoleConfig> getAdminRoles(@Nullable String companyCode, @Nullable String memberId) {
        if (StringUtils.isBlank(companyCode) || StringUtils.isBlank(memberId)) {
            return Lists.newArrayList();
        }
        Map<String, AdminRoleConfig> adminRoleConfigMap = configService.getAdminRoleConfig().stream().collect(Collectors.toMap(AdminRoleConfig::getAdminRoleCode, Function.identity()));
        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/roles/getRolesByMember/%s/%s", authorityUri, companyCode, memberId),
                    HttpUtils.Options.custom()
                )
                .then(res -> {
                    RoleListResponse result = objectMapper.readValue(res.getEntity().getContent(), RoleListResponse.class);
                    if (result.getStatusCode() == 0) {
                        if (result.getData() != null) {
                            return result.getData().stream()
                                .map(Role::getRoleCode)
                                .map(adminRoleConfigMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        }

                        return Lists.<AdminRoleConfig>newArrayList();
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get member roles error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 查詢指定組織資訊
     */
    @NonNull
    public DepartmentInfo getDepartmentByCode(@NonNull String companyCode, @NonNull String departmentCode) {
        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/org/department/%s/%s", authorityUri, companyCode, departmentCode),
                    HttpUtils.Options.custom()
                )
                .then(res -> {
                    DepartmentInfoResponse result = objectMapper.readValue(res.getEntity().getContent(), DepartmentInfoResponse.class);
                    if (result.getStatusCode() == 0) {
                        return result.getData();
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get department info error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 取得該人員所有上層及下層單位資料
     */
    @NonNull
    public List<DepartmentInfo> getAllDirectlyDepartments(@Nullable String companyCode, @Nullable String departmentCode) {
        if (StringUtils.isBlank(companyCode) || StringUtils.isBlank(departmentCode)) {
            return Lists.newArrayList();
        }

        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/org/departmentNoStruct/%s/%s", authorityUri, companyCode, departmentCode),
                    HttpUtils.Options.custom()
                )
                .then(res -> {
                    DepartmentInfoListResponse result = objectMapper.readValue(res.getEntity().getContent(), DepartmentInfoListResponse.class);
                    if (result.getStatusCode() == 0) {
                        return Optional.ofNullable(result.getData()).orElseGet(Lists::newArrayList);
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get department infos error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 取得 carplus 指定組織人員資訊
     */
    @NonNull
    public List<MemberInfo> getMembersByDepartmentCode(@Nullable String departmentCode) {
        if (StringUtils.isBlank(departmentCode)) {
            return Lists.newArrayList();
        }

        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/org/member/getByDepartment", authorityUri),
                    HttpUtils.Options.custom().queryString("companyCode", CARPLUS_COMPANY_CODE).queryString("departmentCode", departmentCode)
                )
                .then(res -> {
                    MemberInfoListResponse result = objectMapper.readValue(res.getEntity().getContent(), MemberInfoListResponse.class);
                    if (result.getStatusCode() == 0) {
                        return Optional.ofNullable(result.getData()).orElseGet(Lists::newArrayList);
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get member infos by department error：", e);
            throw new ServerException(e);
        }
    }

    /**
     * 取得所有督導人員
     */
    @NonNull
    public List<MemberInfo> getAllSupervisor() {
        List<MemberInfo> result = Lists.newArrayList();

        boolean testEnabled = Optional.ofNullable(authorityProperties.getTest()).map(AuthorityProperties.Test::isEnabled).orElse(false);
        if (testEnabled) {
            Sets.newHashSet(
                authorityProperties.getTest().getMemberIdN1(),
                authorityProperties.getTest().getMemberIdN2(),
                authorityProperties.getTest().getMemberIdC(),
                authorityProperties.getTest().getMemberIdS(),
                authorityProperties.getTest().getMemberIdE()
            ).forEach(memberId -> result.addAll(getMemberInfos(memberId)));
        } else {
            getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getN1).orElseThrow(() -> new ServerException("無法取得門市北ㄧ區組織代碼"))
            )
                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                .ifPresent(result::add);
            getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getN2).orElseThrow(() -> new ServerException("無法取得門市北二區組織代碼"))
            )
                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                .ifPresent(result::add);
            getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getC).orElseThrow(() -> new ServerException("無法取得門市中區組織代碼"))
            )
                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                .ifPresent(result::add);
            getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getS).orElseThrow(() -> new ServerException("無法取得門市南區組織代碼"))
            )
                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                .ifPresent(result::add);
        }

        return result;
    }

    /**
     * 訂閱業務部主管
     */
    @Nullable
    public MemberInfo getScarMMaster() {
        boolean testEnabled = Optional.ofNullable(authorityProperties.getTest()).map(AuthorityProperties.Test::isEnabled).orElse(false);

        if (testEnabled) {
            return getMemberInfos(authorityProperties.getTest().getMemberIdSCarM()).stream().findFirst().orElse(null);
        } else {
            return getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getSCARD).orElseThrow(() -> new ServerException("無法取得訂閱業務管理課組織代碼"))
            ).stream().filter(m -> Objects.equals(m.getDirector(), 1)).findFirst().orElse(null);
        }
    }

    /**
     * 訂閱業務部主管
     */
    @Nullable
    public MemberInfo getScarGMaster() {
        boolean testEnabled = Optional.ofNullable(authorityProperties.getTest()).map(AuthorityProperties.Test::isEnabled).orElse(false);

        if (testEnabled) {
            return getMemberInfos(authorityProperties.getTest().getMemberIdSCarM()).stream().findFirst().orElse(null);
        } else {
            return getMembersByDepartmentCode(
                Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getSCARG).orElseThrow(() -> new ServerException("無法取得訂閱業務管理課組織代碼"))
            ).stream().filter(m -> Objects.equals(m.getDirector(), 1)).findFirst().orElse(null);
        }
    }

    /**
     * 依員編取得後台人員資料（在職）
     */
    @NonNull
    public List<MemberInfo> getMemberInfosNotRetired(@Nullable String memberId) {
        if (StringUtils.isBlank(memberId)) {
            return Lists.newArrayList();
        }

        try {
            return HttpUtils.get(
                    String.format("%s/internal/authority/v1/org/memberInfo", authorityUri),
                    HttpUtils.Options.custom().header(CarPlusConstant.AUTH_HEADER_MEMBER, memberId)
                )
                .then(res -> {
                    MemberInfoListResponse result = objectMapper.readValue(res.getEntity().getContent(), MemberInfoListResponse.class);
                    if (result.getStatusCode() == 0) {
                        if (result.getData() == null || result.getData().isEmpty()) {
                            throw new ServerException("查無員工資料");
                        }
                        return result.getData();
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call authority-service for get member infos error：", e);
            throw new ServerException(e);
        }
    }

    public List<MemberInfo> getMasterByStationAndAuditorLevel(@Nullable Stations station, @Nullable AuditorLevel auditorLevel) {
        List<MemberInfo> result = Lists.newArrayList();

        boolean testEnabled = Optional.ofNullable(authorityProperties.getTest()).map(AuthorityProperties.Test::isEnabled).orElse(false);
        if (station != null && auditorLevel != null) {
            switch (auditorLevel) {
                // 處級主管
                case SENIOR_MANAGER:
                    if (testEnabled) {
                        result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdL1()));
                    } else {
                        getMembersByDepartmentCode(
                            Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getL1).orElseThrow(() -> new ServerException("無法取得短租事業群組織代碼"))
                        )
                            .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                            .ifPresent(result::add);
                    }
                    break;
                // 部級主管
                case ASSISTANT_MANAGER:
//                    if (testEnabled) {
//                        result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdL2()));
//                    } else {
//                        getMembersByDepartmentCode(GeoDefine.GeoRegion.getAreaDepartCode(authorityProperties.getDepartmentCode(), station.getGeo()))
//                            .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
//                            .ifPresent(result::add);
//                    }
                    break;
                // 督導
                case SUPERVISOR:
                    switch (GeoDefine.GeoRegion.of(station.getGeoRegion())) {
                        case N1:
                            if (testEnabled) {
                                result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdN1()));
                            } else {
                                getMembersByDepartmentCode(
                                    Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getN1).orElseThrow(() -> new ServerException("無法取得門市北ㄧ區組織代碼"))
                                )
                                    .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                    .ifPresent(result::add);
                            }
                            break;
                        case N2:
                            if (testEnabled) {
                                result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdN2()));
                            } else {
                                getMembersByDepartmentCode(
                                    Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getN2).orElseThrow(() -> new ServerException("無法取得門市北二區組織代碼"))
                                )
                                    .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                    .ifPresent(result::add);
                            }
                            break;
                        case C:
                            if (testEnabled) {
                                result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdC()));
                            } else {
                                getMembersByDepartmentCode(
                                    Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getC).orElseThrow(() -> new ServerException("無法取得門市中區組織代碼"))
                                )
                                    .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                    .ifPresent(result::add);
                            }
                            break;
                        case S:
                            if (testEnabled) {
                                result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdS()));
                            } else {
                                getMembersByDepartmentCode(
                                    Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getS).orElseThrow(() -> new ServerException("無法取得門市南區組織代碼"))
                                )
                                    .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                    .ifPresent(result::add);
                            }
                            break;
                        case E:
                            if (testEnabled) {
                                result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdE()));
                            } else {
                                getMembersByDepartmentCode(
                                    Optional.ofNullable(authorityProperties.getDepartmentCode()).map(AuthorityProperties.Department::getE).orElseThrow(() -> new ServerException("無法取得門市東區組織代碼"))
                                )
                                    .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                    .ifPresent(result::add);
                            }
                            break;
                        default:
                    }
                    break;
                // 站長
                case MASTER:
                    if (testEnabled) {
                        result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdMaster()));
                    } else {
                        if (station.getStationCategory() == StationDefine.StationCategory.DEALER) {
                            getMembersByDepartmentCode(stationsRepository.getOne(station.getParentStationCode()).getNewLevelCode())
                                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                .ifPresent(result::add);
                        } else {
                            getMembersByDepartmentCode(station.getNewLevelCode())
                                .stream().filter(memberInfo -> Objects.equals(memberInfo.getDirector(), 1)).findFirst()
                                .ifPresent(result::add);
                        }
                    }
                    break;
                // 副站長
                case SUB_MASTER:
                    if (testEnabled) {
                        result.addAll(getMemberInfosNotRetired(authorityProperties.getTest().getMemberIdMaster()));
                    } else {
                        if (station.getStationCategory() == StationDefine.StationCategory.DEALER) {
                            getMembersByDepartmentCode(stationsRepository.getOne(station.getParentStationCode()).getNewLevelCode())
                                .stream().filter(memberInfo -> Objects.equals(JobTitle.J1031.name(), memberInfo.getJobTitleCode()))
                                .forEach(result::add);
                        } else {
                            getMembersByDepartmentCode(station.getNewLevelCode())
                                .stream().filter(memberInfo -> Objects.equals(JobTitle.J1031.name(), memberInfo.getJobTitleCode()))
                                .forEach(result::add);
                        }
                    }
                    break;
                default:
            }
        }

        return result;
    }

    /**
     * 更新會員證件審核狀態
     *
     * @param acctId 會員識別碼
     */
    public void updateUserApproval(@NonNull int acctId) {
        try {
            HttpUtils.post(
                    auditUri + "/internal/audit/v1/users/profile/" + acctId + "/approval",
                    HttpUtils.Options.custom()
                        .header(CarPlusConstant.AUTH_HEADER_PLATFORM, Platform.SERVER)
                        .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, SystemKind.SUB))
                .then(res -> {
                    Result<?> responseObj = objectMapper.readValue(EntityUtils.toString(res.getEntity()), Result.class);

                    if (responseObj.getStatusCode() != 0) {
                        throw new ServerException(responseObj.getMessage());
                    }

                    return responseObj.getData();
                })
                .fetch();
        } catch (CarPlusException e) {
            log.error("call authority-service for update user approval error：{}", e.getMessage());
        } catch (Exception e) {
            log.error("call authority-service for update user approval error：{}", e.getMessage());
        }
    }
}
