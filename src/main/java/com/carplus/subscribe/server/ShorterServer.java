package com.carplus.subscribe.server;

import carplus.common.response.Result;
import carplus.common.response.exception.CarPlusException;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.CastUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class ShorterServer {

    @Value("${carplus.service.shorter}")
    private String shorterUri;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 產生短網址
     *
     * @param expiresDate 有效期限
     * @param longUrl     實際網址
     */
    @NonNull
    public String createShort(long expiresDate, @NonNull String longUrl) {
        Map<String, Object> body = Maps.newHashMap();
        body.put("expiresDate", expiresDate);
        body.put("longUrl", longUrl);

        try {
            return HttpUtils.post(
                    shorterUri + "/internal/s/createShort",
                    HttpUtils.Options.custom().entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    Result<Map<String, Object>> result = CastUtils.cast(objectMapper.readValue(res.getEntity().getContent(), Result.class));

                    if (result.getData() != null) {
                        String shortUrl = StringUtils.trim(result.getData().get("shortUrl"));
                        if (StringUtils.isNotBlank(shortUrl)) {
                            return shortUrl;
                        }
                    }

                    throw new ServerException(result.getMessage());
                })
                .fetch();
        } catch (Exception e) {
            log.error("call shorter-service for create shorter url error：", e);
            if (e instanceof CarPlusException) {
                throw (CarPlusException) e;
            }
            throw new ServerException(e);
        }
    }
}
