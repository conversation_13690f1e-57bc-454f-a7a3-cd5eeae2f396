package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.exception.SubscribeException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.NO_HOLIDAY_LIST_FOUND;

@Slf4j
@Component
public class SrentalServer {
    private final ObjectMapper objectMapper;

    @Value("${carplus.service.srental}")
    private String srentalHost;

    public SrentalServer(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * getsrental
     */
    public List<Stations> getStationList() {
        try {
            return HttpUtils.get(
                    srentalHost + "/common/srental/v1/station",
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.SMART2GO)
                )
                .then(response -> {
                    if (200 != response.getStatusLine().getStatusCode()) {
                        throw new HttpException("srental API 異常 ");
                    }

                    Result<List<Stations>> res = objectMapper.readValue(response.getEntity().getContent(), new TypeReference<Result<List<Stations>>>() {
                    });
                    return res.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error(" call srental-service /common/srental/v1/station error: {}", e);
            throw new ServerException("srental 服務異常");
        }
    }

    public List<Stations> getAllStationList() {
        try {
            return HttpUtils.get(
                    srentalHost + "/internal/srental/v1/station",
                    HttpUtils.Options.custom()
                        .header("X-Platform", HeaderDefine.Platform.SERVER)
                        .header("X-System-Kind", HeaderDefine.SystemKind.SMART2GO)
                )
                .then(response -> {
                    if (200 != response.getStatusLine().getStatusCode()) {
                        throw new HttpException("srental API 異常 ");
                    }

                    Result<List<Stations>> res = objectMapper.readValue(response.getEntity().getContent(), new TypeReference<Result<List<Stations>>>() {
                    });
                    return res.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error(" call srental-service /common/srental/v1/station error: {}", e);
            throw new ServerException("srental 服務異常");
        }
    }


    public List<String> getNormalHolidayCalendar() {
        try {
            return HttpUtils.get(
                    srentalHost + "/common/srental/v1/calendar/holiday"
                )
                .then(response -> {
                    if (200 != response.getStatusLine().getStatusCode()) {
                        throw new HttpException("srental API 異常 ");
                    }

                    Result<List<String>> res = objectMapper.readValue(response.getEntity().getContent(), new TypeReference<Result<List<String>>>() {
                    });
                    return res.getData();
                })
                .fetch();
        } catch (Exception e) {
            log.error(" call srental-service /common/srental/v1/calendar/holiday error: {}", e);
            throw new ServerException("srental 服務異常");
        }
    }

    /**
     * 取得下一個距現在最近的假日日期並設置時間
     */
    public Calendar getNextUpcomingHoliday(int hourOfDay, int minute, int second, int millisecond) {
        Calendar nextUpcomingHoliday = Calendar.getInstance();
        List<String> normalHolidayCalendar = getNormalHolidayCalendar();
        if (normalHolidayCalendar.isEmpty()) {
            throw new SubscribeException(NO_HOLIDAY_LIST_FOUND);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            nextUpcomingHoliday.setTime(sdf.parse(normalHolidayCalendar.get(0)));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        nextUpcomingHoliday.set(Calendar.HOUR_OF_DAY, hourOfDay);
        nextUpcomingHoliday.set(Calendar.MINUTE, minute);
        nextUpcomingHoliday.set(Calendar.SECOND, second);
        nextUpcomingHoliday.set(Calendar.MILLISECOND, millisecond);

        return nextUpcomingHoliday;
    }
}
