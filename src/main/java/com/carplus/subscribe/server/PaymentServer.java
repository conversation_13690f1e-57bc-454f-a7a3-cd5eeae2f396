package com.carplus.subscribe.server;

import carplus.common.enums.HeaderDefine.Platform;
import carplus.common.enums.HeaderDefine.SystemKind;
import carplus.common.response.Result;
import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.model.payment.req.PayAuthRequest;
import com.carplus.subscribe.model.payment.resp.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class PaymentServer {

    @Value("${carplus.service.payment}")
    private String uri;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 付款
     */
    public PayAuthResponse pay(@NonNull PayAuthRequest paymentRequest) throws Exception {
        try {
            return HttpUtils.post(
                    uri + "/common/payment/v1/payment" + (paymentRequest.getType() == PayAuthRequest.PayType.prime ? "/payAuth" : "/payAuthCard"),
                    HttpUtils.Options.custom()
                        .header("X-AcctId", String.valueOf(paymentRequest.getAcctId()))
                        .header("X-Platform", Platform.SERVER)
                        .header("X-System-Kind", SystemKind.GOSMART)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(paymentRequest), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), PayAuthResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("訂單: {}, call payment-service for payAuth error:", paymentRequest.getOrderNo(), e);
            throw e;
        }
    }

    /**
     * 依訂單編號查詢付/退款紀錄
     */
    public AccountListResponse accountList(@NonNull String orderNo) throws Exception {
        try {
            return HttpUtils.get(
                    uri + "/internal/payment/v1/account/orderNo/" + orderNo + "/tradeIds",
                    HttpUtils.Options.custom()
                        .header("X-Platform", Platform.SERVER)
                        .header("X-System-Kind", SystemKind.GOSMART))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), AccountListResponse.class))
                .fetch();
        } catch (Exception e) {
            log.error("訂單: {}, call payment-service for accountList error:", orderNo, e);
            throw e;
        }
    }

    /**
     * 退款
     */
    @Nullable
    public Result refund(RefundRequest refundRequest) {

        try {
            return HttpUtils.post(
                    uri + "/internal/payment/v1/payment/refund",
                    HttpUtils.Options.custom()
                        .header("X-Platform", Platform.SERVER)
                        .header("X-System-Kind", "SUB")
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(refundRequest), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), Result.class))
                .fetch();
        } catch (Exception e) {
            log.error("call payment-service for refund error：", e);
            throw new ServerException("Payment API refund 失敗");
        }
    }

    /**
     * 當天請款
     */
    @Nullable
    public Result capToday(int acctId, @NonNull String orderNo, @NonNull String tradeId) {
        Map<String, Object> body = Maps.newHashMap();
        body.put("orderNo", orderNo);
        body.put("tradeId", tradeId);

        try {
            return HttpUtils.post(
                    uri + "/common/payment/v1/payment/capToday",
                    HttpUtils.Options.custom()
                        .header("X-AcctId", String.valueOf(acctId))
                        .header("X-Platform", Platform.SERVER)
                        .header("X-System-Kind", SystemKind.GOSMART)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(body), ContentType.APPLICATION_JSON)))
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), Result.class))
                .fetch();
        } catch (Exception e) {
            log.error("call payment-service for capToday error：", e);
            throw new ServerException("Payment API capToday 失敗");
        }
    }

    /**
     * 依訂單編號查詢付/退款紀錄
     */
    @Nullable
    public Result getAccountByOrderNoOrTradeId(@NonNull String orderNo, @Nullable String tradeId) {
        HttpUtils.Options options = null == tradeId
            ? HttpUtils.Options.custom() : HttpUtils.Options.custom().queryString("tradeId", tradeId);
        try {
            return HttpUtils.get(
                    uri + "/internal/payment/v1/account/orderNo/" + orderNo,
                    options)
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), Result.class))
                .fetch();
        } catch (Exception e) {
            log.error("call payment-service for getAccountByOrderNoOrTradeId error：", e);
        }
        return null;
    }


    @Nullable
    public PayOrderHeadList getPaymentByOrderNo(@NonNull String orderNo) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom().header("X-Platform", "SERVER").header("X-System-Kind", "SUB");
            return HttpUtils.get(
                    uri + "/internal/payment/v1/payment/admin/query/" + orderNo, options)
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), PayOrderHeadList.class))
                .fetch();
        } catch (Exception e) {
            log.error("call payment-service for getPaymentByOrderNo error：", e);
        }
        return null;
    }

    /**
     * 查詢Tappay交易紀錄
     */
    @Nullable
    public TradeHistoryResp getTappayHistory(@NonNull String tradeId) {
        try {
            HttpUtils.Options options = HttpUtils.Options.custom().header("X-Platform", "SERVER").header("X-System-Kind", "SUB");
            return HttpUtils.get(
                    uri + "/internal/payment/v1/payment/admin/history/" + tradeId, options)
                .then(res -> objectMapper.readValue(EntityUtils.toString(res.getEntity()), TradeHistoryResp.class))
                .fetch();
        } catch (Exception e) {
            log.error("call payment-service for getPaymentByOrderNo error：", e);
        }
        return null;
    }
}
