package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.model.CustomizedSearchPage;
import com.carplus.subscribe.model.insurance.InsurePlanListRequest;
import com.carplus.subscribe.model.insurance.InsurePlanSearchResultResponse;
import com.carplus.subscribe.model.insurance.RequisitionCreateRequest;
import com.carplus.subscribe.model.insurance.policy.PolicyDetailResponse;
import com.carplus.subscribe.model.insurance.policy.PolicyListRequest;
import com.carplus.subscribe.model.insurance.policy.PolicyListResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.carplus.subscribe.constant.CarPlusConstant.*;

@Service
@FeignClient(contextId = "InsuranceService", name = "insurance", url = "${carplus.service.insurance}")
public interface InsuranceClient {

    /**
     * 批次投保單暫存資料
     */
    @RequestMapping(value = "/internal/carinsurance/v1/requisitions_batch", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<?> addBatchInsurance(@RequestHeader(HEADER_COMPANY_CODE) String companyId, @RequestHeader(AUTH_HEADER_MEMBER) String memberId, @RequestBody List<RequisitionCreateRequest> requisitionCreateRequests);

    /**
     * 查詢最適保險方案
     */
    @RequestMapping(value = "/internal/carinsurance/v1/plan/insurePlanList", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<List<InsurePlanSearchResultResponse>> searchInsurePlanList(@RequestBody List<InsurePlanListRequest> requisitionCreateRequests);

    /**
     * 保單、退保查詢列表
     */
    @RequestMapping(value = "/internal/carinsurance/v1/policy", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<CustomizedSearchPage<PolicyListResponse>> queryPolicy(@RequestHeader(AUTH_HEADER_PLATFORM) String platform, @RequestHeader(AUTH_HEADER_SYSTEM_KIND) String systemKind, @SpringQueryMap PolicyListRequest policyListRequest);

    /**
     * 保單、退保詳細資料
     */
    @RequestMapping(value = "/internal/carinsurance/v1/policy/{policyId}", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<PolicyDetailResponse> queryPolicyDetail(@RequestHeader(AUTH_HEADER_PLATFORM) String platform, @RequestHeader(AUTH_HEADER_SYSTEM_KIND) String systemKind, @PathVariable String policyId);
}
