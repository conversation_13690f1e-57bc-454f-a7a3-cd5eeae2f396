package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.model.request.task.TaskAddRequest;
import com.carplus.subscribe.model.request.task.UpdateTaskRequest;
import com.carplus.subscribe.model.response.task.AddRentalTaskResponse;
import com.carplus.subscribe.model.response.task.TaskDetailResponse;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Service
@FeignClient(contextId = "TaskService", name = "task", url = "${carplus.service.taskservice}")
public interface TaskClient {

    /**
     * 新增出車/還車任務
     */
    @RequestMapping(value = "/internal/taskservice/task/v1/rentalTask", method = RequestMethod.PUT, headers = {"Content-Type=application/json"})
    Result<AddRentalTaskResponse> addRentalTask(@RequestBody TaskAddRequest taskAddRequest);


    /**
     * 刪除出還車任務
     */
    @RequestMapping(value = "/internal/taskservice/task/v1/rentalTask/id/{taskId}", method = RequestMethod.DELETE, headers = {"Content-Type=application/json"})
    Result<AddRentalTaskResponse> deleteRentalTask(@PathVariable String taskId);

    /**
     * 重製出還車任務
     */
    @RequestMapping(value = "/internal/taskservice/task/v1/rentalTask/reset/{taskId}", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<AddRentalTaskResponse> resetRentalTask(@PathVariable Integer taskId);


    /**
     * 取得任務詳細資訊
     */
    @RequestMapping(value = "/internal/taskservice/task/v1/rentalTask/{taskId}", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<TaskDetailResponse> getRentalTask(@PathVariable String taskId);


    /**
     * 強制異動任務
     */
    @RequestMapping(value = "/internal/taskservice/task/v1/rentalTask", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<TaskDetailResponse> updateRentalTask(@RequestBody UpdateTaskRequest updateTaskRequest);
}
