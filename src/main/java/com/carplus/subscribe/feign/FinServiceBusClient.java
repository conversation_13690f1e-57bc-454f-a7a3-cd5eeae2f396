package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.config.FeignLongTimeoutConfig;
import com.carplus.subscribe.model.finbus.BuildAccountReq;
import com.carplus.subscribe.model.finbus.ModifyMonthlyItemReq;
import com.carplus.subscribe.model.finbus.SecurityChangeCarReq;
import com.carplus.subscribe.model.invoice.v2.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Service
@FeignClient(contextId = "InvoiceService", name = "push", url = "${carplus.service.fin-service-bus}", configuration = FeignLongTimeoutConfig.class)
public interface FinServiceBusClient {


    @RequestMapping(value = "/internal/finservicebus/v1/invoice/issue", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<InvoiceInfoQueue> createInvoice(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestHeader("X-MemberId") String memberId, @RequestBody InvoiceIssueRequest invoiceIssueRequest);


    @RequestMapping(value = "/internal/finservicebus/v1/invoice/batch/cancel", method = RequestMethod.PATCH, headers = {"Content-Type=application/json"})
    Result<List<InvoiceCancelResponse>> cancelInvoice(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestHeader("X-MemberId") String memberId,
                                                      @RequestBody List<InvoiceCancelRequest> invoiceCancelRequestList);

    @RequestMapping(value = "/internal/finservicebus/v1/invoiceMaster", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<List<InvoiceMasterSearchResponse>> getInvoiceMaster(@RequestParam("invoiceNoSet") List<String> invoiceNoSet);

    @RequestMapping(value = "/internal/finservicebus/v1/invoiceData", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<List<InvoiceDataSearchResponse>> getInvoiceData(@RequestParam("invoiceNoSet") List<String> invoiceNoSet);

    @RequestMapping(value = "/internal/finservicebus/v1/allowance/issueByInvoiceNo", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<AllowanceInfoQueue> allowanceInvoice(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind, @RequestHeader("X-MemberId") String memberId,
                                                @RequestBody AllowanceIssueByInvoiceNoRequest allowanceIssueByInvoiceNoRequest);

    @RequestMapping(value = "/internal/finservicebus/v2/account/build", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<Object> checkout(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind,
                            @RequestBody BuildAccountReq req);

    @RequestMapping(value = "/internal/finservicebus/v1/account/update", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<Object> securityDepositChangeCar(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind,
                                            @RequestBody SecurityChangeCarReq req);

    @RequestMapping(value = "/internal/finservicebus/v2/account/modifyMonthlyItem", method = RequestMethod.POST, headers = {"Content-Type=application/json"})
    Result<Object> modifyMonthlyItem(@RequestHeader("X-Platform") String platform, @RequestHeader("X-System-Kind") String systemKind,
                                     @RequestBody ModifyMonthlyItemReq req);

}


