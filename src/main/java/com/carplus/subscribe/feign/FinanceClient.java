package com.carplus.subscribe.feign;

import carplus.common.response.Result;
import com.carplus.subscribe.config.FeignLongTimeoutConfig;
import com.carplus.subscribe.model.finance.ICBCRep;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Service
@FeignClient(contextId = "FinanceService", name = "FinanceService", url = "${carplus.service.finance}", configuration = FeignLongTimeoutConfig.class)
public interface FinanceClient {


    @RequestMapping(value = "/internal/finance/v1/remit/icbc", method = RequestMethod.GET, headers = {"Content-Type=application/json"})
    Result<List<ICBCRep>> getRemitInfo(@RequestParam(value = "incAccount") String incAccount,
                                        @RequestParam(value = "valueDt1") String valueDt1,
                                        @RequestParam(value = "valueDt2") String valueDt2,
                                        @RequestParam(value = "depNo") String depNo,
                                        @RequestParam(value = "remitterNo", required = false) String remitterNo);


}


