package com.carplus.subscribe.model;

import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.utils.DateUtil;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import lombok.Getter;

import java.time.temporal.ChronoUnit;
import java.util.Date;

@Getter
public class IncomeDTO {

    private static final String TIME_ZONE = "Asia/Taipei";
    @Parsed(field = "發票號碼")
    private String invNo;
    @Parsed(field = "訂單編號")
    private String orderNo;
    @Parsed(field = "開立日期")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date createDate;
    @Parsed(field = "開立金額(未)")
    private Integer unTax;
    @Parsed(field = "月費金額(未)")
    private Integer amount;
    @Parsed(field = "費用期數")
    private Integer stage;
    @Parsed(field = "費用起日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date startDate;
    @Parsed(field = "費用迄日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date endDate;
    @Parsed(field = "目標日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date targetDate;

    private long startToTargetDays;

    @Parsed(field = "總天數")
    private long dayDiffs;

    @Parsed(field = "已實現起日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date getEffectiveStartDate() {
        if (DateUtil.convertToStartOfInstant(startDate.toInstant()).isAfter(DateUtil.convertToStartOfInstant(targetDate.toInstant()))) {
            return null;
        } else {
            return startDate;
        }
    }

    @Parsed(field = "已實現迄日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date getEffectiveEndDate() {

        if (DateUtil.convertToEndOfInstant(startDate.toInstant()).isBefore(DateUtil.convertToEndOfInstant(targetDate.toInstant()))) {
            return targetDate;
        } else {
            return null;
        }
    }

    @Parsed(field = "已實現金額")
    private long getEffectiveAmount() {
        if (getEffectiveEndDate() == null) {
            return 0;
        } else {
            long result = Math.round(1.0 * amount * (1 - getUnEffectiveRatio()));
            if (result > amount) {
                return amount;
            }
            return result;
        }
    }

    @Parsed(field = "未實現金額")
    public long getUnEffectiveAmount() {
        if (getEffectiveEndDate() == null) {
            return amount;
        } else {
            return amount - getEffectiveAmount();
        }
    }


    @Parsed(field = "未實現起日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date getUnEffectiveStartDate() {
        if (startDate.toInstant().isAfter(targetDate.toInstant())) {
            return startDate;
        } else {
            return Date.from(DateUtil.convertToStartOfInstant(targetDate.toInstant().plus(1, ChronoUnit.DAYS)));
        }
    }

    @Parsed(field = "未實現迄日")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date getUnEffectiveEndDate() {
        return endDate;
    }

    @Parsed(field = "未來天數")
    private long getUnEffectiveDays() {
        long result = dayDiffs - startToTargetDays;
        return result > 0 ? result : 0;
    }

    @Parsed(field = "未來佔比")
    private double getUnEffectiveRatio() {
        return Math.round(100.0 * getUnEffectiveDays() / dayDiffs) / 100.0;
    }


    public static final String[] CSV_HEADER = {
        "發票號碼", "開立日期", "開立金額(未)", "月費金額(未)", "訂單編號", "費用期數", "費用起日", "費用迄日", "已實現金額", "已實現起日", "已實現迄日", "未實現金額", "未實現起日", "未實現迄日", "總天數", "未來天數", "未來佔比"};

    public IncomeDTO(String invNo, String orderNo, Date createDate, Integer unTax, Integer amount, Integer stage, CalculateStage calculateStage, Date targetDate) {
        this.invNo = invNo;
        this.orderNo = orderNo;
        this.createDate = createDate;
        this.unTax = unTax;
        this.amount = Long.valueOf(Math.round(1.0 * amount / 1.05)).intValue();
        this.stage = stage;
        this.startDate = Date.from(DateUtil.convertToStartOfInstant(calculateStage.getStartDate()));
        this.endDate = Date.from(DateUtil.convertToEndOfInstant(calculateStage.getEndDate()));
        this.targetDate = Date.from(DateUtil.convertToEndOfInstant(targetDate.toInstant()));

        this.dayDiffs = DateUtil.calculateDiffDate(startDate.toInstant(), endDate.toInstant(), ChronoUnit.DAYS) + 1L;
        this.startToTargetDays = getEffectiveStartDate() == null ? 0 : DateUtil.calculateDiffDate(getEffectiveStartDate().toInstant(), targetDate.toInstant(), ChronoUnit.DAYS) + 1L;
    }

    public void addAmount(Integer amount) {
        this.amount += Long.valueOf(Math.round(1.0 * amount / 1.05)).intValue();
    }

}
