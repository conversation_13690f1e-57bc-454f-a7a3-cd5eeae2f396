package com.carplus.subscribe.model;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPriceInfoCriteria {

    private Integer id;

    /**
     * 訂單編號
     */
    private List<String> orderNo;

    /**
     * 期數
     */
    private Integer stage;

    /**
     * 最後付款期限
     */
    private Instant lastPayDate;

    /**
     * 費用類別
     */
    private List<PriceInfoDefinition.PriceInfoCategory> category;

    /**
     * 費用型態
     */
    private Integer type;

    /**
     * 總里程
     */
    private Integer totalMileage;

    /**
     * 每公里里程費
     */
    private Double mileageFee;

    /**
     * 月費
     */
    private Integer monthlyFee;

    /**
     * 月數
     */
    private Integer month;

    /**
     * recTradeId編號
     */
    private String recTradeId;

    /**
     * payment編號
     */
    private Integer paymentId;

    /**
     * 關聯費用資訊
     */
    private Integer refPriceInfoNo;

    /**
     * 群組編號
     */
    private String uid;

    /**
     * 異動起始日
     */
    private Instant dateFrom;

    /**
     * 異動結束日
     */
    private Instant dateTo;
}
