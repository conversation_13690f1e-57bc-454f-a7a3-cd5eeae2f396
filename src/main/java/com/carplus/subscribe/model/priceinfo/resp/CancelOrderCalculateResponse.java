package com.carplus.subscribe.model.priceinfo.resp;

import com.carplus.subscribe.enums.CancellationPolicy;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class CancelOrderCalculateResponse {
    @Schema(description = "訂單取消等級")
    private CancellationPolicy level;
    @Schema(description = "保證金金額")
    private Integer securityDepositAmt;
    @Schema(description = "已付保證金")
    private Integer paidSecurityDeposit;

    @Schema(description = "訂單不同等級取消金額")
    private Map<String, CancellationPolicy> levelRefundAmtMap;
}
