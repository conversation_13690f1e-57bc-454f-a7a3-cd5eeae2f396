package com.carplus.subscribe.model.priceinfo;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.utils.DateUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND;

public class PriceInfoWrapper {

    // 初始構造函數，只能從 List 創建
    public PriceInfoWrapper(List<OrderPriceInfo> initialList) {
        this.orderPriceInfoList = Optional.ofNullable(initialList).orElseGet(ArrayList::new);
    }

    private final List<OrderPriceInfo> orderPriceInfoList;

    // 提供一個方法來獲取 Filter
    public PriceInfoWrapperFilter toFilter() {
        return new PriceInfoWrapperFilter(this.orderPriceInfoList);
    }

    // --- PriceInfoWrapper 內部的 Filter 類別 ---
    public static class PriceInfoWrapperFilter {
        private final List<OrderPriceInfo> originalList; // 篩選者也引用原始列表
        private final List<Predicate<OrderPriceInfo>> predicates; // 累積篩選條件
        private Comparator<OrderPriceInfo> comparator; // 篩選結果排序

        // Filter 的構造函數，只能從 PriceInfoWrapper 實體構建
        private PriceInfoWrapperFilter(List<OrderPriceInfo> initialList) {
            this.originalList = initialList;
            this.predicates = new ArrayList<>(); // 初始化空的 Predicate 列表
        }

        /**
         * 費用項目
         */
        public PriceInfoWrapperFilter category(PriceInfoDefinition.PriceInfoCategory... includes) {
            List<PriceInfoDefinition.PriceInfoCategory> includeList = Arrays.asList(includes);
            this.predicates.add(orderPriceInfo -> includeList.contains(orderPriceInfo.getCategory()));
            return this;
        }

        /**
         * 費用項目
         */
        public PriceInfoWrapperFilter notCategory(PriceInfoDefinition.PriceInfoCategory... excludes) {
            for (PriceInfoDefinition.PriceInfoCategory category : excludes) {
                this.predicates.add(orderPriceInfo -> orderPriceInfo.getCategory() != category);
            }
            return this;
        }

        /**
         * 費用型態：費用、折扣、退款
         */
        public PriceInfoWrapperFilter type(PriceInfoDefinition.PriceInfoType... includes) {
            List<Integer> includeList = Arrays.stream(includes).map(PriceInfoDefinition.PriceInfoType::getCode).collect(Collectors.toList());
            this.predicates.add(orderPriceInfo -> includeList.contains(orderPriceInfo.getType()));
            return this;
        }

        /**
         * 費用型態：費用、折扣、退款
         */
        public PriceInfoWrapperFilter notType(PriceInfoDefinition.PriceInfoType... excludes) {
            for (PriceInfoDefinition.PriceInfoType type : excludes) {
                this.predicates.add(orderPriceInfo -> orderPriceInfo.getType() != type.getCode());
            }
            return this;
        }

        /**
         * 未付款
         */
        public PriceInfoWrapperFilter unpaid() {
            return paid(false);
        }

        /**
         * 已付款
         */
        public PriceInfoWrapperFilter paid() {
            return paid(true);
        }

        /**
         * 是否付款
         */
        public PriceInfoWrapperFilter paid(boolean paid) {
            this.predicates.add(orderPriceInfo -> orderPriceInfo.isPaid() == paid);
            return this;
        }

        /**
         * 可收款
         */
        public PriceInfoWrapperFilter receivable() {
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getReceivableDate().isBefore(Instant.now()));
            return this;
        }

        /**
         * 網刷收款
         */
        public PriceInfoWrapperFilter paidOnline() {
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getPaymentId() != null);
            return this;
        }

        /**
         * 匯款收款
         */
        public PriceInfoWrapperFilter paidRemit() {
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getRemitAccountIds() != null && !orderPriceInfo.getRemitAccountIds().isEmpty());
            return this;
        }

        /**
         * 期數
         */
        public PriceInfoWrapperFilter stage(Integer stage) {
            this.predicates.add(orderPriceInfo -> Objects.equals(orderPriceInfo.getStage(), stage));
            return this;
        }

        /**
         * 期數
         */
        public PriceInfoWrapperFilter notStage(Integer stage) {
            this.predicates.add(orderPriceInfo -> !Objects.equals(orderPriceInfo.getStage(), stage));
            return this;
        }

        /**
         * 登打當下應收款項
         */
        public PriceInfoWrapperFilter currentReceivable() {
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getCategory() != SecurityDeposit && orderPriceInfo.getCategory() != ETag);
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getReceivableDate().compareTo(DateUtil.convertToEndOfInstant(Instant.now())) <= 0 || orderPriceInfo.getReceivedAmount() != 0);
            this.predicates.add(orderPriceInfo -> orderPriceInfo.getRefPriceInfoNo() == null || Optional.ofNullable(orderPriceInfo.getRefPriceInfo()).map(OrderPriceInfo::getCategory).orElse(null) != SecurityDeposit);

            return this;
        }

        /**
         * 彈性客制條件
         */
        public PriceInfoWrapperFilter add(Predicate<OrderPriceInfo> predicate) {
            this.predicates.add(predicate);
            return this;
        }

        /**
         * 通用排序方法
         *
         * @param keyExtractor 欄位取值函數，例如 OrderPriceInfo::getAmount
         * @param direction    升冪或降冪
         */
        public <U extends Comparable<? super U>> PriceInfoWrapperFilter sortBy(
            Function<OrderPriceInfo, U> keyExtractor,
            PriceInfoDefinition.SortDirection direction
        ) {
            Comparator<OrderPriceInfo> newComparator = Comparator.comparing(keyExtractor);

            if (direction == PriceInfoDefinition.SortDirection.DESC) {
                newComparator = newComparator.reversed();
            }

            // 支援多層排序：如果之前已設定 comparator，就用 thenComparing 串接
            if (this.comparator == null) {
                this.comparator = newComparator;
            } else {
                this.comparator = this.comparator.thenComparing(newComparator);
            }

            return this;
        }

        // --- 終端操作：構建最終結果 ---

        /**
         * 執行所有累積的篩選，並將結果收集為 List。
         * 此方法是 Filter 的終端操作，會觸發 Stream 處理。
         *
         * @return 經過篩選的 OrderPriceInfo 列表。
         */
        public List<OrderPriceInfo> collect() {
            Stream<OrderPriceInfo> stream = this.originalList.stream(); // 從原始 List 獲取 Stream

            // 應用所有累積的 Predicate
            for (Predicate<OrderPriceInfo> predicate : this.predicates) {
                stream = stream.filter(predicate);
            }

            // 如果有設定 comparator，就進行排序
            if (this.comparator != null) {
                stream = stream.sorted(this.comparator);
            }

            return stream.collect(Collectors.toList());
        }

        /**
         * 執行所有累積的篩選，並將結果封裝到一個新的 PriceInfoWrapper 物件中。
         * 此方法是 Filter 的終端操作，會觸發 Stream 處理。
         *
         * @return 一個新的 PriceInfoWrapper 物件，包含篩選後的數據。
         */
        public PriceInfoWrapper wrap() {
            return new PriceInfoWrapper(this.collect());
        }
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(this.orderPriceInfoList);
    }

    public boolean isNotEmpty() {
        return CollectionUtils.isNotEmpty(this.orderPriceInfoList);
    }

    public List<OrderPriceInfo> getList() {
        return new ArrayList<>(this.orderPriceInfoList); // 返回副本以保持內部列表安全
    }

    public OrderPriceInfo getById(Integer id) {
        return this.orderPriceInfoList.stream()
            .filter(orderPriceInfo -> Objects.equals(orderPriceInfo.getId(), id))
            .findFirst()
            .orElse(null);
    }

    public int getActualPrice() {
        return this.orderPriceInfoList.stream()
            .mapToInt(PriceInfoInterface::getActualPrice)
            .sum();
    }

    public int getActualReceivePrice() {
        return this.orderPriceInfoList.stream()
            .mapToInt(PriceInfoInterface::getActualReceivePrice)
            .sum();
    }

    /**
     * 每公里里程費
     */
    public double getMileageFeePerKM() {
        return this.orderPriceInfoList.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory() == MileageFee)
            .findFirst()
            .map(OrderPriceInfo::getInfoDetail)
            .map(PriceInfoDetail::getMileageFee)
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND));
    }
}
