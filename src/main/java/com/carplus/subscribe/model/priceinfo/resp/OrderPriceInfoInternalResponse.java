package com.carplus.subscribe.model.priceinfo.resp;

import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Data
@NoArgsConstructor
@Slf4j
public class OrderPriceInfoInternalResponse extends OrderPriceInfoResponse {

    @Schema(description = "最後更新者")
    private String updater;

    public OrderPriceInfoInternalResponse(OrderPriceInfo orderPriceInfo) {
        super(orderPriceInfo);
        // 額外設定 Internal 專用欄位
        this.updater = orderPriceInfo.getUpdater();
    }

    public OrderPriceInfoInternalResponse(OrderPriceInfo orderPriceInfo, Map<String, Sku> skuMap) {
        super(orderPriceInfo, skuMap);
        // 額外設定 Internal 專用欄位
        this.updater = orderPriceInfo.getUpdater();
    }

    public OrderPriceInfoInternalResponse(OrderPriceInfo orderPriceInfo, ETagInfo etagInfo, CalculateStage calculateStage, Map<String, Sku> skuMap) {
        super(orderPriceInfo, etagInfo, calculateStage, skuMap);
        // 額外設定 Internal 專用欄位
        this.updater = orderPriceInfo.getUpdater();
    }
}
