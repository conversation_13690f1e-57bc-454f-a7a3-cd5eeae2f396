package com.carplus.subscribe.model.priceinfo;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 費用鍵值類別，用於組織和比較費用記錄
 */
@Getter
@EqualsAndHashCode
@ToString
public class FeeKey {
    private final PriceInfoDefinition.PriceInfoCategory category;
    private final Integer stage;
    private final Integer type;

    public FeeKey(PriceInfoDefinition.PriceInfoCategory category, Integer stage, Integer type) {
        this.category = category;
        this.stage = stage;
        this.type = type;
    }
}