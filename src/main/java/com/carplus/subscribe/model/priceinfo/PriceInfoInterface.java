package com.carplus.subscribe.model.priceinfo;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.EmpDiscount;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.CouponDiscount;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.*;

public interface PriceInfoInterface {

    PriceInfoDetail getInfoDetail();

    List<Long> getRemitAccountIds();

    int getType();

    Integer getAmount();

    Integer getReceivedAmount();

    Instant getReceivableDate();

    Integer getPaymentId();

    String getUid();

    PriceInfoDefinition.PriceInfoCategory getCategory();

    Integer getRefPriceInfoNo();

    default boolean isPaid() {
        // 優惠券折扣的特殊邏輯：通過 receivedAmount 來判斷
        if (getCategory() == CouponDiscount && getType() == Discount.getCode()) {
            // 對於優惠券折扣，當關聯費用付款時，系統會設置 receivedAmount = amount
            // 這是優惠券折扣的標準付款標記方式
            return getAmount() != null && getReceivedAmount() != null &&
                   Math.abs(getAmount()) == Math.abs(getReceivedAmount()) &&
                   getReceivedAmount() != 0;
        }

        // 一般費用的付款判斷邏輯
        // 是否有網刷紀錄
        boolean hasPaymentId = getPaymentId() != null;
        // 是否有匯款紀錄
        boolean hasRemitAccounts = getRemitAccountIds() != null && !getRemitAccountIds().isEmpty();
        // 是否應收不為0且已收不為0 或是 應收為0
        boolean validAmounts = (getAmount() > 0 && getReceivedAmount() > 0) || getAmount() == 0;

        return hasPaymentId || (hasRemitAccounts && validAmounts);
    }

    @Schema(description = "取得付款狀態")
    default PayStatus getPayStatus() {
        if (StringUtils.isNotBlank(getUid())) {
            Boolean isAgree = Optional.ofNullable(getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(null);
            if (isAgree == null) {
                return PayStatus.CREDIT;
            } else if (!isAgree) {
                return PayStatus.NONE_PASS;
            }
        }

        if (isPaid()) {
            return PayStatus.PAID;
        } else if (getReceivedAmount() == 0 && getReceivableDate().isBefore(Instant.now())) {
            return PayStatus.UNPAID;
        } else {
            return PayStatus.PENDING;
        }
    }

    @Schema(description = "取得付款狀態顯示名稱")
    default String getPayStatusName() {
        return getPayStatus().getDisplayName(getType());
    }

    default int getActualPrice() {
        // 若有 uid 且未同意,金額為 0
        if (!isCredit()) {
            return 0;
        }
        // 付款為正數,退款為負數
        return (getType() == Pay.getCode() ? 1 : -1) * getAmount();
    }

    default boolean isEmpDiscount() {
        return getCategory().equals(EmpDiscount) && (getType() == Discount.getCode() || getType() == Refund.getCode()) && (getInfoDetail() == null || Boolean.TRUE == getInfoDetail().getIsAgree());
    }

    default int getActualReceivePrice() {
        if (getActualPrice() == 0) {
            return 0;
        }
        return (getActualPrice() > 0 ? 1 : -1) * getReceivedAmount();
    }

    default boolean isCredit() {
        return StringUtils.isBlank(getUid())
            || Optional.ofNullable(getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(false);
    }


}