package com.carplus.subscribe.model.priceinfo;

import com.carplus.subscribe.utils.DateUtil;
import lombok.Data;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Data
public class CalculateStage {
    private int stage;
    private Instant startDate;
    private Instant endDate;
    private Integer month;
    private Integer day;

    public Integer getMonth() {
        if (month == null) {
            month = DateUtil.calculateDiffMonth(startDate, endDate);
        }
        return month;
    }

    public Integer getDay() {
        if (day == null) {
            day = (int) DateUtil.calculateDiffDate(startDate, endDate, ChronoUnit.DAYS);
        }
        return day;
    }
}
