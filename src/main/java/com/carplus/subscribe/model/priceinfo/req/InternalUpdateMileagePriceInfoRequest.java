package com.carplus.subscribe.model.priceinfo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class InternalUpdateMileagePriceInfoRequest extends UpdateMileagePriceInfoRequest {

    /**
     * 使用者編號
     */
    @NotNull(message = "使用者編號不可為空")
    @Schema(description = "使用者編號")
    private Integer acctId;

    /**
     * 前次里程 (起始里程)
     */
    @Schema(description = "前次里程 (起始里程)")
    private Integer previousMileage;
}
