package com.carplus.subscribe.model.priceinfo.resp;

import com.carplus.subscribe.model.auth.AuthUser;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;

@Data
public class PriceInfoCSV {

    public static String[] HEADS = Arrays.stream(PriceInfoCSV.class.getDeclaredFields())
        .filter(field -> field.isAnnotationPresent(Parsed.class))
        .map(field -> field.getAnnotation(Parsed.class))
        .map(parsed -> parsed.field()[0])
        .toArray(String[]::new);

    private static final String TIME_ZONE = "Asia/Taipei";

    @Parsed(field = "費用編號")
    private Integer id;

    @Parsed(field = "訂單編號")
    private String orderNo;

    @Parsed(field = "車號")
    private String platNo;

    @Parsed(field = "期數")
    private Integer stage;

    @Parsed(field = "費用類別")
    private String category;

    @Parsed(field = "項目說明")
    private String description;

    @Parsed(field = "應收金額")
    private Integer amount;

    @Parsed(field = "實收金額")
    private Integer receivedAmount;

    @Parsed(field = "付款狀態")
    private String isPaid;

    @Parsed(field = "更新日期")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date updateDate;

    @Parsed(field = "客戶姓名")
    private String acctName;

    @Parsed(field = "手機國碼")
    private String nationalCode;

    @Parsed(field = "手機號碼")
    private String mainCell;

    public PriceInfoCSV(OrderPriceInfoWithDescription orderPriceInfo, String plateNo, AuthUser user) {
        this.id = orderPriceInfo.getId();
        this.orderNo = orderPriceInfo.getOrderNo();
        this.platNo = plateNo;
        this.stage = orderPriceInfo.getStage();
        this.description = orderPriceInfo.getDescription();
        this.category = orderPriceInfo.getCategory().getDescriptionName();
        this.amount = positive(orderPriceInfo.getType()) * orderPriceInfo.getAmount();
        this.receivedAmount = positive(orderPriceInfo.getType()) * orderPriceInfo.getReceivedAmount();
        this.isPaid = orderPriceInfo.isPaid() ? "已付款" : "未付款";
        this.updateDate = Date.from(orderPriceInfo.getInstantUpdateDate());
        if (user != null) {
            this.acctName = user.getAcctName();
            this.nationalCode = user.getNationalCode();
            this.mainCell = user.getMainCell();
        }
    }

    private int positive(Integer type) {
        if (type == 0) {
            return 1;
        }
        return -1;
    }
}
