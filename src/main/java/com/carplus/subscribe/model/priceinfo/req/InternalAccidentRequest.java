package com.carplus.subscribe.model.priceinfo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class InternalAccidentRequest {
    @Schema(description = "是否還車議價中")
    private String returnNego = "N";
    @Schema(description = "備註")
    private String remark;
    @Schema(description = "是否車損")
    private boolean carDamaged;
    @Schema(description = "應收自付額")
    private int aRCarLossAmt;
    @Schema(description = "實收自付額")
    private int carLossAmt;

}
