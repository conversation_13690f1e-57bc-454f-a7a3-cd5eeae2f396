package com.carplus.subscribe.model.priceinfo.resp;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MileageFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.MonthlyFee;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Discount;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;

@Data
@NoArgsConstructor
public class StageOrderPriceInfoResponse {

    @Schema(description = "費用清單明細")
    private List<? extends OrderPriceInfoResponse> orderPriceInfoList;

    @Schema(description = "期數資訊")
    private CalculateStage calculateStage;

    @Schema(description = "優惠資訊")
    private List<DiscountInfo> discountInfoList;

    @JsonIgnore
    private static NumberFormat numberFormat = NumberFormat.getNumberInstance();

    public StageOrderPriceInfoResponse(List<? extends OrderPriceInfoResponse> list) {
        this.orderPriceInfoList = list;
        this.calculateStage = list.get(0).getCalculateStage();

        discountInfoList = new ArrayList<>();
        orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory().equals(MileageFee) && orderPriceInfo.getType() == Pay.getCode()).findFirst().ifPresent(orderPriceInfo -> {
            generateMileageDiscountInfo(discountInfoList, "長租期優惠", Optional.ofNullable(orderPriceInfo.getInfoDetail().getRentalDiscountMileage()).orElse(0));
            generateMileageDiscountInfo(discountInfoList, "原車續約優惠", Optional.ofNullable(orderPriceInfo.getInfoDetail().getRenewDiscountMileage()).orElse(0));
            // 預設優惠為舊的優惠呈現
            int originalDiscountMileage = Optional.ofNullable(orderPriceInfo.getInfoDetail().getOriginalDiscountMileage()).orElse(0)
                - Optional.ofNullable(orderPriceInfo.getInfoDetail().getRenewDiscountMileage()).orElse(0)
                - Optional.ofNullable(orderPriceInfo.getInfoDetail().getRentalDiscountMileage()).orElse(0);
            generateMileageDiscountInfo(discountInfoList, "預設優惠", originalDiscountMileage);
            generateMileageDiscountInfo(discountInfoList, "贈送里程優惠", Optional.ofNullable(orderPriceInfo.getInfoDetail().getDiscountMileage()).orElse(0)
                - Optional.ofNullable(orderPriceInfo.getInfoDetail().getOriginalDiscountMileage()).orElse(0));
        });
        orderPriceInfoList.stream().filter(orderPriceInfo -> orderPriceInfo.getCategory().equals(MonthlyFee) && orderPriceInfo.getType() == Discount.getCode()).findFirst().ifPresent(orderPriceInfo -> {
            discountInfoList.add(DiscountInfo.builder().discountDesc("基本月費優惠").discountCondition(String.format("每月 %s 元",
                numberFormat.format(Optional.ofNullable(orderPriceInfo.getAmount()).orElse(0) / Optional.ofNullable(orderPriceInfo.getInfoDetail().getMonth()).orElse(1)))).build());
        });
        orderPriceInfoList.stream().filter(OrderPriceInfoResponse::isEmpDiscount)
            .mapToInt(OrderPriceInfoResponse::getAmount).reduce(Integer::sum).ifPresent(stageEmpDiscount ->
                discountInfoList.add(DiscountInfo.builder().discountDesc("門市折扣").discountCondition(String.format("$ %s 元", numberFormat.format(stageEmpDiscount))).build()));
    }

    public int getTotalAmt() {
        return orderPriceInfoList.stream().filter(p ->
                !p.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.SecurityDeposit))
            .mapToInt(PriceInfoInterface::getActualPrice).sum();
    }

    public void generateMileageDiscountInfo(List<DiscountInfo> discountInfoList, String name, Integer mileage) {
        if (mileage != null && mileage > 0) {
            discountInfoList.add(DiscountInfo.builder().discountDesc(name).discountCondition(String.format("每期 %s km",
                numberFormat.format(Optional.of(mileage).orElse(0)))).build());
        }


    }
}
