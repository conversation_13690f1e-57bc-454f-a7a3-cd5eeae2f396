package com.carplus.subscribe.model.priceinfo.resp;

import carplus.common.utils.BeanUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPriceInfoWithDescription extends OrderPriceInfo {

    @Schema(description = "項目說明")
    private String description;

    public OrderPriceInfoWithDescription(OrderPriceInfo orderPriceInfo, Map<String, Sku> skuMap) {
        BeanUtils.copyProperties(orderPriceInfo, this);

        this.description = orderPriceInfo.getCategory() == PriceInfoDefinition.PriceInfoCategory.Merchandise
            ? Optional.ofNullable(orderPriceInfo.getSkuCode())
            .filter(StringUtils::isNotBlank)
            .filter(skuCode -> skuMap != null && skuMap.containsKey(skuCode))
            .map(skuCode -> skuMap.get(skuCode).getName())
            .orElse(orderPriceInfo.getCategory().getDescriptionName())
            : Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getReason).orElse(null);
    }
}
