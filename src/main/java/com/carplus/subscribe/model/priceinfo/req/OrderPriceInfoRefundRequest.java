package com.carplus.subscribe.model.priceinfo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class OrderPriceInfoRefundRequest {

    @Schema(description = "退款清單")
    private List<OrderPriceInfoRefund> refunds;

    @Data
    public static class OrderPriceInfoRefund {
        @Schema(description = "項目編號")
        private Integer orderPriceInfoId;
        @Schema(description = "退款金額")
        private Integer refundAmount;
    }
}
