package com.carplus.subscribe.model.priceinfo.resp;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@Data
public class RelatedPriceInfo {

    @Schema(description = "費用類別")
    @Enumerated(EnumType.STRING)
    private PriceInfoDefinition.PriceInfoCategory category;

    @Schema(description = "說明")
    private String memo;

    @Schema(description = "金額")
    private Integer amount;

    @Schema(description = "費用型態")
    private int type;

    @Schema(description = "付款狀態名稱")
    private String payStatusName;

    @Schema(description = "可開始收款日期")
    private Instant receivableDate;

    public RelatedPriceInfo(OrderPriceInfo orderPriceInfo) {
        this.category = orderPriceInfo.getCategory();
        this.memo = orderPriceInfo.getCategory().getDescriptionName();
        this.amount = orderPriceInfo.getAmount();
        this.type = orderPriceInfo.getType();
        this.receivableDate = orderPriceInfo.getReceivableDate();

        if (StringUtils.isNotBlank(orderPriceInfo.getUid())) {
            Boolean isAgree = Optional.ofNullable(orderPriceInfo.getInfoDetail()).map(PriceInfoDetail::getIsAgree).orElse(null);
            if (isAgree == null) {
                this.payStatusName = PayStatus.CREDIT.getDisplayName(type);
            } else if (!isAgree) {
                this.payStatusName = PayStatus.NONE_PASS.getDisplayName(type);
            }
            if (!Objects.equals(Boolean.TRUE, isAgree)) {
                return;
            }
        }
        boolean hasPaymentId = orderPriceInfo.getPaymentId() != null;
        boolean hasRemitAccounts = orderPriceInfo.getRemitAccountIds() != null && !orderPriceInfo.getRemitAccountIds().isEmpty();
        boolean validAmounts = (getAmount() > 0 && orderPriceInfo.getReceivedAmount() > 0) || getAmount() == 0;
        if (hasPaymentId || (hasRemitAccounts && validAmounts)) {
            this.payStatusName = PayStatus.PAID.getDisplayName(type);
        } else if (orderPriceInfo.getReceivedAmount() == 0 && receivableDate.isBefore(Instant.now())) {
            this.payStatusName = PayStatus.UNPAID.getDisplayName(type);
        } else {
            this.payStatusName = PayStatus.PENDING.getDisplayName(type);
        }
    }
}
