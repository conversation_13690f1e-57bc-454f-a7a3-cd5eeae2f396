package com.carplus.subscribe.model.priceinfo.resp;

import com.carplus.subscribe.enums.PriceInfoDefinition;

public class ExtraOrderPriceModel {

    private final Integer sequence;
    private final PriceInfoDefinition.PriceInfoCategory category;
    private final Integer stdPrice;

    public ExtraOrderPriceModel(Integer sequence, PriceInfoDefinition.PriceInfoCategory category) {
        this.sequence = sequence;
        this.category = category;
        this.stdPrice = category.getStdPrice();
    }

    private String getName() {
        return category.getDescriptionName();
    }

    ;

}
