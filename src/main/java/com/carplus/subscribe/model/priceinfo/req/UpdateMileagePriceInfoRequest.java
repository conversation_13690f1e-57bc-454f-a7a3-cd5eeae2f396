package com.carplus.subscribe.model.priceinfo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateMileagePriceInfoRequest {
    /**
     * 目前里程
     */
    @NotNull(message = "目前里程不可為空")
    @Schema(description = "目前里程")
    private Integer currentMileage;
    /**
     * 里程費ID
     */
    @Schema(description = "里程費款項明細ID")
    private Integer orderPriceInfoId;
}
