package com.carplus.subscribe.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.NonNull;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

@Data
@Schema(description = "共同駕駛人")
public class Driver {

    @Schema(description = "身份證字號")
    @NotEmpty(message = "身份證字號不可為空")
    private String id;
    @Schema(description = "姓名")
    @NotEmpty(message = "姓名不可為空")
    private String name;
    @Schema(description = "手機號碼")
    private String phone;
    @Schema(description = "手機國碼")
    private String nationalCode;
    @Schema(description = "生日")
    @NotNull(message = "生日不可為空")
    private Date birthday;
    @Schema(description = "電子信箱")
    private String email;
    @Schema(description = "戶籍縣市")
    private Integer cityId;
    @Schema(description = "戶籍區域")
    private Integer areaId;
    @Schema(description = "地址")
    private String address;
    @Schema(description = "駕照管轄編號")
    private String licenseNo;
    @Schema(description = "是否外國人", example = "身份 0: 本國人(含居留), 1: 外國人", allowableValues = "0,1")
    private Integer isForeigner;
    @Schema(description = "性別")
    private String gender;
    @Schema(description = "國籍")
    private String nationalName;
    @Schema(description = "是否已徵信", allowableValues = "Y, N")
    private String verify = "N";
    @Schema(description = "是否購買安心免責", allowableValues = "Y, N")
    private String noDuty = "N";
    @Schema(description = "安心免責自動續保", allowableValues = "Y, N")
    private String autoRenew = "Y";

    public boolean isEqual(@NonNull Driver driver) {
        return Objects.equals(this.id, driver.getId())
            && Objects.equals(this.name, driver.getName())
            && Objects.equals(this.nationalCode, driver.getNationalCode())
            && Objects.equals(this.phone, driver.getPhone())
            && Objects.equals(this.email, driver.getEmail())
            && Objects.equals(this.areaId, driver.getAreaId())
            && Objects.equals(this.cityId, driver.getCityId())
            && Objects.equals(this.address, driver.getAddress())
            && Objects.equals(this.birthday, driver.getBirthday())
            && Objects.equals(this.licenseNo, driver.getLicenseNo())
            && Objects.equals(this.isForeigner, driver.getIsForeigner())
            && Objects.equals(this.gender, driver.getGender())
            && Objects.equals(this.nationalName, driver.getNationalName());
    }

    public boolean isEqualId(@NonNull String id) {
        return this.id.equals(id);
    }
}

