package com.carplus.subscribe.model.calendar;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

@NoArgsConstructor
@Data
public class CalendarDeleteRequest {
    @Schema(description = "日期時間,yyyyMMdd")
    @NotNull(message = "日期時間清單不得為空")
    @Size(min = 1, message = "日期時間清單至少需包含一個日期")
    private List<@Pattern(regexp = "\\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])", message = "日期格式無效，必須為 yyyyMMdd") String> dateList;
}
