package com.carplus.subscribe.model.calendar;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarUpdateRequest extends CalendarDeleteRequest {

    @NotNull(message = "適用站點代碼清單不得為空")
    @Schema(description = "適用站點代碼, [] 表示全部站點")
    private List<String> stationCodes;
}
