package com.carplus.subscribe.model.authority;

import com.carplus.subscribe.model.config.AdminRoleConfig;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
public class AdminUser {

    @Schema(description = "員工編號")
    private String memberId;
    @Schema(description = "公司代號")
    private String companyCode;
    @Schema(description = "人員資料")
    private List<MemberInfo> memberInfos = Lists.newArrayList();
    @Schema(description = "角色")
    private List<AdminRoleConfig> roles = Lists.newArrayList();

    @NonNull
    public String getMemberName() {
        return Optional.ofNullable(memberInfos).orElseGet(ArrayList::new)
            .stream().findFirst().map(MemberInfo::getMemberName).orElse("");
    }

    @NonNull
    public String getEmail() {
        return Optional.ofNullable(memberInfos).orElseGet(ArrayList::new)
            .stream().findFirst().map(MemberInfo::getEmail).orElse("");
    }
}
