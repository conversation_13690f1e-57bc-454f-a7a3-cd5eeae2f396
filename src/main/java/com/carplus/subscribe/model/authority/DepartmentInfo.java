package com.carplus.subscribe.model.authority;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "單位資料")
public class DepartmentInfo {

    @Schema(description = "公司代碼")
    private String companyCode;
    @Schema(description = "公司名稱")
    private String companyName;
    @Schema(description = "單位代碼")
    private String departmentCode;
    @Schema(description = "單位名稱")
    private String departmentName;
    @Schema(description = "Email")
    private String email;
    @Schema(description = "單位層級代碼")
    private LevelCode levelCode;
    @Schema(description = "單位層級名稱")
    private String levelName;
    @Schema(description = "手機號碼")
    private String mobilePhone;
    @Schema(description = "上層單位代碼")
    private String parentDepartmentCode;
    @Schema(description = "上層單位名稱")
    private String parentDepartmentName;
    @Schema(description = "排序")
    private Integer sequence;
    @Schema(description = "停/啟用 0:啟用 1:停用")
    private String stop;
}
