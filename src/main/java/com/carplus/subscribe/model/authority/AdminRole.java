package com.carplus.subscribe.model.authority;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AdminRole {

    SR000(false, "短租系統開發人員"),
    SR001(false, "短租系統管理員"),
    SR100(true, "短租門市單位"),
    SR200(false, "客服單位"),
    SR300(false, "財會單位"),
    SR400(false, "短租企劃部"),
    SR900(true, "經銷商總公司(外部)"),
    SUB001(false, "訂閱車業務部"),
    SUB002(true, "訂閱_中古門市單位"),
    SUB003(false, "訂閱_顧關單位"),
    SUB004(false, "訂閱_行銷單位"),
    SUB005(false, "訂閱_中古管理單位"),
    SUB006(false, "訂閱_企劃單位"),
    SUB100(false, "授信管理課");

    private static final Map<String, AdminRole> adminRoleMap = Arrays.stream(values()).collect(Collectors.toMap(AdminRole::name, Function.identity()));
    private boolean checkStation;
    private String roleName;

    @Nullable
    public static AdminRole of(@Nullable String roleCode) {
        return adminRoleMap.get(roleCode);
    }
}
