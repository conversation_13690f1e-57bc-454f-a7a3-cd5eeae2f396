package com.carplus.subscribe.model.authority;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 單位層級
 */
@Getter
@AllArgsConstructor
public enum LevelCode {

    L01("董事長級"),
    L011("總經理級"),
    L02("副總經理級"),
    L03("處級"),
    L04("部級"),
    L041("區課級"),
    L05("課級"),
    L06("組級"),
    L07("站級"),
    ;

    private static final Map<String, LevelCode> levelCodeMap = Arrays.stream(values()).collect(Collectors.toMap(LevelCode::name, Function.identity()));
    private String title;

    @Nullable
    public static LevelCode of(@Nullable String code) {
        return levelCodeMap.get(code);
    }
}
