package com.carplus.subscribe.model.authority;

import com.carplus.subscribe.enums.GeoDefine;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
public class AuthorityProperties {

    private Test test;
    private Department departmentCode;

    @Getter
    @Setter
    public static class Test {
        /**
         * 是否啟用測試
         */
        private boolean enabled;
        /**
         * 短租事業群(處)主管員編
         */
        private String memberIdL1;
        /**
         * 門市部(部)主管員編
         */
        private String memberIdL2;
        /**
         * 站所分區 N1 主管員編
         */
        private String memberIdN1;
        /**
         * 站所分區 N2 主管員編
         */
        private String memberIdN2;
        /**
         * 站所分區 C 主管員編
         */
        private String memberIdC;
        /**
         * 站所分區 S 主管員編
         */
        private String memberIdS;
        /**
         * 站所分區 E 主管員編
         */
        private String memberIdE;
        /**
         * 站長員編
         */
        private String memberIdMaster;
        /**
         * 授信管理課(課)
         */
        private String memberIdCdM;
        /**
         * 訂閱車業務管理課(課)
         */
        private String memberIdSCarM;
    }

    @Getter
    @Setter
    public static class Department {
        /**
         * 短租事業群(處)
         */
        private String L1;
        /**
         * 門市部(部)
         */
        private String L2;
        /**
         * 站所分區 N1
         */
        private String N1;
        /**
         * 站所分區 N2
         */
        private String N2;
        /**
         * 站所分區 C
         */
        private String C;
        /**
         * 站所分區 S
         */
        private String S;
        /**
         * 站所分區 E
         */
        private String E;
        /**
         * 授信管理課(課)
         */
        private String CDM;
        /**
         * 訂閱車業務管理課(課)
         */
        private String SCARM;
        /**
         * 訂閱車業務管理部(部)
         */
        private String SCARD;
        /**
         * 訂閱車業務推展部(課)
         */
        private String SCARM2;

        /**
         * 汽車訂閱事業群
         */
        private String SCARG;

        public String get(@NonNull GeoDefine.GeoRegion region) {
            switch (region) {
                case N1:
                    return N1;
                case N2:
                    return N2;
                case C:
                    return C;
                case S:
                    return S;
                case E:
                    return E;
                default:
                    return null;
            }
        }
    }
}
