package com.carplus.subscribe.model.authority;

import carplus.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.Nullable;

@Data
@Schema(description = "人員資料")
public class MemberInfo {

    @Schema(description = "公司代碼")
    private String companyCode;
    @Schema(description = "公司名稱")
    private String companyName;
    @Schema(description = "單位代碼")
    private String departmentCode;
    @Schema(description = "單位名稱")
    private String departmentName;
    @Schema(description = "是否為主管 0:非主管 1:主管")
    private Integer director;
    @Schema(description = "Email")
    private String email;
    @Schema(description = "是否為代理")
    private Integer isAdjunct;
    private Integer jobGrade;
    @Schema(description = "職位代碼")
    private String jobTitleCode;
    @Schema(description = "職位名稱")
    private String jobTitleName;
    @Schema(description = "單位層級代碼")
    private String levelCode;
    @Schema(description = "單位層級名稱")
    private String levelName;
    @Schema(description = "人員編號")
    private String memberId;
    @Schema(description = "人員姓名")
    private String memberName;
    @Schema(description = "手機號碼")
    private String mobilePhone;
    @Schema(description = "聯絡電話")
    private String contactNumber;
    @Schema(description = "備用電話")
    private String alternatePhoneNumber;
    @Schema(description = "停/啟用 0:啟用 1:停用")
    private Integer stop;

    @Nullable
    public String getEmail() {
        if (StringUtils.isNotBlank(email)) {
            return email;
        }

        return null;
    }

    @Nullable
    public String getPhone() {
        final String TW_CELLPHONE_PATTERN = "^09[0-9]{8}$";

        if (StringUtils.isNotBlank(mobilePhone) && mobilePhone.matches(TW_CELLPHONE_PATTERN)) {
            return mobilePhone;
        }
        if (StringUtils.isNotBlank(contactNumber) && contactNumber.matches(TW_CELLPHONE_PATTERN)) {
            return contactNumber;
        }
        if (StringUtils.isNotBlank(alternatePhoneNumber) && alternatePhoneNumber.matches(TW_CELLPHONE_PATTERN)) {
            return alternatePhoneNumber;
        }

        return null;
    }
}
