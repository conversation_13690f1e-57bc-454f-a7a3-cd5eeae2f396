package com.carplus.subscribe.model;

import com.carplus.subscribe.constant.PriceInfoConstant;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@NoArgsConstructor
public class PriceInfo implements Serializable {
    private Integer amount = 0;

    @Schema(description = "保證金資訊")
    private SecurityDepositInfo securityDepositInfo;
    @Schema(description = "基本月費")
    private int monthlyFee;
    @Schema(description = "優惠月費")
    private int discountMonthlyFee;
    @Schema(description = "基本里程費")
    private double originalMileageFee;
    @Schema(description = "折扣後里程費")
    private double mileageFee;
    @Schema(description = "總月費")
    private int totalMonthlyFee;
    @Schema(description = "是否折扣月費")
    private boolean monthlyDiscounted;
    @Schema(description = "是否啟用超激優惠")
    private Boolean levelDiscounted;
    @Schema(description = "月數")
    private int month;
    @Deprecated
    @Schema(description = "續約折扣月費")
    private int renewDiscountTotalMonthlyFee;
    @Schema(description = "里程數優惠")
    private List<MileageDiscount> mileageDiscount;
    @Schema(description = "保免責險費用")
    private int disclaimerFee;
    @Schema(description = "保溢價險費用")
    private int premiumFee;
    @Schema(description = "代步車費用")
    private int replacementCarFee = PriceInfoDefinition.PriceInfoCategory.Replacement.getStdPrice();
    @Schema(description = "全額保")
    private int allInsuranceFee;
    @Schema(description = "調度費")
    private int dispatchFee;
    @Schema(description = "車況")
    private CarDefine.CarState carState;
    @Schema(description = "里程費版本 原始0,2024/10/31上線後的1")
    private Integer version;

    public PriceInfo(SubscribeLevel adoptedSubscribeLevel, int month) {
        this.securityDepositInfo = initSecurityDepositInfo(adoptedSubscribeLevel);
        this.monthlyFee = adoptedSubscribeLevel.getMonthlyFee();
        this.discountMonthlyFee = adoptedSubscribeLevel.getDiscountMonthlyFee();
        this.originalMileageFee = adoptedSubscribeLevel.getMileageFee();
        this.mileageFee = adoptedSubscribeLevel.getMileageFee();
        this.month = month;
        this.totalMonthlyFee = this.month * getUseMonthlyFee();
        this.mileageDiscount = adoptedSubscribeLevel.getMileageDiscount();
        this.disclaimerFee = PriceInfoDefinition.PriceInfoCategory.Insurance.getStdPrice();
        this.premiumFee = PriceInfoConstant.premiumFee;
        this.allInsuranceFee = PriceInfoConstant.allInsuranceFee;
        this.dispatchFee = PriceInfoDefinition.PriceInfoCategory.Dispatch.getStdPrice();
        this.replacementCarFee = PriceInfoDefinition.PriceInfoCategory.Replacement.getStdPrice();
        this.version = 1;
    }

    private SecurityDepositInfo initSecurityDepositInfo(SubscribeLevel subscribeLevel) {
        SecurityDepositInfo info = new SecurityDepositInfo();
        info.setSecurityDeposit(subscribeLevel.getSecurityDeposit());
        info.setUnpaidSecurityDeposit(subscribeLevel.getSecurityDeposit());
        info.setRealSecurityDeposit(subscribeLevel.getSecurityDeposit());
        return info;
    }

    /**
     * 取得使用月費
     */
    public int getUseMonthlyFee() {
        return this.monthlyDiscounted ? this.discountMonthlyFee : this.monthlyFee;
    }

    public int setVersion() {
        LocalDate targetLocalDate = LocalDate.parse("20241031", DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (LocalDate.now().isBefore(targetLocalDate)) {
            version = 1;
        }
        version = 0;
        return version;
    }

    public int getVersion() {
        if (version == null) {
            version = setVersion();
        }
        return version;
    }

    public boolean isLevelDiscounted() {
        return Boolean.TRUE.equals(levelDiscounted);
    }
}
