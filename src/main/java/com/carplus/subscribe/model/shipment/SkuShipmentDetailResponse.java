package com.carplus.subscribe.model.shipment;

import com.carplus.subscribe.enums.ShipmentStatus;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
public class SkuShipmentDetailResponse {
    private String orderNo;
    private Integer orderStatus;
    private Instant orderStartDate;
    private Integer orderPriceInfoId;
    private Integer amount;
    private List<ShipmentHistoryInfo> historyList;

    @Data
    public static class ShipmentHistoryInfo {
        private Integer id;
        private ShipmentStatus status;
        private String statusName;
        private Instant updateDate;
        private String operator;
        private String operatorName;
    }
}