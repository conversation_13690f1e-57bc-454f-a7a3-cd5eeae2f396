package com.carplus.subscribe.model.shipment;

import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.db.mysql.entity.contract.SkuShipment;
import com.carplus.subscribe.enums.OrderStatus;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * SKU 出貨 CSV 匯出模型
 */
@Data
@NoArgsConstructor
public class SkuShipmentCSV {

    private static final String TIME_ZONE = "Asia/Taipei";

    /**
     * 動態生成 CSV 表頭
     */
    public static String[] HEADS = Arrays.stream(SkuShipmentCSV.class.getDeclaredFields())
        .filter(field -> field.isAnnotationPresent(Parsed.class))
        .map(field -> field.getAnnotation(Parsed.class))
        .filter(Objects::nonNull)
        .map(parsed -> parsed.field()[0])
        .toArray(String[]::new);

    @Parsed(field = "訂單編號")
    private String orderNo;

    @Parsed(field = "訂單狀態")
    private String orderStatusName;

    @Parsed(field = "起租時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date startDate;

    @Parsed(field = "費用編號")
    private Integer orderPriceInfoId;

    @Parsed(field = "費用品名")
    private String skuName;

    @Parsed(field = "費用金額")
    private Integer amount;

    @Parsed(field = "出貨編號")
    private Integer shipmentId;

    @Parsed(field = "出貨狀態")
    private String shipmentStatusName;

    @Parsed(field = "最後更新者")
    private String lastUpdater;

    @Parsed(field = "最後更新時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date lastUpdateTime;

    /**
     * 從 SkuShipment 實體建立 CSV 記錄
     */
    public SkuShipmentCSV(SkuShipment shipment, OrderPriceInfo orderPriceInfo, Orders order, String updaterName, Map<String, Sku> skuMap, Map<Integer, List<OrderPriceInfo>> refOrderPriceInfoMap) {
        this.orderNo = shipment.getOrderNo();
        this.orderStatusName = OrderStatus.of(order.getStatus()).getName();
        
        // 起租時間：優先使用實際出車時間，否則使用預期出車時間
        this.startDate = Optional.ofNullable(order.getStartDate())
            .map(Date::from)
            .orElseGet(() -> Optional.ofNullable(order.getExpectStartDate())
                .map(Date::from)
                .orElse(null));
        
        this.orderPriceInfoId = shipment.getOrderPriceInfoId();
        this.skuName = Optional.ofNullable(skuMap.get(shipment.getSkuCode()))
            .map(Sku::getName)
            .orElse(null);
        this.amount = Optional.ofNullable(orderPriceInfo)
            .map(opi -> opi.getAmount() + Optional.ofNullable(refOrderPriceInfoMap.get(shipment.getOrderPriceInfoId()))
                .map(list -> list.stream().mapToInt(OrderPriceInfo::getActualPrice).sum())
                .orElse(0))
            .orElse(0);
        
        this.shipmentId = shipment.getId();
        this.shipmentStatusName = shipment.getStatus().getDescription();
        
        this.lastUpdater = updaterName;
        this.lastUpdateTime = shipment.getUpdateDate();
    }
}
