package com.carplus.subscribe.model.shipment;

import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.ShipmentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@NoArgsConstructor
@Data
public class SkuShipmentResponse {
    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "訂單狀態")
    private Integer orderStatus;
    @Schema(description = "訂單狀態名稱")
    private String orderStatusName;
    @Schema(description = "訂單起租時間")
    private Instant orderStartDate;
    @Schema(description = "汽車用品費用清單")
    private List<SkuOrderPriceInfo> skuList;

    @Data
    public static class SkuOrderPriceInfo {
        @Schema(description = "費用編號")
        private Integer orderPriceInfoId;
        @Schema(description = "費用金額")
        private Integer amount;
        @Schema(description = "數量")
        private Integer quantity;
        @Schema(description = "付款狀態")
        private PayStatus payStatus;
        @Schema(description = "付款狀態名稱")
        private String payStatusName;
        @Schema(description = "汽車用品代碼")
        private String skuCode;
        @Schema(description = "汽車用品名稱")
        private String skuName;
        @Schema(description = "出貨資料清單")
        private List<ShipmentInfo> skuShipmentList;
    }

    @Data
    public static class ShipmentInfo {
        @Schema(description = "出貨編號")
        private Integer id;
        @Schema(description = "出貨狀態")
        private ShipmentStatus status;
        @Schema(description = "出貨狀態名稱")
        private String statusName;
        @Schema(description = "建立時間")
        private Instant createDate;
        @Schema(description = "更新時間")
        private Instant updateDate;
        @Schema(description = "最後異動人員")
        private String updater;
        @Schema(description = "最後異動人員名稱")
        private String updaterName;
    }
}