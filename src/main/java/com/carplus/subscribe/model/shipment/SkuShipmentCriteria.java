package com.carplus.subscribe.model.shipment;

import com.carplus.subscribe.enums.PayStatus;
import com.carplus.subscribe.enums.ShipmentStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "出貨查詢條件")
public class SkuShipmentCriteria {
    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "汽車用品代碼")
    private String skuCode;
    @Schema(description = "出貨狀態")
    private List<ShipmentStatus> status;
    @Schema(description = "付款狀態")
    private List<PayStatus> payStatus;
} 