package com.carplus.subscribe.model.push.req;

import com.carplus.subscribe.model.notify.Email;
import com.carplus.subscribe.model.notify.MailAttachment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.util.List;

@Data
@NoArgsConstructor
public class MailSendV3Request {

    @Schema(description = "收件信箱（`,`分隔）")
    public String receive;
    @Schema(description = "主旨")
    public String subject;
    @Schema(description = "內文")
    public String content;
    @Schema(description = "副本（`,`分隔）")
    public String cc;
    @Schema(description = "密件副本（`,`分隔）")
    public String bcc;
    @Schema(description = "附件網址")
    public String filename;
    @Schema(description = "附件檔名")
    public String displayFilename;
    @Schema(description = "會員編號")
    private int acctId;
    @Schema(description = "附件")
    private List<MailAttachment> attachments;

    public MailSendV3Request(@NonNull Email email) {
        this.acctId = email.getAcctId();

        this.receive = email.getReceive();
        this.subject = email.getSubject();
        this.content = email.getContent();
        this.cc = email.getCc();
        this.bcc = email.getBcc();
        this.filename = email.getAttachmentUrl();
        this.displayFilename = email.getDisplayFilename();
    }
}
