package com.carplus.subscribe.model.cars.resp;

import carplus.common.utils.BeanUtils;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarResponseWithChangeLogs extends CarResponse {

    @Schema(description = "異動歷程")
    private List<EntityChangeLog> changeLogs;

    public CarResponseWithChangeLogs(CarResponse carResponse, List<EntityChangeLog> changeLogs) {
        BeanUtils.copyProperties(carResponse, this);
        this.changeLogs = changeLogs;
    }
}
