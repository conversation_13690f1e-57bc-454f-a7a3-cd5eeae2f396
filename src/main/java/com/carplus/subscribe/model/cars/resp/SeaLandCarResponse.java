package com.carplus.subscribe.model.cars.resp;

import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeaLandCarResponse {

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "車輛狀態")
    private String carStatus;

    @Schema(description = "廠牌名稱")
    private String brandName;

    @Schema(description = "車型名稱")
    private String carModelName;

    @Schema(description = "基本月費")
    private Integer monthlyFee;

    @Schema(description = "里程費率")
    private Double mileageFee;

    @Schema(description = "保證金")
    private Integer securityDepositFee;

    @Schema(description = "是否啟用優惠月費")
    private Boolean isDiscount;

    public SeaLandCarResponse(CarBrandModelDTO carBrandModelDTO, SubscribeLevel level) {
        this.plateNo = carBrandModelDTO.getCar().getPlateNo();
        this.carStatus = Optional.ofNullable(CarDefine.CarStatus.of(carBrandModelDTO.getCar().getCarStatus())).map(CarDefine.CarStatus::name).orElse(null);
        this.brandName = carBrandModelDTO.getBrand().getBrandNameEn();
        this.carModelName = carBrandModelDTO.getModel().getCarModelName();
        this.monthlyFee = level.getMonthlyFee();
        this.mileageFee = level.getMileageFee();
        this.securityDepositFee = level.getSecurityDeposit();
        this.isDiscount = Optional.of(carBrandModelDTO).map(CarBrandModelDTO::getCar).map(Cars::isMonthlyDiscounted).orElse(false);
    }
}