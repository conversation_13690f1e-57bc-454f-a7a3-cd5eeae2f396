package com.carplus.subscribe.model.cars.req;

import com.carplus.subscribe.enums.CarDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarCriteria {
    @Schema(description = "所在站所")
    private List<String> stationCode;
    @Schema(description = "車輛類別")
    private CarDefine.CarType carType;
    @Schema(description = "車號")
    private List<String> plateNo;
    @Schema(description = "訂閱類別")
    private CarDefine.CarState carState;
    @Schema(description = "車輛編號")
    private String carNo;
    @Schema(description = "車輛所在區域")
    private String geoRegion;
    @Schema(description = "廠牌代號")
    private List<String> brandCode;
    @Schema(description = "車型代號")
    private List<String> carModelCode;
    @Schema(description = "方案等級")
    private List<Integer> subscribeLevel;
    @Schema(description = "上下架")
    private List<CarDefine.Launched> launched;
    @Schema(description = "是否刪除")
    private Boolean isDelete;
    @Schema(description = "狀態", hidden = true)
    private List<String> carStatuses;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "過濾虛擬車")
    private Boolean isFilterVirtualCar;
    @Schema(description = "SL是否上架")
    private Boolean isSealandLaunched;
    @Schema(description = "能源類別")
    private List<CarDefine.EnergyType> energyType;
    @Schema(description = "車籍統編")
    private List<String> vatNo;
    @Schema(description = "活動編號")
    private Integer campaignId;
}
