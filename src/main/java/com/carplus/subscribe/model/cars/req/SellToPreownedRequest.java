package com.carplus.subscribe.model.cars.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SellToPreownedRequest {

    @NotNull(message = "區域代碼不可為空")
    @NotBlank(message = "區域代碼不可為空")
    @Schema(description = "區域代碼")
    private String areaCode;

    @NotNull(message = "站點代碼不可為空")
    @NotBlank(message = "站點代碼不可為空")
    @Schema(description = "站點代碼")
    private String locationCode;
}
