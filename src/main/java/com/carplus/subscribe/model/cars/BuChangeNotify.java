package com.carplus.subscribe.model.cars;

import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.model.response.dealer.DealerSubscriptionInfoForResponse;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.carplus.subscribe.server.cars.model.CarHistorySearchRep;
import lombok.Data;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@Data
public class BuChangeNotify {
    private String plateNo;
    private String returnStationCode;
    private Instant effectiveDate; // 根據訂單狀態取預計/實際還車時間或取消時間
    private CarHistorySearchRep carHistory;

    public BuChangeNotify(OrderQueryResponse orderQueryResponse, CarHistorySearchRep carHistory) {
        this.plateNo = orderQueryResponse.getPlateNo();
        this.returnStationCode = orderQueryResponse.getReturnStation();
        this.carHistory = carHistory;

        switch (OrderStatus.of(orderQueryResponse.getStatus())) {
            case ARRIVE_NO_CLOSE:
            case CLOSE:
                this.effectiveDate = Optional.ofNullable(orderQueryResponse.getEndDate()).orElse(orderQueryResponse.getExpectEndDate());
                break;
            case CANCEL:
                this.effectiveDate = orderQueryResponse.getCancelDate();
                break;
            default:
                break;
        }
    }

    public BuChangeNotify(DealerOrderQueryResponse dealerOrderQueryResponse, CarHistorySearchRep carHistory) {
        this.plateNo = dealerOrderQueryResponse.getPlateNo();
        this.carHistory = carHistory;

        DealerSubscriptionInfoForResponse subscriptionInfo = dealerOrderQueryResponse.getSubscriptionInfo();
        this.returnStationCode = Optional.ofNullable(subscriptionInfo)
            .map(DealerSubscriptionInfoForResponse::getReturnStation)
            .orElseGet(() -> Optional.ofNullable(subscriptionInfo)
                .map(DealerSubscriptionInfoForResponse::getExpectReturnStation)
                .orElse(null));

        Instant returnDate = Optional.ofNullable(subscriptionInfo)
            .map(DealerSubscriptionInfoForResponse::getReturnDate)
            .orElseGet(() -> Optional.ofNullable(subscriptionInfo)
                .map(DealerSubscriptionInfoForResponse::getExpectReturnDate)
                .orElse(null));

        switch (Objects.requireNonNull(ContractStatus.codeOfValue(dealerOrderQueryResponse.getOrderStatus()))) {
            case COMPLETE:
                this.effectiveDate = returnDate;
                break;
            case CANCEL:
                this.effectiveDate = dealerOrderQueryResponse.getCancelDate();
                break;
            default:
                break;
        }
    }
}
