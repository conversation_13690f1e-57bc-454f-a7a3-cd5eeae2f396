package com.carplus.subscribe.model.cars.resp;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.*;
import com.carplus.subscribe.enums.GeoDefine;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "車輛資訊")
public class CarCommonResponse extends CarResponse {

    @Schema(description = "所在站所區域")
    private String locationGeoRegion;

    @JsonProperty("isInWishlist")
    @Schema(description = "是否在收藏清單")
    private boolean isInWishlist;

    public CarCommonResponse(Cars cars, CarModel carModel, List<CarModelImage> images, CarBrand carBrand, CarRegistration carRegistration) {
        super(cars, carModel, images, carBrand, carRegistration);
    }

    public Integer getUseMonthlyFee() {
        return getUseMonthlyFee(getLevel());
    }

    @JsonIgnore
    public Integer getMonthlyFee() {
        return getLevel().getMonthlyFee();
    }

    @JsonIgnore
    public Integer getDiscountMonthlyFee() {
        return getLevel().getDiscountMonthlyFee();
    }

    @Schema(description = "訂閱區域編號")
    public String getSubGeoRegion() {
        return StringUtils.isNotBlank(locationGeoRegion) ? GeoDefine.GeoRegion.enumToCode(GeoDefine.GeoRegion.valueOf(locationGeoRegion)) : null;
    }

    @Schema(description = "訂閱區域名稱")
    public String getSubGeoRegionName() {
        return StringUtils.isNotBlank(locationGeoRegion) ? GeoDefine.GeoRegion.enumToName(GeoDefine.GeoRegion.valueOf(locationGeoRegion)) : null;
    }
}
