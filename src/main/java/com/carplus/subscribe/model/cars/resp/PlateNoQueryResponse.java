package com.carplus.subscribe.model.cars.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class PlateNoQueryResponse {

    @Schema(description = "車牌號碼")
    private String plateNo;
    @Schema(description = "狀態")
    private String status;
    @Schema(description = "公里數")
    private Integer km;
    @Schema(description = "車輛區域代碼")
    private String areaId;
    @Schema(description = "車輛區域名稱")
    private String areaName;
    @Schema(description = "車輛停放點代碼")
    private String locationId;
    @Schema(description = "車輛停放點名稱")
    private String locationName;
}
