package com.carplus.subscribe.model.cars.resp;

import com.carplus.subscribe.db.mysql.entity.cars.*;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.model.cars.ProcessingOrder;
import com.carplus.subscribe.model.subscribelevel.SubscribeLevelResponse;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.UrlUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "車輛資訊")
public class CarResponse implements CarPropertyProvider, Orderable {
    @Schema(description = "車牌號碼")
    private String plateNo;
    @Schema(description = "車輛狀態")
    private String carStatus;
    @Schema(description = "實際排氣量")
    private BigDecimal displacement;
    @Schema(description = "車型")
    private CarDefine.CarType carType;
    @Schema(description = "油料類型")
    private CarDefine.FuelType fuelType;
    @Schema(description = "排檔類型")
    private CarDefine.GearType gearType;
    @Schema(description = "統編")
    private String taxId;
    @Schema(description = "出廠年份")
    private String mfgYear;
    @Schema(description = "出廠月份")
    private String mfgMonth;
    @Schema(description = "座位數")
    private int seat;
    @Schema(description = "所屬站所")
    private String regisStationCode;
    @Schema(description = "當前所在站所")
    private String locationStationCode;
    @Schema(description = "當前里程數")
    private Integer currentMileage;
    @Schema(description = "車況")
    private CarDefine.CarState carState;
    @Schema(description = "車輛狀態")
    private CarDefine.SaleStatus saleStatus;
    @Schema(description = "上下架狀態")
    private CarDefine.Launched launched;
    @Schema(description = "配件")
    private List<Integer> equipIds;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "etag型式")
    private ETagModelEnum etagModel;
    @Schema(description = "etag序號 (etag 照片 url)")
    private String etagNo;
    @Schema(description = "顏色說明")
    private String colorDesc;
    @Schema(description = "中文說明")
    private String cnDesc;
    @Schema(description = "最後修改時間")
    private Long updateDate;
    @Schema(description = "車輛編號/商品編號")
    private String carNo;
    @Schema(description = "車型資訊")
    private CarModel carModel;
    @Schema(description = "廠牌資訊")
    private CarBrand carBrand;
    @Schema(description = "車圖資訊")
    private List<CarModelImage> images;
    @Schema(description = "車籍公司所屬")
    private CarRegistration carRegistration;
    @Schema
    private List<ProcessingOrder> processOrders;
    @Schema(description = "方案")
    private SubscribeLevelResponse level;
    @Schema(description = "超激優惠方案")
    private SubscribeLevelResponse discountLevel;
    @Schema(description = "撥車申請單單號")
    private Integer buChangeMasterId;
    @Schema(description = "是否在SL上架")
    private Boolean isSealandLaunched;
    @Schema(description = "牌價")
    private Integer stdPrice;
    @JsonIgnore
    @Schema(description = "crs編號")
    private Integer crsNo;
    @Schema(description = "能源類別")
    private CarDefine.EnergyType energyType;

    @Schema(description = "是否專案車")
    private boolean isProjectCar;

    @Schema(description = "調度車態名稱")
    private String statusName;
    @Schema(description = "用途分配名稱")
    private String purposeCodeName;
    @Schema(description = "車籍統編")
    private String vatNo;
    @Schema(description = "準備工作天數")
    private Integer prepWorkdays;

    @JsonIgnore
    @Schema(description = "是否已刪除")
    private Boolean isDeleted;

    public CarResponse(Cars cars, CarModel carModel, List<CarModelImage> images, CarBrand carBrand, CarRegistration carRegistration) {
        if (cars != null) {
            this.plateNo = cars.getPlateNo();
            this.displacement = cars.getDisplacement();
            this.carType = cars.getCarType();
            this.fuelType = cars.getFuelType();
            this.gearType = cars.getGearType();
            this.taxId = cars.getTaxId();
            this.mfgYear = cars.getMfgYear();
            this.mfgMonth = cars.getMfgMonth();
            this.regisStationCode = cars.getRegisStationCode();
            this.locationStationCode = cars.getLocationStationCode();
            this.currentMileage = cars.getCurrentMileage();
            this.carState = cars.getCarState();
            this.launched = cars.getLaunched();
            this.carStatus = cars.getCarStatus();
            this.equipIds = cars.getEquipIds();
            this.tagIds = cars.getTagIds();
            this.etagModel = cars.getEtagModel();
            this.etagNo = UrlUtils.normalizeGcsUrl(cars.getEtagNo());
            this.colorDesc = cars.getColorDesc();
            this.cnDesc = cars.getCnDesc();
            this.carNo = cars.getCarNo();
            this.seat = cars.getSeat();
            this.buChangeMasterId = cars.getBuChangeMasterId();
            this.isSealandLaunched = cars.isSealandLaunched();
            this.stdPrice = cars.getStdPrice();
            this.crsNo = cars.getCrsCarNo();
            this.energyType = cars.getEnergyType();
            this.isProjectCar = cars.isProjectCar();
            this.vatNo = cars.getVatNo();
            this.isDeleted = cars.getIsDeleted();
            this.prepWorkdays = cars.getPrepWorkdays();
        }
        this.carModel = carModel;
        this.images = images;
        this.carBrand = carBrand;
        this.carRegistration = carRegistration;
    }

    @Schema(description = "訂閱車方案")
    public Integer getSubscribeLevel() {
        return Optional.ofNullable(level).map(SubscribeLevelResponse::getLevel).orElse(null);
    }

    @Schema(description = "訂閱車方案名稱")
    public String getSubscribeLevelName() {
        return Optional.ofNullable(level).map(SubscribeLevelResponse::getName).orElse(null);
    }

    @Schema(description = "是否格上車")
    private Boolean isCarPlusCar() {
        return CarsUtil.isCarPlusCar(this.vatNo);
    }
}
