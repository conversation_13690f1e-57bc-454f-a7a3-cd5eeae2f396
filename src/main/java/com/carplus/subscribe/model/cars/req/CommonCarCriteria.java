package com.carplus.subscribe.model.cars.req;

import com.carplus.subscribe.enums.SubscribeCarDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommonCarCriteria extends CarCriteria {

    @Schema(description = "排序")
    private SubscribeCarDefine.OrderBy orderBy = SubscribeCarDefine.OrderBy.USE_MONTH_FEE;
    @Schema(description = "正序/反序")
    private SubscribeCarDefine.Sort sort = SubscribeCarDefine.Sort.ASC;
    @Schema(description = "月費起始")
    private Integer monthFeeStart;
    @Schema(description = "月費結束")
    private Integer monthFeeEnd;
    @Min(value = 1900, message = "出廠年份起必須大於或等於 1900")
    @Max(value = 2100, message = "出廠年份起不能超過 2100")
    @Schema(description = "出廠年份起，範圍應為 1900 至當前年份")
    private Integer mfgYearFrom;
    @Min(value = 1900, message = "出廠年份迄必須大於或等於 1900")
    @Max(value = 2100, message = "出廠年份迄不能超過 2100")
    @Schema(description = "出廠年份迄，範圍應為 1900 至當前年份，且不得小於出廠年份起")
    private Integer mfgYearTo;
    @NotNull(message = "排序不可為空")
    @Min(value = 0, message = "略過筆數不可小於 0")
    @Schema(description = "略過筆數")
    private Integer skip;
    @NotNull(message = "呈現筆數不可為空")
    @Min(value = 1, message = "呈現筆數不可小於 1")
    @Schema(description = "呈現筆數")
    private Integer limit;
}
