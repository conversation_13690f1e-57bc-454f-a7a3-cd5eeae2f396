package com.carplus.subscribe.model.cars;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessingOrder {
    @Schema(description = "訂單號碼")
    private String orderNo;
    @Schema(description = "出車日期")
    private Instant departDate;
    @Schema(description = "還車日期")
    private Instant returnDate;
}
