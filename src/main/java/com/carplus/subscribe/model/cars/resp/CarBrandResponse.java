package com.carplus.subscribe.model.cars.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "車廠牌")
public class CarBrandResponse {

    @Schema(description = "廠牌代碼")
    private String brandCode;
    @Schema(description = "廠牌名稱")
    private String brandName;
    @Schema(description = "廠牌英文名稱")
    private String brandNameEn;
    @Schema(description = "車廠牌是否呈現官網介紹")
    @JsonProperty("isAppearOnOfficial")
    private boolean isAppearOnOfficial;
    @Schema(description = "是否顯示品牌專頁")
    @JsonProperty("isShowPage")
    private boolean isShowPage;
    @Schema(description = "LOGO圖卡")
    private String logoImgUrl;
    @Schema(description = "車輛圖卡")
    private String carImgUrl;
    @Schema(description = "排序號碼")
    private Integer seqNo;
}
