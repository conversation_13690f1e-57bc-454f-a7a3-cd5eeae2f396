package com.carplus.subscribe.model.cars.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CarChangeLimitRequest {
    @Schema(description = "Crs車輛編號")
    @NotNull(message = "crs車輛編號不可為空")
    private Integer crsCarNo;
    @Schema(description = "異動類別")
    @NotNull(message = "異動類別不可為空")
    private String changeType;
    @Schema(description = "異動備註")
    @NotNull(message = "異動備註不可為空")
    private String changeMemo;
    @Schema(description = "是否管制")
    private boolean launched;
}
