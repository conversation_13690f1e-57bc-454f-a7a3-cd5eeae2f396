package com.carplus.subscribe.model.cars.req;

import com.carplus.subscribe.enums.BuChangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
public class AutoChangeBuRequest {
    @Schema(description = "訂單號碼")
    private String orderNo;
    @Schema(description = "車牌號碼")
    private String plateNo;
    @Schema(description = "預期領牌照日")
    private Instant licenseExpDate;
    @Schema(description = "撥車,CHANGE[撥車申請],BATCH_CHANGE[營業用撥車],AUTO_BATCH_CHANGE[自動營業用撥車]")
    private BuChangeEnum.ChangeTypeOfAssign changeType;
    @Schema(description = "附件路徑")
    private List<String> attachmentId;

}
