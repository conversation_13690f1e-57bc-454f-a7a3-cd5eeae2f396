package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@Schema(description = "承租人資訊")
public class ContractCustomerInfo {

    @Schema(description = "承租人簡名")
    private String shortName;
    @Schema(description = "承租人全名")
    private String fullName;
    @Schema(description = "資本額")
    private Integer coamt;
    @Schema(description = "公司登記日")
    private String redt;
    @Schema(description = "負責人")
    private String boss;
    @Schema(description = "負責人生日")
    private String brdt;
    @Schema(description = "郵遞區號")
    private String live1;
    @Schema(description = "營業地址")
    private String live;
    @Schema(description = "聯絡地址郵遞區號")
    private String comm1;
    @Schema(description = "聯絡地址")
    private String comm;
    @Schema(description = "電話一")
    private String tel1;
    @Schema(description = "電話二")
    private String tel2;
    @Schema(description = "傳真電話")
    private String fax;
    @Schema(description = "E-mail")
    private String email;
    @Schema(description = "E-mail2")
    private String email2;
    @Schema(description = "承辦會計")
    private String acman;
    @Schema(description = "承租人姓名")
    private String link;
    @Schema(description = "營業項目")
    private String item;
    @Schema(description = "年營業額")
    private Integer amty;
    @Schema(description = "承租人行業別")
    private String work;
    @Schema(description = "往來銀行")
    private String bank;
    @Schema(description = "帳號")
    private String acct;
    @Schema(description = "建檔日期")
    private String crdt;
    @Schema(description = "異動日期")
    private String eddt;
    @Schema(description = "客戶到租處理注意事項")
    private String memo;
    @Schema(description = "原開拓業代")
    private String oempID;
    @Schema(description = "原開拓單位")
    private String ounitID;
    @Schema(description = "介紹保有")
    private Integer isIntroduce;
    @Schema(description = "通路商業代")
    private Integer channelAgentAuto;
    @Schema(description = "客戶來源")
    private Integer custCome;
    @Schema(description = "申購單號")
    private Long ordersAuto;
    @Schema(description = "實收資本額")
    private Long realCapitalization;
    @Schema(description = "第一次起租日")
    private String firstTradeDate;

}