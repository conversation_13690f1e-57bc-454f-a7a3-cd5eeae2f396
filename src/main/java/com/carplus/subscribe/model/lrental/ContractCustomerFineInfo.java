package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class ContractCustomerFineInfo {

    @Schema(description = "罰單地址郵遞區號")
    private String padd1;
    @Schema(description = "罰單地址")
    private String padd;
    @Schema(description = "罰單註記")
    private String deal;
    @Schema(description = "罰單收件抬頭")
    private String polit;
    @Schema(description = "罰單連絡電話")
    private String polTel;
    @Schema(description = "罰單行動電話")
    private String polMobile;
    @Schema(description = "罰單email")
    private String polEmail;
    @Schema(description = "罰單傳真")
    private String polFax;

}