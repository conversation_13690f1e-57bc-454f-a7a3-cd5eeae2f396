package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class ContractCustomerInvoiceInfo {

    @Schema(description = "發票郵遞區號")
    private String invPost;
    @Schema(description = "發票地址")
    private String invAdds;
    @Schema(description = "發票收件人")
    private String invName;
    @Schema(description = "發票收件抬頭")
    private String invTit;
    @Schema(description = "發票連絡電話")
    private String invTel;
    @Schema(description = "發票行動電話")
    private String invMobile;
    @Schema(description = "發票email")
    private String invEmail;
    @Schema(description = "發票傳真")
    private String invFax;
    @Schema(description = "da_invout")
    private String invOut;

}
