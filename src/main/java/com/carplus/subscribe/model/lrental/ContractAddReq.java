package com.carplus.subscribe.model.lrental;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.enums.ContractEnum;
import com.carplus.subscribe.model.crs.PurchaseProjectCarSearchResponse;
import com.carplus.subscribe.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.time.Instant;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractAddReq {
    /**
     * 契約類型
     */
    @Schema(description = "契約類型", required = true)
    ContractEnum.ContractType contractType;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼", required = true)
    String plateNo;

    /**
     * 契約起始日 yyyMMdd
     */
    @Schema(description = "契約起始日 yyyMMdd", example = "1110101", required = true)
    String contractStartDate;

    /**
     * 契約結束日 yyyMMdd
     */
    @Schema(description = "契約結束日 yyyMMdd", example = "1120101")
    String contractEndDate;

    /**
     * 契約建立者
     */
    //for da21log.da_userid、da21log.da_userid、da41log.da_userid
    @Schema(description = "契約建立者", required = true)
    String userId;

    /**
     * 舊契約編號-for 舊約解約
     */
    @Schema(description = "舊契約編號-for 舊約解約")
    String oldContractNo;

    /**
     * 契約異動註記
     */
    @Schema(description = "契約異動註記")
    String mem2;
    /**
     * 成本歸屬單位代碼
     * for 短租、共享，以單位代碼(各BU自定義)取得承租人統一編號
     * for 專車，以業務別取得承租人統一編號 crs serviceCode: assign_location_special
     */
    @Schema(description = "成本歸屬單位代碼, for 短租、共享、專車，取得承租人統一編號")
    String departmentCode;
    /**
     * 是否使用標準殘值
     */
    @Schema(description = "是否使用標準殘值")
    @Builder.Default
    Boolean isUseCarAgeResidualValue = true;
    /**
     * 庫位代碼
     * for 撥車，收車庫位
     */
    @Schema(description = "庫位代碼, for 撥車，收車庫位")
    private Integer buID;
    /**
     * 殘值
     */
    @Schema(description = "殘值")
    private Integer residualValue;

    /**
     * 訂單編號
     */
    @Schema(description = "訂單編號")
    private String orderNo;


    /**
     * 承租人統編
     */
    private String rentalTaxID;

    /**
     * 契約期數(月)
     */
    private Integer contractPeriod;

    /**
     * 月租金(含稅)
     */
    private Integer monthlyVatRent;

    /**
     * 月租金(未稅)
     */
    private Integer monthlyRent;

    /**
     * 保證金
     */
    private Integer promiseAmount;

    /**
     * 里程費率
     */
    private Double mileageRate;

    /**
     * 總保修金額
     * P.S.里程數*3000
     */
    private Integer fixAmount;

    /**
     * 代步車方式
     */
    private String replaceCode;

    /**
     * 車輛來源
     */
    private String carSource;

    /**
     * 契約備註
     */
    private String contractMemo;

    public void validateProjectCar(PurchaseProjectCarSearchResponse purchaseProjectCarSearchResponse, Instant endDate) {
        if (purchaseProjectCarSearchResponse != null && Objects.equals(purchaseProjectCarSearchResponse.getIsProjectCar(), Boolean.TRUE)) {

            purchaseProjectCarSearchResponse.validate(endDate);
            try {
                this.setContractEndDate(DateUtil.transferADDateToMinguoDate(DateUtil.stringToDate(purchaseProjectCarSearchResponse.getContractEndDate(), "yyyy/MM/dd", DateUtils.ZONE_TPE).toInstant()));

                this.setIsUseCarAgeResidualValue(false);
                this.setResidualValue(purchaseProjectCarSearchResponse.getResidualValue());
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
