package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CarplusCompanyResponse {
    @Schema(description = "公司別")
    private String comp;

    @Schema(description = "會計公司別")
    private String acct;

    @Schema(description = "財務系統公司別")
    private String accountId;

    @Schema(description = "公司名稱簡寫")
    private String compShortName;

    @Schema(description = "公司名稱全名")
    private String compFullName;

    @Schema(description = "統一編號")
    private String rent;

    @Schema(description = "稅籍編號")
    private String taxno;

    @Schema(description = "公司地址")
    private String coadds;

    @Schema(description = "備註")
    private String memo;

    @Schema(description = "是否為領牌公司:1.是")
    private String licenseCompany;
}

