package com.carplus.subscribe.model.lrental;

import com.carplus.subscribe.server.cars.model.CarPropertyResp;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class UpdateCarProperty {
    private Integer propertyStatus;
    private Integer purposeCode;
    private Integer pyAuto;
    private String restrictMemo;

    public UpdateCarProperty(CarPropertyResp carPropertyResp) {
        this.propertyStatus = carPropertyResp.getStatus();
        this.purposeCode = carPropertyResp.getPurposeCode();
        this.pyAuto = carPropertyResp.getPyAuto();
    }
}
