package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CheckCarInfo {

    @Schema(description = "驗車收件抬頭")
    private String chkCarTit;
    @Schema(description = "驗車收件人")
    private String chkCarName;
    @Schema(description = "驗車郵遞區號")
    private String chkCarPost;
    @Schema(description = "驗車地址")
    private String chkCarAdds;
    @Schema(description = "驗車連絡電話")
    private String chkCarTEL;
    @Schema(description = "驗車行動電話")
    private String chkCarMobile;
    @Schema(description = "驗車email")
    private String chkCarEmail;
    @Schema(description = "驗車傳真")
    private String chkCarFax;
    @Schema(description = "eTag手機號碼")
    private String polETagMobile;

}
