package com.carplus.subscribe.model.lrental;

import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.config.SubscribeLRentalConfig;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.dealer.DealerCustomerInfoForCreate;
import com.carplus.subscribe.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Schema(description = "保有客戶新增請求")
@Data
@FieldNameConstants
@Slf4j
public class ContractCustomerCreateRequest {

    @Schema(description = "承租人代碼", required = true)
    private String rent;
    @Schema(description = "客戶資訊", required = true)
    private ContractCustomerInfo customerInfo;
    @Schema(description = "罰單資訊")
    private ContractCustomerFineInfo customerFineInfo;
    @Schema(description = "發票資訊")
    private ContractCustomerInvoiceInfo customerInvoiceInfo;
    @Schema(description = "驗車資訊")
    private CheckCarInfo checkCarInfo;


    public ContractCustomerCreateRequest(Orders order, AuthUser authUser, List<City> cities, SubscribeLRentalConfig config) {
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
            this.setRent(authUser.getLoginId());
            AtomicReference<Area> area = new AtomicReference<>(new Area());
            ContractCustomerInfo customerInfo = new ContractCustomerInfo();
            customerInfo.setShortName(getFirstWord(authUser.getAcctName()));
            customerInfo.setFullName(authUser.getAcctName());

            StringBuilder address = new StringBuilder();
            if (authUser.getHhrCityId() != null && authUser.getHhrAreaId() != null) {
                City city = cities.stream().filter(c -> c.getCityId() == authUser.getHhrCityId()).findAny().orElse(null);
                if (city != null) {
                    if (city.getArea() != null) {
                        Optional.ofNullable(city.getArea()).orElse(Collections.emptyList()).stream()
                            .filter(a -> a.getAreaId() == authUser.getHhrAreaId())
                            .findAny().ifPresent(a -> {
                                area.set(a);
                                address.append(city.getCityName()).append(a.getAreaName()).append(authUser.getHhrAddr());
                            });
                    }
                }
            }

            customerInfo.setBoss(getFirstWord(authUser.getAcctName()));
            customerInfo.setBrdt(DateUtil.transferADDateToMinguoDate(sf.parse(authUser.getBirthday()).toInstant()));
            customerInfo.setLive1(area.get().getAreaCode());
            customerInfo.setLive(address.toString());
            customerInfo.setComm1(area.get().getAreaCode());
            customerInfo.setComm(address.toString());
            customerInfo.setTel1(authUser.getMainCell());
            customerInfo.setTel2(authUser.getSubCell());
            customerInfo.setEmail(authUser.getEmail());
            customerInfo.setMemo("");
            customerInfo.setOempID(Optional.ofNullable(config).map(SubscribeLRentalConfig::getOempID).orElse("K2085"));
            customerInfo.setOunitID(Optional.ofNullable(config).map(SubscribeLRentalConfig::getOunitID).orElse("2399"));
            customerInfo.setIsIntroduce(0);
            customerInfo.setChannelAgentAuto(0);
            customerInfo.setCustCome(20);
            customerInfo.setOrdersAuto(0L);
            customerInfo.setRealCapitalization(0L);
            customerInfo.setFax(authUser.getMainCell());
            this.customerInfo = customerInfo;

            ContractCustomerFineInfo fineInfo = new ContractCustomerFineInfo();
            fineInfo.setPadd1(area.get().getAreaCode());
            fineInfo.setPadd(address.toString());
            fineInfo.setPolit(getFirstWord(authUser.getAcctName()));
            fineInfo.setPolMobile(authUser.getMainCell());
            fineInfo.setPolTel(StringUtils.isNotBlank(authUser.getMainTel()) ? authUser.getMainTel() : authUser.getMainCell());
            fineInfo.setPolEmail(authUser.getEmail());
            fineInfo.setPolFax(authUser.getMainCell());
            fineInfo.setDeal("");
            this.customerFineInfo = fineInfo;

            ContractCustomerInvoiceInfo invoiceInfo = new ContractCustomerInvoiceInfo();
            invoiceInfo.setInvPost(area.get().getAreaCode());
            invoiceInfo.setInvAdds(address.toString());
            invoiceInfo.setInvName(getFirstWord(authUser.getAcctName()));
            invoiceInfo.setInvTit(getFirstWord(authUser.getAcctName()));
            invoiceInfo.setInvTel(StringUtils.isNotBlank(authUser.getMainTel()) ? authUser.getMainTel() : authUser.getMainCell());
            invoiceInfo.setInvMobile(authUser.getMainCell());
            invoiceInfo.setInvEmail(authUser.getEmail());
            invoiceInfo.setInvFax(authUser.getMainCell());
            invoiceInfo.setInvOut("");
            this.customerInvoiceInfo = invoiceInfo;

            this.checkCarInfo = new CheckCarInfo();
            checkCarInfo.setChkCarTit(getFirstWord(authUser.getAcctName()));
            checkCarInfo.setChkCarName(getFirstWord(authUser.getAcctName()));
            checkCarInfo.setChkCarPost(area.get().getAreaCode());
            checkCarInfo.setChkCarAdds(address.toString());
            checkCarInfo.setChkCarMobile(authUser.getMainCell());
            checkCarInfo.setChkCarEmail(authUser.getEmail());
            checkCarInfo.setChkCarTEL(StringUtils.isNotBlank(authUser.getMainTel()) ? authUser.getMainTel() : authUser.getMainCell());
            checkCarInfo.setChkCarFax(authUser.getMainCell());
            checkCarInfo.setPolETagMobile("");


        } catch (Exception e) {
            throw new BadRequestException(String.format("建立長租客戶資料失敗,orderNo:%s", order.getOrderNo()));
        }
    }

    private String getFirstWord(String str) {
        int spaceIndex = str.indexOf(' ');
        return spaceIndex == -1 ? str : str.substring(0, spaceIndex);
    }


    public ContractCustomerCreateRequest(String orderNo, List<City> cities, DealerCustomerInfoForCreate customerInfo) {
        try {
            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
            this.setRent(customerInfo.getIdNo());
            AtomicReference<Area> area = new AtomicReference<>(new Area());
            ContractCustomerInfo contractCustomerInfo = new ContractCustomerInfo();
            contractCustomerInfo.setShortName(getFirstWord(customerInfo.getUserName()));
            contractCustomerInfo.setFullName(customerInfo.getUserName());

            StringBuilder address = new StringBuilder();
            if (customerInfo.getCity() != null && customerInfo.getArea() != null) {
                City city = cities.stream().filter(c -> c.getCityId() == customerInfo.getCity()).findAny().orElse(null);
                if (city != null) {
                    if (city.getArea() != null) {
                        Optional.of(city.getArea()).orElse(Collections.emptyList()).stream()
                            .filter(a -> a.getAreaId() == customerInfo.getArea())
                            .findAny().ifPresent(a -> {
                                area.set(a);
                                address.append(city.getCityName()).append(a.getAreaName()).append(customerInfo.getAddress());
                            });
                    }
                }
            }

            contractCustomerInfo.setBoss(getFirstWord(customerInfo.getUserName()));
            contractCustomerInfo.setBrdt(DateUtil.transferADDateToMinguoDate(sf.parse(customerInfo.getBirthDay()).toInstant()));
            contractCustomerInfo.setLive1(area.get().getAreaCode());
            contractCustomerInfo.setLive(address.toString());
            contractCustomerInfo.setComm1(area.get().getAreaCode());
            contractCustomerInfo.setComm(address.toString());
            contractCustomerInfo.setTel1(customerInfo.getMainCell());
            contractCustomerInfo.setTel2(customerInfo.getMainCell());
            contractCustomerInfo.setEmail(customerInfo.getEmail());
            contractCustomerInfo.setMemo("");
            contractCustomerInfo.setOempID("K1701");
            contractCustomerInfo.setOunitID("2330");
            contractCustomerInfo.setIsIntroduce(0);
            contractCustomerInfo.setChannelAgentAuto(0);
            contractCustomerInfo.setCustCome(20);
            contractCustomerInfo.setOrdersAuto(0L);
            contractCustomerInfo.setRealCapitalization(0L);
            contractCustomerInfo.setFax(customerInfo.getMainCell());
            this.customerInfo = contractCustomerInfo;

            ContractCustomerFineInfo fineInfo = new ContractCustomerFineInfo();
            fineInfo.setPadd1(area.get().getAreaCode());
            fineInfo.setPadd(address.toString());
            fineInfo.setPolit(getFirstWord(customerInfo.getUserName()));
            fineInfo.setPolMobile(customerInfo.getMainCell());
            fineInfo.setPolTel(customerInfo.getMainCell());
            fineInfo.setPolEmail(customerInfo.getEmail());
            fineInfo.setPolFax(customerInfo.getMainCell());
            fineInfo.setDeal("");
            this.customerFineInfo = fineInfo;

            ContractCustomerInvoiceInfo invoiceInfo = new ContractCustomerInvoiceInfo();
            invoiceInfo.setInvPost(area.get().getAreaCode());
            invoiceInfo.setInvAdds(address.toString());
            invoiceInfo.setInvName(getFirstWord(customerInfo.getUserName()));
            invoiceInfo.setInvTit(getFirstWord(customerInfo.getUserName()));
            invoiceInfo.setInvTel(customerInfo.getMainCell());
            invoiceInfo.setInvMobile(customerInfo.getMainCell());
            invoiceInfo.setInvEmail(customerInfo.getEmail());
            invoiceInfo.setInvFax(customerInfo.getMainCell());
            invoiceInfo.setInvOut("");
            this.customerInvoiceInfo = invoiceInfo;

            this.checkCarInfo = new CheckCarInfo();
            checkCarInfo.setChkCarTit(getFirstWord(customerInfo.getUserName()));
            checkCarInfo.setChkCarName(getFirstWord(customerInfo.getUserName()));
            checkCarInfo.setChkCarPost(area.get().getAreaCode());
            checkCarInfo.setChkCarAdds(address.toString());
            checkCarInfo.setChkCarMobile(customerInfo.getMainCell());
            checkCarInfo.setChkCarEmail(customerInfo.getEmail());
            checkCarInfo.setChkCarTEL(customerInfo.getMainCell());
            checkCarInfo.setChkCarFax(customerInfo.getMainCell());
            checkCarInfo.setPolETagMobile("");


        } catch (Exception e) {
            throw new BadRequestException(String.format("建立長租客戶資料失敗,orderNo:%s", orderNo));
        }
    }
}
