package com.carplus.subscribe.model.lrental;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "契約資訊查詢結果")
public class ContractSearchRep {
    @Schema(description = "契約編號")
    private String dano;

    @Schema(description = "訂單編號")
    private String daodno;

    @Schema(description = "公司別")
    private String dacomp;

    @Schema(description = "承租人")
    private String darent;

    @Schema(description = "起租日")
    private String dadt1;

    @Schema(description = "迄租日")
    private String dadt2;

    @Schema(description = "月租數")
    private Integer damm;

    @Schema(description = "繳款起日")
    private String daduedt;

    @Schema(description = "輸入日")
    private String dachdt;

    @Schema(description = "檢交日")
    private String datodt;

    @Schema(description = "台數")
    private BigDecimal daqty;

    @Schema(description = "單台月租金")
    private BigDecimal daamtm;

    @Schema(description = "月租金總額")
    private BigDecimal daamtt;

    @Schema(description = "營業稅")
    private BigDecimal datax;

    @Schema(description = "天期付款")
    private Integer dapayday;

    @Schema(description = "一次付款月")
    private Integer dapaymm;

    @Schema(description = "殘值")
    private BigDecimal daover;

    @Schema(description = "繳保證金碼")
    private String dainsur1;

    @Schema(description = "繳保證金額")
    private BigDecimal dainsur;

    @Schema(description = "廠牌代碼")
    private String daspec;

    @Schema(description = "契約型式 idcode.cbokind=契約型式New")
    private String datwo;

    @Schema(description = "租賃方式 1.租購  2.純租  3.融資  4.其他")
    private String datrid;

    @Schema(description = "收款方式 1.期票  2.匯款  3.收款")
    private String dapayid;

    @Schema(description = "保證人A(字)")
    private String dashor1;

    @Schema(description = "保證人A(名)")
    private String dashor1na;

    @Schema(description = "保證人B(字)")
    private String dashor2;

    @Schema(description = "保證人B(名)")
    private String dashor2na;

    @Schema(description = "業務員編號")
    private String daid;

    @Schema(description = "租賃業務名")
    private String dasalena;

    @Schema(description = "原業務姓名")
    private String dasalena2;

    @Schema(description = "銷售公司")
    private String dasaco;

    @Schema(description = "銷售單位")
    private String daunit;

    @Schema(description = "單位業務員姓名")
    private String dauname;

    @Schema(description = "超收公里元 原TEL")
    private String daunitte;

    @Schema(description = "輪胎更換條數")
    private Integer dacercnt;

    @Schema(description = "起租里程")
    private BigDecimal dakmstar;

    @Schema(description = "保養類別 1.一般  2.自修")
    private String damatf;

    @Schema(description = "保用總公里數 【1】表合約不限里程")
    private BigDecimal damatn;

    @Schema(description = "每台總保修金額")
    private BigDecimal damatnt;

    @Schema(description = "異動日期")
    private String daeddt;

    @Schema(description = "契約狀態 IdCode.cbokind=契約狀態")
    private String dastat;

    @Schema(description = "契約狀態名稱")
    private String dastatName;

    @Schema(description = "特別條款")
    private String damemo;

    @Schema(description = "代步車 0.無 1.出險 2.失竊 3.接送 4.洪水 5.維修")
    private String dachang;

    @Schema(description = "保險代碼 1.自保  2.保險  3.客自保")
    private String daprotf;

    @Schema(description = "燃牌稅自理 (N/Y)")
    private String dataxf;

    @Schema(description = "期票註記")
    private String damem2;

    @Schema(description = "罰單註記")
    private String daposn;

    @Schema(description = "租賃性質 1.營業性  2.資本性  3.附條件")
    private String daRentType;

    @Schema(description = "前約契編")
    private String danoPre;

    @Schema(description = "前約來源別")
    private String daFlagPre;

    @Schema(description = "租金分段否 0否, 1是")
    private Integer daCutNo;

    @Schema(description = "合約編號")
    private String daCntNo;

    @Schema(description = "櫃號")
    private String daCabNo;

    @Schema(description = "客戶來源 CarItem.Type=101")
    private Integer custcome;

    @Schema(description = "車輛來源 CarItem.Type=106")
    private Integer carSource;

    @Schema(description = "")
    private String note;

    @Schema(description = "")
    private Integer saleAmt;

    @Schema(description = "")
    private Integer mUSER;

    @Schema(description = "")
    private Date mDT;

    @Schema(description = "")
    private String saleEmpID;

    @Schema(description = "")
    private BigDecimal freeMonAmt;

    @Schema(description = "")
    private Integer ordersTAuto;

    @Schema(description = "")
    private Integer isShared;

    @Schema(description = "專案別")
    private Integer projectCode;

    @Schema(description = "代步車保險比照原合約")
    private Integer arrangeInsure;

    @Schema(description = "參考版維修費")
    private Integer carFixAdj;

    @Schema(description = "替代車號 for新車未到")
    private String subMakno4New;

    @Schema(description = "業務員序號")
    private Integer salesAuto;

    @Schema(description = "通路商媒介人代號")
    private Integer channelAgentAuto;

    @Schema(description = "")
    private Integer isFinNo;

    @Schema(description = "契約來源 [CarItem.Type=34]")
    private Integer contractSource;

    @Schema(description = "是否開通車上機(開通車上話機服務)")
    private Integer isCellServiceON;

    @Schema(description = "(試算系統)申購單號")
    private Integer ordersAuto;

    @Schema(description = "非新車投保 0毋須處理 1準備投保 2完成投保")
    private Integer forInsure;

    @Schema(description = "業務分類 [CarItem.Type=100]")
    private Integer salesType;

    @Schema(description = "帳戶型租購含修 0否 1是")
    private Integer depositProjFlag;

    @Schema(description = "帳戶型租購含修變更日 (for租購不含修→帳戶型租購含修)")
    private Date depositProjDT;

    @Schema(description = "是否為user透過地球建立的契約，0：否、1：是")
    private Integer isUserCreate;

    @Schema(description = "契約所屬庫位")
    private Integer buId;
}
