package com.carplus.subscribe.model.excel;

import com.univocity.parsers.annotations.Parsed;
import lombok.Data;

@Data
public class CarModelCSV {

    @Parsed(field = "廠牌代碼")
    private String brandCode;
    @Parsed(field = "廠牌名稱")
    private String brandName;
    @Parsed(field = "廠牌英文名稱")
    private String brandNameEn;
    @Parsed(field = "車型名稱")
    private String carModelName;
    @Parsed(field = "車型代碼")
    private String carModelCode;
    @Parsed(field = "車型種類名稱")
    private String carKindName;
    @Parsed(field = "已刪除")
    private String isDeleted;
}
