package com.carplus.subscribe.model.sensor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SensorEventReq {

    @Schema(description = "神策事件清單")
    List<SensorEvent> sensorEventList;

    public SensorEventReq(SensorEvent event) {
        this.sensorEventList = Collections.singletonList(event);
    }

}
