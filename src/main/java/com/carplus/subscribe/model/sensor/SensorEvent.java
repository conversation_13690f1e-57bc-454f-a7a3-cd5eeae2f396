package com.carplus.subscribe.model.sensor;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

@Data
public class SensorEvent {
    @NotEmpty
    @Schema(description = "神策ID")
    private String sensorId;

    @NotEmpty
    @Schema(description = "事件名稱")
    private String eventName;

    @Schema(description = "事件資訊")
    private Map<String, Object> params;
}
