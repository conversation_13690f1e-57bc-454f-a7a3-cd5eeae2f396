package com.carplus.subscribe.model.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class PreownedInv {

    private String stationCode;

    private String realArea;

    private String area;

    private String areaName;

    private List<PreownedLocation> locations;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PreownedLocation {
        private String location;

        private String realLocation;

        private String locationName;

        private PreownedInv preownedInv;

    }
}
