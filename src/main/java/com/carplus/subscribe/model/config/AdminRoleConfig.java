package com.carplus.subscribe.model.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AdminRoleConfig {
    private String adminRoleCode;
    private boolean isCheckStation;
    private String adminRoleName;
    private Boolean superAdmin;

    public boolean isSuperAdmin() {
        return superAdmin != null && superAdmin;
    }
}
