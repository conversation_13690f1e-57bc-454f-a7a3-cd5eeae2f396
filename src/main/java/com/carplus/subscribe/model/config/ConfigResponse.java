package com.carplus.subscribe.model.config;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.ConfigEntity;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

@Data
public class ConfigResponse {

    private Integer id;

    private String code;

    private Object value;

    public ConfigResponse(ConfigEntity entity) {
        if (entity != null) {
            this.id = entity.getId();
            this.code = entity.getCode();
            if (StringUtils.isNotBlank(entity.getValue())) {
                try {
                    this.value = new ObjectMapper().readValue(entity.getValue(), new TypeReference<Object>() {
                    });
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

}
