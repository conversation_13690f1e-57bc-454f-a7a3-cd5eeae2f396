package com.carplus.subscribe.model.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthDealerUser {

    /**
     * 會員身分ID
     */
    private String idNo;

    /**
     * 會員姓名
     */
    private String userName;

    /**
     * 會員國碼
     */
    private String nationalCode;

    /**
     * 會員手機號碼
     */
    private String mainCell;

    /**
     * 會員生日
     */
    private String birthDay;

    /**
     * 會員信箱
     */
    private String email;

    /**
     * 會員戶籍地址-縣市ID
     */
    private Integer hhcityId;

    /**
     * 會員戶籍地址-行政區ID
     */
    private Integer hhareaId;

    /**
     * 會員戶籍地址
     */
    private String hhaddress;

    /**
     * 會員服務單位-統編
     */
    private String vatNumber;

    /**
     * 會員公司抬頭
     */
    private String companyName;

    /**
     * 會員公司地址
     */
    private String companyLocation;
}
