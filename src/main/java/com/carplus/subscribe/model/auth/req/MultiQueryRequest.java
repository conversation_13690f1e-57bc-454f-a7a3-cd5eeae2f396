package com.carplus.subscribe.model.auth.req;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MultiQueryRequest {

    private List<Integer> acctIDList = Lists.newArrayList();

    public MultiQueryRequest(Integer... acctIds) {
        for (int acctId : acctIds) {
            this.acctIDList.add(acctId);
        }
    }
}
