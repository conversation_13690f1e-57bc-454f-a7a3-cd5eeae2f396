package com.carplus.subscribe.model.auth.resp;

import com.carplus.subscribe.model.auth.AuthDealerUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class AuthDealerUserResponse extends AuthDealerUser {

    /**
     * 會員資料編號
     */
    private Long id;

    /**
     * 會員資料建立時間
     */
    private Date createDate;

    /**
     * 會員資料建立者
     */
    private String createBy;

    /**
     * 會員資料更新時間
     */
    private Date updateDate;

    /**
     * 會員資料更新者
     */
    private String updateBy;
}
