package com.carplus.subscribe.model.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class DriverDto {

    @Schema(description = "共同駕駛人index")
    private int index;
    @Schema(description = "駕駛人auth acctId")
    private Long acctId;
    @Schema(description = "身份證字號")
    @NotEmpty(message = "身份證字號不可為空")
    private String id;
    @Schema(description = "姓名")
    @NotEmpty(message = "姓名不可為空")
    private String name;
    @Schema(description = "手機號碼")
    private String phone;
    @Schema(description = "手機國碼")
    private String nationalCode;
    @Schema(description = "生日")
    @NotNull(message = "生日不可為空")
    private Date birthday;
    @Schema(description = "電子信箱")
    private String email;
    @Schema(description = "戶籍縣市")
    private Integer cityId;
    @Schema(description = "戶籍區域")
    private Integer areaId;
    @Schema(description = "地址")
    private String address;
    @Schema(description = "汽車駕照管轄編號")
    private String licenseNo;
    @Schema(description = "機車駕照管轄編號")
    private String motoLicenseNo;
    @Schema(description = "身份 0: 本國人(含居留), 1: 外國人", allowableValues = "0,1")
    private Integer isForeigner;
    @Schema(description = "性別")
    private String gender;
    @Schema(description = "國籍")
    private String nationalName;
    @Schema(description = "客戶資料卡路徑")
    private String custCardUrl;
    @Schema(description = "個資同意書路徑")
    private String agreementUrl;
    @Schema(description = "緊急聯絡人姓名")
    private String emergencyContactName;
    @Schema(description = "緊急聯絡人國碼")
    private String ecCellNationalCode;
    @Schema(description = "緊急聯絡人手機")
    private String ecCell;
    @Schema(description = "緊急聯絡人市話區碼")
    private String ecTelAreaCode;
    @Schema(description = "緊急聯絡人市話")
    private String ecTel;
    @Schema(description = "緊急聯絡人email")
    private String ecEmail;
    @Schema(description = "緊急聯絡人關係")
    private String emergencyContactDesc;


}
