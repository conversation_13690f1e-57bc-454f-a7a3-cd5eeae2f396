package com.carplus.subscribe.model.auth;

import carplus.common.utils.StringUtils;
import lombok.Data;

import java.util.Date;
import java.util.Optional;

@Data
public class AuthUser {

    /**
     * 會員識別碼
     */
    private Integer acctId;
    /**
     * 會員姓名
     */
    private String acctName;
    /**
     * 暱稱
     */
    private String nickName;
    /**
     * 通訊地址
     */
    private String addr;
    /**
     * 聯絡地址-行政區ID
     */
    private Integer areaId;
    /**
     * 出生日期(文字)
     */
    private String birthday;
    /**
     * 聯絡地址-縣市ID
     */
    private Integer cityId;
    /**
     * 建立時間
     */
    private Date creDate;
    /**
     * 申請IP
     */
    private String creIP;
    /**
     * 申請人員
     */
    private String creUser;
    /**
     * 會員狀態 A:申請、B:初審通過、C:初審不通過、D:會員、E:黑名單、F廢除、I失效、G:帳號刪除
     */
    private String dbSts;
    /**
     * 備註
     */
    private String descr;
    /**
     * 駕照狀態
     */
    private String driveLicensStatus;
    /**
     * 駕照狀態查詢日期時間
     */
    private Date driverLicenseCheckTime;
    /**
     * 緊急聯絡人-行動電話
     */
    private String ecCell;
    /**
     * 緊急聯絡人-市話
     */
    private String ecTel;
    /**
     * 信箱
     */
    private String email;
    /**
     * Email驗證
     */
    private String emailConfirmed;
    /**
     * 與緊急連絡人關係
     */
    private String emergencyContactDesc;
    /**
     * 緊急連絡人
     */
    private String emergencyContactName;
    /**
     * 訂閱電子報(Y:是,N:否)
     */
    private String epaperSts;
    /**
     * 戶籍地址
     */
    private String hhrAddr;
    /**
     * 戶籍地址-行政區ID
     */
    private Integer hhrAreaId;
    /**
     * 戶籍地址-縣市ID
     */
    private Integer hhrCityId;
    /**
     * 服務單位-全稱
     */
    private String invTitle;
    /**
     * 服務單位-市話
     */
    private String jobTel;
    /**
     * 服務單位-職稱
     */
    private String jobTitle;
    /**
     * 管轄編號
     */
    private String jurisdictionNum;
    /**
     * 身分證字號
     */
    private String loginId;
    /**
     * 主要聯絡電話-行動電話
     */
    private String mainCell;
    /**
     * 次要聯絡電話-行動電話
     */
    private String subCell;
    /**
     * 主要聯絡電話-市話
     */
    private String mainTel;
    /**
     * 主要聯絡電話-市話區碼
     */
    private String mainTelAreaCode;
    /**
     * 國碼
     */
    private String nationalCode;
    /**
     * 次要國碼
     */
    private String subNationalCode;
    /**
     * 手機驗證
     */
    private String phoneNumberConfirmed;
    /**
     * MD5密碼
     */
    private String psw;
    /**
     * 接收簡訊(Y:是,N:否)
     */
    private String smsSts;
    /**
     * 最後異動時間
     */
    private Date updDate;
    /**
     * 最後異動IP
     */
    private String updIP;
    /**
     * 最後異動人員
     */
    private String updUser;
    /**
     * 服務單位-統編
     */
    private String vatNumber;
    /**
     * 個資同意Flag
     */
    private String allowPromote;
    /**
     * 性別
     */
    private String gender;
    /**
     * 轉入來源
     */
    private String srcSys;
    /**
     * 驗證身份類別代碼
     */
    private String vipCode;
    /**
     * VIP到期時間
     */
    private Date vipExpiredDate;
    /**
     * 違規未結次數
     */
    private String penaltycount;
    /**
     * 欠費總金額
     */
    private String penaltyamt;
    /**
     * 驗證身份類別時間
     */
    private Date memberverifydatetime;
    /**
     * 好友邀請碼
     */
    private String referralCode;
    /**
     * 會員等級
     */
    private String rentLevel;
    /**
     * 身份 0: 本國人(含居留), 1: 外國人
     */
    private Integer isForeigner;
    /**
     * 國籍
     */
    private String nationalName;
    /**
     * vip 名稱
     */
    private String vipName;

    public String getCustomerName() {
        return Optional.ofNullable(acctName).map(acctName -> StringUtils.mask(acctName, '*')).orElse("");
    }

    public String combineNationalCodeAndPhoneNumber() {

        String nationalCode = getNationalCode();
        String phone = getMainCell();

        if (StringUtils.isBlank(phone) && StringUtils.isBlank(nationalCode)) {
            return "";
        }

        nationalCode = StringUtils.isBlank(nationalCode) ? "886" : nationalCode.startsWith("+") ? nationalCode.substring(1) : nationalCode;

        phone = StringUtils.isNotBlank(phone) ? phone.replaceFirst("^0", "") : "";

        return "+" + nationalCode + phone;
    }
}
