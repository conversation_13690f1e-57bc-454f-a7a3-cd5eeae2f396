package com.carplus.subscribe.model.subscribelevel;

import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.enums.SubscribeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

import static com.carplus.subscribe.enums.SubscribeType.SEASON;

@Data
public class SubscribeLevelResponse implements MonthlyFeeProvider {

    /**
     * 訂閱車方案
     */
    @Schema(description = "訂閱車方案")
    private int level;
    /**
     * 訂閱車方案名稱
     */
    @Schema(description = "訂閱車方案名稱")
    private String name;
    /**
     * 保證金
     */
    @Schema(description = "保證金")
    private int securityDeposit;
    /**
     * 基本月費
     */
    @Schema(description = "基本月費")
    private int monthlyFee;
    /**
     * 里程費
     */
    @Schema(description = "里程費")
    private double mileageFee;

    @Schema(description = "方案期數限制")
    private SubscribeType type = SEASON;

    /**
     * 優惠價
     */
    @Schema(description = "優惠月費")
    private int discountMonthlyFee;

    @Schema(description = "是否自動授信")
    private boolean autoCredit;

    @Schema
    private List<MileageDiscount> mileageDiscount;

    public SubscribeLevelResponse() {
    }

    public SubscribeLevelResponse(SubscribeLevel subscribeLevel) {
        this.level = subscribeLevel.getLevel();
        this.name = subscribeLevel.getName();
        this.securityDeposit = subscribeLevel.getSecurityDeposit();
        this.monthlyFee = subscribeLevel.getMonthlyFee();
        this.mileageFee = subscribeLevel.getMileageFee();
        this.discountMonthlyFee = subscribeLevel.getDiscountMonthlyFee();
        this.type = subscribeLevel.getType();
        this.autoCredit = subscribeLevel.isAutoCredit();
        this.mileageDiscount = subscribeLevel.getMileageDiscount();
    }
}
