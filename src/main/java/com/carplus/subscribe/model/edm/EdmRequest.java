package com.carplus.subscribe.model.edm;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.EdmType;
import com.carplus.subscribe.enums.OrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EdmRequest {

    @Schema(description = "EDM類型")
    private EdmType type;

    @Schema(description = "身份證字號")
    private String custId;
    @Schema(description = "訂車人")
    private String custName;

    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "訂單時間")
    private Date createDate;
    @Schema(description = "訂單狀態")
    private String status;
    @Schema(description = "車牌號碼")
    private String plateNo;
    @Schema(description = "合約期間起（取車時間）")
    private Date departDate;
    @Schema(description = "合約期間迄")
    private Date returnDate;
    @Schema(description = "車型名稱")
    private String carModelName;
    @Schema(description = "車廠牌名稱")
    private String carBrandName;

    @Schema(description = "訂閱保證金")
    private int securityDeposit;
    @Schema(description = "中古車/新車")
    private CarDefine.CarState carState;
    @Schema(description = "基本月費")
    private int monthlyFee;
    @Schema(description = "優惠月費")
    private int discountMonthlyFee;
    @Schema(description = "月數")
    private Integer months;
    @Schema(description = "里程費")
    private double mileageFee;
    @Schema(description = "是否折扣月費")
    private boolean monthlyDiscounted;
    @Schema(description = "是否啟用超激優惠")
    private boolean levelDiscounted;
    @Schema(description = "實際使用月費")
    private int useMonthlyFee;
    @Schema(description = "保險方案")
    private Integer insurance;
    @Schema(description = "代步車")
    private Integer replacement;


    @Schema(description = "取車站所名稱")
    private String departStationName;
    @Schema(description = "取車站所電話")
    private String departStationTel;
    @Schema(description = "取車站所地址")
    private String departStationAddr;


    @Schema(description = "還車站所名稱")
    private String returnStationName;
    @Schema(description = "還車站所電話")
    private String returnStationTel;
    @Schema(description = "還車站所地址")
    private String returnStationAddr;
    @Schema(description = "線上應付費用(保證金)")
    private int unpaidSecurityDeposit;
    @Schema(description = "期數月份")
    private int stageMonth;

    /***********************************
     *  ***********首期應付費用***********
     *  ********************************
     */
    @Schema(description = "首期月費")
    private int firstStageMonthlyFee;
    @Schema(description = "首期保險費")
    private int firstStageInsurance;
    @Schema (description = "首期代步費")
    private int firstReplacementCarFee;
    @Schema(description = "調度費")
    private int dispatchFee;

    /***********************************
     *  ***********每期應付費用***********
     *  ********************************
     */
    @Schema(description = "每期月費")
    private int stageMonthlyFee;
    @Schema(description = "每期保險費")
    private int stageInsurance;
    @Schema(description = "每期代步費")
    private int stageReplacementCarFee;

    @Schema(description = "每期最後繳款期限")
    private Date lastPayDate;


    public static String status(@NonNull EdmType type, @NonNull Orders order) {
        switch (type) {
            case SUB_CREATE_ORDER:
                return order.getStage() == 1 ? "已付保證金" : "已續約";
            case SUB_CREDIT_SUCCESS:
                return "待繳付保證金";
            case SUB_CREDIT_FAIL:
                return "審核未通過";
            case SUB_CREDIT_DEMAND:
                return "審核中";
            case SUB_CANCEL_ORDER:
                return "已取消";
            case SUB_RENEW_CALL:
                return "合約中";
            case SUB_RENEW_CONFIRM:
                return "已續約";
            case SUB_STAGE_PAY:
                return OrderStatus.of(order.getStatus()).getName();
            case SUB_UPDATE_ORDER:
                OrderStatus orderStatus = OrderStatus.of(order.getStatus());
                switch (orderStatus) {
                    case CREDIT_PENDING:
                    case BOOKING:
                        return orderStatus.getName();
                    case CREDITED:
                        return "待繳付保證金";
                    case DEPART:
                        return "合約中";
                    default:
                        return "";
                }
            default:
                return "";
        }
    }
}
