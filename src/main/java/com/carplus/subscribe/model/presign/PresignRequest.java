package com.carplus.subscribe.model.presign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PresignRequest {

    @Schema(name = "路徑", example = "s2g/img")
    private String filePath;

    @Schema(name = "副檔名(不用’.’)", example = "PNG/JPG/CSV")
    private String fileType;

    @Schema(name = "來源", example = "SMART2GO")
    private String source;

    @Schema(name = "是否放入內部Bucket")
    private boolean isSecretBucket = false;
}
