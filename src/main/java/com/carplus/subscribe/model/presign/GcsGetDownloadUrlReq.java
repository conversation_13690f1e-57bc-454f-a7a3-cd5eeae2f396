package com.carplus.subscribe.model.presign;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GcsGetDownloadUrlReq {

    private String source; // 來源
    private String filePath; // 路徑
    private Boolean isTemp; // 是否為暫存檔案 (暫存檔案在不同的 bucket)
    private List<String> fileNames; // 檔案名稱, 如果不給檔名清單 會給路徑底下所有的檔案
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer minutes = 1; //有效時間，寄信時需指定效期
}