package com.carplus.subscribe.model.presign;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class GcsUrlRes {

    private String exampleUpload;
    private String exampleDownload;
    private List<TypeResponse> signedUrls;

    @Data
    @NoArgsConstructor
    public static class TypeResponse {
        private String fileName; // 檔案名稱
        private String mediaType; // 媒體形式
        private String signedUrl; // Signed Url
    }
}