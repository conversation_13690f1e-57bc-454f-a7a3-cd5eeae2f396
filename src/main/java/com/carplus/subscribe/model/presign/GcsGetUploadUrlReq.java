package com.carplus.subscribe.model.presign;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GcsGetUploadUrlReq {

    private String source; // 來源
    private String filePath; // 路徑
    private Boolean isTemp = Boolean.FALSE; // 是否為暫存檔案 (預設為 false) (暫存檔案在不同的 bucket)
    private List<TypeCount> requestList; // 上傳簽章需求清單

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeCount {
        private String mediaType; // 媒體形式
        private int quantity; // 數量
    }
}