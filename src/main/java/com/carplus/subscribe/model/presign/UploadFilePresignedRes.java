package com.carplus.subscribe.model.presign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UploadFilePresignedRes {

    @Schema(description = "編號", example = "567")
    private Integer id;
    @Schema(description = "檔案名稱", example = "a3mf9asfd3rdfi4lf-f.png")
    private String fileName; // 檔案名稱
    @Schema(description = "媒體形式", example = "image/png")
    private String mediaType; // 媒體形式
    @Schema(description = "Signed Url", example = "https://....")
    private String signedUrl; // Signed Url

}
