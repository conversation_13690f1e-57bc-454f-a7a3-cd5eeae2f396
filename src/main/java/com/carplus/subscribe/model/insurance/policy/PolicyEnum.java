package com.carplus.subscribe.model.insurance.policy;

import lombok.Getter;

public class PolicyEnum {

    @Getter
    public enum Status {
        WAIT("wait", "待請款"),
        PAYING("paying", "請款中"),
        VAILD("valid", "有效保單"),
        CANCEL("cancel", "已退保"),
        EXPIRED("expired", "已過期"),
        INVALID("invalid", "作廢");

        private final String code;
        private final String name;

        Status(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }
}
