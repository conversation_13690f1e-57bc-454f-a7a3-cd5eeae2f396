package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "投保車籍")
@FieldNameConstants
public class CarInfoView extends CarInfo {

    @Schema(description = "格上車種說明")
    private String carTypeDesc;

    @Schema(description = "廠牌說明")
    private String brandName;

    @Schema(description = "車型說明")
    private String className;

    @Schema(description = "牌照狀態說明")
    private String licenseTypeDesc;

    @Schema(description = "車輛來源說明")
    private String carSourceDesc;

    @Schema(description = "車輛狀態說明")
    private String licenseStatusDesc;
}
