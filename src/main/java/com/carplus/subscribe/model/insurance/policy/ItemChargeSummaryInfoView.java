package com.carplus.subscribe.model.insurance.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Data
@Schema(description = "險種異動紀錄資訊")
public class ItemChargeSummaryInfoView extends ItemChargeSummaryInfo {

    @Schema(description = "異動類型說明")
    private String chargeTypeDesc;

    @Schema(description = "請款狀態說明")
    private String paymentStatusDesc;

}
