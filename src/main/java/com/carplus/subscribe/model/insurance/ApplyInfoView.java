package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "投保內容總覽")
@FieldNameConstants
public class ApplyInfoView extends ApplyInfo {

    @Schema(description = "投保類別說明")
    private String policyTypeDesc;

    @Schema(description = "(1.新保 2.續保) 說明")
    private String policyNewDesc;

    @Schema(description = "試算指定保險公司名稱")
    protected String insuranceCompanyName;

    @Schema(description = "保險報價單保險公司名稱")
    protected String quoteCompanyName;

    @Schema(description = "指定保險業務員公司名稱")
    private String paymentSalesCompanyName;

    @Schema(description = "指定保險業務員名稱")
    private String paymentSalesName;

    @Schema(description = "指定保險業務員手機")
    private String paymentSalesCellPhone;

    @Schema(description = "特殊條件代碼說明")
    private String specialTermsDesc;

    @Schema(description = "代收代付金額")
    private long customerPay;

    @Schema(description = "投保聯絡人姓名")
    private String contactName;

    @Schema(description = "投保聯絡人email")
    private String contactEmail;

    @Schema(description = "投保聯絡人聯絡電話")
    private String contactPhone;
}
