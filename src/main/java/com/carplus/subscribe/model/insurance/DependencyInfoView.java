package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Data
public class DependencyInfoView extends DependencyInfo {

    @Schema(description = "建立來源系統別說明 service_code=insurance-depenceDocSystemCode")
    private String depenceDocSystemDesc;

    @Schema(description = "建立來源單據別說明 service_code=insurance-depenceDocType")
    private String depenceDocTypeDesc;
}
