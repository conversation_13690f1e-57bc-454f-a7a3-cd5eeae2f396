package com.carplus.subscribe.model.insurance;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "投保車籍")
public class CarInfo {

    //資料庫欄位 - 車型
    //==========================================================
    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String carStandardCode;
    /**
     * 車型代碼 - 廠牌
     */
    @Schema(description = "車型代碼 - 廠牌")
    private String brandCode;
    /**
     * 車型代碼 - 車型
     */
    @Schema(description = "車型代碼 - 車型")
    private String classCode;
    /**
     * 車型代碼 - 牌價
     */
    @Schema(description = "車型代碼 - 牌價")
    private String stdPriceCode;
    /**
     * 車型代碼 - code4
     */
    @Schema(description = "車型代碼 - code4")
    private String code4;
    /**
     * 當下牌價
     */
    @Schema(description = "當下牌價")
    private Integer stdPrice;
    /**
     * 車型明細
     */
    @Schema(description = "車型明細")
    private String classDetail;
    /**
     * 車種
     */
    @Schema(description = "車種")
    private String carType;
    /**
     * CC數
     */
    @Schema(description = "CC數")
    private Integer cylinder;
    /**
     * 乘載數
     */
    @Schema(description = "乘載數")
    private Integer seats;


    //資料庫欄位 - 車籍
    //==========================================================
    /**
     * 車輛識別碼
     */
    @Schema(description = "車輛識別碼")
    private Integer carNo;
    /**
     * 投保車號
     */
    @Schema(description = "投保車號")
    private String plateNo;
    /**
     * 出廠年月
     */
    @Schema(description = "出廠年月")
    private String publishDate;
    /**
     * 引擎號碼
     */
    @Schema(description = "引擎號碼")
    private String engineNo;
    /**
     * 加保配件內容
     */
    @Schema(description = "加保配件內容")
    private String insureAccessoriesContent;
    /**
     * 加保配件金額
     */
    @Schema(description = "加保配件金額")
    private Integer insureAccessoriesAmount;
    /**
     * 車身號碼
     */
    @Schema(description = "車身號碼")
    private String bodyNo;
    /**
     * 預計領牌日
     */
    @Schema(description = "預計領牌日")
    private Instant reserveLicenseDate;
    /**
     * 車輛進價
     */
    @Schema(description = "車輛進價")
    private Integer getPrice;
    /**
     * 投保牌照類型
     */
    @Schema(description = "投保牌照類型")
    private String licenseType;
    /**
     * 車輛來源
     */
    @Schema(description = "車輛來源")
    private String carSource;


    //邏輯欄位
    //==========================================================

    //CRS 車籍狀態
    @JsonIgnore
    private String crsCarStatus;

}
