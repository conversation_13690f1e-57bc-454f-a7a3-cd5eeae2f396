package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "投保客戶資料")
public class CustomerInfo {
    /**
     * 要保人ID
     */
    @Schema(description = "要保人ID")
    private String proposerId;
    /**
     * 要保人姓名
     */
    @Schema(description = "要保人姓名")
    private String proposerName;
    /**
     * 被保人ID
     */
    @Schema(description = "被保人ID")
    private String insuredId;
    /**
     * 被保人姓名
     */
    @Schema(description = "被保人姓名")
    private String insuredName;
    /**
     * 使用人ID
     */
    @Schema(description = "使用人ID")
    private String insureUserId;
    /**
     * 使用人姓名
     */
    @Schema(description = "使用人姓名")
    private String insureUserName;


}
