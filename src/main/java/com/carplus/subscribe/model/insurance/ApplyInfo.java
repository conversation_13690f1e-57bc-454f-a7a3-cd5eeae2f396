package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "投保內容")
public class ApplyInfo {
    /**
     * 保單類別(1.強制 2.任意) service_code=insurance-insuranceType
     */
    @Schema(description = "保單類別(1.強制 2.任意) service_code=insurance-insuranceType")
    protected Integer policyType;
    /**
     * 投保類別(1.新保 2.續保) service_code=insurance-insureType
     */
    @Schema(description = "投保類別(1.新保 2.續保) service_code=insurance-insureType")
    protected Integer policyNew;
    /**
     * 試算指定保險公司
     */
    @Schema(description = "試算指定保險公司")
    protected String insuranceCompanyId;
    /**
     * 試算指定保險公司
     */
    @Schema(description = "指定保險公司折扣率")
    private BigDecimal insuranceCompanyDiscountRate;
    /**
     * 試算指定保險公司
     */
    @Schema(description = "特殊條件  service_code=insurance-specialTerms")
    protected String specialTerms;
    /**
     * 保險報價單保險公司
     */
    @Schema(description = "保險報價單保險公司")
    private String quoteCompanyId;
    /**
     * 保險報價單編號
     */
    @Schema(description = "保險報價單編號")
    private String quoteNo;
    /**
     * 保險報價單保費
     */
    @Schema(description = "保險報價單保費")
    private BigDecimal quoteAmount;
    /**
     * 客戶自理  0:否 1:是
     */
    @Schema(description = "客戶自理  0:否 1:是")
    private Integer customerInsure;
    /**
     * 首年保險費由車商贈送  0:否 1:是
     */
    @Schema(description = "首年保險費由車商贈送  0:否 1:是")
    private Integer insureFreeYear1;
    /**
     * 預計起保日
     */
    @Schema(description = "預計起保日")
    private Instant requisitionStartDate;
    /**
     * 投保車價
     */
    @Schema(description = "投保車價")
    protected Integer insureCarPrice;
    /**
     * 續保備註
     */
    @Schema(description = "續保備註")
    private String renewMemo;
    /**
     * 通知保險公司備註
     */
    @Schema(description = "通知保險公司備註")
    private String proposeMemo;
    /**
     * 投保單備註
     */
    @Schema(description = "投保單備註")
    private String requisitionMemo;

    @Schema(description = "是否為計程車")
    private Boolean isTaxi;

}

