package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ItemInfo {
    /**
     * 險種代碼
     */
    @Schema(description = "險種代碼")
    private String itemId;
    /**
     * 險種計價公式(0.標準 1.富邦聯保)
     */
    @Builder.Default
    @Schema(description = "險種計價公式(0.標準 1.富邦聯保)")
    private Integer itemFormula = 0;
    /**
     * 保額
     */
    @Schema(description = "保額")
    private Long coverage;
    /**
     * 保額參數1
     */
    @Schema(description = "保額參數1")
    private Long parameter1Code;
    /**
     * 保額參數2
     */
    @Schema(description = "保額參數2")
    private Long parameter2Code;
    /**
     * 保額參數3
     */
    @Schema(description = "保額參數3")
    private Long parameter3Code;
    /**
     * 保額人數
     */
    @Schema(description = "保額人數")
    private Integer person;
    /**
     * 成本保費
     */
    @Schema(description = "成本保費")
    private Integer costPremium;
    /**
     * 計價保費
     */
    @Schema(description = "計價保費")
    private Integer quotePremium;
    /**
     * 核保保費
     */
    @Schema(description = "核保保費")
    private Integer commitPremium;
    /**
     * 分配指定保險公司
     */
    @Schema(description = "分配指定保險公司")
    private String insuranceDispatchCompanyId;

}
