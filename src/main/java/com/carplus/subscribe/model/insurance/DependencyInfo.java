package com.carplus.subscribe.model.insurance;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "建立來源資訊")
public class DependencyInfo {

    @Schema(description = "建立來源系統別 service_code=insurance-depenceDocSystemCode")
    private String depenceDocSystemCode;

    @Schema(description = "建立來源單據別 service_code=insurance-depenceDocType")
    private String depenceDocType;

    @Schema(description = "建立來源識別碼")
    private String depenceDocNo;

    @Schema(description = "來源單據上的備註")
    private String depenceDocMemo;

    @Schema(description = "由來源轉入的行為說明")
    private String depenceMemo;

    @Schema(description = "需求來源部級ID")
    private String depenceDocUpDepartmentCode;

    @Schema(description = "需求來源部級名稱")
    private String depenceDocUpDepartmentName;

    @Schema(description = "需求來源課級ID")
    private String depenceDocDepartmentCode;

    @Schema(description = "需求來源課級名稱")
    private String depenceDocDepartmentName;

    @Schema(description = "需求來源課級業務ID")
    private String depenceDocUserID;

    @Schema(description = "需求來源課級業務姓名")
    private String depenceDocUserName;


}
