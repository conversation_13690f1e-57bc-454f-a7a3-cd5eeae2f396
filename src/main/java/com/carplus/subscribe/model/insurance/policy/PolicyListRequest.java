package com.carplus.subscribe.model.insurance.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@FieldNameConstants
public class PolicyListRequest {

    @NotNull(message = "請輸入是否為退保查詢")
    @Schema(description = "是否為退保查詢")
    private Boolean isRejectQuery;

    @Schema(description = "投保牌照號碼")
    private Set<String> plateNo;

    @Schema(description = "保單號碼")
    private Set<String> policyNumber;

    @Schema(description = "保單起日-開始")
    private Instant policyStartDateFrom;

    @Schema(description = "保單起日-結束")
    private Instant policyStartDateTo;

    @Schema(description = "保單迄日-開始")
    private Instant policyEndDateFrom;

    @Schema(description = "保單迄日-結束")
    private Instant policyEndDateTo;

    @Schema(description = "保險公司")
    private String policyCompanyId;

    @Schema(description = "續保狀態")
    private Set<Integer> renewStatus;

    @Schema(description = "請款狀態")
    private Set<String> status;

    @Schema(description = "引擎號碼")
    private Set<String> engineNo;

    @Schema(description = "承租人姓名")
    private String insureUserName;

    @Schema(description = "建立來源單據別")
    private String depenceDocType;

    @Schema(description = "建立來源識別碼")
    private String depenceDocNo;

    @Schema(description = "車籍唯一碼", hidden = true)
    private List<Integer> carNoList;

    @Schema(description = "保單類型, 1.強制險，2.任意險")
    private Integer policyType;

    @Schema(description = "保單建案日-起")
    private Instant policyCreateDateFrom;

    @Schema(description = "保單建案日-迄")
    private Instant policyCreateDateTo;

    @Schema(description = "新/績保 1.新保 2.繼保")
    private Integer policyNew;

    @Schema(defaultValue = "0")
    private Integer offset;
    @Schema(defaultValue = "10")
    private Integer limit;
}
