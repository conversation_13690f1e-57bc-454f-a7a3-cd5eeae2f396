package com.carplus.subscribe.model.insurance;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class ApplyInfoEnum {

    @Getter
    @AllArgsConstructor
    public enum PolicyType {
        COMPULSORY(1, "強制險"),
        ARBITRARILY(2, "任意險"),
        ;

        private final Integer code;
        private final String name;
        private final String serviceCode = "insurance_policytype";
        private final int order = ordinal();
        private final String memo = "保單類別";
    }
}
