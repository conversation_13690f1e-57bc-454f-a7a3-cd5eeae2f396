package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class InsurePlanListRequest {
    @Schema(description = "保險用途", required = true)
    private Integer useTypeId;

    @Schema(description = "格上車種代碼", required = true)
    private String carType;

    @Schema(description = "車系", required = true)
    private String carSeries;

    @Schema(description = "廠牌")
    private String brandCode;

    @Schema(description = "車型")
    private String classCode;

    @Schema(description = "車型明細")
    private String stdPriceCode;

    @Schema(description = "code4")
    private String code4;

    @Schema(description = "格上能源別代碼", example = "1:汽油;2:柴油;3:汽油/電力;4:汽油/LPG;5:電動;6:柴油/電力", allowableValues = "1,2,3,4,5,6")
    private String carPlusEnergyCode;


}
