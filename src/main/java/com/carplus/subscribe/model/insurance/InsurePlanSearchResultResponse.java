package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class InsurePlanSearchResultResponse {
    @Schema(description = "保險用途")
    private Integer useTypeId;

    @Schema(description = "格上車種代碼")
    private String carType;

    @Schema(description = "車系")
    private String carSeries;

    @Schema(description = "廠牌")
    private String brandCode;

    @Schema(description = "車型")
    private String classCode;

    @Schema(description = "車型明細")
    private String stdPriceCode;

    @Schema(description = "code4")
    private String code4;

    @Schema(description = "格上能源別代碼")
    private String carPlusEnergyCode;

    @Schema(description = "保險方案清單")
    private InsurePlanResponse insurePlanResponse;

}
