package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@Builder
@Schema(description = "請款資訊")
@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
public class PaymentInfo {
    @Schema(description = "請款-分攤部門別ID")
    private String paymentDepartmentCode;

    @Schema(description = "請款-分攤部門別ID")
    private String paymentDepartmentName;

}
