package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "投保險種資訊 & 投保險種預設方案ID 只能擇一且必須其一")
public class RequisitionCreateRequest {
    @Schema(description = "投保內容資訊")
    private ApplyInfo applyInfo;

    @Schema(description = "投保客戶資訊")
    private CustomerInfo customerInfo;

    @Schema(description = "投保車輛資訊")
    private CarInfo carInfo;

    @Schema(description = "投保來源")
    private DependencyInfo depenceInfo;

    @Schema(description = "請款資訊")
    private PaymentInfo paymentInfo;

    @Schema(description = "投保險種資訊")
    private List<ItemInfo> itemInfoList;

    @Schema(description = "投保險種預設方案ID")
    private Long itemPlanId;

    @Schema(description = "錯誤訊息")
    private String errMsg;


//    @Schema(description="投保缺件(應補件)資訊")
//    private List<LackInfo> lackInfoList;


    /**
     * 轉換為強制險
     */
    public void convertToCompulsoryInsurance(Instant licenseExpDate) {
        itemInfoList = Collections.singletonList(getNewCompulsoryItemInfo());
        Optional.ofNullable(applyInfo).ifPresent(applyInfo1 -> {
            applyInfo1.setPolicyType(1);
            applyInfo1.setRequisitionStartDate(licenseExpDate);
        });
        Optional.ofNullable(carInfo).ifPresent(carInfo1 -> carInfo1.setReserveLicenseDate(licenseExpDate));
        itemPlanId = null;
    }


    private ItemInfo getNewCompulsoryItemInfo() {
        ItemInfo itemInfo = new ItemInfo();
        itemInfo.setItemId("81");             //格上強制險險種 ID
        itemInfo.setItemFormula(0);           //險種計價公式 0.標準
        itemInfo.setCoverage(2000000L);       //保額2百萬
        itemInfo.setParameter1Code(200000L);  //每一體傷 20 萬
        itemInfo.setParameter2Code(2000000L); //每一死亡 2百萬
        itemInfo.setParameter3Code(0L);       //自負額 0
        itemInfo.setPerson(0);
        itemInfo.setInsuranceDispatchCompanyId("06"); //預設保險公司為新安
        return itemInfo;
    }
}
