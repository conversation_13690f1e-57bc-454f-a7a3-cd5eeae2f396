package com.carplus.subscribe.model.insurance;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@Data
@FieldNameConstants
public class CrsCarLicenseInfo {
    /**
     * 車輛唯一碼
     */
    private Integer carNo;
    /**
     * 牌照號碼
     */
    private String plateNo;
    /**
     * 舊牌照號碼
     */
    private String oldPlateNo;
    /**
     * 所屬公司統編
     */
    private String companyNo;
    /**
     * 領牌日
     */
    private Instant licenseDate;
    /**
     * 行照有效日
     */
    private Instant licenseDueDate;
    /**
     * 牌照類型代碼
     */
    private String licenseType;
    /**
     * 異動日期
     */
    private Instant statusChangeDate;
    /**
     * 車牌狀態代碼
     */
    private String licenseStatus;
    /**
     * 丙種認證到期日
     */
    private Instant certificationDueDate;
    /**
     * 領牌費用基準日
     */
    private Instant benchmarkDate;
    /**
     * py_auto
     */
    private Integer pyAuto;
    /**
     * 建檔日期
     */
    private Instant createDate;

    /**
     * 建檔人員
     */
    private String createUserId;

    /**
     * 異動日期
     */
    private Instant updateDate;

    /**
     * 異動人員
     */
    private String updateUserId;

    /**
     * 牌照類型名稱
     */
    private String licenseTypeName;

    /**
     * 牌照狀態名稱
     */
    private String licenseStatusName;

    /**
     * 繳銷日
     */
    private Instant payOffDate;
}
