package com.carplus.subscribe.model.insurance.policy;

import com.carplus.subscribe.model.insurance.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.List;

@Data
@Schema(description = "保單詳細資料")
@FieldNameConstants
public class PolicyDetailResponse {
    /** 保單識別碼 */
    @Schema(description = "保單識別碼")
    private String policyId;
    /** 保單號碼 */
    @Schema(description = "保單號碼")
    private String policyNumber;
    /** 保單類型 */
    @Schema(description = "保單類型")
    private Integer policyNew;
    /** 起保日 */
    @Schema(description = "起保日")
    private Instant policyStartDate;
    /** 迄保日 */
    @Schema(description = "迄保日")
    private Instant policyEndDate;
    /** 退保日 */
    @Schema(description = "退保日")
    private Instant stopPolicyDate;
    /** 保單狀態 */
    @Schema(description = "保單狀態")
    private String status;
    /** 保單狀態代碼 */
    @Schema(description = "保單狀態代碼")
    private String statusCode;
    /** 保險公司代碼 */
    @Schema(description = "保險公司代碼")
    private String policyCompanyId;
    /** 保險公司名稱 */
    @Schema(description = "保險公司名稱")
    private String policyCompanyName;
    /** 車籍公司別 */
    @Schema(description = "車籍公司別")
    private String carCompanyId;
    /** 車籍公司別名稱 */
    @Schema(description = "車籍公司別名稱")
    private String carCompanyName;
    /** 車輛狀態 */
    @Schema(description = "車輛狀態")
    private String licenseStatus;
    /** 車輛狀態說明 */
    @Schema(description = "車輛狀態說明")
    private String licenseStatusDesc;
    /** 險種異動中Flag */
    @Schema(description = "險種異動中Flag")
    private Boolean itemChangeFlag;
    /** 投保內容總覽 */
    @Schema(description = "投保內容總覽")
    private ApplyInfoView applyInfoView;
    /** 投保車型 & 車籍 */
    @Schema(description = "投保車型 & 車籍")
    private CarInfoView carInfoView;
    /** 建立來源資訊 */
    @Schema(description = "建立來源資訊")
    private DependencyInfoView depenceInfo;
    /** 投保單來源資訊 */
    @Schema(description = "投保單來源資訊")
    private DependencyInfoView requisitionDepenceInfo;
    /** 投保客戶資料 */
    @Schema(description = "投保客戶資料")
    private CustomerInfo customerInfo;
    /** 險種異動紀錄摘要 */
    @Schema(description = "險種異動紀錄摘要")
    private List<ItemChargeSummaryInfoView> itemChargeSummaryInfoView;
    /** 險種資訊 */
    @Schema(description = "險種資訊")
    private List<ItemSummaryInfo> itemSummaryInfo;
    /** 保單請款資訊 */
    @Schema(description = "保單請款資訊")
    private List<PolicyPaymentInfo> policyPaymentInfo;
    /** 新保總金額 */
    @Schema(description = "新保總金額")
    private Integer newTotalAmount;
    /** 續保總金額 */
    @Schema(description = "續保總金額")
    private Integer renewTotalAmount;
    /** 批加金額 */
    @Schema(description = "批加金額")
    private Integer addItemAmount;
    /** 批減金額 */
    @Schema(description = "批減金額")
    private Integer removeItemAmount;
    /** 退保金額 */
    @Schema(description = "退保金額")
    private Integer rejectAmount;
    /** 建檔人員 --> 盡量用 createUserInfo.createUserName */
    @Schema(description = "建檔人員 --> 盡量用 createUserInfo.createUserName")
    private String createUserId;
    /** 建檔人員姓名 --> 盡量用 createUserInfo.createUserName */
    @Schema(description = "建檔人員姓名 --> 盡量用 createUserInfo.createUserName")
    private String createUserName;
    /** 建檔終端機IP --> 盡量用 createUserInfo.createIP */
    @Schema(description = "建檔終端機IP --> 盡量用 createUserInfo.createIP")
    private String createIP;
    /** 建檔日期 --> 盡量用 createUserInfo.createDate */
    @Schema(description = "建檔日期 --> 盡量用 createUserInfo.createDate")
    private Instant createDate;
    /** 異動人員 --> 盡量用 updateUserInfo.userId */
    @Schema(description = "異動人員 --> 盡量用 updateUserInfo.userId")
    private String updateUserId;
    /** 異動人員姓名 --> 盡量用 updateUserInfo.userName */
    @Schema(description = "異動人員姓名 --> 盡量用 updateUserInfo.userName")
    private String updateUserName;
    /** 異動終端機IP --> 盡量用 updateUserInfo.updateIP */
    @Schema(description = "異動終端機IP --> 盡量用 updateUserInfo.updateIP")
    private String updateIP;
    /** 異動日期 --> 盡量用 updateUserInfo.updateDate */
    @Schema(description = "異動日期 --> 盡量用 updateUserInfo.updateDate")
    private Instant updateDate;
    /** 保單請款分攤單位 */
    @Schema(description = "保單請款分攤單位")
    private PaymentInfo paymentInfo;
    /** 保單原始單據別 */
    @Schema(description = "保單原始單據別")
    private String originalDocType;
    /** 保單原始單據別名稱 */
    @Schema(description = "保單原始單據別名稱")
    private String originalDocTypeName;
    /** 保單原始投保來源單據 */
    @Schema(description = "保單原始投保來源單據")
    private String originalDocNo;
    /** 續保前一保單號碼 */
    @Schema(description = "續保前一保單號碼")
    private String previousPolicyNumber;
    /** 建單人員資訊 */
    @Schema(description = "建單人員資訊")
    private UserInfo createUserInfo;
    /** 更新人員資訊 */
    @Schema(description = "更新人員資訊")
    private UserInfo updateUserInfo;
    /** 車型資訊 */
    @Schema(description = "車型資訊")
    private CarSpecSearchRep carSpecInfo;
    /** 牌照資訊 */
    @Schema(description = "牌照資訊")
    private CrsCarLicenseInfo carLicenseInfo;
}
