package com.carplus.subscribe.model.insurance;

import com.carplus.subscribe.enums.CompulsoryInsurance;
import com.carplus.subscribe.model.auth.AuthUser;
import lombok.Data;

import java.time.Instant;

@Data
public class SubscribeInsurance {
    /**
     * 預計領牌日
     */
    private Instant licenseExpDate;

    /**
     * 申請日期
     */
    private Instant startDate;

    /**
     * 要保人
     */
    private AuthUser authUser;

    /**
     * 車牌號碼
     */
    private String plateNo;

    /**
     * 建立來源別備註(訂閱契約備註)
     */
    private String depenceDocMemo;

    /**
     * 由來源轉入的行為說明,ex:訂閱車契約建立
     */
    private String depenceMemo;

    /**
     * 建立來源識別碼,撥車申請單或契約編號
     */
    private String depenceDocNo;

    /**
     * 建立來源單據別
     */
    private String depenceDocType;

    private CompulsoryInsurance compulsoryInsurance;


}
