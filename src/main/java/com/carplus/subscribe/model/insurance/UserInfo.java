package com.carplus.subscribe.model.insurance;

import com.carplus.subscribe.model.authority.MemberInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {
    /**
     * 使用者ID
     */
    private String userId;
    /**
     * 使用者ip
     */
    private String ip;
    /**
     * 時間
     */
    private Instant date;
    /**
     * 使用者資訊
     */
    private MemberInfo memberInfo;
}
