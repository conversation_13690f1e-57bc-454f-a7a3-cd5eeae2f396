package com.carplus.subscribe.model.insurance.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.List;

@Data
@FieldNameConstants
@Schema(description = "保單明細資訊")
public class PolicyListResponse {
    @Schema(description = "投保車輛編碼")
    private Integer carNo;
    @Schema(description = "投保車號")
    private String plateNo;
    @Schema(description = "保單識別碼")
    private String policyId;
    @Schema(description = "保單號碼")
    private String policyNumber;
    @Schema(description = "投保類型")
    private Integer policyNew;
    @Schema(description = "投保類型")
    private String policyNewDesc;
    @Schema(description = "起保日")
    private Instant policyStartDate;
    @Schema(description = "迄保日")
    private Instant policyEndDate;
    @Schema(description = "退保日")
    private Instant stopPolicyDate;
    @Schema(description = "保險公司代碼")
    private String policyCompanyId;
    @Schema(description = "保險公司名稱")
    private String policyCompanyName;
    @Schema(description = "保單類型(1.強制 2.任意)")
    private String policyType;
    @Schema(description = "保單類型(1.強制 2.任意)說明")
    private String policyTypeDesc;
    @Schema(description = "保單備註")
    private String memo;
    @Schema(description = "保單狀態")
    private String status;
    @Schema(description = "保單狀態代碼")
    private String statusCode;
    @Schema(description = "車籍公司別")
    private String carCompanyId;
    @Schema(description = "車籍公司別名稱")
    private String carCompanyName;
    @Schema(description = "牌照狀態")
    private String licenseStatus;
    @Schema(description = "車輛狀態說明")
    private String licenseStatusDesc;
    @Schema(description = "險種異動中Flag")
    private Boolean itemChangeFlag;
    @Schema(description = "是否可退保Flag")
    private Boolean isReject;
    @Schema(description = "險種資訊")
    private List<ItemSummaryInfo> itemSummaryInfo;
    @Schema(description = "險種異動紀錄摘要")
    private List<ItemChargeSummaryInfoView> itemChargeSummaryInfoView;
    @Schema(description = "建檔人員")
    private String createUserId;
    @Schema(description = "建檔人員姓名")
    private String createUserName;
    @Schema(description = "建檔終端機IP")
    private String createIP;
    @Schema(description = "建檔日期")
    private Instant createDate;
    @Schema(description = "異動人員")
    private String updateUserId;
    @Schema(description = "異動人員姓名")
    private String updateUserName;
    @Schema(description = "異動終端機IP")
    private String updateIP;
    @Schema(description = "異動日期")
    private Instant updateDate;
    @Schema(description = "crs目前最新車號")
    private String crsCarBasePlateNo;
    @Schema(description = "要保人id")
    private String proposerId;
    @Schema(description = "要保人名稱")
    private String proposerName;
    @Schema(description = "被保人id")
    private String insuredId;
    @Schema(description = "被保人貝名稱")
    private String insuredName;
    @Schema(description = "引擎號碼")
    private String engineNo;
    @Schema(description = "車身號碼")
    private String bodyNo;
    @Schema(description = "使用人id")
    private String insureUserId;
    @Schema(description = "使用人名稱")
    private String insureUserName;
    @Schema(description = "保單明細資訊")
    private List<PolicyPaymentInfo> policyPaymentInfo;
}
