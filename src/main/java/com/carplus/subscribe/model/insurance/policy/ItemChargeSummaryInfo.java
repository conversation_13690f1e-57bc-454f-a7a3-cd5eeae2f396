package com.carplus.subscribe.model.insurance.policy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@Data
@FieldNameConstants
public class ItemChargeSummaryInfo {
    @Schema(description = "保單號碼")
    private String policyId;
    @Schema(description = "險種紀錄編號")
    private Long itemChargeAuto;
    @Schema(description = "險種代碼")
    private String itemId;
    @Schema(description = "險種名稱")
    private String name;
    @Schema(description = "異動類型")
    private Integer chargeType;
    @Schema(description = "異動生效日")
    private Instant takeEffectDate;
    @Schema(description = "保額")
    private Integer coverage;
    @Schema(description = "狀態")
    private Integer status;
    @Schema(description = "異動說明類型說明")
    private String changeDescTypeDesc;
    @Schema(description = "異動說明類型")
    private String changeDescType;
    @Schema(description = "異動說明")
    private String changeDesc;
    @Schema(description = "簽單金額")
    private Integer amount;
    @Schema(description = "應付保額")
    private Integer changePremium;
    @Schema(description = "請款批號")
    private String paymentBatchId;
    @Schema(description = "保額參數1")
    private Long parameter1Code;
    @Schema(description = "保額參數2")
    private Long parameter2Code;
    @Schema(description = "保額參數3")
    private Long parameter3Code;
    @Schema(description = "保險公司保險說明 (文字帶入保額、參數1、參數2、參數3)")
    private String coverageDesc;
    @Schema(description = "保險公司保險簡易說明 (文字帶入保額、參數1、參數2、參數3)")
    private String coverageExpression;
    @Schema(description = "保險公司保險簡易說明(for 舊系統資料)")
    private String coverageExpressionForTemp;
    @Schema(description = "是否有效險種")
    private Boolean isValid;
    @Schema(description = "建檔人員")
    private String createUserId;
    @Schema(description = "建檔人員姓名")
    private String createUserName;
    @Schema(description = "建檔終端機IP")
    private String createIP;
    @Schema(description = "異動申請日")
    private Instant createDate;
    @Schema(description = "異動人員")
    private String updateUserId;
    @Schema(description = "異動人員姓名")
    private String updateUserName;
    @Schema(description = "異動終端機IP")
    private String updateIP;
    @Schema(description = "異動日期")
    private Instant updateDate;
}
