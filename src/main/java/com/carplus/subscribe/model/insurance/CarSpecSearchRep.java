package com.carplus.subscribe.model.insurance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.sql.Timestamp;

@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
@Builder
@Data
@Schema(description = "查詢車型資料結果")
public class CarSpecSearchRep {

    /**
     * 廠牌代碼
     */
    @Schema(description = "廠牌代碼")
    private String brandCode;
    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String classCode;
    /**
     * 牌價代碼
     */
    @Schema(description = "牌價代碼")
    private String stdPriceCode;
    /**
     * 代碼4
     */
    @Schema(description = "代碼4")
    private String code4;
    /**
     * 車型明細名稱
     */
    @Schema(description = "車型明細名稱")
    private String classDetail;
    /**
     * 牌價
     */
    @Schema(description = "牌價")
    private String stdPrice;
    /**
     * 格上車種代碼
     */
    @Schema(description = "格上車種代碼")
    private String carPlusCarType;
    /**
     * 車種代碼(新安)
     */
    @Schema(description = "車種代碼(新安)")
    private String carType;
    /**
     * 車損係數代碼
     */
    @Schema(description = "車損係數代碼")
    private String cdRateCode;
    /**
     * 竊盜係數代碼
     */
    @Schema(description = "竊盜係數代碼")
    private String clRateCode;
    /**
     * 車型使用狀態代碼
     */
    @Schema(description = "車型使用狀態代碼")
    private String useStatus;
    /**
     * 是否為跑車
     */
    @Schema(description = "是否為跑車")
    private String sportsCar;
    /**
     * 年份
     */
    @Schema(description = "年份")
    private String makeYear;
    /**
     * 排氣量
     */
    @Schema(description = "排氣量")
    private Integer cylinder;
    /**
     * 格上能源別代碼
     */
    @Schema(description = "格上能源別代碼")
    private String carPlusEnergyCode;
    /**
     * 車系代碼
     */
    @Schema(description = "車系代碼")
    private String carSeriesCode;
    /**
     * 座位數
     */
    @Schema(description = "座位數")
    private Integer seats;
    /**
     * 車型輪胎數
     */
    @Schema(description = "車型輪胎數")
    private String carTires;
    /**
     * 官方更新日期
     */
    @Schema(description = "官方更新日期")
    private Timestamp officialDate;
    /**
     * 正式廠牌代碼
     */
    @Schema(description = "正式廠牌代碼")
    private String correctBrandCode;
    /**
     * 正式車型代碼
     */
    @Schema(description = "正式車型代碼")
    private String correctClassCode;
    /**
     * 正式車型標準售價代碼
     */
    @Schema(description = "正式車型標準售價代碼")
    private String correctStdPriceCode;
    /**
     * 正式Code4
     */
    @Schema(description = "正式Code4")
    private String correctCode4;
    /**
     * 車型轉置日期
     */
    @Schema(description = "車型轉置日期")
    private Timestamp changeDate;
    /**
     * 車型轉置程式
     */
    @Schema(description = "車型轉置程式")
    private String changeUserId;
    /**
     * 最後一次領牌日
     */
    @Schema(description = "最後一次領牌日")
    private Timestamp lastLicensedDate;
    /**
     * 格上廠牌代碼
     */
    @Schema(description = "格上廠牌代碼")
    private String carPlusBrandCode;
    /**
     * 格上車型代碼
     */
    @Schema(description = "格上車型代碼")
    private String carPlusClassCode;
    /**
     * 格上車系代碼
     */
    @Schema(description = "格上車系代碼")
    private String carPlusCarSeriesCode;
    /**
     * 車型資料來源
     */
    @Schema(description = "車型資料來源")
    private String sourceType;
    /**
     * CarForSync 流水號
     */
    @Schema(description = "CarForSync 流水號")
    private Integer carForSyncID;

    /**
     * 殘值係數是否屏蔽
     */
    @Schema(description = "殘值係數是否屏蔽")
    private String isResidualHide;

    /**
     * 天書車型代碼
     */
    @Schema(description = "天書車型代碼")
    private String skyBookCarCode;

    /**
     * 是否為當月統購車型
     */
    @Schema(description = "是否為當月統購車型")
    private Boolean isBulkBuy;

    /**
     * 建檔日期
     */
    @Schema(description = "建檔日期")
    private Timestamp createDate;

    /**
     * 建檔人員
     */
    @Schema(description = "建檔人員")
    private String createUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Timestamp updateDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 廠牌名稱
     */
    @Schema(description = "廠牌名稱")
    private String brandName;

    /**
     * 車型名稱
     */
    @Schema(description = "車型名稱")
    private String className;

    /**
     * 格上能源別名稱
     */
    @Schema(description = "格上能源別名稱")
    private String carPlusEnergyName;

    /**
     * 車型使用狀態名稱
     */
    @Schema(description = "車型使用狀態名稱")
    private String useStatusName;

    /**
     * 資料來源名稱
     */
    @Schema(description = "資料來源名稱")
    private String sourceName;

    /**
     * 車型代碼(8/9碼)
     */
    @Schema(description = "車型代碼(8/9碼)")
    private String carCode;

    /**
     * 轉換後車型資料
     */
    @Schema(description = "轉換後車型資料")
    private CarSpecSearchRep correctCarSpecs;

    /**
     * 建檔人員名稱
     */
    @Schema(description = "建檔人員名稱")
    private String createUserName;

    /**
     * 異動人員名稱
     */
    @Schema(description = "異動人員名稱")
    private String updateUserName;

    /**
     * 格上廠牌名稱
     */
    @Schema(description = "格上廠牌名稱")
    private String carplusBrandName;

    /**
     * 格上車型名稱
     */
    @Schema(description = "格上車型名稱")
    private String carplusClassName;

    /**
     * 格上車種別名稱
     */
    @Schema(description = "格上車種別名稱")
    private String carPlusCarTypeName;

    /**
     * 格上車系名稱
     */
    @Schema(description = "格上車系名稱")
    private String carPlusCarSeriesCodeName;
}
