package com.carplus.subscribe.model.insurance.policy;

import com.carplus.subscribe.model.insurance.PaymentInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "保單請款資訊")
public class PolicyPaymentInfo extends PaymentInfo {

    @Schema(description = "請款批號")
    private String batchID;

    @Schema(description = "請款日")
    private Instant payDate;

    @Schema(description = "付款對象-付款方式(1.匯款 2.期票)")
    private Integer payType;

    @Schema(description = "付款對象-付款方式說明")
    private String payTypeDesc;

    @Schema(description = "付款對象-保險公司")
    private String payCompanyId;

    @Schema(description = "付款對象-保險公司說明")
    private String payCompanyName;

    @Schema(description = "簽呈主旨")
    private String subject;

    @Schema(description = "請款狀態(-10. 取消請款 0.請款中 10.請款完成)")
    private Integer paymentStatus;

    @Schema(description = "請款狀態說明")
    private String paymentStatusDesc;

    @Schema(description = "異動類別(0.新保 1. 續保 2.退保 3.批加 4.批減)")
    private String chargeType;

    @Schema(description = "異動類別說明")
    private String chargeTypeDesc;

    @Schema(description = "其他說明")
    private String memo;

    @Schema(description = "作廢原因")
    private String cancelReason;

    @Schema(description = "建檔人員")
    private String createUserId;

    @Schema(description = "建檔人員姓名")
    private String createUserIdName;

    @Schema(description = "建檔終端機IP")
    private String createIP;

    @Schema(description = "建檔日期")
    private Instant createDate;

    @Schema(description = "異動人員")
    private String updateUserId;

    @Schema(description = "異動人員姓名")
    private String updateUserIdName;

    @Schema(description = "異動終端機IP")
    private String updateIP;

    @Schema(description = "異動日期")
    private Instant updateDate;

}
