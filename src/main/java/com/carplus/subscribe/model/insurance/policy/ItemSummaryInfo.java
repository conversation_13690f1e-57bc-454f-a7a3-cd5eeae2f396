package com.carplus.subscribe.model.insurance.policy;

import com.carplus.subscribe.model.insurance.ItemInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Data
public class ItemSummaryInfo extends ItemInfo {

    @Schema(description = "是否有效險種")
    private Boolean isValid;

    @Schema(description = "保險單號")
    private String policyId;

    @Schema(description = "起保日")
    private Instant itemStartDate;

    @Schema(description = "迄保日")
    private Instant itemEndDate;

    @Schema(description = "險種紀錄編號")
    private Long itemChargeAuto;

}
