package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "出車")
public class CarMoveDTO {

    /**
     * 撥、調車明細流水號
     */
    private Integer buChangeDetailId;

    /**
     * 撥、調車申請單單號
     */
    private Integer buChangeMasterId;

    /**
     * 出車人員
     */
    private String outUser;

    /**
     * 出車里程
     */
    private Integer outKM;

    /**
     * 取車人員
     */
    private String outGetUser;

    /**
     * 出車日期
     */
    private Timestamp outDate;

    /**
     * 出車備註
     */
    private String outMemo;

    /**
     * 出/收車取消原因，出車：leave-unchange-type、收車：receive-unchange-type
     */
    private String cancelType;

    /**
     * 出/收車取消說明
     */
    private String cancelReason;

    /**
     * 異動人員
     */
    private String userId;

    /**
     * 出收車狀態 (0未出車 5取消出車 10已出車 30 已收車)
     */
    private String buChangeDetailStatus;

    /**
     * 出車車號
     */
    private String plateNo;

    /**
     * 交車人員
     */
    private String receiveGiveUser;

    /**
     * 收車人員
     */
    private String receiveUser;

    /**
     * 收車日期
     */
    private Timestamp receiveDate;

    /**
     * 收車里程
     */
    private Integer receiveKM;

    /**
     * 收車備註
     */
    private String receiveMemo;

    /**
     * 撥入區域(組織代碼)
     */
    private String targetArea;

    /**
     * 撥入區域(各系統自定義代碼)
     */
    private String targetRealArea;

    /**
     * 撥入區域名稱 (各系統自定義)
     */
    private String targetAreaName;

    /**
     * 撥入停放點(組織代碼)
     */
    private String targetLocation;

    /**
     * 撥入停放點(各系統自定義代碼)
     */
    private String targetRealLocation;

    /**
     * 撥入停放點名稱(子系統)
     */
    private String targetLocationName;

    /**
     * 是否派拖，0 - 非派拖(預設)、1-派拖，未來要儲存派拖單編號
     */
    @Schema(description = "是否派拖，0 - 非派拖(預設)、1-派拖，未來要儲存派拖單編號")
    @Builder.Default
    private Integer entrustNo = 0;


}