package com.carplus.subscribe.model.crs;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.exception.SubscribeException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.sql.Date;
import java.time.Instant;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CRS_PURCHASE_CAR_CONTRACT_END_DATE_INVALIDATE;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CRS_PURCHASE_CAR_INVALIDATE;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Schema(description = "查詢營業用車採購-專案車相關資訊結果")
public class PurchaseProjectCarSearchResponse {
    /**
     * 引擎編號
     */
    @Schema(description = "引擎編號")
    private String engineNo;

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 殘值
     */
    @Schema(description = "殘值")
    private Integer residualValue;

    /**
     * 契約結束日 yyyy/MM/dd
     */
    @Schema(description = "契約結束日 yyyy/MM/dd")
    private String contractEndDate;

    /**
     * 是否專案車
     */
    @Schema(description = "是否專案車")
    private Boolean isProjectCar;

    public void validate() {
        if (residualValue == null || contractEndDate == null) {
            throw new SubscribeException(CRS_PURCHASE_CAR_INVALIDATE);
        }
    }


    public void validate(Instant contractExpectEndDate) {
        validate();
        if (DateUtils.toDateString(Date.from(contractExpectEndDate), "yyyy/MM/dd").compareTo(contractEndDate) > 0) {
            throw new SubscribeException(CRS_PURCHASE_CAR_CONTRACT_END_DATE_INVALIDATE);
        }
    }
}
