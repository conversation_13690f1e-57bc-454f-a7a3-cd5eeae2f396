package com.carplus.subscribe.model.crs;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface CarBaseEnum {

    /**
     * 營運狀態異動事由 serviceCode=car_base-bustatus
     */
    @Getter
    @AllArgsConstructor
    enum BuStatus {

        /**
         * 1 營運
         */
        A("1", "營運"),
        /**
         * 2 整備
         */
        B("2", "整備"),
        //        C("3","庫存"),
//        D("4","出售中"),
        /**
         * 5 已除帳
         */
        E("5", "已除帳"),
        //        F("6","拍賣中"),
        /**
         * 7 債管件
         */
        G("7", "債管件"),
//        H(8,"遺失"),
        /**
         * 9 新增車輛
         */
        newCar("9", "新增車輛"),
        ;

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(BuStatus::getCode, BuStatus::getName));

        private static final Map<String, BuStatus> CarBaseStausMap = Arrays.stream(values()).collect(Collectors.toMap(BuStatus::getCode, Function.identity()));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }

        @Nullable
        public static BuStatus ofEnum(@Nullable String code) {
            return CarBaseStausMap.get(code);
        }
    }

    /**
     * 車輛狀態 serviceCode=car_base-carstatus
     */
    @Getter
    @AllArgsConstructor
    enum CarStatus {
        /**
         * 0,購入
         */
        newCar("0", "購入"),
        /**
         * 4,報廢
         */
        scrapped("4", "報廢"),
        /**
         * 5,出售
         */
        sale("5", "出售"),
        /**
         * E,購入退回
         */
        cancelSetting("E", "取消設定"),
        /**
         * F,購入退回
         */
        purchaseReturn("F", "購入退回"),
        ;
        private String code;
        private String name;

        private static final Map<String, CarStatus> carStatusMap = Arrays.stream(values()).collect(Collectors.toMap(CarStatus::getCode, Function.identity()));

        @Nullable
        public static CarStatus ofEnum(@Nullable String code) {
            return carStatusMap.get(code);
        }
    }

    /**
     * 異動憑證類型 serviceCode=car_base_log-depencedoctype
     * car_base_log & car_license_log 共用
     */
    @Getter
    @AllArgsConstructor
    enum LogDepencedoctype {
        /**
         * 1 契約編號 da21.da_no
         */
        contractNo("1", "契約編號"),

        /**
         * 2 契約編號(短租)
         */
        contractNoOfSRental("2", "契約編號(短租)"),

        /**
         * 3 契約編號(S2G)
         */
        contractNoOfS2G("3", "契約編號(S2G)"),

        /**
         * 4 車輛整備單
         **/
        remediationNo("4", "車輛整備單"),

        /**
         * 5 撥車申請單單號
         **/
        assignNo("5", "撥車申請單單號"),

        /**
         * 6 車輛交修單
         **/
        repairNo("6", "交修單單號"),

        /**
         * 7 公務車出車編號
         **/
        officalCar("7", "公務車出車編號"),

        /**
         * 8 還車確認表序號
         **/
        issueCarNo("8", "還車分配序號"),

        /**
         * 9 保險中台系統-要保單號
         */
        insurance("9", "保險中台系統-要保單號"),

        /**
         * 10 系統排程
         */
        cronJob("10", "系統排程"),
        ;
        private String code;
        private String name;

        private static final Map<String, LogDepencedoctype> map = Arrays.stream(LogDepencedoctype.values())
            .collect(Collectors.toMap(LogDepencedoctype::getCode, Function.identity()));

        public static LogDepencedoctype of(String code) {
            return map.getOrDefault(code, null);
        }
    }

    /**
     * 租賃方式
     */
    @Getter
    @AllArgsConstructor
    enum DaTrid {
        // 租賃方式::1.租購 2.純租(含短租、共享) 3.融資  4.其他(含訂閱、中古、調度) 5.不詳(舊系統無契約) ' AFTER `py_auto`,
        DisuseOld("1", "租購"),
        ReturnCar("2", "純租"),
        Financing("3", "融資"),
        Other("4", "其他"),
        Unknown("5", "不詳");

        private String code;
        private String name;
    }
}
