package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

@Data
@FieldNameConstants
@NoArgsConstructor
@AllArgsConstructor
public class CarBase {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 所屬公司統編
     */
    @Schema(description = "所屬公司統編")
    private String companyNo;

    /**
     * 引擎號碼
     */
    @Schema(description = "引擎號碼")
    private String engineNo;

    /**
     * 車身號碼
     */
    @Schema(description = "車身號碼")
    private String bodyNo;

    /**
     * 出廠年月
     */
    @Schema(description = "出廠年月")
    private String publishDate;

    /**
     * 顏色
     */
    @Schema(description = "顏色")
    private String color;

    /**
     * 廠牌代碼
     */
    @Schema(description = "廠牌代碼")
    private String brandCode;

    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String classCode;

    /**
     * 車型明細代碼
     */
    @Schema(description = "車型明細代碼")
    private String stdPriceCode;

    /**
     * 取得日期
     */
    @Schema(description = "取得日期")
    private Date getDate;

    /**
     * 代碼4
     */
    @Schema(description = "代碼4")
    private String code4;

    /**
     * 牌價(入籍當時牌價)
     */
    @Schema(description = "牌價(入籍當時牌價)")
    private Integer stdPrice;

    /**
     * 取得價
     */
    @Schema(description = "取得價")
    private Integer getPrice;

    /**
     * 車輛來源
     */
    @Schema(description = "車輛來源")
    private String carSource;

    /**
     * 購入時是否為新車
     */
    @Schema(description = "購入時是否為新車")
    private String newCar;

    /**
     * 是否水貨車(新增時使用)
     */
    @Schema(description = "是否水貨車(新增時使用)")
    private Boolean isDemoCar;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    private String plateNo;

    /**
     * 庫位代碼
     */
    @Schema(description = "庫位代碼")
    private Integer buId;

    /**
     * 營運狀態代碼
     */
    @Schema(description = "營運狀態代碼")
    private String buStatus;

    /**
     * 目前里程
     */
    @Schema(description = "目前里程")
    private Integer km;

    /**
     * 出售日期
     */
    @Schema(description = "出售日期")
    private Date saleDate;

    /**
     * 保有退出日期
     */
    @Schema(description = "保有退出日期")
    private Date quitDate;

    /**
     * 資產編號(.net系統用)
     */
    @Schema(description = "資產編號(.net系統用)")
    private Integer pyAuto;

    /**
     * BuStatusLog.buStatusLogId 庫位與營運狀態異動記錄編號
     */
    @Schema(description = "BuStatusLog.buStatusLogId 庫位與營運狀態異動記錄編號")
    private Integer buStatusLogId;

    /**
     * 車輛狀態代碼 service_code=car_base-carstatus
     */
    @Schema(description = "車輛狀態代碼 service_code=car_base-carstatus")
    private String carStatus;

    /**
     * 是否開放撥車(true 開放/ false 不開放)
     */
    @Schema(description = "是否開放撥車(true 開放/ false 不開放)")
    private Boolean isOpen;

    /**
     * 車體等級，serviceCode = car_base-bodylevel
     */
    @Schema(description = "車體等級，serviceCode = car_base-bodylevel")
    private String bodyLevel;

    /**
     * 非車輛所屬BU接收車籍異動que(單一service)
     */
    @Schema(description = "非車輛所屬BU接收車籍異動que(單一service)")
    private String carBaseModifyQue;

    private String createUserId;

    /**
     * 新增日期
     * 因為需要儲存car_base_log，因此createDate由程式set系統時間，
     * 而非由DB自動塞入系統時間，所以不設定 insertable = false
     */
    @Schema(description = "新增日期")
    private Date createDate;
    private String updateUserId;

    /**
     * 異動日期
     * 因為需要儲存car_base_log，因此updateDate由程式set系統時間，
     * 而非由DB自動塞入系統時間，所以不設定 insertable = false,updatable = false
     */
    @Schema(description = "異動日期")
    private Date updateDate;


    /**
     * 車輛狀態名稱
     */
    @Schema(description = "車輛狀態名稱")
    private String carStatusName;


    /**
     * 營運狀態名稱
     */
    @Schema(description = "營運狀態名稱")
    private String buStatusName;


    public String getCarCode() {
        return brandCode + classCode + stdPriceCode + code4;
    }

}

