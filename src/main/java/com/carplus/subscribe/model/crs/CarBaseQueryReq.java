package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "車輛基本資料查詢")
public class CarBaseQueryReq {

//    /**
//     * 車身號碼
//     */
//    @Schema(description = "車身號碼")
//    private String bodyNo;
//
//    /**
//     * 車身號碼列表
//     */
//    @Schema(description = "車身號碼列表")
//    private List<String> bodyNoList;
//
//    /**
//     * 廠牌代碼
//     */
//    @Schema(description = "廠牌代碼")
//    private String brandCode;
//
//    /**
//     * 庫位代碼
//     */
//    @Schema(description = "庫位代碼")
//    private Integer buId;
//
//    /**
//     * 庫位狀態
//     */
//    @Schema(description = "庫位狀態")
//    private String buStatus;
//
//    /**
//     * 營運狀態 service_code=car_base-buStatus
//     */
//    @Schema(description = "營運狀態 service_code=car_base-buStatus")
//    private List<String> buStatusList;
//
//    /**
//     * 車輛編號
//     */
//    @Schema(description = "車輛編號")
//    private Integer carNo;

    /**
     * 車輛編號列表
     */
    @Schema(description = "車輛編號列表")
    private List<Integer> carNoList;

//    /**
//     * 購買類型
//     */
//    @Schema(description = "購買類型")
//    private String carSource;
//
//    /**
//     * 車型代碼
//     */
//    @Schema(description = "車型代碼")
//    private String classCode;
//
//    /**
//     * 車型代碼4
//     */
//    @Schema(description = "車型代碼4")
//    private String code4;
//
//    /**
//     * 公司序號
//     */
//    @Schema(description = "公司序號")
//    private String companyNo;
//
//    /**
//     * 引擎編號
//     */
//    @Schema(description = "引擎編號")
//    private String engineNo;
//
//    /**
//     * 引擎編號列表
//     */
//    @Schema(description = "引擎編號列表")
//    private List<String> engineNoList;
//
//    /**
//     * 車牌號碼
//     */
//    @Schema(description = "車牌號碼")
//    private String plateNo;

    /**
     * 車牌號碼列表
     */
    @Schema(description = "車牌號碼列表")
    private List<String> plateNoList;

//    /**
//     * 資產編號
//     */
//    @Schema(description = "資產編號")
//    private Integer pyAuto;
//
//    /**
//     * 標準售價代碼
//     */
//    @Schema(description = "標準售價代碼")
//    private String stdPriceCode;
//
//    /**
//     * 更新日期結束
//     */
//    @Schema(description = "更新日期結束")
//    private Instant updateDateEnd;
//
//    /**
//     * 更新日期開始
//     */
//    @Schema(description = "更新日期開始")
//    private Instant updateDateStart;
}

