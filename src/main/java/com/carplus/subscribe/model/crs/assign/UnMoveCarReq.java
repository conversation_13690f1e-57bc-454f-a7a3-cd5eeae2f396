package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "取消出、收車2")
public class UnMoveCarReq {
    @Schema(description = "撥、調車明細流水號", required = true)
    @NotNull(message = "撥、調車明細流水號，不可為空")
    private Integer buChangeDetailId;

    @Schema(description = "撥、調車申請單單號", required = true)
    private Integer buChangeMasterId;

    @Schema(description = "出/收車取消原因，出車：leave-unchange-type、收車：receive-unchange-type", example = "1", required = true)
    private String cancelType;

    @Schema(description = "出/收車取消說明", required = true)
    private String cancelReason;

    @Schema(description = "異動人員", required = true)
    private String userId;
}
