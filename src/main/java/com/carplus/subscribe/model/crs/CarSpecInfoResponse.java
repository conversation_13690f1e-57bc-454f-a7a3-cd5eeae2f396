package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "車型資料Response")
public class CarSpecInfoResponse {

    /**
     * 廠牌代碼
     */
    @Schema(description = "廠牌代碼")
    private String brandCode;

    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String classCode;

    /**
     * 標準售價代碼
     */
    @Schema(description = "標準售價代碼")
    private String stdPriceCode;

    /**
     * 代碼4
     */
    @Schema(description = "代碼4")
    private String code4;

    /**
     * 車種代碼
     */
    @Schema(description = "車種代碼")
    private String carType;

    /**
     * 廠牌名稱
     */
    @Schema(description = "廠牌名稱")
    private String brandName;

    /**
     * 車型名稱
     */
    @Schema(description = "車型名稱")
    private String className;

    /**
     * 車型明細
     */
    @Schema(description = "車型明細")
    private String classDetail;

    /**
     * 牌價
     */
    @Schema(description = "牌價")
    private String stdPrice;

    /**
     * 格上能源別代碼
     */
    @Schema(description = "格上能源別代碼")
    private Integer carPlusEnergyCode;

    /**
     * 格上能源別名稱
     */
    @Schema(description = "格上能源別名稱")
    private String carPlusEnergyName;

    /**
     * 排氣量
     */
    @Schema(description = "排氣量")
    private Integer cylinder;

    /**
     * 座位數
     */
    @Schema(description = "座位數")
    private Integer seats;

    /**
     * 車型使用狀態
     */
    @Schema(description = "車型使用狀態")
    private String useStatusName;

    /**
     * 車損係數代碼
     */
    @Schema(description = "車損係數代碼")
    private String cdRateCode;

    /**
     * 竊盜係數代碼
     */
    @Schema(description = "竊盜係數代碼")
    private String clRateCode;

    /**
     * 資料來源名稱
     */
    @Schema(description = "資料來源名稱")
    private String sourceName;

    /**
     * 車型完整代碼
     */
    @Schema(description = "車型完整代碼")
    private String carCode;

    /**
     * 正式廠牌代碼
     */
    @Schema(description = "正式廠牌代碼")
    private String correctBrandCode;

    /**
     * 正式車型代碼
     */
    @Schema(description = "正式車型代碼")
    private String correctClassCode;

    /**
     * 正式車型標準售價代碼
     */
    @Schema(description = "正式車型標準售價代碼")
    private String correctStdPriceCode;

    /**
     * 正式Code4
     */
    @Schema(description = "正式Code4")
    private String correctCode4;

    /**
     * 建檔人員
     */
    @Schema(description = "建檔人員")
    private String createUserId;

    /**
     * 建檔時間
     */
    @Schema(description = "建檔時間")
    private Date createDate;

    /**
     * 更新人員
     */
    @Schema(description = "更新人員")
    private String updateUserId;

    /**
     * 更新時間
     */
    @Schema(description = "更新時間")
    private Date updateDate;

    /**
     * 出廠年份
     */
    @Schema(description = "出廠年份")
    private String makeYear;

    /**
     * 格上廠牌代碼
     */
    @Schema(description = "格上廠牌代碼")
    private String carPlusBrandCode;

    /**
     * 格上車型代碼
     */
    @Schema(description = "格上車型代碼")
    private String carPlusClassCode;

    /**
     * 格上廠牌名稱
     */
    @Schema(description = "格上廠牌名稱")
    private String carplusBrandName;

    /**
     * 格上車型名稱
     */
    @Schema(description = "格上車型名稱")
    private String carplusClassName;

    /**
     * 格上車種代碼
     */
    @Schema(description = "格上車種代碼")
    private String carPlusCarType;

    /**
     * 格上車種名稱
     */
    @Schema(description = "格上車種名稱")
    private String carPlusCarTypeName;

    /**
     * 格上車系代碼
     */
    @Schema(description = "格上車系代碼")
    private String carPlusCarSeriesCode;

    /**
     * 格上車系名稱
     */
    @Schema(description = "格上車系名稱")
    private String carPlusCarSeriesCodeName;

}
