package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsOfAssignReq {

    @Schema(description = "車種代碼 serviceCode =bu_change_insurance-cartype", required = true, example = "01")
    @NotNull(message = "車種代碼，不可為空")
    private String carType;

    @Schema(description = "保險類型 serviceCode =bu_change_insurance-insurancekind", required = true, example = "01")
    @NotNull(message = "保險類型，不可為空")
    private String insuranceKind;

    @Schema(description = "保險險種 serviceCode = bu_change_insurance-insurancetype", example = "01")
    private String insuranceType;

    @Schema(description = "保險內容", example = "乙式 自付額1萬")
    private String insuranceMemo;

}
