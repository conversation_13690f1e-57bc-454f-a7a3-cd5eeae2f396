package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignDetailReq {
    /**
     * 使用車號-車輛唯一碼(car_base.carNo)
     */
    @Schema(description = "使用車號-車輛唯一碼(car_base.carNo)", example = "28")
    @NotNull(message = "使用車號-車輛唯一碼，不可為空")
    private Integer carNo;
    /**
     * 使用車號(car_base.plateNo)
     */
    @Schema(description = "使用車號(car_base.plateNo)", example = "RDK-7873")
    @NotNull(message = "使用車號，不可為空")
    private String plateNo;
    /**
     * 撥入庫位(bu.id)
     */
    @Schema(description = "撥入庫位(bu.id)", required = true, example = "3")
    @NotNull(message = "撥入庫位，不可為空")
    private Integer targetBu;
    /**
     * 撥入區域(組織代碼)
     */
    @Schema(description = "撥入區域(組織代碼)", example = "C02000")
    private String targetArea;
    /**
     * 撥入區域(各系統自定義代碼)
     */
    @Schema(description = "撥入區域(各系統自定義代碼)", example = "C50002")
    private String targetRealArea;
    /**
     * 撥入區域名稱 (各系統自定義)
     */
    @Schema(description = "撥入區域名稱 (各系統自定義)", example = "共享：台北--乙")
    private String targetAreaName;
    /**
     * 撥入停放點(組織代碼)
     */
    @Schema(description = "撥入停放點(組織代碼)", example = "C02000")
    private String targetLocation;
    /**
     * 撥入停放點(各系統自定義代碼)
     */
    @Schema(description = "撥入停放點(各系統自定義代碼)", example = "C10064")
    private String targetRealLocation;
    /**
     * 撥入停放點名稱(子系統)
     */
    @Schema(description = "撥入停放點名稱(子系統)", example = "捷運永寧站")
    private String targetLocationName;
    /**
     * 撥出庫位(bu.id)
     */
    @Schema(description = "撥出庫位(bu.id)", required = true, example = "3")
    @NotNull(message = "出車站點-庫位，不可為空")
    private Integer fromBu;
    /**
     * 撥出區域(組織代碼)
     */
    @Schema(description = "撥出區域(組織代碼)", example = "C02000")
    private String fromArea;
    /**
     * 撥出區域(各系統自定義代碼)
     */
    @Schema(description = "撥出區域(各系統自定義代碼)", example = "C50002")
    private String fromRealArea;
    /**
     * 撥出區域名稱 (各系統自定義代碼)
     */
    @Schema(description = "撥出區域名稱 (各系統自定義代碼)", example = "共享：台北--乙")
    private String fromAreaName;
    /**
     * 撥出停放點(組織代碼)
     */
    @Schema(description = "撥出停放點(組織代碼)", example = "C02000")
    private String fromLocation;
    /**
     * 撥出停放點(各系統自定義代碼)
     */
    @Schema(description = "撥出停放點(各系統自定義代碼)", example = "C10064")
    private String fromRealLocation;
    /**
     * 撥出停放點名稱(各系統自定義代碼)
     */
    @Schema(description = "撥出停放點名稱(各系統自定義代碼)", example = "捷運永寧站")
    private String fromLocationName;
    /**
     * 出車備註
     */
    @Schema(description = "出車備註", example = "本車為還車分派車輛，整備完成後自動建立出車記錄")
    private String leaveMemo;
    /**
     * 收車備註
     */
    @Schema(description = "收車備註", example = "本車為還車分派車輛，整備完成後自動建立收車記錄")
    private String receiveMemo;
    /**
     * 保險方案代碼
     */
    @Schema(description = "保險方案代碼", example = "7")
    private String insurancePlanNo;

}
