package com.carplus.subscribe.model.crs.assign;

import com.carplus.subscribe.enums.BuChangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignAddReq {

    /**
     * 申請事由(撥車):serviceCode=bu-change-changetype-assign
     */
    @Schema(description = "申請事由(撥車):serviceCode=bu-change-changetype-assign", required = true, example = "CHANGE")
    @NotNull(message = "申請事由，不可為空")
    private BuChangeEnum.ChangeTypeOfAssign changeType;

    /**
     * 新增人員所屬公司代碼
     */
    @Schema(description = "新增人員所屬公司代碼", required = true)
    @NotEmpty(message = "新增人員所屬公司代碼，不可為空")
    private String companyCode;

    /**
     * 新增人員
     */
    @Schema(description = "新增人員", required = true)
    @NotNull(message = "新增人員，不可為空")
    private String userId;

    /**
     * 預估使用日期-起日
     */
    @Schema(description = "預估使用日期-起日", required = true, example = "1651248000000")
    @NotNull(message = "預估使用日期-起日，不可為空")
    private Instant preUseDateStart;

    /**
     * 預估使用日期-迄日
     */
    @Schema(description = "預估使用日期-迄日", required = true, example = "1651248000000")
    @NotNull(message = "預估使用日期-迄日，不可為空")
    private Instant preUseDateEnd;

    /**
     * 文件上傳fileId
     */
    @Schema(description = "文件上傳fileId")
    private List<String> fileIdList;

    /**
     * 申請說明
     */
    @Schema(description = "申請說明", example = "撥車至...")
    private String memo;

    /**
     * 原調車申請單單號 for 撥車-營業用還車
     */
    @Schema(description = "原調車申請單單號 for 撥車-營業用還車")
    private String documentNo;

    /**
     * 撥出車輛資訊
     */
    @Schema(description = "撥出車輛資訊", required = true)
    @NotNull(message = "撥出車輛資訊，不可為空")
    @Valid
    private List<AssignDetailReq> assignDetailList;

    /**
     * 保險資訊
     */
    @Schema(description = "保險資訊")
    @Valid
    private List<InsOfAssignReq> insuranceList;
    /**
     * 預計領牌日
     */
    @Schema(description = "預計領牌日")
    private Instant licenseExpDate;

}

