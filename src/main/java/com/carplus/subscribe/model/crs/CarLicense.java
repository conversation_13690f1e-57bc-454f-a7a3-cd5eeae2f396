package com.carplus.subscribe.model.crs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CarLicense extends CarImport {
    /**
     * 車號
     */
    private String plateNo;
    /**
     * 領牌日
     */
    private Date licenseDate;
    /**
     * 行照車型
     */
    @JsonProperty("licenseTypeName")
    private String licenseCarType;
    /**
     * 行照有效日期
     */
    private Date licenseValidDate;
    /**
     * 採購單流水號
     */
    private Integer itemNo;

    private String licenseStatus;



}

