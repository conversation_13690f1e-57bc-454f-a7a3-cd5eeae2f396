package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查詢撥、調車申請單結果")
public class BUChangeSearchResponse {

    /**
     * 申請單單號
     */
    @Schema(description = "申請單單號")
    private Integer buChangeMasterId;

    /**
     * 撥、調車
     */
    @Schema(description = "撥、調車")
    private String changeKind;

    /**
     * 申請事由
     * 撥車 serviceCode=bu-change-changetype-assign
     * 調車 serviceCode=bu-change-changetype-transfer
     */
    @Schema(description = "申請事由，撥車 serviceCode=bu-change-changetype-assign，調車 serviceCode=bu-change-changetype-transfer")
    private String changeType;

    /**
     * 預估使用日期-起日
     */
    @Schema(description = "預估使用日期-起日")
    private Timestamp preUseDateStart;

    /**
     * 預估使用日期-迄日
     */
    @Schema(description = "預估使用日期-迄日")
    private Timestamp preUseDateEnd;

    /**
     * 申請說明
     */
    @Schema(description = "申請說明")
    private String memo;

    /**
     * 申請單狀態 serviceCode=bu_change_master-changestatus
     */
    @Schema(description = "申請單狀態 serviceCode=bu_change_master-changestatus")
    private String buChangeMasterStatus;

    /**
     * 取消撥車原因代碼
     */
    @Schema(description = "取消撥車原因代碼")
    private String cancelType;

    /**
     * 取消撥車原因
     */
    @Schema(description = "取消撥車原因")
    private String cancelReason;

    /**
     * 新增人員
     */
    @Schema(description = "新增人員")
    private String createUserId;

    /**
     * 建立人員所屬部門代碼 for 資料權限控管
     */
    @Schema(description = "建立人員所屬部門代碼 for 資料權限控管")
    private String createUserDepCode;

    /**
     * 新增日期
     */
    @Schema(description = "新增日期")
    private Timestamp createDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Timestamp updateDate;

    /**
     * 簽核案件編號(caseMaster.caseMasterId)
     */
    @Schema(description = "簽核案件編號(caseMaster.caseMasterId)")
    private Integer caseMasterId;

    /**
     * 使用人 for 公關用車
     */
    @Schema(description = "使用人 for 公關用車")
    private String carUser;

    /**
     * 公務使用地點 for 一般公務
     */
    @Schema(description = "公務使用地點 for 一般公務")
    private String useLocation;

    /**
     * 費用 for 公關用車
     */
    @Schema(description = "費用 for 公關用車")
    private Integer price;

    /**
     * 公司名稱 for 公關用車、合約用車
     */
    @Schema(description = "公司名稱 for 公關用車、合約用車")
    private String companyName;

    /**
     * 職稱 for 公關用車
     */
    @Schema(description = "職稱 for 公關用車")
    private String jobTitle;

    /**
     * 合約類型 for 合約用車
     */
    @Schema(description = "合約類型 for 合約用車")
    private String contractType;

    /**
     * 統編 for 合約用車
     */
    @Schema(description = "統編 for 合約用車")
    private String companyNo;

    /**
     * 合約車號 for 合約用車
     */
    @Schema(description = " 合約車號 for 合約用車")
    private String contractPlateNo;

    /**
     * 月租金 for 合約用車
     */
    @Schema(description = "月租金 for 合約用車")
    private Integer rent;

    /**
     * 申購單號、交修序號 for 合約用車
     */
    @Schema(description = "申購單號、交修序號 for 合約用車")
    private String documentNo;

    /**
     * 申請單明細
     */
    @Schema(description = "申請單明細")
    private List<BUChangeDetailSearchResponse> buChangeDetailList;


    /**
     * 是否已申請營業用還車
     */
    @Schema(description = "是否已申請營業用還車")
    private Boolean isReturned;

}
