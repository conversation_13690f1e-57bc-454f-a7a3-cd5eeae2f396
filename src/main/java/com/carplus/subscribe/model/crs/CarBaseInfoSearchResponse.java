package com.carplus.subscribe.model.crs;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 查詢車輛資訊結果
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "查詢車輛資訊結果")
public class CarBaseInfoSearchResponse {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    private String plateNo;

    /**
     * 庫位代碼
     */
    @Schema(description = "庫位代碼")
    private Integer buId;

    /**
     * 車輛基本檔 car_base 異動日期
     */
    @Schema(description = "車輛基本檔 car_base 異動日期")
    private Date updateDate;

    /**
     * 車輛基本資料
     */
    private CarBase carBase;

    /**
     * 車齡(月)
     */
    @Schema(description = "車齡(月)")
    private Integer carAgeMonth;

    /**
     * 車子規格
     */
    @Schema(description = "車子規格")
    private CarSpecInfoResponse carSpecInfoResponse;

    /**
     * 子系統車輛資訊
     */
    @Schema(description = "子系統車輛資訊")
    private SubSystemCarInfo subSystemCarInfo;

    /**
     * 牌照資訊
     */
    private CarLicense carLicense;

    @Schema(description = "是否為專案車")
    @JsonProperty("isProjectCar")
    private Boolean isProjectCar;
}

