package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@NoArgsConstructor
@Data
public class CarBaseInfoQueryResponse {

    /**
     * 車體等級，serviceCode = car_base-bodylevel
     */
    @Schema(description = "車體等級，serviceCode = car_base-bodylevel")
    private String bodyLevel;

    /**
     * 車身號碼
     */
    @Schema(description = "車身號碼")
    private String bodyNo;

    /**
     * 廠牌代碼
     */
    @Schema(description = "廠牌代碼")
    private String brandCode;

    /**
     * 庫位
     */
    @Schema(description = "庫位")
    private Integer buId;

    /**
     * 營運狀態，serviceCode=car_base-bustatus
     */
    @Schema(description = "營運狀態，serviceCode=car_base-bustatus")
    private String buStatus;

    /**
     * 庫位與營運狀態異動記錄編號
     */
    @Schema(description = "庫位與營運狀態異動記錄編號")
    private Integer buStatusLogId;

    /**
     * carCode
     */
    @Schema(description = "carCode")
    private String carCode;

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 車輛來源，serviceCode=car_base-carsource
     */
    @Schema(description = "車輛來源，serviceCode=car_base-carsource")
    private String carSource;

    /**
     * 車輛狀態，serviceCode=car_base-carstatus
     */
    @Schema(description = "車輛狀態，serviceCode=car_base-carstatus")
    private String carStatus;

    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String classCode;

    /**
     * 車型代碼4
     */
    @Schema(description = "車型代碼4")
    private String code4;

    /**
     * 顏色
     */
    @Schema(description = "顏色")
    private String color;

    /**
     * 公司統編
     */
    @Schema(description = "公司統編")
    private String companyNo;

    /**
     * 創建日期
     */
    @Schema(description = "創建日期")
    private Instant createDate;

    /**
     * 創建者ID
     */
    @Schema(description = "創建者ID")
    private String createUserId;

    /**
     * 引擎編號
     */
    @Schema(description = "引擎編號")
    private String engineNo;

    /**
     * 取得日期
     */
    @Schema(description = "取得日期")
    private Instant getDate;

    /**
     * 進價
     */
    @Schema(description = "進價")
    private Integer getPrice;

    /**
     * 是否水貨車
     */
    @Schema(description = "是否水貨車")
    private Boolean isDemoCar;

    /**
     * 是否開放撥車
     */
    @Schema(description = "是否開放撥車")
    private Boolean isOpen;

    /**
     * 目前里程
     */
    @Schema(description = "目前里程")
    private Integer km;

    /**
     * 購入時是否為新車
     */
    @Schema(description = "購入時是否為新車")
    private String newCar;

    /**
     * 開始開放日期
     */
    @Schema(description = "開始開放日期")
    private Instant openStartDate;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    private String plateNo;

    /**
     * 出廠年月
     */
    @Schema(description = "出廠年月")
    private String publishDate;

    /**
     * 資產編號
     */
    @Schema(description = "資產編號")
    private Integer pyAuto;

    /**
     * 退車日期
     */
    @Schema(description = "退車日期")
    private Instant quitDate;

    /**
     * 銷售日期
     */
    @Schema(description = "銷售日期")
    private Instant saleDate;

    /**
     * 座位數(含司機)
     */
    @Schema(description = "座位數(含司機)")
    private Integer seats;

    /**
     * 牌價(入籍當時牌價)
     */
    @Schema(description = "牌價(入籍當時牌價)")
    private String stdPrice;

    /**
     * 標準售價代碼
     */
    @Schema(description = "標準售價代碼")
    private String stdPriceCode;

    /**
     * 更新日期
     */
    @Schema(description = "更新日期")
    private Instant updateDate;

    /**
     * 更新者ID
     */
    @Schema(description = "更新者ID")
    private String updateUserId;
}

