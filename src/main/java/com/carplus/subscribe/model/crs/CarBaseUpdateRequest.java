package com.carplus.subscribe.model.crs;

import carplus.common.utils.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.Locale;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "車輛基本資料更新")
public class CarBaseUpdateRequest {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號", required = true)
    @NotNull(message = "車輛編號，不可為空")
    private Integer carNo;

    /**
     * 所屬公司統編
     */
    @Schema(description = "所屬公司統編")
    private String companyNo;

    /**
     * 引擎號碼
     */
    @Schema(description = "引擎號碼")
    private String engineNo;

    /**
     * 車身號碼
     */
    @Schema(description = "車身號碼")
    private String bodyNo;

    /**
     * 出廠年月
     */
    @Schema(description = "出廠年月")
    private String publishDate;

    /**
     * 顏色
     */
    @Schema(description = "顏色")
    private String color;

    /**
     * 廠牌代碼
     */
    @Schema(description = "廠牌代碼")
    private String brandCode;

    /**
     * 車型代碼
     */
    @Schema(description = "車型代碼")
    private String classCode;

    /**
     * 車型明細代碼
     */
    @Schema(description = "車型明細代碼")
    private String stdPriceCode;

    /**
     * 取得日期
     */
    @Schema(description = "取得日期")
    private Timestamp getDate;

    /**
     * 代碼4
     */
    @Schema(description = "代碼4")
    private String code4;

    /**
     * 牌價(入籍當時牌價)
     */
    @Schema(description = "牌價(入籍當時牌價)")
    private String stdPrice;

    /**
     * 取得價
     */
    @Schema(description = "取得價")
    private Integer getPrice;

    /**
     * 車輛來源
     */
    @Schema(description = "車輛來源")
    private String carSource;

    /**
     * 購入時是否為新車
     */
    @Schema(description = "購入時是否為新車")
    private String newCar;

    /**
     * 是否水貨車(新增時使用)
     */
    @Schema(description = "是否水貨車(新增時使用)")
    private Boolean isDemoCar;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    private String plateNo;

    /**
     * 庫位代碼
     */
    @Schema(description = "庫位代碼")
    private Integer buId;

    /**
     * 購車bu
     */
    @Schema(description = "購車bu")
    private Integer firstBU;

    /**
     * 營運狀態代碼
     */
    @Schema(description = "營運狀態代碼")
    private String buStatus;

    /**
     * 目前里程
     */
    @Schema(description = "目前里程")
    private Integer km;

    /**
     * 出售日期
     */
    @Schema(description = "出售日期")
    private Timestamp saleDate;

    /**
     * 保有退出日期
     */
    @Schema(description = "保有退出日期")
    private Timestamp quitDate;

    /**
     * 資產編號(.net系統用)
     */
    @Schema(description = "資產編號(.net系統用)")
    private Integer pyAuto;

    /**
     * 租賃方式
     */
    @Schema(description = "租賃方式")
    private String rentalPlan;

    /**
     * BuStatusLog.buStatusLogId 庫位與營運狀態異動記錄編號
     */
    @Schema(description = "BuStatusLog.buStatusLogId 庫位與營運狀態異動記錄編號")
    private Integer buStatusLogId;

    /**
     * 車輛狀態代碼 service_code=car_base-carstatus
     */
    @Schema(description = "車輛狀態代碼 service_code=car_base-carstatus")
    private String carStatus;

    /**
     * 是否開放撥車(true 開放/ false 不開放)
     */
    @Schema(description = "是否開放撥車(true 開放/ false 不開放)")
    private Boolean isOpen;

    /**
     * 開放撥車起日(isOpen=開放時才更新)
     */
    @Schema(description = "開放撥車起日(isOpen=開放時才更新)")
    private Timestamp openStartDate;

    /**
     * 車體等級，serviceCode = car_base-bodylevel
     */
    @Schema(description = "車體等級，serviceCode = car_base-bodylevel")
    private String bodyLevel;

    /**
     * 座位數(含司機)
     */
    @Schema(description = "座位數(含司機)")
    private Integer seats;

    /**
     * 首次契約編號
     */
    @Schema(description = "首次契約編號")
    private String firstContractNo;

    /**
     * 異動憑證類型
     */
    @Schema(description = "異動憑證類型 serviceCode=car_base_log-depenceDocType")
    private CarBaseEnum.LogDepencedoctype depenceDocType;

    /**
     * 異動憑證單號
     */
    @Schema(description = "異動憑證單號")
    private String depenceDocNo;

    /**
     * 非車輛所屬BU接收車籍異動que(單一service)
     * 改為不想接收時，請給:notDeque
     */
    @Schema(description = "非車輛所屬BU接收車籍異動que(單一service)，改為不想接收時，請給:notDeque")
    private String carBaseModifyQue;

    /**
     * 資料異動說明
     */
    @Schema(description = "資料異動說明", required = true)
    @NotNull(message = "資料異動說明，不可為空")
    private String memo;

    /**
     * 異動人員代碼、程式名稱、系統名稱
     */
    @Schema(description = "異動人員代碼、程式名稱、系統名稱", required = true)
    @NotNull(message = "異動人員代碼、程式名稱、系統名稱，不可為空")
    private String userId;

    /**
     * 車牌號碼，一律轉為大寫
     */
    public void setPlateNo(String plateNo) {
        this.plateNo = StringUtils.isNotBlank(plateNo) ? plateNo.trim().toUpperCase(Locale.ROOT) : null;
    }

    /**
     * 車牌號碼，一律轉為大寫
     */
    public String getPlateNo() {
        return StringUtils.isNotBlank(plateNo) ? plateNo.trim().toUpperCase(Locale.ROOT) : null;
    }
}
