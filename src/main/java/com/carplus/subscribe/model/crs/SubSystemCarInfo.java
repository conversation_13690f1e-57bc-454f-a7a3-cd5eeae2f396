package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 子系統車輛資訊
 */
@Data
@NoArgsConstructor
public class SubSystemCarInfo {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼")
    private String plateNo;

    /**
     * 車輛里程
     */
    @Schema(description = "車輛里程")
    private Integer km;

    /**
     * 車輛區域代碼
     */
    @Schema(description = "車輛區域代碼")
    private String areaId;

    /**
     * 區域代碼-成本單位
     */
    @Schema(description = "區域代碼-成本單位")
    private String areaIdNew;

    /**
     * 車輛區域名稱
     */
    @Schema(description = "車輛區域名稱")
    private String areaName;

    /**
     * 車輛停放點代碼
     */
    @Schema(description = "車輛停放點代碼")
    private String locationId;

    /**
     * 停放點代碼-成本單位
     */
    @Schema(description = "停放點代碼-成本單位")
    private String locationIdNew;

    /**
     * 車輛停放點名稱
     */
    @Schema(description = "車輛停放點名稱")
    private String locationName;


}