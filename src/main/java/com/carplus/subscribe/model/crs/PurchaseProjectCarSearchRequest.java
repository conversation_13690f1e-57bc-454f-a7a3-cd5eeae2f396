package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Schema(description = "查詢營業用車採購-專案車相關資訊")
public class PurchaseProjectCarSearchRequest {
    /**
     * 引擎編號List
     */
    @Schema(description = "引擎編號List")
    private List<String> engineNoList;

    /**
     * 車輛編號List
     */
    @Schema(description = "車輛編號List")
    private List<Integer> carNoList;


}
