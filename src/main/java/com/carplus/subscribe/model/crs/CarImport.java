package com.carplus.subscribe.model.crs;

import lombok.Data;

import java.util.Date;

@Data
public class CarImport {
    /**
     * 車型代碼
     */
    private String carCode;
    /**
     * 引擎號碼
     */
    private String engineNo;
    /**
     * 車身號碼
     */
    private String bodyNo;
    /**
     * 公司別
     */
    private String companyNo;
    /**
     * 顏色
     */
    private String color;
    /**
     * 座位數(含司機)
     */
    private Integer seats;
    /**
     * 牌照種類
     */
    private String licenseType;
    /**
     * 預計領牌日
     */
    private Date licenseExpDate;
    /**
     * 出廠年月
     */
    private String publishDate;
    /**
     * 車體等級
     */
    private String carBodyLevel;
    /**
     * 里程數
     */
    private Integer km;
    /**
     * 新車
     */
    private String newCar;
    /**
     * 水貨車
     */
    private String isDemoCar;
    /**
     * 廠商統編
     */
    private String vatNumber;
    /**
     * 發票類型1
     */
    private String invType1;
    /**
     * 發票日期1
     */
    private Date invDate1;
    /**
     * 發票號碼1(折讓尾款發票)
     */
    private String invNo1;
    /**
     * 發票金額1
     */
    private Integer invAmt1;
    /**
     * 發票類型2
     */
    private String invType2;
    /**
     * 發票日期2
     */
    private Date invDate2;
    /**
     * 發票號碼2
     */
    private String invNo2;
    /**
     * 發票金額2
     */
    private Integer invAmt2;
    /**
     * 發票類型3
     */
    private String invType3;
    /**
     * 發票日期3
     */
    private Date invDate3;
    /**
     * 發票號碼3
     */
    private String invNo3;
    /**
     * 發票金額3
     */
    private Integer invAmt3;
    /**
     * 折讓日期
     */
    private Date rebateDate;
    /**
     * 折讓金額
     */
    private Integer rebateAmt;
    /**
     * 部門站點
     */
    private String unitId;
    

}

