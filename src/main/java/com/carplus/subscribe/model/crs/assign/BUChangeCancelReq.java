package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "取消 撥、調車申請單")
public class BUChangeCancelReq {

    @Schema(description = "撥、調車申請單單號", required = true, example = "1")
    @NotNull(message = "撥、調車申請單單號，不可為空")
    private Integer buChangeMasterId;

    @Schema(description = "取消申請原因代碼 service_code=dispatch_master-canceltype", required = true, example = "1")
    @NotNull(message = "取消申請原因代碼，不可為空")
    private String cancelType;

    @Schema(description = "取消申請原因", required = true, example = "取消申請")
    @NotNull(message = "取消申請原因，不可為空")
    private String cancelReason;

    @Schema(description = "異動人員/程式", required = true)
    @NotNull(message = "異動人員，不可為空")
    private String userId;


}
