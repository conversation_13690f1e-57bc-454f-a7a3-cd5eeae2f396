package com.carplus.subscribe.model.crs;

import com.carplus.subscribe.enums.CarControlEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "更新車輛管制狀態")
public class CarControlSaveRequest {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號", required = true)
    @NotNull(message = "車輛編號，不可為空")
    private Integer carNo;

    /**
     * 車牌號碼
     */
    @Schema(description = "車牌號碼", required = true)
    @NotNull(message = "車牌號碼，不可為空")
    private String plateNo;

    /**
     * 車輛管制狀態 serviceCode=car_control-controlStatus
     */
    @Schema(description = "車輛管制狀態 serviceCode=car_control-controlStatus", required = true)
    @NotNull(message = "車輛管制狀態，不可為空")
    private CarControlEnum.ControlStatus controlStatus;

    /**
     * 異動事由
     */
    @Schema(description = "異動事由", required = true)
    @NotNull(message = "異動事由，不可為空")
    private String changeType;

    /**
     * 異動事由說明
     */
    @Schema(description = "異動事由說明")
    private String changeMemo;

    /**
     * 是否開放撥車(true 開放、 false 不開放、null 不更新)
     */
    @Schema(description = "是否開放撥車(true 開放、 false 不開放、null 不更新)")
    private Boolean isOpen;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員", required = true)
    @NotNull(message = "異動人員，不可為空")
    private String userId;

    public CarControlSaveRequest(Integer carNo, CarControlEnum.ChangeType changeType, String plateNo, String userId) {
        this.carNo = carNo;
        this.changeType = changeType.getChangeType();
        this.changeMemo = changeType.getChangeMemo();
        this.plateNo = plateNo;
        this.userId = userId;
        this.isOpen = changeType.getIsOpen();
        this.controlStatus = isOpen ? CarControlEnum.ControlStatus.open : CarControlEnum.ControlStatus.close;

    }
}
