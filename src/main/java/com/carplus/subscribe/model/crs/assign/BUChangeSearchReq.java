package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查詢撥、調車申請單")
public class BUChangeSearchReq {

    public BUChangeSearchReq(String keyword) {
        this.keyword = keyword;
    }

    /**
     * 關鍵字類型
     */
    @Schema(description = "關鍵字類型", example = "A", required = true)
    @NotEmpty(message = "關鍵字類型，不可為空")
    private String type = "B";

    /**
     * 關鍵字
     */
    @Schema(description = "關鍵字")
    private String keyword;


    /**
     * 從第幾筆
     */
    @Schema(description = "從第幾筆", example = "0")
    @Builder.Default
    private Integer offset = 0;

    /**
     * 取幾筆
     */
    @Schema(description = "取幾筆", example = "20")
    private Integer limit = 20;


}
