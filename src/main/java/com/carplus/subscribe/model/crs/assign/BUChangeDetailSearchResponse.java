package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查詢撥、調車申請單明細結果")
public class BUChangeDetailSearchResponse {

    /**
     * 流水號
     */
    @Schema(description = "流水號")
    private Integer buChangeDetailId;

    /**
     * 撥、調車申請單單號 (bu_change_master.masterId)
     */
    @Schema(description = "撥、調車申請單單號")
    private Integer buChangeMasterId;

    /**
     * 車輛編號 (car_base.carNo)
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 目前車牌號碼 (car_base.plateNo)
     */
    @Schema(description = "目前車牌號碼")
    private String plateNo;

    /**
     * 撥、調出車輛庫位(bu.id)
     */
    @Schema(description = " 撥、調出車輛庫位")
    private Integer fromBu;

    /**
     * 撥、調出區域(組織代碼)
     */
    @Schema(description = "撥、調出區域(組織代碼)")
    private String fromArea;

    /**
     * 撥、調出區域(各系統自定義代碼)
     */
    @Schema(description = "撥、調出區域(各系統自定義代碼)")
    private String fromRealArea;

    /**
     * 撥、調出區域名稱 (各系統自定義)
     */
    @Schema(description = "撥、調出區域名稱 (各系統自定義)")
    private String fromAreaName;

    /**
     * 撥、調出停放點(組織代碼)
     */
    @Schema(description = "撥、調出停放點(組織代碼)")
    private String fromLocation;

    /**
     * 撥、調出停放點(各系統自定義代碼)
     */
    @Schema(description = "撥、調出停放點(各系統自定義代碼)")
    private String fromRealLocation;

    /**
     * 撥、調出停放點名稱 (各系統自定義)
     */
    @Schema(description = "撥、調出停放點名稱 (各系統自定義)")
    private String fromLocationName;

    /**
     * 撥、調入庫位
     */
    @Schema(description = "撥、調入庫位")
    private Integer targetBu;

    /**
     * 撥、調入區域(組織代碼)
     */
    @Schema(description = "撥、調入區域(組織代碼)")
    private String targetArea;

    /**
     * 撥、調入區域(各系統自定義代碼)
     */
    @Schema(description = "撥、調入區域(各系統自定義代碼)")
    private String targetRealArea;

    /**
     * 撥、調入區域名稱 (各系統自定義)
     */
    @Schema(description = "撥、調入區域名稱 (各系統自定義)")
    private String targetAreaName;

    /**
     * 撥、調入停放點(組織代碼)
     */
    @Schema(description = "撥、調入停放點(組織代碼)")
    private String targetLocation;

    /**
     * 撥、調入停放點(各系統自定義代碼)
     */
    @Schema(description = "撥、調入停放點(各系統自定義代碼)")
    private String targetRealLocation;

    /**
     * 撥、調入停放點名稱 (各系統自定義)
     */
    @Schema(description = "撥、調入停放點名稱 (各系統自定義)")
    private String targetLocationName;

    /**
     * 出車時間
     */
    @Schema(description = "出車時間")
    private Timestamp leaveDate;

    /**
     * 出車人員
     */
    @Schema(description = "出車人員")
    private String leaveGetUser;

    /**
     * 取車人員
     */
    @Schema(description = "取車人員")
    private String leaveUser;

    /**
     * 出車里程
     */
    @Schema(description = "出車里程")
    private Integer leaveKM;

    /**
     * 出車備註
     */
    @Schema(description = "出車備註")
    private String leaveMemo;

    /**
     * 收車時間
     */
    @Schema(description = "收車時間")
    private Timestamp receiveDate;

    /**
     * 交車人員
     */
    @Schema(description = "交車人員")
    private String receiveGiveUser;

    /**
     * 收車人員
     */
    @Schema(description = "收車人員")
    private String receiveUser;

    /**
     * 收車里程
     */
    @Schema(description = "收車里程")
    private Integer receiveKM;

    /**
     * 收車備註
     */
    @Schema(description = "收車備註")
    private String receiveMemo;

    /**
     * 出、收車狀態 serviceCode = bu_change_detail-buchangedetailstatus
     */
    @Schema(description = "出、收車狀態 serviceCode = bu_change_detail-buchangedetailstatus")
    private String buChangeDetailStatus;

    /**
     * 取消出、收車代碼 serviceCode=bu_change_detail-unleave-type、bu_change_detail-unreceive-type
     */
    @Schema(description = "取消出、收車代碼 serviceCode=bu_change_detail-unleave-type、bu_change_detail-unreceive-type")
    private String unchangeType;

    /**
     * 取消出、收車原因
     */
    @Schema(description = "取消出、收車原因")
    private String unchangeReason;

    /**
     * 是否派拖，0 - 非派拖、1-派拖，未來要儲存派拖單編號
     */
    @Schema(description = "是否派拖，0 - 非派拖、1-派拖，未來要儲存派拖單編號")
    private Integer entrustNo;

    /**
     * 帳面殘值(未稅)
     */
    @Schema(description = "帳面殘值(未稅)")
    private Integer untaxSalvageValue;

    /**
     * 標準殘值(未稅)
     */
    @Schema(description = "標準殘值(未稅)")
    private Integer untaxStandardResidual;

    /**
     * 到期殘值(未稅)
     */
    @Schema(description = "到期殘值(未稅)")
    private Integer untaxExpiredResidual;

    /**
     * 找補金(未稅)
     */
    @Schema(description = "找補金(未稅)")
    private Integer untaxDiffAmount;

    /**
     * 費用租金(年)(未稅)
     */
    @Schema(description = "費用租金(年)(未稅)")
    private Integer untaxYearRent;

    /**
     * 折舊費用(年)(未稅)
     */
    @Schema(description = "折舊費用(年)(未稅)")
    private Integer untaxDepreciateYearAmount;

    /**
     * 預計領牌日
     */
    @Schema(description = "預計領牌日")
    private Timestamp licenseExpDate;

    /**
     * 強制險核保保單編號
     */
    @Schema(description = "強制險核保保單編號")
    private String compulsoryApplyNo;

    /**
     * 任意險核保保單編號
     */
    @Schema(description = "任意險核保保單編號")
    private String voluntaryApplyNo;

    /**
     * 保險方案代碼
     */
    @Schema(description = "保險方案代碼")
    private String insurancePlanNo;

    /**
     * 新增人員
     */
    @Schema(description = "新增人員")
    private String createUserId;

    /**
     * 新增日期
     */
    @Schema(description = "新增日期")
    private Timestamp createDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Timestamp updateDate;

}
