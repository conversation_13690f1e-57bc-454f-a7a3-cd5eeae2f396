package com.carplus.subscribe.model.crs.assign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeListResponse {

    @Schema(description = "表單編號")
    private Integer id;

    @Deprecated
    @Schema(description = "撥入庫位代碼")
    private Integer targetBU;

    @Deprecated
    @Schema(description = "撥入庫位名稱")
    private String targetBuName;

    @Deprecated
    @Schema(description = "撥入區域代碼 (子系統 區域代碼)")
    private String targetArea;

    @Deprecated
    @Schema(description = "撥入區域名稱 (子系統 區域名稱)")
    private String targetAreaName;

    @Deprecated
    @Schema(description = "撥入停放點 (子系統 停放點代碼)")
    private String targetLocation;

    @Deprecated
    @Schema(description = "撥入停放點名稱 (子系統 停放點名稱)")
    private String targetLocationName;

    @Schema(description = "撥/調車申請單代碼，serviceCode=bu_change_master-changekind")
    private String changeKind;

    @Schema(description = "撥/調車申請單")
    private String changeKindName;

    @Schema(description = "申請事由代碼 撥車 serviceCode=bu-change-changetype-assign，"
        + " 調車 serviceCode=bu-change-changetype-transfer ")
    private String changeType;

    @Schema(description = "申請事由")
    private String changeTypeName;

    @Schema(description = "預估使用起始日期")
    private Timestamp preUseDateStart;

    @Schema(description = "預估使用結束日期")
    private Timestamp preUseDateEnd;

    @Schema(description = "申請說明")
    private String memo;

    @Schema(description = "撥/調車申請單狀態代碼，serviceCode=bu_change_master-buchangemasterstatus")
    private String status;

    @Schema(description = "撥/調車申請單狀態名稱")
    private String statusName;

    @Schema(description = "總申請車輛數")
    private Integer carTotal;

    @Schema(description = "車輛數")
    private Integer cars;

    @Schema(description = "取消申請原因代碼，serviceCode=bu_change_master-canceltype")
    private String cancelType;

    @Schema(description = "取消申請原因代碼名稱")
    private String cancelTypeName;

    @Schema(description = "取消申請原因說明")
    private String cancelReason;

    @Schema(description = "簽核案件編號")
    private Integer caseMasterId;

    @Schema(description = "使用人_公關用車")
    private String carUser;

    @Schema(description = "公務使用地點_一般公務")
    private String useLocation;

    @Schema(description = "費用_公關用車")
    private Integer price;

    @Schema(description = "公司名稱_公關用車、合約用車")
    private String companyName;

    @Schema(description = "職稱_公關用車")
    private String jobTitle;

    @Schema(description = "合約類型_合約用車")
    private String contractType;

    @Schema(description = "合約類型名稱 for 合約用車")
    private String contractTypeName;

    @Schema(description = "統編_合約用車")
    private String companyNo;

    @Schema(description = "合約車號_合約用車")
    private String contractPlateNo;

    @Schema(description = "月租金_合約用車")
    private Integer rent;

    @Schema(description = "申購單號/交修序號_合約用車、原調車申請單單號_營業用還車")
    private String documentNo;

    @Schema(description = "新增人員名稱")
    private String createUserIdName;

    @Schema(description = "新增人員工號")
    private String createUserId;

    @Schema(description = "新增時間")
    private Timestamp createDate;

    @Schema(description = "更新人員名稱")
    private String updateUserIdName;

    @Schema(description = "更新人員工號")
    private String updateUserId;

    @Schema(description = "更新時間")
    private Timestamp updateDate;

    /**
     * 申請單明細
     */
    @Schema(description = "申請單明細")
    private List<ChangeDetailResponse> buChangeDetails;

    /**
     * 是否已申請營業用還車
     */
    @Schema(description = "是否已申請營業用還車")
    private Boolean isReturned;

}
