package com.carplus.subscribe.model.crs;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "更新車輛最新里程數")
public class CarBaseUpdateKmReq {

    @Schema(description = "CRS 車輛編號")
    private Integer carNo;

    @Schema(description = "異動憑證單號 (訂單編號)")
    private String depenceDocNo;

    @Schema(description = "異動憑證類型 serviceCode=car_base_log-depenceDocType 訂閱帶 1")
    private String depenceDocType;

    @Schema(description = "最新里程數")
    private Integer km;

    @Schema(description = "里程更新說明")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String memo;

    @Schema(description = "異動人員/程式名稱 訂閱帶 subscribe-updateKm")
    private String userId;
}
