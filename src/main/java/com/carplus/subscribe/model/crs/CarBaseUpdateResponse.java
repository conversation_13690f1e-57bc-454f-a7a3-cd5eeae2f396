package com.carplus.subscribe.model.crs;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Schema(description = "車輛基本資料更新結果")
public class CarBaseUpdateResponse {

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 錯誤訊息
     */
    @Schema(description = "錯誤訊息")
    private String errMsg = "";
}
