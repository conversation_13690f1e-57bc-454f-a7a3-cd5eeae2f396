package com.carplus.subscribe.model;

import com.carplus.subscribe.enums.LandmarksCategory;
import lombok.*;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class Landmark {

    /**
     * 地標名稱
     */
    private String name;

    /**
     * 地標與站所之距離 單位公里
     */
    private double distance;

    /**
     * 地標分類
     */
    private LandmarksCategory category;

}
