package com.carplus.subscribe.model.sp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriceDetailBuilder {
    private String rsvNo;
    private String contractNo;
    private Integer pricingAuto;
    private Integer seqAuto;
    private String campaignCode;
    private String countDateFrom;
    private String timeFrom;
    private String countDateTo;
    private String timeTo;
    private int orgPrcAmt;
    private int rentAmt;
    private String lastUserCode;
    private String lastUpdateIP;
}
