package com.carplus.subscribe.model.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "車輛所屬站點資訊")
public class CarLocationInfo {

    @Schema(description = "站點名稱(各BU自行定義)")
    private String name;

    @Schema(description = "站點代碼(各BU自行定義)")
    private String code;

    @Schema(description = "站點所屬部門(for 簽核、權限控管)")
    private String codeNew;

    @Schema(description = "數量")
    private Integer count;

    @Schema(description = "百分比")
    private String percent;

    @Schema(description = "站點統編")
    private String vatNo;
}
