package com.carplus.subscribe.model.station;

import carplus.common.utils.BeanUtils;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class StationResponseWithChangeLogs extends StationResponse {

    @Schema(description = "異動歷程")
    private List<EntityChangeLog> changeLogs;

    public StationResponseWithChangeLogs(StationResponse response, List<EntityChangeLog> changeLogs) {
        BeanUtils.copyProperties(response, this);
        this.changeLogs = changeLogs;
    }
}
