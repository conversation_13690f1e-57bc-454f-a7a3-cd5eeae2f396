package com.carplus.subscribe.model.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "車輛所屬區域與站點資訊，以及車輛數統計")
public class CarAreaInfoListWithCount {

    @Schema(description = "總車輛數")
    public int count;

    @Schema(description = "車輛所屬區域與站點資訊")
    public List<CarAreaInfo> subCarInfo;
}
