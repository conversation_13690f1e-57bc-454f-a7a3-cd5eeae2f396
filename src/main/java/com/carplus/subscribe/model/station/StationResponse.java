package com.carplus.subscribe.model.station;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.aspects.ChineseName;
import com.carplus.subscribe.db.mysql.entity.ConfigEntity;
import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.enums.StationDefine;
import com.carplus.subscribe.model.Landmark;
import com.carplus.subscribe.model.Photo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class StationResponse {

    @Schema(description = "站所代碼")
    private String stationCode;
    @Schema(description = "站所中文名")
    private String stationName;
    @Schema(description = "站所英文名")
    private String stationNameEn;
    @Schema(description = "站所短名")
    private String shortName;
    @Schema(description = "站所城市分區")
    private Integer cityId;
    @Schema(description = "所在區域 N:北部, C:中部, S:南部, E:東部 ,短租規劃")
    private String geoRegion;
    @Schema(description = "所在區域 N:北部, C:中部, S:南部, E:東部")
    private String locateGeoRegion;
    @Schema(description = "所分區 0:雙北, 1:桃竹苗, 2:中彰投, 3:雲嘉南, 4:高屏, 5:宜蘭, 6:花蓮, 7:玉里, 8:台東")
    private Integer stationRegion;
    @Schema(description = "部門代碼")
    private String orgLevelCode;
    @Schema(description = "中文地址")
    private String addr;
    @Schema(description = "英文地址")
    private String addrEn;
    @Schema(description = "聯絡電話")
    private String tel;
    @Schema(description = "傳真")
    private String fax;
    @Schema(description = "電子郵件")
    private String email;
    @Schema(description = "經度")
    private Double lng;
    @Schema(description = "緯度")
    private Double lat;
    @Schema(description = "中文簡介")
    private String descr;
    @Schema(description = "英文簡介")
    private String descrEn;
    @Schema(description = "營業起始時間")
    private String startHours;
    @Schema(description = "營業結束時間")
    private String endHours;
    @Schema(description = "靜態地圖")
    private String staticMap;
    @Schema(description = "附近地標資訊")
    private List<Landmark> landmarks;
    @Schema(description = "照片")
    private List<Photo> photos;
    @Schema(description = "是否為訂閱站點")
    private Boolean isSubscribe;
    @Schema(description = "站點狀態")
    private String status;
    @Schema(description = "站點類別")
    private StationDefine.StationCategory stationCategory;
    @Schema(description = "業務別")
    private StationDefine.CarplusService carplusService;
    @Schema(description = "收銀台是否可見")
    private boolean visible;
    @ChineseName(value = "是否啟用電子出租單", belongsTo = ConfigEntity.class)
    @Schema(description = "是否啟用電子出租單")
    private boolean forceOnlineRForm;

    public String getSubGeoRegion() {
        return StringUtils.isNotBlank(locateGeoRegion) ? GeoDefine.GeoRegion.enumToCode(GeoDefine.GeoRegion.valueOf(locateGeoRegion)) : null;
    }

    public String getSubGeoRegionName() {
        return StringUtils.isNotBlank(locateGeoRegion) ? GeoDefine.GeoRegion.enumToName(GeoDefine.GeoRegion.valueOf(locateGeoRegion)) : null;
    }
}
