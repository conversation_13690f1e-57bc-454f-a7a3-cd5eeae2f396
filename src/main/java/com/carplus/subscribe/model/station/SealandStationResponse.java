package com.carplus.subscribe.model.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SealandStationResponse {
    @Schema(description = "站點編號")
    private String stationCode;
    @Schema(description = "站點名稱")
    private String stationName;
    @Schema(description = "站所狀態 A:啟用, D:停用")
    private String status;
    @Schema(description = "是否可用")
    private Boolean active;
}
