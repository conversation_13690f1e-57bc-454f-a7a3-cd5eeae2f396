package com.carplus.subscribe.model.station;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "車輛所屬區域資訊")
public class CarAreaInfo {

    @Schema(description = "區域名稱(各BU自行定義)")
    private String name;

    @Schema(description = "區域代碼(各BU自行定義)")
    private String code;

    @Schema(description = "區域所屬部門(for 簽核)")
    private String codeNew;

    @Schema(description = "數量")
    private Integer count;

    @Schema(description = "百分比")
    private String percent;

    @Schema(description = "車輛所屬站點資訊")
    private List<CarLocationInfo> underCarInfo;
}
