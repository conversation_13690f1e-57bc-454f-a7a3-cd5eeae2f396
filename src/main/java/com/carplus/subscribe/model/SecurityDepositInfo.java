package com.carplus.subscribe.model;

import com.carplus.subscribe.enums.ManualRefundStatus;
import com.carplus.subscribe.enums.ManualRefundMethod;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.util.Date;

@Data
@NoArgsConstructor
@Schema(description = "訂閱資訊")
public class SecurityDepositInfo {

    /* 保證金 start */
    @Schema(description = "方案保證金")
    private int securityDeposit;
    @Schema(description = "實際應收保證金")
    private int realSecurityDeposit;
    @Schema(description = "已收保證金")
    private int paidSecurityDeposit;
    @Schema(description = "未付保證金")
    private int unpaidSecurityDeposit;
    @Schema(description = "保證金付款日期")
    private Date securityDepositDate;
    @Schema(description = "保證金退款日期")
    private Date refundSecurityDepositDate;
    @Schema(description = "請求保證金退款日期")
    private Date refundSecurityDepositRequestDate;
    @Schema(description = "保證金退款金額")
    private int refundSecurityDeposit;
    @Schema(description = "是否人工退款保證金")
    private boolean isManualRefundSecurityDeposit;
    @Schema(description = "人工退款狀態")
    private ManualRefundStatus manualRefundStatus;
    @Schema(description = "人工退款類別")
    private ManualRefundMethod refundMethod;
    @Schema(description = "最後更新者")
    private String manualRefundUpdater;
    @Schema(description = "最後更新日期")
    private Date manualRefundUpdateDate;

    /**
     * 應收/待退保證金
     */
    @SuppressWarnings("unused")
    public int getTotalSecurityDeposit() {
        return paidSecurityDeposit + unpaidSecurityDeposit;
    }

    public void getSecurityDepositPaid(int amount) {
        this.paidSecurityDeposit += amount;
        this.unpaidSecurityDeposit -= amount;
    }

    /**
     * payment callback 紀錄實際退款時間、退款日期
     */
    public void refundSecurityDeposit(@NonNull Date refundDate) {
        refundSecurityDepositDate = refundDate;
    }

    /**
     * payment callback 紀錄請求退款時間、退款日期
     */
    public void requestRefundSecurityDeposit(@NonNull Date refundDate, int refundAmt) {
        refundSecurityDepositRequestDate = refundDate;
        refundSecurityDeposit += refundAmt;
    }

    /**
     * 人工保證金退款
     */
    public void manualRefundSecurityDeposit(@NonNull Date refundDate, ManualRefundMethod manualRefundMethod, String headerMemberId) {
        this.refundSecurityDepositDate = refundDate;
        this.isManualRefundSecurityDeposit = true;
        this.refundMethod = manualRefundMethod;
        this.manualRefundUpdater = headerMemberId;
        this.manualRefundUpdateDate = new Date();
    }

    public ManualRefundMethod getRefundMethod() {
        if (refundMethod != null) {
            return refundMethod;
        }
        if (refundSecurityDepositDate != null || manualRefundUpdateDate != null) {
            return ManualRefundMethod.Credit;
        }
        return null;
    }
}
