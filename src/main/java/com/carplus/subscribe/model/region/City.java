package com.carplus.subscribe.model.region;

import carplus.common.redis.serializer.Version;
import lombok.Data;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@Version(1)
@Data
public class City {

    private int cityId;
    private String cityName;
    private List<Area> area;

    @NonNull
    public String lookup(@Nullable Integer areaId) {
        String cityAreaStr = cityName;
        if (areaId != null) {
            cityAreaStr += area.stream().filter(area -> area.getAreaId() == areaId).findFirst().map(Area::getAreaName).orElse("");
        }

        return cityAreaStr;
    }
}