package com.carplus.subscribe.model.finance;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ICBCRep {
    @Schema(description = "公司別")
    @JsonProperty("incName")
    private String incName;

    @Schema(description = "ICBC編號")
    @JsonProperty("ICBCAuto")
    private Long ICBCAuto;

    @Schema(description = "類別")
    @JsonProperty("inComeType")
    private String inComeType;

    @Schema(description = "匯款金額")
    @JsonProperty("Amount")
    private Integer txAmount;

    @Schema(description = "部門代碼")
    @JsonProperty("DeptNo")
    private String depNo;

    @Schema(description = "匯款代號")
    @JsonProperty("remitNo")
    private String pRKey1;

    @Schema(description = "匯款人")
    @JsonProperty("remitMan")
    private String pRKey2;

    @Schema(description = "入帳日期")
    @JsonProperty("remitDate")
    private String valueDT;

    @Schema(description = "客戶統編")
    @JsonProperty("rentB")
    private String rentB;

    @Schema(description = "客戶名稱")
    @JsonProperty("sName")
    private String sName;

    @Schema(description = "狀態")
    @JsonProperty("status")
    private String status;
}