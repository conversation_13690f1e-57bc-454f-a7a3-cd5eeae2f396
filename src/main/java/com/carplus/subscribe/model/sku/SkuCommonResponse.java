package com.carplus.subscribe.model.sku;

import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Schema(description = "汽車用品基本資訊")
public class SkuCommonResponse {

    @Schema(description = "商品編號")
    private String code;

    @Schema(description = "商品類型")
    private String type;

    @Schema(description = "商品名稱")
    private String name;

    @Schema(description = "商品單價")
    private Integer unitPrice;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品圖片路徑")
    private String imgPath;

    public SkuCommonResponse(Sku sku) {
        this.code = sku.getCode();
        this.type = sku.getType();
        this.name = sku.getName();
        this.unitPrice = sku.getUnitPrice();
        this.description = sku.getDescription();
        this.imgPath = sku.getImgPath();
    }
}