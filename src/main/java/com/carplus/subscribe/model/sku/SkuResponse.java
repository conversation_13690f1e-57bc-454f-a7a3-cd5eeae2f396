package com.carplus.subscribe.model.sku;

import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "汽車用品完整資訊")
public class SkuResponse extends SkuCommonResponse {

    @Schema(description = "是否顯示於官網")
    private Boolean isOfficial;

    @Schema(description = "是否顯示於收銀台")
    private Boolean isCashier;

    public SkuResponse(Sku sku) {
        super(sku);
        this.isOfficial = sku.isOfficial();
        this.isCashier = sku.isCashier();
    }
}
