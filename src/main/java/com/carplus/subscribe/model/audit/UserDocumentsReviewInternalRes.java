package com.carplus.subscribe.model.audit;

import lombok.Data;

import java.util.Date;

@Data
public class UserDocumentsReviewInternalRes {

    private Long acctId;
    private Integer idCardFrontStatus = 0;
    private String idCardFrontReason;
    private Long idCardFrontId;
    private String idCardFrontUrl;
    private Integer idCardBackStatus = 0;
    private String idCardBackReason;
    private Long idCardBackId;
    private String idCardBackUrl;
    private Integer driverLicenceFrontStatus = 0;
    private String driverLicenceFrontReason;
    private Long driverLicenceFrontId;
    private String driverLicenceFrontUrl;
    private Integer driverLicenceBackStatus = 0;
    private String driverLicenceBackReason;
    private Long driverLicenseBackId;
    private String driverLicenseBackUrl;
    private String notificationBy;
    private String notification;
    private Date notificationDate;
    private Integer selfieStatus = 0;
    private String selfieReason;
    private Long selfieId;
    private String selfieUrl;
    private Long driverCardId;
    private String driverCardUrl;
    private Integer liveVideoStatus = 0;
    private String liveVideoReason;
    private Long liveVideoId;
    private String liveVideoUrl;
    private Integer specialIdentityStatus = 0;
    private String specialIdentityReason;
    private Long specialIdentityId;
    private String specialIdentityUrl;
    private Integer uploadStatus;
    private String verifyBy;
    private Boolean isVerifyPass;
    private Date verifyDate;
    private Integer verifyStatus;
    private String accountStatusName;
    private String accountStatusReason;
    private Date suspendEndTime;
    private String documentCreSystem;
    private Date documentCreDate;
    private String documentUpdateSystem;
    private Date documentUpdateDate;
    private String submitSystem;
    private Date submitDate;
    private Boolean doOcr;
    private Boolean doDetect;
    private Boolean autoReview;

}
