package com.carplus.subscribe.model.audit;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class UserDocumentsReviewByCashierPUTV2Req {
    @Schema(description = "user Id", example = "3345678")
    private Integer acctId;
    @Schema(description = "身份証正面", example = "1")
    private Long idCardFrontId;
    @Schema(description = "身份証背面", example = "1")
    private Long idCardBackId;
    @Schema(description = "駕照正面", example = "1")
    private Long driverLicenseFrontId;
    @Schema(description = "審核狀態")
    private CashierReviewStatus reviewStatus = CashierReviewStatus.NONE;

    public enum CashierReviewStatus {
        REJECT, PASS, NONE
    }
}
