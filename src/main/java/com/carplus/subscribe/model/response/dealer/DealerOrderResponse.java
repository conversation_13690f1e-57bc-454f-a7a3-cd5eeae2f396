package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CustSource;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;
import java.util.Optional;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DealerOrderResponse extends DealerOrderQueryResponse {

    @Schema(description = "車輛編號")
    private String carNo;

    @Schema(description = "撥車申請單單號")
    private Integer buChangeMasterId;

    @Schema(description = "車輛庫位代碼")
    private Integer buId;

    @Schema(description = "車輛庫位")
    private String carBuName;

    @Schema(description = "代步車方式")
    private String replaceCode;

    @Schema(description = "訂單來源")
    private Integer custSource;

    @Schema(description = "訂閱類別")
    private String carState;

    @Schema(description = "撥車紀錄")
    private BuChangeLog buChangeLog;

    public DealerOrderResponse(DealerOrder dealerOrder, CarResponse carResponse, CarBaseInfoSearchResponse carBaseInfoSearchResponse,
                               ContractSearchRep contractSearchRep, List<DealerOrderPriceInfo> dealerOrderPriceInfos, List<DealerOrder> relatedDealerOrders) {
        super(dealerOrder, carResponse.getCarModel(), carResponse.getCarBrand(), dealerOrderPriceInfos, relatedDealerOrders);
        this.setCarNo(carResponse.getCarNo());
        this.setBuChangeMasterId(carResponse.getBuChangeMasterId());
        this.custSource = CustSource.SEALAND.getSource();
        this.carState = Optional.ofNullable(carResponse.getCarState()).map(Enum::name).orElse(null);
        // TODO 確認是否要使用性別與是否外國人
//        this.isForeigner = dealerOrder.getIsForeigner();
//        this.gender = authUser.getGender();
        if (carBaseInfoSearchResponse != null) {
            this.buId = carBaseInfoSearchResponse.getBuId();
            this.carBuName = Optional.ofNullable(BuIdEnum.ofEnum(carBaseInfoSearchResponse.getBuId())).map(BuIdEnum::getName).orElse(null);
        }
        if (contractSearchRep != null) {
            this.replaceCode = Optional.of(contractSearchRep).map(ContractSearchRep::getDachang).orElse("0");
        }
    }
}
