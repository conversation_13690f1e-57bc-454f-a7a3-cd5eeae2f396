package com.carplus.subscribe.model.response.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SignModel {

    @Schema(description = "簽名網址")
    private String url;

    @Schema(description = "簽名時間")
    private Date signDate;

    @Schema(description = "是否代理人簽名")
    private boolean isAgentSign;

    public SignModel(String url) {
        this.url = url;
    }

}
