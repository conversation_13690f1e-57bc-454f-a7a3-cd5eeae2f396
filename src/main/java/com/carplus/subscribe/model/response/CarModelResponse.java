package com.carplus.subscribe.model.response;

import carplus.common.utils.BeanUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.enums.CarDefine;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;

import javax.persistence.Tuple;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@FieldNameConstants
@AllArgsConstructor
@Data
@Slf4j
public class CarModelResponse {

    @Schema(description = "廠牌代碼")
    private String brandCode;
    @Schema(description = "廠牌名稱")
    private String brandName;
    @Schema(description = "廠牌英文名稱")
    private String brandNameEn;
    @Schema(description = "車型名稱")
    private String carModelName;
    @Schema(description = "車型代碼")
    private String carModelCode;
    @Schema(description = "車型種類")
    private int carKind;
    @Schema(description = "車型種類名稱")
    private String carKindName;
    @Schema(description = "車型照片")
    private List<CarModelImageResponse> images;
    @Schema(description = "已刪除")
    private int isDeleted;
    @Schema(description = "短租車型代碼")
    private String originalCarModelCode;

    public CarModelResponse(@NonNull CarModel cm, List<CarModelImage> images, String gcsUrl, String brandName) {
        BeanUtils.copyProperties(cm, this);
        Optional.ofNullable(brandName).ifPresent(this::setBrandName);
        Optional.ofNullable(cm.getCarKind()).map(ck -> ck.getCode()).ifPresent(this::setCarKind);
        Optional.ofNullable(cm.getCarKind()).map(ck -> ck.getName()).ifPresent(this::setCarKindName);
        Optional.ofNullable(cm.isDeleted()).map(d -> cm.isDeleted() ? 1 : 0).ifPresent(this::setIsDeleted);
        Optional.ofNullable(images)
            .map(imgs -> imgs.stream().map(img -> new CarModelImageResponse(img, gcsUrl)).collect(Collectors.toList())).ifPresent(this::setImages);
    }

    public CarModelResponse(@NonNull Tuple t, @NonNull ObjectMapper mapper, String gcsUrl) {
        Optional.ofNullable(t.get(Fields.brandCode)).map(bc -> (String) bc).ifPresent(this::setBrandCode);
        Optional.ofNullable(t.get(Fields.brandName)).map(bn -> (String) bn).ifPresent(this::setBrandName);
        Optional.ofNullable(t.get(Fields.carModelName)).map(cmn -> (String) cmn).ifPresent(this::setCarModelName);
        Optional.ofNullable(t.get(Fields.carModelCode)).map(cmc -> (String) cmc).ifPresent(this::setCarModelCode);
        Optional.ofNullable(t.get(Fields.brandNameEn)).map(cmc -> (String) cmc).ifPresent(this::setBrandNameEn);
        Optional.ofNullable(t.get(Fields.carKind)).map(ck -> ((Byte) ck).intValue()).ifPresent(this::setCarKind);
        Optional.ofNullable(t.get(Fields.carKind)).map(ck -> CarDefine.CarKind.of(((Byte) ck).intValue()).getName()).ifPresent(this::setCarKindName);
        Optional.ofNullable(t.get(Fields.isDeleted)).map(isd -> ((Byte) isd).intValue()).ifPresent(this::setIsDeleted);
        Optional.ofNullable(t.get(Fields.originalCarModelCode)).map(occ -> (String) occ).ifPresent(this::setOriginalCarModelCode);
        Optional.ofNullable(t.get("images")).map(imgStr -> {
            String imgJson = (String) imgStr;
            try {
                List<CarModelImageResponse> imageResponses = Arrays.asList(mapper.readValue(imgJson, CarModelImageResponse[].class));
                return imageResponses.stream().map(imgr -> new CarModelImageResponse(new CarModelImage(imgr.getYear(), this.getCarModelCode(), imgr.getPaths()), gcsUrl))
                    .collect(Collectors.toList());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }).ifPresent(this::setImages);
    }
}
