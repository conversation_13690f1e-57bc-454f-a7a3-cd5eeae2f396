package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DealerOrderQueryResponse {

    @Schema(description = "經銷商訂單編號")
    private String orderNo;

    @Schema(description = "訂車人資訊ID")
    private Long dealerUserId;

    @Schema(description = "經銷商名稱")
    private String dealerName;

    @Schema(description = "訂單狀態")
    private Integer orderStatus;

    @Schema(description = "是否新訂單")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信")
    private Boolean isAudit;

    @Schema(description = "是否支付保證金")
    private Boolean isPaySecurityDeposit;

    @Schema(description = "保證金支付時間")
    private Instant securityDepositDate;

    @Schema(description = "母約編號")
    private String parentOrderNo;

    @Schema(description = "續約編號")
    private String nextStageOrderNo;

    @Schema(description = "期數")
    private String stage;

    @Schema(description = "長租契約編號")
    private String lrentalContractNo;

    @Schema(description = "是否投保免責險")
    private boolean disclaimer;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "保險ID")
    private String insuranceId;

    @Schema(description = "訂車人資訊")
    private DealerCustomerInfoForResponse customerInfo;

    @Schema(description = "訂閱方案資訊")
    private DealerSubscriptionInfoForResponse subscriptionInfo;

    @Schema(description = "款項明細")
    private String infoDetail;

    @Schema(description = "是否取消")
    private Boolean isCancel;

    @Schema(description = "取消時間")
    private Instant cancelDate;

    @Schema(description = "取消備註")
    private String cancelRemark;

    @Schema(description = "是否同意建立格上會員")
    private Boolean isCreateAccount;

    @Schema(description = "建立日期時間")
    private Instant createDate;

    @Schema(description = "更新日期時間")
    private Instant updateDate;

    @Schema(description = "廠牌英文名稱")
    private String brandNameEn;

    @Schema(description = "車型名稱")
    private String carModelName;

    @Schema(description = "經銷商訂單費用資訊")
    private List<DealerOrderPriceInfo> dealerOrderPriceInfos;

    @Schema(description = "關聯訂單資料")
    private List<DealerOrder> relatedDealerOrders;

    public DealerOrderQueryResponse(DealerOrder dealerOrder) {
        this.orderNo = dealerOrder.getOrderNo();
        this.dealerName = dealerOrder.getDealerName();
        this.orderStatus = dealerOrder.getOrderStatus();
        this.isNewOrder = dealerOrder.getIsNewOrder();
        this.isAudit = dealerOrder.getIsAudit();
        this.isPaySecurityDeposit = dealerOrder.getIsPaySecurityDeposit();
        this.securityDepositDate = dealerOrder.getSecurityDepositDate();
        this.parentOrderNo = dealerOrder.getParentOrderNo();
        this.nextStageOrderNo = dealerOrder.getNextStageOrderNo();
        this.stage = dealerOrder.getStage();
        this.lrentalContractNo = dealerOrder.getLrentalContractNo();
        this.disclaimer = dealerOrder.isDisclaimer();
        this.plateNo = dealerOrder.getPlateNo();
        this.insuranceId = dealerOrder.getInsuranceId();
        this.dealerUserId = dealerOrder.getDealerUserId();
        this.customerInfo = new DealerCustomerInfoForResponse();
        this.customerInfo.setDealerUserId(dealerOrder.getDealerUserId());
        this.subscriptionInfo = new DealerSubscriptionInfoForResponse();
        this.subscriptionInfo.setExpectDepartStation(dealerOrder.getExpectDepartStation());
        this.subscriptionInfo.setExpectReturnStation(dealerOrder.getExpectReturnStation());
        this.subscriptionInfo.setDepartStation(dealerOrder.getDepartStation());
        this.subscriptionInfo.setReturnStation(dealerOrder.getReturnStation());
        this.subscriptionInfo.setExpectDepartDate(dealerOrder.getExpectDepartDate());
        this.subscriptionInfo.setExpectReturnDate(dealerOrder.getExpectReturnDate());
        this.subscriptionInfo.setDepartDate(dealerOrder.getDepartDate());
        this.subscriptionInfo.setReturnDate(dealerOrder.getReturnDate());
        this.subscriptionInfo.setSecurityDeposit(dealerOrder.getSecurityDeposit());
        this.subscriptionInfo.setMonthlyFee(dealerOrder.getMonthlyFee());
        this.subscriptionInfo.setActualMileageRate(dealerOrder.getActualMileageRate());
        this.subscriptionInfo.setSubscribeMonth(dealerOrder.getSubscribeMonth());
        this.infoDetail = dealerOrder.getInfoDetail();
        this.subscriptionInfo.setBeginAmt(dealerOrder.getBeginAmt());
        this.subscriptionInfo.setCloseAmt(dealerOrder.getCloseAmt());
        this.subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt());
        this.subscriptionInfo.setPaidAmt(dealerOrder.getPaidAmt());
        this.isCancel = dealerOrder.getIsCancel();
        this.cancelDate = dealerOrder.getCancelDate();
        this.cancelRemark = dealerOrder.getCancelRemark();
        this.isCreateAccount = dealerOrder.getIsCreateAccount();
        this.createDate = dealerOrder.getInstantCreateDate();
        this.updateDate = dealerOrder.getInstantUpdateDate();
    }

    public DealerOrderQueryResponse(DealerOrder dealerOrder, CarModel carModel, CarBrand carBrand, List<DealerOrderPriceInfo> dealerOrderPriceInfos, List<DealerOrder> relatedDealerOrders) {
        this(dealerOrder);
        this.brandNameEn = Optional.ofNullable(carBrand).map(CarBrand::getBrandNameEn).orElse(null);
        this.carModelName = Optional.ofNullable(carModel).map(CarModel::getCarModelName).orElse(null);
        this.dealerOrderPriceInfos = dealerOrderPriceInfos;
        this.relatedDealerOrders = relatedDealerOrders;
    }

    public DealerOrder toEntity() {
        return DealerOrder.builder()
            .orderNo(orderNo)
            .dealerName(dealerName)
            .orderStatus(orderStatus)
            .isNewOrder(isNewOrder)
            .isAudit(isAudit)
            .isPaySecurityDeposit(isPaySecurityDeposit)
            .securityDepositDate(securityDepositDate)
            .parentOrderNo(parentOrderNo)
            .nextStageOrderNo(nextStageOrderNo)
            .stage(stage)
            .lrentalContractNo(lrentalContractNo)
            .plateNo(plateNo)
            .insuranceId(insuranceId)
            .dealerUserId(dealerUserId)
            .expectDepartStation(subscriptionInfo.getExpectDepartStation())
            .expectReturnStation(subscriptionInfo.getExpectReturnStation())
            .departStation(subscriptionInfo.getDepartStation())
            .returnStation(subscriptionInfo.getReturnStation())
            .expectDepartDate(subscriptionInfo.getExpectDepartDate())
            .expectReturnDate(subscriptionInfo.getExpectReturnDate())
            .departDate(subscriptionInfo.getDepartDate())
            .returnDate(subscriptionInfo.getReturnDate())
            .securityDeposit(subscriptionInfo.getSecurityDeposit())
            .monthlyFee(subscriptionInfo.getMonthlyFee())
            .actualMileageRate(subscriptionInfo.getActualMileageRate())
            .originalMileageRate(subscriptionInfo.getOriginalMileageRate())
            .subscribeMonth(subscriptionInfo.getSubscribeMonth())
            .infoDetail(infoDetail)
            .totalAmt(subscriptionInfo.getTotalAmt())
            .paidAmt(subscriptionInfo.getPaidAmt())
            .isReturned(subscriptionInfo.getIsReturned())
            .isCancel(isCancel)
            .cancelDate(cancelDate)
            .cancelRemark(cancelRemark)
            .isCreateAccount(isCreateAccount)
            .monthlyFeeDiscount(subscriptionInfo.getMonthlyFeeDiscount())
            .mileageRateDiscount(subscriptionInfo.getMileageRateDiscount())
            .prepaidMonths(subscriptionInfo.getPrepaidMonths())
            .prepaidMileage(subscriptionInfo.getPrepaidMileage())
            .prepaidMileageDiscount(subscriptionInfo.getPrepaidMileageDiscount())
            .flexibleMileage(subscriptionInfo.getFlexibleMileage())
            .actualMileageUsed(subscriptionInfo.getActualMileageUsed())
            .offsetMileage1(subscriptionInfo.getOffsetMileage1())
            .offsetMileage2(subscriptionInfo.getOffsetMileage2())
            .prepaidMileageFee(subscriptionInfo.getPrepaidMileageFee())
            .beginAmt(subscriptionInfo.getBeginAmt())
            .closeAmt(subscriptionInfo.getCloseAmt())
            .build();
    }
}
