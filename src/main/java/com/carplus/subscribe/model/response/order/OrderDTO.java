package com.carplus.subscribe.model.response.order;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Data;
import org.hibernate.Hibernate;

@Data
public class OrderDTO {
    private Orders orders;
    private Contract contract;
    private MainContract mainContract;
    private CarModel carModel;
    private CarBrand carBrand;
    private Cars car;

    public OrderDTO(Orders orders, Contract contract, MainContract mainContract, CarModel carModel, CarBrand carBrand, Cars car) {
        this.orders = orders;
        this.contract = contract;
        this.mainContract = mainContract;
        this.carModel = Hibernate.isInitialized(carModel) ? carModel : null;
        this.carBrand = Hibernate.isInitialized(carBrand) ? carBrand : null;
        this.car = Hibernate.isInitialized(car) ? car : null;
    }
}
