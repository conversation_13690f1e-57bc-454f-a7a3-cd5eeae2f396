package com.carplus.subscribe.model.response.campaign;

import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.Instant;

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class CampaignInternalBaseResponse extends CampaignCommonBaseResponse {

    @Schema(description = "活動開始時間")
    private Instant startDate;

    @Schema(description = "活動結束時間")
    private Instant endDate;

    @JsonProperty("isDeleted")
    @Schema(description = "是否已刪除")
    private boolean isDeleted;

    @Schema(description = "建立時間")
    private Instant createDate;

    @Schema(description = "最後異動時間")
    private Instant updateDate;

    @Schema(description = "最後異動人員")
    private String updater;

    @Schema(description = "最後異動人員名稱")
    private String updaterName;

    public CampaignInternalBaseResponse(Campaign campaign, String updaterName) {
        super(campaign);
        this.startDate = campaign.getStartDate();
        this.endDate = campaign.getEndDate();
        this.isDeleted = campaign.isDeleted();
        this.createDate = campaign.getInstantCreateDate();
        this.updateDate = campaign.getInstantUpdateDate();
        this.updater = campaign.getUpdater();
        this.updaterName = updaterName;
    }
}
