package com.carplus.subscribe.model.response.campaign;

import com.carplus.subscribe.db.mysql.entity.Campaign;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class CampaignCommonBaseResponse {

    @Schema(description = "活動編號")
    private Integer id;

    @Schema(description = "活動標題")
    private String title;

    public CampaignCommonBaseResponse(Campaign campaign) {
        this.id = campaign.getId();
        this.title = campaign.getTitle();
    }
}
