package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.model.request.dealer.BaseDealerCustomerInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DealerCustomerInfoForResponse extends BaseDealerCustomerInfo {

    @Schema(description = "訂車人性別")
    private String gender;
}
