package com.carplus.subscribe.model.response.invoice;

import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
public class InvoicesWithAcctId {

    private Integer acctId;

    private String carrierId;

    private String companyId;

    private String companyTitle;

    private int defaultInvoiceType;

    private String donateCompanyId;

    private String donateCompanyTitle;

    private boolean printInvoice;

    private Date settingTime;

    public static InvoicesWithAcctId fromInvoiceEntry(Map.Entry<Integer, Invoices> invoicesEntry) {
        return new InvoicesWithAcctId(
                invoicesEntry.getKey(),
                invoicesEntry.getValue().getInvoice().getCarrierID(),
                invoicesEntry.getValue().getInvoice().getCompanyId(),
                invoicesEntry.getValue().getInvoice().getCompanyTitle(),
                invoicesEntry.getValue().getInvoice().getDefaultInvoiceType(),
                invoicesEntry.getValue().getInvoice().getDonateCompanyId(),
                invoicesEntry.getValue().getInvoice().getDonateCompanyTitle(),
                invoicesEntry.getValue().getInvoice().isPrintInvoice(),
                invoicesEntry.getValue().getCreatedAt()
        );
    }
}
