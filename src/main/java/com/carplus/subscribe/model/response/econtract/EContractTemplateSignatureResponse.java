package com.carplus.subscribe.model.response.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateSignatureResponse {

    @Schema(description = "x軸座標")
    private Double x;

    @Schema(description = "y軸座標")
    private Double y;

    @Schema(description = "電子簽名檔寬度")
    private Double signatureWidth;

    @Schema(description = "電子簽名檔高度")
    private Double signatureHeight;

    @Schema(description = "紙張寬度")
    private Double pageWidth;

    @Schema(description = "紙張高度")
    private Double pageHeight;

}
