package com.carplus.subscribe.model.response.order;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.RenewableType;
import com.carplus.subscribe.enums.SubOrderStatus;
import com.carplus.subscribe.model.order.CommonOrderPriceInfoResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.Optional;

@Data
public class UserOrderResponse {
    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "合約編號")
    private String contractNo;
    @Schema(description = "預計開始時間")
    private Instant expectStartDate;
    @Schema(description = "預計結束時間")
    private Instant expectEndDate;
    @Schema(description = "實際開始時間")
    private Instant startDate;
    @Schema(description = "實際結束時間")
    private Instant endDate;
    @Schema(description = "月份")
    private int month;
    @Schema(description = "狀態")
    private int status;
    @Schema(description = "訂單狀態")
    private SubOrderStatus subOrderStatus;
    @Schema(description = "是否需要付款")
    private boolean isNeedPay;
    @Schema(description = "是否可續約")
    private RenewableType isRenewable;
    @Schema(description = "里程費率")
    private Double mileageFee;

    public String getSubOrderStatusName() {
        return Optional.ofNullable(subOrderStatus).map(SubOrderStatus::getName).orElse(null);
    }

    public UserOrderResponse(Orders order, CommonOrderPriceInfoResponse priceInfo, RenewableType isRenewable) {
        this.orderNo = order.getOrderNo();
        this.contractNo = order.getContractNo();
        this.expectStartDate = order.getExpectStartDate();
        this.expectEndDate = order.getExpectEndDate();
        this.startDate = order.getStartDate();
        this.endDate = order.getEndDate();
        this.month = order.getMonth();
        this.status = order.getStatus();
        if (priceInfo.getAmount() > 0 && OrderStatus.of(order.getStatus()).isNeedPay()) {
            this.isNeedPay = true;
        }
        this.isRenewable = isRenewable;
        this.subOrderStatus = SubOrderStatus.getByOrder(order);
        this.mileageFee = order.getContract().getMainContract().getOriginalPriceInfo().getMileageFee();
    }
}
