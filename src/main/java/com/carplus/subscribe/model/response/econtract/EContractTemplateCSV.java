package com.carplus.subscribe.model.response.econtract;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ConditionOrderSource;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateCSV {

    private static final String TIME_ZONE = "Asia/Taipei";

    @Schema(description = "範本代碼")
    @Parsed(field = "範本代碼")
    private String templateCode;

    @Schema(description = "版本號")
    @Parsed(field = "版本號")
    private String versionId;

    @Schema(description = "範本名稱-營業")
    @Parsed(field = "範本名稱-營業")
    private String templateNameSales;

    @Schema(description = "範本名稱-官網")
    @Parsed(field = "範本名稱-官網")
    private String templateNameCust;

    @Schema(description = "訂單來源")
    @Parsed(field = "訂單來源")
    private String conditionOrderSource;

    @Schema(description = "訂閱租期")
    @Parsed(field = "訂閱租期")
    private Integer conditionOrderMonth;

    @Schema(description = "車輛所屬")
    @Parsed(field = "車輛所屬")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌")
    @Parsed(field = "車輛廠牌")
    private String conditionCarBrand;

    @Schema(description = "訂閱類別")
    @Parsed(field = "訂閱類別")
    private String conditionCarState;

    @Schema(description = "車輛牌價起始")
    @Parsed(field = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @Schema(description = "車輛牌價結束")
    @Parsed(field = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "檔案ID")
    private Integer UploadFileId;

    @Schema(description = "顯示用檔案名稱")
    private List<String> DisplayFilename;

    @Schema(description = "啟用時間")
    @Parsed(field = "啟用時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date enableDate;

    @Schema(description = "修訂說明")
    @Parsed(field = "修訂說明")
    private String reviseMemo;

    @Schema(description = "是否有免責費用")
    @Parsed(field = "是否有免責費用")
    private boolean isDisclaimerFee;

    @Schema(description = "是否冰宇車")
    @Parsed(field = "是否冰宇車")
    private boolean isSeaLandCar;

    @Schema(description = "車型")
    @Parsed(field = "車型")
    private String conditionCarModel;

    @Schema(description = "更新人員編號")
    @Parsed(field = "更新人員編號")
    private String updateMemberId;

    @Schema(description = "更新人員")
    @Parsed(field = "更新人員")
    private String updateUser;

    @Schema(description = "更新日期時間")
    @Parsed(field = "更新日期時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date updateDate;

    public EContractTemplateCSV(EContractTemplate econtractTemplate, CarBrand carBrand, CarModel carModel) {
        this.templateCode = econtractTemplate.getTemplateCode();
        this.versionId = econtractTemplate.getVersionId();
        this.templateNameSales = econtractTemplate.getTemplateNameSales();
        this.templateNameCust = econtractTemplate.getTemplateNameCust();
        this.conditionOrderSource = Optional.ofNullable(econtractTemplate.getConditionOrderSource()).map(ConditionOrderSource::codeOfValue).map(ConditionOrderSource::getDesc).orElse(null);
        this.conditionOrderMonth = econtractTemplate.getConditionOrderMonth();
        this.conditionCarBu = Optional.of(Integer.valueOf(econtractTemplate.getConditionCarBu())).map(BuIdEnum::ofEnum).map(BuIdEnum::getName).orElse(null);
        this.conditionCarBrand = carBrand.getBrandNameEn();
        this.conditionCarState = Optional.ofNullable(econtractTemplate.getConditionCarState()).map(CarDefine.CarState::valueOf).map(CarDefine.CarState::getName).orElse(null);
        this.conditionCarPriceStart = econtractTemplate.getConditionCarPriceStart();
        this.conditionCarPriceEnd = econtractTemplate.getConditionCarPriceEnd();
        this.enableDate = Optional.ofNullable(econtractTemplate.getEnableDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
        this.reviseMemo = econtractTemplate.getReviseMemo();
        this.updateMemberId = econtractTemplate.getUpdateUser();
        this.updateDate = econtractTemplate.getUpdateDate();
        this.isDisclaimerFee = econtractTemplate.isDisclaimerFee();
        this.isSeaLandCar = econtractTemplate.isSeaLandCar();
        Optional.ofNullable(econtractTemplate.getConditionCarModel()).ifPresent(model -> {
            this.conditionCarModel = String.format("%s(%s)", carModel.getCarModelName(), model);
        });
    }

}
