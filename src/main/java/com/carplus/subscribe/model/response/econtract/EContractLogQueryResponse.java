package com.carplus.subscribe.model.response.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractLogQueryResponse {

    @Schema(description = "合約檔案編號")
    private String eContractNo;

    @Schema(description = "合約檔案類型")
    private Integer eContractType;

    @Schema(description = "合約檔案名稱")
    private String eContractName;

    @Schema(description = "範本代碼")
    private String templateId;

    @Schema(description = "範本版本號")
    private String versionId;

    @Schema(description = "合約範本下載url")
    private List<String> templateFileDownloadUrl;

    @Schema(description = "收件者信箱")
    private String email;

    @Schema(description = "修訂說明")
    private String reviseMemo;

    @Schema(description = "寄送人員")
    private String createUser;

    @Schema(description = "更新人員")
    private String updateUser;

}
