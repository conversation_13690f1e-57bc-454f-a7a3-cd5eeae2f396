package com.carplus.subscribe.model.response.subscribelevel;

import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Data
public class SubscribeLevelInternalResponse extends SubscribeLevelCommonResponse {

    @Schema(description = "是否刪除 0:無刪除 1:刪除")
    private boolean isDeleted;

    @Schema(description = "建立時間")
    private Instant createDate;

    @Schema(description = "最後異動時間")
    private Instant updateDate;

    @Schema(description = "建立人員工號")
    private String createdBy;

    @Schema(description = "最後異動人員工號")
    private String updatedBy;

    @Schema(description = "建立人員名稱")
    private String creatorName;

    @Schema(description = "最後異動人員名稱")
    private String updaterName;

    public SubscribeLevelInternalResponse(SubscribeLevel subscribeLevel, String discountLevelName, String creatorName, String updaterName) {
        super(subscribeLevel, discountLevelName);
        this.isDeleted = subscribeLevel.isDeleted();
        this.createDate = subscribeLevel.getInstantCreateDate();
        this.updateDate = subscribeLevel.getInstantUpdateDate();
        this.createdBy = subscribeLevel.getCreatedBy();
        this.updatedBy = subscribeLevel.getUpdatedBy();
        this.creatorName = creatorName;
        this.updaterName = updaterName;
    }
}
