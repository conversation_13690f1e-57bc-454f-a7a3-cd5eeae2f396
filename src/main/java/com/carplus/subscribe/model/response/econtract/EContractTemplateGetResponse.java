package com.carplus.subscribe.model.response.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateGetResponse {

    @Schema(description = "範本編號")
    private Integer templateId;

    @Schema(description = "範本代碼")
    private String templateCode;

    @Schema(description = "版本號")
    private String versionId;

    @Schema(description = "呈現名稱")
    private String displayFilename;

    @Schema(description = "範本名稱-營業")
    private String templateNameSales;

    @Schema(description = "範本名稱-官網")
    private String templateNameCust;

    @Schema(description = "訂單來源")
    private Integer conditionOrderSource;

    @Schema(description = "訂閱租期")
    private Integer conditionOrderMonth;

    @Schema(description = "車輛所屬")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌")
    private String conditionCarBrand;

    @Schema(description = "訂閱類別")
    private String conditionCarState;

    @Schema(description = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @Schema(description = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "啟用時間")
    private Instant enableDate;

    @Schema(description = "修訂說明")
    private String reviseMemo;

    @Schema(description = "檔案ID")
    private Integer uploadFileId;

    @Schema(description = "建立人員")
    private String createUser;

    @Schema(description = "建立日期時間")
    private Instant createDate;

    @Schema(description = "更新人員")
    private String updateUser;

    @Schema(description = "更新日期時間")
    private Instant updateDate;

    @Schema(description = "是否有免責費用")
    private boolean isDisclaimerFee;

    @Schema(description = "是否冰宇車")
    private boolean isSeaLandCar;

    @Schema(description = "車型")
    private String conditionCarModel;

}
