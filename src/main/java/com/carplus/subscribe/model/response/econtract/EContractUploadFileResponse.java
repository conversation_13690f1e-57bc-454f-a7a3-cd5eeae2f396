package com.carplus.subscribe.model.response.econtract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Column;

@Data
public class EContractUploadFileResponse extends GeneralEntity {

    private Integer id;

    @Schema(description = "檔名")
    @Column(name = "filename")
    private String filename;

    @Schema(description = "MIME type")
    @Column(name = "mediaType")
    private String mediaType;

    @Schema(description = "檔案顯示名稱")
    @Column(name = "displayFilename")
    private String displayFilename;
}