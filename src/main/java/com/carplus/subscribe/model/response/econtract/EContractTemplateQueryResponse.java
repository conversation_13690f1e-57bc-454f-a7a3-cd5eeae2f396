package com.carplus.subscribe.model.response.econtract;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ConditionOrderSource;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;
import java.util.Optional;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateQueryResponse {

    @Schema(description = "範本編號")
    private Integer templateId;

    @Schema(description = "範本代碼")
    private String templateCode;

    @Schema(description = "版本號")
    private String versionId;

    @Schema(description = "範本名稱-營業")
    private String templateNameSales;

    @Schema(description = "範本名稱-官網")
    private String templateNameCust;

    @Schema(description = "訂單來源代碼")
    private Integer conditionOrderSourceCode;

    @Schema(description = "訂單來源名稱")
    private String conditionOrderSourceName;

    @Schema(description = "訂閱租期")
    private Integer conditionOrderMonth;

    @Schema(description = "車輛所屬代碼")
    private String conditionCarBuCode;

    @Schema(description = "車輛所屬名稱")
    private String conditionCarBuName;

    @Schema(description = "車輛廠牌代碼")
    private String conditionCarBrandCode;

    @Schema(description = "車輛廠牌名稱")
    private String conditionCarBrandName;

    @Schema(description = "訂閱類別代碼")
    private String conditionCarStateCode;

    @Schema(description = "訂閱類別名稱")
    private String conditionCarStateName;

    @Schema(description = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @Schema(description = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "檔案ID")
    private Integer uploadFileId;

    @Schema(description = "顯示用檔案名稱")
    private String displayFilename;

    @Schema(description = "啟用時間")
    private Instant enableDate;

    @Schema(description = "修訂說明")
    private String reviseMemo;

    @Schema(description = "建立人員編號")
    private String createMemberId;

    @Schema(description = "是否有免責費用")
    private boolean isDisclaimerFee;

    @Schema(description = "是否冰宇車")
    private Boolean isSeaLandCar;

    @Schema(description = "車型")
    private String conditionCarModel;

    @Schema(description = "建立人員名稱")
    private String createUser;

    @Schema(description = "建立日期時間")
    private Instant createDate;

    @Schema(description = "更新人員編號")
    private String updateMemberId;

    @Schema(description = "更新人員名稱")
    private String updateUser;

    @Schema(description = "更新日期時間")
    private Instant updateDate;

    @Schema(description = "狀態")
    @JsonProperty("isEnabled")
    public boolean isEnabled;

    public EContractTemplateQueryResponse(EContractTemplate econtractTemplate, CarBrand carBrand, Boolean queryEnabled) {
        this.templateId = econtractTemplate.getTemplateId();
        this.displayFilename = econtractTemplate.getDisplayFilename();
        this.uploadFileId = econtractTemplate.getUploadFileId();
        this.templateCode = econtractTemplate.getTemplateCode();
        this.versionId = econtractTemplate.getVersionId();
        this.templateNameSales = econtractTemplate.getTemplateNameSales();
        this.templateNameCust = econtractTemplate.getTemplateNameCust();
        this.conditionOrderSourceCode = econtractTemplate.getConditionOrderSource();
        this.conditionOrderSourceName = Optional.ofNullable(econtractTemplate.getConditionOrderSource()).map(ConditionOrderSource::codeOfValue).map(ConditionOrderSource::getDesc).orElse(null);
        this.conditionOrderMonth = econtractTemplate.getConditionOrderMonth();
        this.conditionCarBuCode = econtractTemplate.getConditionCarBu();
        this.conditionCarBuName = Optional.of(Integer.valueOf(econtractTemplate.getConditionCarBu())).map(BuIdEnum::ofEnum).map(BuIdEnum::getName).orElse(null);
        this.conditionCarBrandCode = econtractTemplate.getConditionCarBrand();
        this.conditionCarBrandName = carBrand.getBrandNameEn();
        this.conditionCarStateCode = econtractTemplate.getConditionCarState();
        this.conditionCarStateName = Optional.ofNullable(econtractTemplate.getConditionCarState()).map(CarDefine.CarState::valueOf).map(CarDefine.CarState::getName).orElse(null);
        this.conditionCarPriceStart = econtractTemplate.getConditionCarPriceStart();
        this.conditionCarPriceEnd = econtractTemplate.getConditionCarPriceEnd();
        this.enableDate = econtractTemplate.getEnableDate();
        this.reviseMemo = econtractTemplate.getReviseMemo();
        this.isDisclaimerFee = econtractTemplate.isDisclaimerFee();
        this.isSeaLandCar = econtractTemplate.isSeaLandCar();
        this.conditionCarModel = econtractTemplate.getConditionCarModel();
        this.createMemberId = econtractTemplate.getCreateUser();
        this.createDate = econtractTemplate.getInstantCreateDate();
        this.updateMemberId = econtractTemplate.getUpdateUser();
        this.updateDate = econtractTemplate.getInstantUpdateDate();
        this.isEnabled = queryEnabled != null && queryEnabled;
    }
}
