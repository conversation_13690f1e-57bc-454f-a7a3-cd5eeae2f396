package com.carplus.subscribe.model.response.subscribelevel;

import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class SubscribeLevelCommonResponse {

    @Schema(description = "訂閱車方案編號")
    private Integer id;

    @Schema(description = "訂閱車方案")
    private int level;

    @Schema(description = "訂閱車方案名稱")
    private String name;

    @Schema(description = "對照超激優惠方案")
    private Integer discountLevel;

    @Schema(description = "對照超激優惠方案名稱")
    private String discountLevelName;

    @Schema(description = "保證金")
    private int securityDeposit;

    @Schema(description = "基本月費")
    private int monthlyFee;

    @Schema(description = "里程費")
    private double mileageFee;

    @Schema(description = "是否自動授信")
    private boolean autoCredit;

    @Schema(description = "續訂折扣")
    private List<Double> renewalDiscountRate;

    @Schema(description = "里程數優惠")
    private List<MileageDiscount> mileageDiscount;

    @Schema(description = "優惠價")
    private int discountMonthlyFee;

    @Schema(description = "方案類型")
    private SubscribeType type;

    public SubscribeLevelCommonResponse(SubscribeLevel subscribeLevel, String discountLevelName) {
        this.id = subscribeLevel.getId();
        this.level = subscribeLevel.getLevel();
        this.name = subscribeLevel.getName();
        this.discountLevel = subscribeLevel.getDiscountLevel();
        this.discountLevelName = discountLevelName;
        this.securityDeposit = subscribeLevel.getSecurityDeposit();
        this.monthlyFee = subscribeLevel.getMonthlyFee();
        this.mileageFee = subscribeLevel.getMileageFee();
        this.autoCredit = subscribeLevel.isAutoCredit();
        this.mileageDiscount = subscribeLevel.getMileageDiscount();
        this.discountMonthlyFee = subscribeLevel.getDiscountMonthlyFee();
        this.type = subscribeLevel.getType();
    }
}
