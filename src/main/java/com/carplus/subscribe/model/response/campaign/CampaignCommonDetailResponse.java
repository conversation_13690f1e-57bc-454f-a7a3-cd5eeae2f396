package com.carplus.subscribe.model.response.campaign;

import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.model.gosmart.BannerCategory;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CampaignCommonDetailResponse extends CampaignCommonBaseResponse {

    @Schema(description = "活動說明")
    private String description;

    @Schema(description = "車輛顯示條件")
    private CarsCondition carsCondition;

    @Schema(description = "橫幅類別")
    private BannerCategory bannerCategory;

    public CampaignCommonDetailResponse(Campaign campaign, BannerCategory bannerCategory) {
        super(campaign);
        this.description = campaign.getDescription();
        this.carsCondition = campaign.getCarsCondition();
        this.bannerCategory = bannerCategory;
    }
}
