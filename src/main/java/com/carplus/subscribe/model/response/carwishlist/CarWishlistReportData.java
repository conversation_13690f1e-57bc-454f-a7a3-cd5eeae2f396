package com.carplus.subscribe.model.response.carwishlist;

import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

@Data
public class CarWishlistReportData {

    private static final String TIME_ZONE = "Asia/Taipei";
    
    /**
     * 客戶姓名
     */
    @Parsed(field = "客戶姓名")
    private String customerName;
    
    /**
     * 手機號碼
     */
    @Parsed(field = "手機號碼")
    private String phoneNumber;
    
    /**
     * 收藏總台數 (excluding deleted items)
     */
    @Parsed(field = "收藏總台數")
    private Long totalWishlistCount;
    
    /**
     * 加入收藏日期 (car_wishlist.updateDate)
     */
    @Parsed(field = "加入收藏日期")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date wishlistAddDate;
    
    /**
     * 車牌號碼
     */
    @Parsed(field = "車牌號碼")
    private String plateNo;
    
    /**
     * 廠牌名稱
     */
    @Parsed(field = "廠牌名稱")
    private String brandName;
    
    /**
     * 車型名稱
     */
    @Parsed(field = "車型名稱")
    private String carModelName;
    
    /**
     * 訂閱類別
     */
    @Parsed(field = "訂閱類別")
    private String carState;
    
    /**
     * 出廠年份
     */
    @Parsed(field = "出廠年份")
    private String mfgYear;
    
    /**
     * 訂閱方案名稱
     */
    @Parsed(field = "訂閱方案名稱")
    private String subscribeLevelName;
    
    /**
     * 庫存狀態
     */
    @Parsed(field = "庫存狀態")
    private String inventoryStatus;
    
    /**
     * 是否已訂閱
     */
    @Parsed(field = "是否已訂閱")
    private String isSubscribed;
    
    /**
     * 收藏時月費
     */
    @Parsed(field = "收藏時月費")
    private Integer monthlyFeeAtWishlist;
    
    /**
     * 收藏時里程費率
     */
    @Parsed(field = "收藏時里程費率")
    private Double mileageRateAtWishlist;
    
    /**
     * 收藏時保證金
     */
    @Parsed(field = "收藏時保證金")
    private Integer securityDepositAtWishlist;

    /**
     * CSV headers for Univocity CSV generation
     */
    public static final String[] HEADS = Arrays.stream(CarWishlistReportData.class.getDeclaredFields())
            .filter(field -> field.isAnnotationPresent(Parsed.class))
            .map(field -> field.getAnnotation(Parsed.class))
            .filter(Objects::nonNull)
            .map(parsed -> parsed.field()[0])
            .toArray(String[]::new);
}