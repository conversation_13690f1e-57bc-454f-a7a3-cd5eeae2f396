package com.carplus.subscribe.model.response.carwishlist;

import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarWishlistResponseWithSnapshot extends CarWishlistResponse {

    @Schema(description = "加入收藏時的使用月費")
    private int monthlyFeeSnapshot;

    @Schema(description = "加入收藏時的里程費")
    private double mileageFeeSnapshot;

    @Schema(description = "加入收藏時的保證金")
    private int securityDepositSnapshot;

    @Schema(description = "加入收藏時的方案類型")
    private SubscribeType typeSnapshot;

    @Schema(description = "加入收藏時的標籤id")
    private List<Integer> tagIdsSnapshot;

    @Schema(description = "加入收藏時的所在站所名稱")
    private String locationNameSnapshot;

    public CarWishlistResponseWithSnapshot(CarWishlist carWishlist, CarResponse carResponse, Map<String, Stations> stationMap) {
        super(carWishlist, carResponse, stationMap.get(carResponse.getLocationStationCode()));
        this.monthlyFeeSnapshot = carWishlist.getUseMonthlyFee();
        this.mileageFeeSnapshot = carWishlist.getMileageFee();
        this.securityDepositSnapshot = carWishlist.getSecurityDeposit();
        this.typeSnapshot = carWishlist.getType();
        this.tagIdsSnapshot = carWishlist.getTagIds();
        this.locationNameSnapshot = Optional.ofNullable(stationMap.get(carWishlist.getLocationStationCode()))
            .map(Stations::getStationName).orElse("");
    }
}

