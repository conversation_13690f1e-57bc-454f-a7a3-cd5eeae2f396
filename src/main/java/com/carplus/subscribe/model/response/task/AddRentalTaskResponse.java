package com.carplus.subscribe.model.response.task;

import com.carplus.subscribe.enums.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.Instant;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AddRentalTaskResponse {

    @Schema(description = "任務編號")
    private Integer taskId;

    @Schema(description = "任務型別")
    @Enumerated(EnumType.STRING)
    private TaskType type;

    @Schema(description = "任務優先度")
    private Integer taskPriority;

    @Schema(description = "任務狀態")
    private Integer status;

    @Schema(description = "被分派者編號")
    private String assignedId;

    @Schema(description = "被分派者名稱")
    private String assignedName;

    @Schema(description = "出/還車站點ID")
    private String stationId;

    @Schema(description = "出/還車站點名稱")
    private String stationName;

    @Schema(description = "任務預計開始時間")
    private Instant expectExecuteDate;

    @Schema(description = "實際出/還車時間")
    private Instant actualDate;

    @Schema(description = "領取任務時間")
    private Instant getAt;

    @Schema(description = "執行開始時間")
    private Instant startAt;

    @Schema(description = "執行結束時間")
    private Instant finishAt;

    @Schema(description = "執行停止時間")
    private Instant stopAt;

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "里程數")
    private Integer mileage;

    @Schema(description = "出/還車備註")
    private String remark;

    @Schema(description = "任務建立時間")
    private Instant createDate;

    @Schema(description = "任務更新時間")
    private Instant updateDate;
}
