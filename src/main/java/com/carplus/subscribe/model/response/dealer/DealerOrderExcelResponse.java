package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.model.request.dealer.DealerOrderValidateError;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DealerOrderExcelResponse {

    @Schema(description = "資料筆數")
    private Integer rowsCount;
    @Schema(description = "成功筆數")
    private Integer successCount;
    @Schema(description = "失敗筆數")
    private Integer failCount;
    @Schema(description = "錯誤訊息清單")
    private List<DealerOrderValidateError> errMessageList;
    @Schema(description = "UUID")
    private String uuid;
}
