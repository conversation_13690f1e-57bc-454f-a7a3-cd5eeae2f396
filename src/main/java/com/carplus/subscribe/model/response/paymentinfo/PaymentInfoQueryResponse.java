package com.carplus.subscribe.model.response.paymentinfo;

import com.carplus.subscribe.enums.ManualRefundMethod;
import com.carplus.subscribe.enums.ManualRefundStatus;
import com.carplus.subscribe.enums.OrderPaymentStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.util.Date;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
@Slf4j
public class PaymentInfoQueryResponse {
    private static final String TIME_ZONE = "Asia/Taipei";
    @Schema(description = "主約編號")
    @Parsed(field = "主約編號")
    private String mainContractNo;
    @Schema(description = "訂單編號")
    @Parsed(field = "訂單編號")
    private String orderNo;
    @Schema(description = "出車站點")
    @Parsed(field = "出車站點")
    private String departStation;
    @Schema(description = "還車站點")
    @Parsed(field = "還車站點")
    private String returnStation;
    @Schema(description = "會員ID")
    @Parsed(field = "會員ID")
    private int acctId;
    @Schema(description = "會員名稱")
    @Parsed(field = "會員名稱")
    private String acctName;
    @Schema(description = "主約建立日期")
    @Parsed(field = "主約建立日期")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date mainCreateDate;
    @Schema(description = "訂單出車日")
    @Parsed(field = "訂單出車日")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date orderStartDate;
    @Schema(description = "訂單狀態")
    private Short orderStatus;
    @Schema(description = "訂單狀態名稱")
    @Parsed(field = "訂單狀態")
    private String orderStatusName;
    @Schema(description = "帳務狀態")
    private Short paymentStatus;
    @Schema(description = "帳務狀態名稱")
    @Parsed(field = "帳務狀態")
    private String paymentStatusName;
    @Schema(description = "收單行")
    @Parsed(field = "收單銀行")
    private String acquirer;
    @Schema(description = "金額")
    @Parsed(field = "金額")
    private Integer amount;
    @Schema(description = "交易時間")
    @Parsed(field = "交易時間")
    private String payDate;
    @Schema(description = "款項編號")
    @Parsed(field = "款項編號")
    private BigInteger paymentId;
    @Schema(description = "保證金退款日")
    @Parsed(field = "保證金退款日")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date refundSecurityDepositDate;
    @Schema(description = "是否人工保證金退款")
    @Parsed(field = "是否人工保證金退款")
    private Boolean isManualRefundSecurityDeposit;
    @Schema(description = "人工退款狀態")
    @Parsed(field = "人工退款狀態")
    private ManualRefundStatus manualRefundStatus;
    @Schema(description = "交易序號")
    @Parsed(field = "交易序號")
    private String transactionNumber;
    @Schema(description = "備註內容")
    @Parsed(field = "備註內容")
    private String remark;
    @Schema(description = "備註更新者")
    @Parsed(field = "備註更新者")
    private String remarker;
    @Schema(description = "最後更新者")
    @Parsed(field = "最後更新者")
    private String manualRefundUpdater;
    @Schema(description = "最後更新日期")
    @Parsed(field = "最後更新日期")
    private Date manualRefundUpdateDate;
    @Schema(description = "退款方式")
    @Parsed(field = "退款方式")
    private ManualRefundMethod refundMethod;


    public ManualRefundMethod getRefundMethod() {
        if (refundMethod != null) {
            return refundMethod;
        }
        if (getPaymentStatus() == OrderPaymentStatus.EXPIRED_REFUND.getCode() && (refundSecurityDepositDate != null || manualRefundUpdateDate != null)) {
            return ManualRefundMethod.Credit;
        }
        return null;
    }
}
