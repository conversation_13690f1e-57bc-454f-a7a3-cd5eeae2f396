package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.Optional;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DealerOrderExcel implements DealerUserCityArea, DealerUser, SubscriptionInfo, HasDealerOrderNo {

    private static final String TIME_ZONE = "Asia/Taipei";

    @Schema(description = "經銷商訂單編號")
    @Parsed(field = "經銷商訂單編號")
    private String orderNo;

    @Schema(description = "訂車人資訊ID")
    private Long dealerUserId;

    @Schema(description = "經銷商名稱")
    @Parsed(field = "經銷商名稱")
    private String dealerName;

    @Schema(description = "訂單狀態")
    @Parsed(field = "訂單狀態")
    private String orderStatus;

    @Schema(description = "是否新訂單")
    @Parsed(field = "是否新訂單")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信")
    @Parsed(field = "是否通過授信")
    private Boolean isAudit;

    @Schema(description = "是否支付保證金")
    @Parsed(field = "是否支付保證金")
    private Boolean isPaySecurityDeposit;

    @Schema(description = "保證金支付時間")
    @Parsed(field = "保證金支付時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date securityDepositDate;

    @Schema(description = "母約編號")
    @Parsed(field = "母約編號")
    private String parentOrderNo;

    @Schema(description = "續約編號")
    private String nextStageOrderNo;

    @Schema(description = "前約編號")
    private String previousOrderNo;

    @Schema(description = "期數")
    @Parsed(field = "期數")
    private String stage;

    @Schema(description = "車牌號碼")
    @Parsed(field = "車牌號碼")
    private String plateNo;

    @Schema(description = "保險ID")
    @Parsed(field = "保險ID")
    private String insuranceId;

    @Schema(description = "訂車人姓名")
    @Parsed(field = "訂車人姓名")
    private String userName;

    @Schema(description = "訂車人身分ID")
    @Parsed(field = "訂車人身分ID")
    private String idNo;

    @Schema(description = "訂車人國碼")
    @Parsed(field = "訂車人國碼")
    private String nationalCode;

    @Schema(description = "訂車人電話")
    @Parsed(field = "訂車人電話")
    private String mainCell;

    @Schema(description = "訂車人生日")
    @Parsed(field = "訂車人生日")
    private String birthDay;

    @Schema(description = "訂車人信箱")
    @Parsed(field = "訂車人信箱")
    private String email;

    @Schema(description = "訂車人戶籍縣市編號")
    @Parsed(field = "訂車人戶籍縣市編號")
    private Integer city;
    @Schema(description = "訂車人戶籍縣市")
    @Parsed(field = "訂車人戶籍縣市")
    private String cityName;

    @Schema(description = "訂車人戶籍區域編號")
    @Parsed(field = "訂車人戶籍區域編號")
    private Integer area;
    @Schema(description = "訂車人戶籍區域")
    @Parsed(field = "訂車人戶籍區域")
    private String areaName;

    @Schema(description = "訂車人戶籍地址")
    @Parsed(field = "訂車人戶籍地址")
    private String address;

    @Schema(description = "法人共同承租人統一編號")
    @Parsed(field = "法人共同承租人統一編號")
    private String vatNumber;

    @Schema(description = "法人共同承租人公司抬頭")
    @Parsed(field = "法人共同承租人公司抬頭")
    private String companyName;

    @Schema(description = "法人共同承租人公司地址")
    @Parsed(field = "法人共同承租人公司地址")
    private String companyLocation;

    @Schema(description = "預定出車站點")
    @Parsed(field = "預定出車站點")
    private String expectDepartStation;
    @Schema(description = "預定出車站點名稱")
    @Parsed(field = "預定出車站點名稱")
    private String expectDepartStationName;

    @Schema(description = "預定還車站點")
    @Parsed(field = "預定還車站點")
    private String expectReturnStation;
    @Schema(description = "預定還車站點名稱")
    @Parsed(field = "預定還車站點名稱")
    private String expectReturnStationName;

    @Schema(description = "實際出車站點")
    @Parsed(field = "實際出車站點")
    private String departStation;
    @Schema(description = "實際出車站點名稱")
    @Parsed(field = "實際出車站點名稱")
    private String departStationName;

    @Schema(description = "實際還車站點")
    @Parsed(field = "實際還車站點")
    private String returnStation;
    @Schema(description = "實際還車站點名稱")
    @Parsed(field = "實際還車站點名稱")
    private String returnStationName;

    @Schema(description = "預定出車時間")
    @Parsed(field = "預定出車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date expectDepartDate;

    @Schema(description = "預定還車時間")
    @Parsed(field = "預定還車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date expectReturnDate;

    @Schema(description = "實際出車時間")
    @Parsed(field = "實際出車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date departDate;

    @Schema(description = "起租金額")
    @Parsed(field = "起租金額")
    private Integer beginAmt;

    @Schema(description = "迄租金額")
    @Parsed(field = "迄租金額")
    private Integer closeAmt;

    @Schema(description = "是否實際還車")
    @Parsed(field = "是否實際還車")
    private Boolean isReturned;

    @Schema(description = "實際還車時間")
    @Parsed(field = "實際還車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date returnDate;

    @Schema(description = "保證金")
    @Parsed(field = "保證金")
    private Integer securityDeposit;

    @Schema(description = "月費租金")
    @Parsed(field = "月費租金")
    private Integer monthlyFee;

    @Schema(description = "里程費率(實際)")
    @Parsed(field = "里程費率(實際)")
    private Double actualMileageRate;

    @Schema(description = "里程費率(原價)")
    @Parsed(field = "里程費率(原價)")
    private Double originalMileageRate;

    @Schema(description = "訂閱租期")
    @Parsed(field = "訂閱租期")
    private Integer subscribeMonth;

    @Schema(description = "預收月數")
    @Parsed(field = "預收月數")
    private Integer prepaidMonths;

    @Schema(description = "款項明細")
    @Parsed(field = "款項明細")
    private String infoDetail;

    @Schema(description = "訂單應收總金額")
    @Parsed(field = "訂單應收總金額")
    private Integer totalAmt;

    @Schema(description = "訂單實收總金額")
    @Parsed(field = "訂單實收總金額")
    private Integer paidAmt;

    @Schema(description = "是否取消")
    @Parsed(field = "是否取消")
    private Boolean isCancel;

    @Schema(description = "取消時間")
    @Parsed(field = "取消時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date cancelDate;

    @Schema(description = "取消備註")
    @Parsed(field = "取消備註")
    private String cancelRemark;

    @Schema(description = "是否同意建立格上會員")
    @Parsed(field = "是否同意建立格上會員")
    private Boolean isCreateAccount;

    @Schema(description = "錯誤訊息")
    @Parsed(field = "錯誤訊息")
    private String errorMessages;

    public DealerOrderExcel(DealerOrder dealerOrder) {
        this.orderNo = dealerOrder.getOrderNo();
        this.dealerName = dealerOrder.getDealerName();
        this.orderStatus = Optional.ofNullable(dealerOrder.getOrderStatus()).map(ContractStatus::codeOfValue).map(ContractStatus::name).orElse(null);
        this.isNewOrder = dealerOrder.getIsNewOrder();
        this.isAudit = dealerOrder.getIsAudit();
        this.isPaySecurityDeposit = dealerOrder.getIsPaySecurityDeposit();
        this.securityDepositDate = Optional.ofNullable(dealerOrder.getSecurityDepositDate()).map(Date::from).orElse(null);
        this.parentOrderNo = dealerOrder.getParentOrderNo();
        this.nextStageOrderNo = dealerOrder.getNextStageOrderNo();
        this.stage = dealerOrder.getStage();
        this.plateNo = dealerOrder.getPlateNo();
        this.insuranceId = dealerOrder.getInsuranceId();
        this.dealerUserId = dealerOrder.getDealerUserId();
        this.expectDepartStation = dealerOrder.getExpectDepartStation();
        this.expectReturnStation = dealerOrder.getExpectReturnStation();
        this.departStation = dealerOrder.getDepartStation();
        this.returnStation = dealerOrder.getReturnStation();
        this.expectDepartDate = Optional.ofNullable(dealerOrder.getExpectDepartDate()).map(Date::from).orElse(null);
        this.expectReturnDate = Optional.ofNullable(dealerOrder.getExpectReturnDate()).map(Date::from).orElse(null);
        this.departDate = Optional.ofNullable(dealerOrder.getDepartDate()).map(Date::from).orElse(null);
        this.returnDate = Optional.ofNullable(dealerOrder.getReturnDate()).map(Date::from).orElse(null);
        this.securityDeposit = dealerOrder.getSecurityDeposit();
        this.monthlyFee = dealerOrder.getMonthlyFee();
        this.actualMileageRate = dealerOrder.getActualMileageRate();
        this.originalMileageRate = dealerOrder.getOriginalMileageRate();
        this.prepaidMonths = dealerOrder.getPrepaidMonths();
        this.beginAmt = dealerOrder.getBeginAmt();
        this.closeAmt = dealerOrder.getCloseAmt();
        this.isReturned = dealerOrder.getIsReturned();
        this.subscribeMonth = dealerOrder.getSubscribeMonth();
        this.infoDetail = dealerOrder.getInfoDetail();
        this.totalAmt = dealerOrder.getTotalAmt();
        this.paidAmt = dealerOrder.getPaidAmt();
        this.isCancel = dealerOrder.getIsCancel();
        this.cancelDate = Optional.ofNullable(dealerOrder.getCancelDate()).map(Date::from).orElse(null);
        this.cancelRemark = dealerOrder.getCancelRemark();
        this.isCreateAccount = dealerOrder.getIsCreateAccount();
    }
}
