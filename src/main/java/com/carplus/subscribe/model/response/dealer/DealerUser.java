package com.carplus.subscribe.model.response.dealer;

import com.carplus.subscribe.model.auth.AuthDealerUser;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public interface DealerUser {

    Long getDealerUserId();

    String getIdNo();

    String getUserName();

    String getNationalCode();

    String getMainCell();

    void setIdNo(String idNo);

    void setUserName(String userName);

    void setNationalCode(String nationalCode);

    void setMainCell(String mainCell);

    void setBirthDay(String birthDay);

    void setEmail(String email);

    void setCity(Integer cityId);

    void setArea(Integer areaId);

    void setAddress(String address);

    void setVatNumber(String vatNumber);

    void setCompanyName(String companyName);

    void setCompanyLocation(String companyLocation);

    static Set<Long> getDealerUserIds(List<? extends DealerUser> dealerUser) {
        return dealerUser.stream()
                .map(DealerUser::getDealerUserId)
                .collect(Collectors.toSet());
    }

    default void setDealerUserInfo(AuthDealerUser authDealerUser) {
        setIdNo(authDealerUser.getIdNo());
        setUserName(authDealerUser.getUserName());
        setNationalCode(authDealerUser.getNationalCode());
        setMainCell(authDealerUser.getMainCell());
        setBirthDay(authDealerUser.getBirthDay());
        setEmail(authDealerUser.getEmail());
        setCity(authDealerUser.getHhcityId());
        setArea(authDealerUser.getHhareaId());
        setAddress(authDealerUser.getHhaddress());
        setVatNumber(authDealerUser.getVatNumber());
        setCompanyName(authDealerUser.getCompanyName());
        setCompanyLocation(authDealerUser.getCompanyLocation());
    }
}