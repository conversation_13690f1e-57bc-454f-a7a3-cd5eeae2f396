package com.carplus.subscribe.model.response.task;

import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.enums.TaskType;
import com.carplus.subscribe.model.Driver;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class TaskDetailResponse {


    private Integer taskId;

    @Schema(description = "出/還車任務類別")
    private TaskType eContractType;

    @Schema(description = "出租單種類(訂閱/短租); SUB/SRENTAL")
    private String eContractCategory;

    @Schema(description = "駕駛名稱")
    private String userName;

    @Schema(description = "車型種類[sedan(0, \"轎車\"), suv(1, \"休旅車\"), truck(2, \"貨車\"), moto(8, \"機車\")]")
    private CarDefine.CarKind eContractCarKind;

    @Schema(description = "出租單編號")
    private String orderNo;

    @Schema(description = "駕駛人")
    private List<Driver> drivers;

    @Schema(description = "預計出車執行時間")
    private Date departExecuteDate;

    @Schema(description = "預計還車執行時間")
    private Date returnExecuteDate;

    @Schema(description = "出車站點")
    private String departStation;

    @Schema(description = "還車站點")
    private String returnStation;

    @Schema(description = "是否車損")
    private Boolean accidentNegotiation;

    @Schema(description = "車損金額")
    private Integer accidentAmount;

    private Integer taskPriority;

    @Schema(description = "任務狀態")
    private Integer status;

    @Schema(description = "被分派者")
    private String assignedId;

    @Schema(description = "被分派者名稱")
    private String assignedName;

    @Schema(description = "站點編號")
    private String stationId;

    @Schema(description = "站點名稱")
    private String stationName;

    @Schema(description = "任務預計開始時間")
    private Date expectExecuteDate;

    @Schema(description = "實際出車時間")
    private Date actualDepartDate;

    @Schema(description = "實際還車時間")
    private Date actualReturnDate;

    @Schema(description = "建立任務時間")
    private Date createAt;

    @Schema(description = "領取任務時間")
    private Date getAt;

    @Schema(description = "執行開始時間")
    private Date startAt;

    @Schema(description = "執行結束時間")
    private Date finishAt;

    @Schema(description = "執行停止時間")
    private Date stopAt;

    @Schema(description = "里程")
    private Integer mileage;

    @Schema(description = "出/還車備註")
    private String remark;

    @Schema(description = "承租人簽名路徑")
    private List<String> driverSignUrls;

    @Schema(description = "門市人員簽名")
    private String assignMemberSignUrl;

    @Schema(description = "車輛四周圖(選填)")
    private List<String> surroundingsVehicleImages;

    @Schema(description = "承租人簽名路徑模型")
    private List<SignModel> driverSignModels;

    @Schema(description = "門市人員簽名模型")
    private SignModel assignMemberSignModel;

    /*
     * 車輛異常區域開始
     */
    private CarAbnormalAreas departCarAbnormalAreas;

    private CarAbnormalAreas returnCarAbnormalAreas;

    /*
     * 車輛異常區域結束
     */

    /*
     * 車輛儀表盤開始
     */
    @Schema(description = "儀錶板有無警示燈")
    private Boolean dashboardAlert;

    @Schema(description = "油量")
    private Float fuel;

    @Schema(description = "中油卡")
    private Boolean cpcCard;

    @Schema(description = "台塑卡")
    private Boolean formosaCard;

    @Schema(description = "行照")
    private Boolean vehicleRegistrationDocument;
    @Schema(description = "儀錶板備註")
    private String dashboardNote;

    /*
     * 車輛儀表盤結束
     */



    /*
     * 車外車況檢查開始
     */

    @Schema(description = "胎紋深度")
    private Float tireTreadDepth;

    @Schema(description = "Etag 有無安裝")
    private Boolean installEtag;

    @Schema(description = "etag型式")
    private ETagModelEnum etagModel;

    @Schema(description = "eTag 序號")
    private String etagNo;

    @Schema(description = "內裝照片(前座、後座、後車廂)")
    private List<String> interiorPhotos;

    @Schema(description = "車外備註")
    private String outsideNote;

    /*
     * 車外車況檢查結束
     */

    /*
     * 內裝車況檢查開始
     */

    @Schema(description = "車內氣味")
    private Boolean scent;

    @Schema(description = "椅座")
    private Boolean seat;

    @Schema(description = "地毯、手套箱")
    private Boolean blanket;

    @Schema(description = "車內備註")
    private String insideNote;



    /*
     * 內裝車況檢查結束
     */

    /*
     *  設備車況檢查開始
     */

    @Schema(description = "引擎")
    private Boolean engine;

    @Schema(description = "冷氣")
    private Boolean airCondition;

    @Schema(description = "車窗")
    private Boolean windows;

    @Schema(description = "音響、多媒體主機")
    private Boolean media;

    @Schema(description = "燈光")
    private Boolean light;

    @Schema(description = "儀表盤、各項開關")
    private Boolean dashboard;

    @Schema(description = "(有/無)行車紀錄器")
    private Boolean dashCams;

    @Schema(description = "(有/無)行李箱蓋板")
    private Boolean trunkLid;

    @Schema(description = "汽車行照")
    private Boolean carLicense;

    @Schema(description = "是否有鑰匙")
    private Boolean hasKey;

    @Schema(description = "鑰匙數量")
    private Integer keys;

    @Schema(description = "有無其他配件")
    private Boolean parts;

    @Schema(description = "配件描述")
    private String partsNote;

    @Schema(description = "設備備註")
    private String equipsNote;

    @Schema(description = "圖片base url")
    private String baseUrl;

    @Schema(description = "出車里程")
    private Integer departMileage;

    @Schema(description = "還車里程")
    private Integer returnMileage;

    @Schema(description = "是否不可異動")
    private Boolean locked;

    @Schema(description = "版本 [2024/08/28 version:0 轎車兩排座位, version:1  轎車三排座位]")
    private Integer version;
}
