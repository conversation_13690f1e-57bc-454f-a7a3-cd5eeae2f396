package com.carplus.subscribe.model.response;

import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CarModelImageResponse {

    @Schema(description = "年份")
    private int year;
    @Schema(description = "路徑")
    private List<String> paths;

    public CarModelImageResponse(@NonNull CarModelImage img, String gcsUrl) {
        this.year = img.getYear();
        this.paths = Optional.ofNullable(img.getPaths()).map(imgs -> imgs.stream().map(path -> gcsUrl + path).collect(Collectors.toList())).orElse(null);
    }
}
