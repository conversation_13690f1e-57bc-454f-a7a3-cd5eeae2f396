package com.carplus.subscribe.model.response.econtract;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ECFilesResponse {
    @JsonProperty("eContractId")
    @Schema(name = "eContractId", description = "電子合約", example = "7")
    private Integer eContractId;
    @JsonProperty("eContractType")
    @Schema(name = "eContractType", description = "合約檔案類型", example = "E_CONTRACT")
    private String eContractType;
    @JsonProperty("uploadFile")
    @Schema(name = "uploadFile", description = "檔案上傳記錄")
    private UploadFileResponse uploadFile;
    @JsonProperty("contractNo")
    @Schema(name = "contractNo", description = "合約編號", example = "C2024051300003")
    private String contractNo;
    @JsonProperty("eRentalSignDate")
    @Schema(name = "eRentalSignDate", description = "電子出租單客戶簽署日期")
    private Instant eRentalSignDate;
    @JsonProperty("eRentalSignInfo")
    @Schema(name = "eRentalSignInfo", description = "電子出租單客戶簽署資訊")
    private List<String> eRentalSignInfo;
    @JsonProperty("departTaskId")
    @Schema(name = "departTaskId", description = "出車任務編號")
    private String departTaskId;
    @JsonProperty("returnTaskId")
    @Schema(name = "returnTaskId", description = "還車任務編號")
    private String returnTaskId;
    @JsonProperty("createUser")
    @Schema(name = "createUser", description = "員工編號 / 員工姓名", example = "K999 / MemberName")
    private String createUser;
    @JsonProperty("createDate")
    @Schema(name = "createDate", description = "創建時間")
    private Instant createDate;

}