package com.carplus.subscribe.model.response.econtract;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UploadFileResponse {
    @JsonProperty("id")
    @Schema(name = "id", description = "上傳檔案 ID", example = "103")
    private Integer id;
    @JsonProperty("type")
    @Schema(name = "type", description = "上傳檔案分類")
    private String type;
    @JsonProperty("filename")
    @Schema(name = "filename", description = "檔名")
    private String filename;
    @JsonProperty("mediaType")
    @Schema(name = "mediaType", description = "MIME type")
    private String mediaType;
    @JsonProperty("path")
    @Schema(name = "path", description = "差別費率")
    private String path;
    @JsonProperty("acctId")
    @Schema(name = "acctId", description = "會員編號")
    private Integer acctId;
    @JsonProperty("status")
    @Schema(name = "status", description = "檔案使用狀態")
    private Integer status;
    @JsonProperty("displayFilename")
    @Schema(name = "displayFilename", description = "檔案顯示名稱")
    private String displayFilename;
    @Schema(description = "上傳檔案備註")
    @Column(name = "remark")
    private String remark;
    @JsonProperty("createUser")
    @Schema(name = "createUser", description = "創建者 員工編號 / 員工姓名", example = "K999 / MemberName")
    private String createUser;
    @JsonProperty("createDate")
    @Schema(name = "createDate", description = "創建時間")
    private Instant createDate;
    @JsonProperty("updateUser")
    @Schema(name = "updateUser", description = "最後異動者")
    private String updateUser;
    @JsonProperty("updateDate")
    @Schema(name = "updateDate", description = "更新時間")
    private Instant updateDate;

    public UploadFileResponse(UploadFile uploadFile, String updateUser) {
        this.id = uploadFile.getId();
        this.type = uploadFile.getType();
        this.filename = uploadFile.getFilename();
        this.mediaType = uploadFile.getMediaType();
        this.path = uploadFile.getPath();
        this.acctId = uploadFile.getAcctId();
        this.status = uploadFile.getStatus();
        this.displayFilename = uploadFile.getDisplayFilename();
        this.remark = uploadFile.getRemark();
        this.createUser = uploadFile.getCreateUser();
        this.createDate = uploadFile.getInstantCreateDate();
        this.updateUser = StringUtils.isNotBlank(updateUser) ? updateUser : uploadFile.getUpdateUser();
        this.updateDate = uploadFile.getInstantUpdateDate();
    }

    public UploadFileResponse(UploadFile uploadFile, String createName, String updateName) {
        this.id = uploadFile.getId();
        this.type = uploadFile.getType();
        this.filename = uploadFile.getFilename();
        this.mediaType = uploadFile.getMediaType();
        this.path = uploadFile.getPath();
        this.acctId = uploadFile.getAcctId();
        this.status = uploadFile.getStatus();
        this.displayFilename = uploadFile.getDisplayFilename();
        this.remark = uploadFile.getRemark();
        this.createUser = StringUtils.isNotBlank(createName) ? createName : uploadFile.getCreateUser();
        this.createDate = uploadFile.getInstantCreateDate();
        this.updateUser = StringUtils.isNotBlank(updateName) ? updateName : uploadFile.getUpdateUser();
        this.updateDate = uploadFile.getInstantUpdateDate();
    }
}