package com.carplus.subscribe.model.response.carwishlist;

import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.subscribelevel.SubscribeLevelResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@NoArgsConstructor
@Data
public class CarWishlistResponse {

    @Schema(description = "車牌號碼")
    private String plateNo;

    // Real-time car data
    @Schema(description = "車輛編號")
    private String carNo;

    @Schema(description = "廠牌英文名稱")
    private String brandNameEn;

    @Schema(description = "車型名稱")
    private String carModelName;

    @Schema(description = "使用月費")
    private int monthlyFee;

    @Schema(description = "里程費")
    private double mileageFee;

    @Schema(description = "保證金")
    private int securityDeposit;

    @Schema(description = "方案類型")
    private SubscribeType type;

    @Schema(description = "標籤id")
    private List<Integer> tagIds;

    @Schema(description = "所在站所名稱")
    private String locationName;

    @Schema(description = "出廠年份")
    private String mfgYear;

    @Schema(description = "訂閱類別")
    private CarDefine.CarState carState;

    @JsonProperty("isLaunched")
    @Schema(description = "是否上架")
    private boolean isLaunched;

    @Schema(description = "車輛圖卡網址")
    private String imgUrl;

    @Schema(description = "建立時間")
    private Instant createDate;
    
    public CarWishlistResponse(CarWishlist carWishlist, CarResponse carResponse, Stations station) {
        this.setPlateNo(carWishlist.getPlateNo());
        this.setCarNo(carResponse.getCarNo());
        this.setBrandNameEn(carResponse.getCarBrand().getBrandNameEn());
        this.setCarModelName(carResponse.getCarModel().getCarModelName());
        SubscribeLevelResponse determinedSubscribeLevel = Optional.ofNullable(carResponse.getDiscountLevel()).orElse(carResponse.getLevel());
        this.setMonthlyFee(carResponse.getUseMonthlyFee(determinedSubscribeLevel));
        this.setMileageFee(determinedSubscribeLevel.getMileageFee());
        this.setSecurityDeposit(determinedSubscribeLevel.getSecurityDeposit());
        this.setType(determinedSubscribeLevel.getType());
        this.setTagIds(carResponse.getTagIds());
        this.setLocationName(Optional.ofNullable(station).map(Stations::getStationName).orElse(""));
        this.setMfgYear(carResponse.getMfgYear());
        this.setCarState(carResponse.getCarState());
        this.setLaunched(carResponse.isOrderableOfficially());
        this.setImgUrl(Optional.ofNullable(carResponse.getImages())
            .filter(images -> !images.isEmpty())
            .map(images -> images.get(0).getPaths().get(0))
            .orElse(null));
        this.setCreateDate(carWishlist.getInstantUpdateDate().isAfter(carWishlist.getInstantCreateDate())
            ? carWishlist.getInstantUpdateDate() : carWishlist.getInstantCreateDate());
    }
}
