package com.carplus.subscribe.model.response.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class CarAbnormalAreas {

    @Schema(description = "車前異常區域")
    private List<AbnormalAreas> front;

    @Schema(description = "車後異常區域")
    private List<AbnormalAreas> back;

    @Schema(description = "車右側異常區域")
    private List<AbnormalAreas> right;

    @Schema(description = "車左側異常區域")
    private List<AbnormalAreas> left;

    @Schema(description = "車頂異常區域")
    private List<AbnormalAreas> top;

    @Schema(description = "車內異常區域")
    private List<AbnormalAreas> inside;

    public List<AbnormalAreas> getFront() {
        beforeGet(front, AreaEnum.FRONT);
        return front;
    }

    public List<AbnormalAreas> getBack() {
        beforeGet(back, AreaEnum.BACK);
        return back;
    }

    public List<AbnormalAreas> getRight() {
        beforeGet(right, AreaEnum.RIGHT);
        return right;
    }

    public List<AbnormalAreas> getLeft() {
        beforeGet(left, AreaEnum.LEFT);
        return left;
    }

    public List<AbnormalAreas> getTop() {
        beforeGet(top, AreaEnum.TOP);
        return top;
    }

    public List<AbnormalAreas> getInside() {
        beforeGet(inside, AreaEnum.INSIDE);
        return inside;
    }

    private void beforeGet(List<AbnormalAreas> list, AreaEnum area) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(a -> a.setArea(area));
        }
    }
}
