package com.carplus.subscribe.model.response.order;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.model.CompanyDriver;
import com.carplus.subscribe.model.PriceInfo;
import com.carplus.subscribe.model.ReferInfo;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static com.carplus.subscribe.utils.DateUtil.DASH_FORMATTER;
import static java.time.temporal.ChronoUnit.DAYS;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderCSV {

    private static final String TIME_ZONE = "Asia/Taipei";

    @Schema(description = "訂單編號")
    @Parsed(field = "訂單編號")
    private String orderNo;

    @Schema(description = "訂單狀態")
    @Parsed(field = "訂單狀態")
    private String status;

    @Schema(description = "是否新訂單")
    @Parsed(field = "是否新單")
    private boolean isNewOrder;

    @Schema(description = "第幾期訂單")
    @Parsed(field = "第幾期訂單")
    private int stage;

    @Schema(description = "授信狀態")
    @Parsed(field = "授信狀態")
    private CreditInfo creditInfo;

    @Schema(description = "是否有未繳款項")
    @Parsed(field = "是否有未繳款項")
    private boolean isUnpaid;

    @Schema(description = "訂單建立時間")
    @Parsed(field = "訂單建立時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date createDate;

    @Schema(description = "預計出車時間")
    @Parsed(field = "預定出車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date expectStartDate;

    @Schema(description = "實際出車時間")
    @Parsed(field = "實際出車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date startDate;

    @Schema(description = "預定還車時間")
    @Parsed(field = "預定還車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date expectEndDate;

    @Schema(description = "實際還車時間")
    @Parsed(field = "實際還車時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date endDate;

    @Schema(description = "法人統一編號")
    @Parsed(field = "法人統一編號")
    private String vatNumber;

    @Schema(description = "法人公司名稱")
    @Parsed(field = "法人公司名稱")
    private String companyName;

    @Schema(description = "法人公司地址")
    @Parsed(field = "法人公司地址")
    private String companyLocation;

    @Schema(description = "發票對象")
    @Parsed(field = "發票對象")
    private String type;

    @Schema(description = "發票統編")
    @Parsed(field = "發票統編")
    private String id;

    @Schema(description = "發票抬頭")
    @Parsed(field = "發票抬頭")
    private String title;

    @Schema(description = "剩餘/預期天數")
    @Parsed(field = "剩餘/預期天數")
    private Long diffDays;

    @Schema(description = "出車站點")
    @Parsed(field = "出車站點")
    private String departStation;

    @Schema(description = "還車站點")
    @Parsed(field = "還車站點")
    private String returnStation;

    @Schema(description = "出車人員編號")
    @Parsed(field = "出車人員編號")
    private String departMemberId;

    @Schema(description = "出車人員")
    @Parsed(field = "出車人員")
    private String departMemberName;

    @Schema(description = "還車人員編號")
    @Parsed(field = "還車人員編號")
    private String returnMemberId;

    @Schema(description = "還車人員")
    @Parsed(field = "還車人員")
    private String returnMemberName;

    @Schema(description = "續約狀態")
    @Parsed(field = "續約狀態")
    private RenewType renewType;

    @Schema(description = "續約訂單號碼")
    @Parsed(field = "續約訂單號碼")
    private String nextStageOrderNo;

    @Schema(description = "不續約原因")
    @Parsed(field = "不續約原因")
    private String nonRenewRemark;

    @Schema(description = "utmCampaign")
    @Parsed(field = "utmCampaign")
    private String utmCampaign;

    @Schema(description = "utmMedium")
    @Parsed(field = "utmMedium")
    private String utmMedium;

    @Schema(description = "utmSource")
    @Parsed(field = "utmSource")
    private String utmSource;

    @Schema(description = "主約編號")
    @Parsed(field = "主約編號")
    private String mainContractNo;

    @Schema(description = "主約狀態")
    @Parsed(field = "主約狀態")
    private String mainContractStatus;

    @Schema(description = "合約編號")
    @Parsed(field = "合約編號")
    private String contractNo;

    @Schema(description = "用戶編號")
    @Parsed(field = "用戶編號")
    private Integer acctId;

    @Schema(description = "用戶名稱")
    @Parsed(field = "訂車人")
    private String custName;

    @Schema(description = "身分證號/居留證號")
    @Parsed(field = "身分證號/居留證號")
    private String idNo;

    @Schema(description = "連絡電話")
    @Parsed(field = "連絡電話")
    private String mainCell;

    @Schema(description = "車牌號碼")
    @Parsed(field = "車牌號碼")
    private String plateNo;

    @Schema(description = "備車狀態")
    @Parsed(field = "備車方式")
    private String carReady;

    @Schema(description = "介紹人")
    @Parsed(field = "介紹人")
    private String referName;

    @Schema(description = "保證金")
    @Parsed(field = "保證金")
    private int securityDeposit;

    @Schema(description = "實際月費")
    @Parsed(field = "實際月費")
    private int actualMonthlyFee;

    @Schema(description = "里程費率")
    @Parsed(field = "里程費率")
    private double mileageFee;

    @Schema(description = "是否啟用優惠月費")
    @Parsed(field = "是否啟用優惠月費")
    private Boolean isDiscountMonthlyFee;

    @Schema(description = "車型名稱")
    @Parsed(field = "車型")
    private String carModelName;

    @Schema(description = "車型編號")
    @Parsed(field = "車型編號")
    private String carModelCode;


    @Schema(description = "廠牌名稱")
    @Parsed(field = "廠牌")
    private String brandName;

    @Schema(description = "廠牌編號")
    @Parsed(field = "廠牌編號")
    private String brandCode;

    @Schema(description = "車輛編號")
    @Parsed(field = "車輛編號")
    private String carNo;

    @Schema(description = "排氣量")
    @Parsed(field = "排氣量")
    private int displacement;

    @Schema(description = "車色")
    @Parsed(field = "車色")
    private String colorDesc;

    @Schema(description = "排檔方式")
    @Parsed(field = "排檔方式")
    private String gearType;

    @Schema(description = "燃料種類")
    @Parsed(field = "燃料種類")
    private String fuelType;

    @Schema(description = "出廠年份")
    @Parsed(field = "出廠年份")
    private String mfgYear;

    @Schema(description = "首期預付月費")
    @Parsed(field = "首期預付月費")
    private Integer monthlyFeeForFirstStage;

    @Schema(description = "首期金額合計")
    @Parsed(field = "首期金額合計")
    private Integer paidForFirstStage;

    @Schema(description = "駕照管轄編號")
    @Parsed(field = "駕照管轄編號")
    private String jurisdictionNum;

    @Schema(description = "出生年月日")
    @Parsed(field = "出生年月日")
    private String birthDay;

    @Schema(description = "戶籍縣市編號")
    @Parsed(field = "戶籍縣市編號")
    private Integer city;
    @Schema(description = "戶籍縣市")
    @Parsed(field = "戶籍縣市")
    private String cityName;

    @Schema(description = "戶籍區域編號")
    @Parsed(field = "戶籍區域編號")
    private Integer area;

    @Schema(description = "戶籍區域")
    @Parsed(field = "戶籍區域")
    private String areaName;

    @Schema(description = "戶籍地址")
    @Parsed(field = "戶籍地址")
    private String address;

    @Schema(description = "訂單備註")
    @Parsed(field = "訂單備註")
    private String remark;

    @Schema(description = "訂單成立時間")
    @Parsed(field = "訂單成立時間")
    @Format(formats = "yyyy/MM/dd HH:mm:ss", options = "timeZone=" + TIME_ZONE)
    private Date securityDepositDate;

    @Schema(description = "總預付月費")
    @Parsed(field = "總預付月費")
    private Integer totalMonthlyPrepaidFee;

    @Schema(description = "實際使用里程數")
    @Parsed(field = "實際使用里程數")
    private Integer actualMileageUsed;

    @Schema(description = "優惠里程數")
    @Parsed(field = "優惠里程數")
    private Integer discountMileage;

    @Schema(description = "每季月費優惠折抵金額")
    @Parsed(field = "每季月費優惠折抵金額")
    private Integer quarterlyFeeDiscountAmount;

    @Schema(description = "加購商品明細")
    @Parsed(field = "加購商品明細")
    private String merchandiseDetail;

    @Schema(description = "車輛統一編號")
    @Parsed(field = "車輛統一編號")
    private String vatNo;

    @Schema(description = "車輛所屬公司名稱")
    @Parsed(field = "車輛所屬公司名稱")
    private String carOwnerName;

    public OrderCSV(Orders orders, MainContract mainContract, CarModel carModel, CarBrand carBrand, Cars cars, ObjectMapper objectMapper) {
        this.orderNo = orders.getOrderNo();
        this.status = OrderStatus.of(orders.getStatus()).getName();
        this.isNewOrder = orders.getIsNewOrder();
        this.stage = orders.getStage();
        this.creditInfo = orders.getCreditInfo();
        this.isUnpaid = Optional.ofNullable(orders.getIsUnpaid()).orElse(false);
        this.createDate = new Date(orders.getInstantCreateDate().toEpochMilli());
        this.expectStartDate = Optional.ofNullable(orders.getExpectStartDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
        this.startDate = Optional.ofNullable(orders.getStartDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
        this.expectEndDate = Optional.ofNullable(orders.getExpectEndDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
        this.endDate = Optional.ofNullable(orders.getEndDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
        this.vatNumber = Optional.ofNullable(mainContract.getCompanyDriver()).map(CompanyDriver::getVatNumber).orElse("");
        this.companyName = Optional.ofNullable(mainContract.getCompanyDriver()).map(CompanyDriver::getCompanyName).orElse("");
        this.companyLocation = Optional.ofNullable(mainContract.getCompanyDriver()).map(CompanyDriver::getCompanyLocation).orElse("");
        this.type = Optional.ofNullable(InvoiceDefine.InvType.of(orders.getInvoice().getType())).map(InvoiceDefine.InvType::getName).orElse(null);
        this.id = Optional.ofNullable(orders.getInvoice().getId()).orElse("");
        this.title = Optional.ofNullable(orders.getInvoice().getTitle()).orElse("");
        this.diffDays = DateUtil.calculateDiffDate(Instant.now(), expectEndDate.toInstant(), DAYS);
        this.departStation = mainContract.getDepartStationCode();
        this.returnStation = mainContract.getReturnStationCode();
        this.departMemberId = orders.getDepartMemberId();
        this.returnMemberId = orders.getReturnMemberId();
        this.renewType = orders.getRenewType();
        this.nextStageOrderNo = orders.getNextStageOrderNo();
        this.nonRenewRemark = orders.getNonRenewRemark();
        this.utmCampaign = mainContract.getUtmCampaign();
        this.utmMedium = mainContract.getUtmMedium();
        this.utmSource = mainContract.getUtmSource();
        this.mainContractNo = mainContract.getMainContractNo();
        this.mainContractStatus = Optional.ofNullable(mainContract.getStatus()).map(ContractStatus::codeOfValue).map(ContractStatus::name).orElse(null);
        this.contractNo = orders.getContractNo();
        this.acctId = mainContract.getAcctId();
        this.idNo = mainContract.getIdNo();
        this.plateNo = mainContract.getPlateNo();
        this.carReady = Optional.ofNullable(mainContract.getCarReady()).map(CarReady::getDescription).orElse(null);
        this.referName = Optional.ofNullable(mainContract.getReferInfo()).map(ReferInfo::getName).orElse(null);
        this.securityDeposit = Optional.ofNullable(mainContract.getOriginalPriceInfo()).map(PriceInfo::getSecurityDepositInfo).map(SecurityDepositInfo::getRealSecurityDeposit).orElse(0);
        this.actualMonthlyFee = Optional.ofNullable(mainContract.getOriginalPriceInfo()).map(PriceInfo::getUseMonthlyFee).orElse(0);
        this.mileageFee = Optional.ofNullable(mainContract.getOriginalPriceInfo()).map(PriceInfo::getMileageFee)
            .map(mileageFee -> {
                if (mileageFee == 0) {
                    return Optional.of(mainContract.getOriginalPriceInfo()).map(PriceInfo::getOriginalMileageFee).orElse(0d);
                } else {
                    return mileageFee;
                }
            }).orElse(0d);
        this.isDiscountMonthlyFee = Optional.ofNullable(mainContract.getOriginalPriceInfo()).map(PriceInfo::isMonthlyDiscounted).orElse(false);
        this.carModelCode = mainContract.getCarModelCode();
        this.carModelName = carModel.getCarModelName();
        this.brandName = carBrand.getBrandNameEn();
        this.brandCode = carBrand.getBrandCode();
        if (orders.getStatus() > OrderStatus.CREDITED.getStatus() && orders.getStatus() <= OrderStatus.CLOSE.getStatus()) {
            this.securityDepositDate = new Date(
                Optional.ofNullable(orders.getSecurityDepositDate()).orElse(
                    Optional.ofNullable(mainContract.getOriginalPriceInfo().getSecurityDepositInfo()).map(SecurityDepositInfo::getSecurityDepositDate).map(Date::toInstant)
                        .orElse(orders.getInstantCreateDate())).toEpochMilli());
        }
        this.carNo = cars.getCarNo();
        this.displacement = Optional.ofNullable(cars.getDisplacement()).map(BigDecimal::intValue).orElse(0);
        this.colorDesc = cars.getColorDesc();
        this.gearType = Optional.ofNullable(cars.getGearType()).map(CarDefine.GearType::getName).orElse("");
        this.fuelType = Optional.ofNullable(cars.getFuelType()).map(CarDefine.FuelType::getName).orElse("");
        this.mfgYear = Optional.ofNullable(cars.getMfgYear()).orElse("");
        this.totalMonthlyPrepaidFee = Optional.ofNullable(mainContract.getOriginalPriceInfo()).map(PriceInfo::getUseMonthlyFee).orElse(0)
            * Optional.ofNullable(orders.getMonth()).orElse(0);
        this.actualMileageUsed = Objects.isNull(orders.getReturnMileage())
            ? Math.max(Optional.ofNullable(orders.getReportMileage()).orElse(0) - Optional.ofNullable(orders.getDepartMileage()).orElse(0), 0)
            : Math.max(Optional.of(orders.getReturnMileage()).orElse(0) - Optional.ofNullable(orders.getDepartMileage()).orElse(0), 0);
        // 格式化訂單備註
        this.remark = Optional.ofNullable(orders.getRemarks())
            .map(remarkList -> {
                try {
                    // 先按照 createTime 降序排序，null 值排在最後
                    List<Remark> sortedRemarks = remarkList.stream()
                        .sorted(Comparator.comparing(Remark::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());

                    String remarkJsonString = objectMapper.writeValueAsString(sortedRemarks);
                    JsonNode remarksArray = objectMapper.readTree(remarkJsonString);

                    if (!remarksArray.isArray()) {
                        return remarkJsonString;
                    }

                    return StreamSupport.stream(remarksArray.spliterator(), false)
                        .map(remarkNode -> {
                            try {
                                String content = remarkNode.get(Remark.Fields.content).asText("");
                                String remarkerName = remarkNode.get(Remark.Fields.remarkerName).asText("");
                                JsonNode createTimeNode = remarkNode.get(Remark.Fields.createTime);

                                // Skip formatting if required fields are empty
                                if (StringUtils.isBlank(content)) {
                                    return null;
                                }

                                String formattedDateTime = "";
                                if (createTimeNode != null && !createTimeNode.isNull()) {
                                    Instant createTime = Instant.ofEpochMilli(createTimeNode.asLong());
                                    formattedDateTime = DateUtil.getFormatString(createTime, DASH_FORMATTER);
                                }

                                return String.format("%s %s：%s", formattedDateTime, remarkerName, content);
                            } catch (Exception e) {
                                return remarkNode.toString();
                            }
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining("；"));
                } catch (Exception e) {
                    return String.valueOf(remarkList);
                }
            })
            .orElse("");
        this.vatNo = cars.getVatNo();
    }
}
