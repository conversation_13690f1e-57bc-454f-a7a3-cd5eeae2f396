package com.carplus.subscribe.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class OrderPriceInfo implements Serializable {
    private Integer amount = 0;

    @Schema(description = "保證金資訊")
    private SecurityDepositInfo securityDepositInfo;
    /* ******************** 目前走需求單模式，先不考慮第Ｎ期 ******************** */
    @Schema(description = "訂閱方案分期")
    private int typeStage = 1;
    @Schema(description = "基本月費")
    private int monthlyFee;
    @Schema(description = "優惠月費")
    private int discountMonthlyFee;
    @Schema(description = "基本里程費")
    private double originalMileageFee;
    @Schema(description = "是否自動授信")
    private boolean autoCredit;

    @Schema(description = "折扣後里程費")
    private double mileageFee;
    @Schema(description = "總月費")
    private int totalMonthlyFee;

    @Schema(description = "總里程費")
    private int totalMileageFee;
    @Schema(description = "總里程")
    private int totalMileage;
    @Schema(description = "折扣里程數")
    private int discountMileage;
    @Schema(description = "折扣里程費")
    private int discountMileageFee;
    @Schema(description = "是否折扣月費")
    private boolean monthlyDiscounted;
    @Schema(description = "月數")
    private int month;
    @Schema(description = "天數")
    private int days;
    @Schema(description = "續約折扣月費")
    private int renewDiscountMonthlyFee;

    public OrderPriceInfo(PriceInfo priceInfo, int month, int days, int stage) {
        this.renewDiscountMonthlyFee = Math.min(stage / 4, 2) * 1000;
        this.monthlyFee = priceInfo.getMonthlyFee();
        this.discountMonthlyFee = priceInfo.getDiscountMonthlyFee();
        this.originalMileageFee = priceInfo.getMileageFee();
        this.month = month;
        this.days = days;
        this.totalMonthlyFee = this.month * (getUseMonthlyFee() - renewDiscountMonthlyFee);
    }

    /**
     * 更新里程費
     *
     * @param useMileage 使用的里程數
     */
    public void updateMileage(int useMileage) {
        this.totalMileage = useMileage;
        this.discountMileageFee = (int) Math.round(this.mileageFee * discountMileage);
        this.totalMileageFee = (int) (totalMileage * this.mileageFee - discountMileageFee);
    }

    /**
     * 試算提早還車預估折扣金額
     */
    public int calculateEarlyReturnDiscountAmtByUseDays(int useDays) {
        int earlyReturnDiscountAmt = this.totalMonthlyFee - (Math.round(this.totalMonthlyFee * 1f / this.days) * useDays) - getUseMonthlyFee();

        return Math.max(earlyReturnDiscountAmt, 0);
    }

    /**
     * 取得使用月費
     */
    public int getUseMonthlyFee() {
        return this.monthlyDiscounted ? this.discountMonthlyFee : this.monthlyFee;
    }


}
