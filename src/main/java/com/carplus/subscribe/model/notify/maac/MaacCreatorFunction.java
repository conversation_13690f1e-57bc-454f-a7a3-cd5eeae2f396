package com.carplus.subscribe.model.notify.maac;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.notify.MaacSubscribeNotify;

@FunctionalInterface
public interface MaacCreatorFunction<T extends BaseDataDTO> {
    Maac<? extends T> createMaac(Orders order, AuthUser user, MaacSubscribeNotify subNotify);
}
