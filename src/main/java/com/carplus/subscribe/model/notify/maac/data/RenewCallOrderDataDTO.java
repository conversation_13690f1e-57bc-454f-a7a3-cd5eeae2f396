package com.carplus.subscribe.model.notify.maac.data;

import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class RenewCallOrderDataDTO extends BaseDataDTO {

    @JsonProperty("line_push")
    private LinePushDTO linePush;
    @JsonProperty("pnp")
    private PnpDTO pnp;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LinePushDTO {
        @JsonProperty("stage")
        private String stage;
        @JsonProperty("customer_name")
        private String customerName;
        @JsonProperty("contract_expired_date")
        private String contractExpiredDate;
        @JsonProperty("renew_url")
        private String renewUrl;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PnpDTO {
        @JsonProperty("contract_time")
        private String contractTime;
        @JsonProperty("url")
        private String url;
    }
}
