package com.carplus.subscribe.model.notify.maac;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BaseDataDTO {

    @JsonProperty("phone_number")
    private String phoneNumber;
    @JsonProperty("line_push_template_id")
    private Integer linePushTemplateId;
    @JsonProperty("sms")
    private SmsDTO sms;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SmsDTO {
        @JsonProperty("content")
        private String content;
    }
}
