package com.carplus.subscribe.model.notify;

import com.carplus.subscribe.enums.NotifyCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@Schema(description = "訂閱車通知")
public class SubscribeNotify {
    @Schema(description = "附件檔名")
    public String displayFilename;
    @Schema(description = "通知類別")
    private NotifyCategory category;
    @Schema(description = "簡訊內容")
    private String smsContent2C;
    @Schema(description = "信件主旨 2C")
    private String emailSubject2C;
    @Schema(description = "信件主旨 2B")
    private String emailSubject2B;
    @Schema(description = "信件內容Template 2C")
    private String emailHtmlTemplate2C;
    @Schema(description = "信件內容Template 2B")
    private String emailHtmlTemplate2B;
    @Schema(description = "夾帶附件網址")
    private String attachmentUrl;
}