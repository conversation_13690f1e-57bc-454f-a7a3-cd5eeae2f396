package com.carplus.subscribe.model.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class Push {
    private String acctid;
    private String msgtitle;
    private String msgmemobile;
    private String msgcontent;
    private String sender = "GOSMART";
    private String msgtype = "訂單訊息";
    @JsonProperty(value = "click_action")
    private String clickAction = "FLUTTER_NOTIFICATION_CLICK";
    @JsonProperty(value = "click_action_data")
    private ClickActionData clickActionData;
    private String msgsave = "Y";
}
