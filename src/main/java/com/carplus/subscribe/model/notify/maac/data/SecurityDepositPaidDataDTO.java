package com.carplus.subscribe.model.notify.maac.data;

import com.carplus.subscribe.model.notify.maac.BaseDataDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SecurityDepositPaidDataDTO extends BaseDataDTO {

    @JsonProperty(value = "line_push")
    private LinePushDTO linePush;
    @JsonProperty("pnp")
    private PnpDTO pnp;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LinePushDTO {
        @JsonProperty("stage")
        private String stage;
        @JsonProperty("customer_name")
        private String customerName;
        @JsonProperty("order_id")
        private String orderId;
        @JsonProperty("payment_amount")
        private String paymentAmount;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PnpDTO {
        @JsonProperty("payment_amount")
        private String paymentAmount;
        @JsonProperty("payment_name")
        private String paymentName;
        @JsonProperty("payment_date")
        private String paymentDate;
        @JsonProperty("order_id")
        private String orderId;
        @JsonProperty("url")
        private String url;
    }
}
