package com.carplus.subscribe.model.notify;

import lombok.*;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class Email {

    /**
     * 收件人
     */
    private String receive;
    /**
     * 信件主旨
     */
    private String subject;
    /**
     * 副本
     */
    private String cc;
    /**
     * 密件副本
     */
    private String bcc;
    /**
     * 信件內容(HTML)
     */
    @Deprecated
    private Map<String, Object> htmlParam;
    @Deprecated
    private String htmlTemplate;
    /**
     * 信件內容（V3）
     */
    private String content;
    /**
     * 會員編號
     */
    private int acctId;
    /**
     * 產品別
     */
    private String systemKind = "SUB";
    /**
     * 平台
     */
    private String platform;
    /**
     * call back結果
     */
    private String callback;
    /**
     * 串接前端 fe-edm 產生 html template；不再由後端處理 template 邏輯
     */
    private boolean withoutHtmlParams;

    /**
     * 附件網址
     */
    private List<MailAttachment> attachments;

    /**
     * 附件網址
     */
    private String attachmentUrl;

    /**
     * 檔案名稱
     */
    private String displayFilename;
}
