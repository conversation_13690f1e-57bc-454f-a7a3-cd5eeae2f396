package com.carplus.subscribe.model.parking;

import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.response.order.OrderQueryResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;

@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "車牌&時間找營業/非營業訂單response")
public class OrderDepartRecordResponse {

    @JsonIgnore
    private static List<City> cities;
    @Schema(description = "已找出符合請求的訂單集合")
    private List<OrderDepartRecord> orderDepartRecordList = new ArrayList<>();
    @Schema(description = "未找出符合請求, 剩下的集合")
    private List<OrderDepartRecordRequest> notFoundRequestList = new ArrayList<>();

    public void setCities(List<City> cities) {
        OrderDepartRecordResponse.cities = cities;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderDepartRecord {
        @Schema(description = "訂單編號")
        private String orderNo;

        @Schema(description = "出車站所代碼")
        private String departStationCode;

        @Schema(description = "出車站所名稱")
        private String departStationName;

        @Schema(description = "出車時間")
        private Date departDate;

        @Schema(description = "還車時間")
        private Date returnDate;

        @Schema(description = "車牌號碼")
        private String plateNo;

        @Schema(description = "業務別")
        private String bu = "訂閱車";

        @Schema(description = "來源(營業或非營業訂單)")
        private String source;

        @Schema(description = "駕駛人會員編號")
        private Long acctId;

        @Schema(description = "駕駛人姓名")
        private String name;

        @Schema(description = "駕駛人手機")
        private String mainCell;

        @Schema(description = "駕駛人地址")
        private String address;

        public OrderDepartRecord(OrderQueryResponse order) {
            this.orderNo = order.getOrderNo();
            this.departStationCode = order.getDepartStation();
            this.departStationName = order.getDepartStationName();
            this.departDate = new Date(order.getStartDate().toEpochMilli());
            this.returnDate = Optional.ofNullable(order.getEndDate()).map(Instant::toEpochMilli).map(Date::new).orElse(null);
            this.plateNo = order.getPlateNo();
            this.source = "營業訂單";

            this.acctId = order.getAcctId().longValue();
            this.name = order.getCustName();
            this.mainCell = order.getMainCell();
            setAddress(order);
        }

        public void setAddress(OrderQueryResponse order) {
            if (order.getHhrCityId() != null && order.getHhrAreaId() != null && cities != null) {
                City city = cities.stream().filter(c -> c.getCityId() == order.getHhrCityId()).findAny().orElse(null);
                if (city != null) {
                    if (city.getArea() != null) {
                        Optional.ofNullable(city.getArea()).orElse(Collections.emptyList()).stream()
                            .filter(area -> area.getAreaId() == order.getHhrAreaId())
                            .findAny().map(Area::getAreaName).ifPresent(areaName -> address = String.format("%s%s%s", city.getCityName(), areaName, order.getHhrAddr()));
                    }
                }
            }
        }
    }

}
