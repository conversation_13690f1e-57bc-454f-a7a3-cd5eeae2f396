package com.carplus.subscribe.model.parking;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.UUID;

@Data
@Schema(description = "車牌&時間找營業/非營業訂單response")
public class OrderDepartRecordRequest {
    @Schema(description = "車牌號碼")
    private String plateNo;
    @Schema(description = "查詢時間")
    private Date parkingDate;
    @JsonIgnore
    private String uuid = UUID.randomUUID().toString();
}
