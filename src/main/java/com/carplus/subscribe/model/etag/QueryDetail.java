package com.carplus.subscribe.model.etag;

import lombok.Data;

@Data
public class QueryDetail {

    /**
     * 流水號 流水號_000……流水號_999.用來識別每一筆detail的唯一值
     */
    private String sequenceNo;

    /**
     * 交易編號 門架之交易編號
     */
    private String txnId;

    /**
     * 過站日期時間 含西元年月日時分秒(YYYYMMDDHHMISS)
     */
    private String tollDate;

    /**
     * 還車日期時間 含西元年月日時分秒(YYYYMMDDHHMISS)
     */
    private String returnDate;

    /**
     * 門架 門架名稱
     */
    private String gantry;

    /**
     * 車行方向 車行方向
     */
    private String direction;

    /**
     * 通行費 含小數點四位數(ex: 20.1234)
     */
    private String fee;

    /**
     * 差別費率 含小數點四位數(ex: 20.1234)，有可能為負值
     */
    private String offset;

    /**
     * 暫停收費 Y:正常收費 N:暫停收費
     */
    private String isToll;

    /**
     * 繳費狀態
     * 00:未繳款
     * 01:信用卡(業者刷卡)
     * 02:信用卡(遠通系統刷卡)
     * 03:現金
     */
    private String paymentStatus;
}
