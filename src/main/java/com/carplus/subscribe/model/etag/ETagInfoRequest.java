package com.carplus.subscribe.model.etag;

import com.carplus.subscribe.enums.ETagPayment;
import com.carplus.subscribe.enums.NotPayableReason;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
@Schema(description = "etag 相關資訊")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ETagInfoRequest {

    @NotNull(message = "etag明細編號不可為空")
    @Schema(description = "etag明細編號")
    private int etagInfoId;
    @Schema(description = "是否要收款")
    @NotNull(message = "是否要收款不可為空")
    private Boolean payabled;
    @Schema(description = "實收金額")
    @NotNull(message = "實收金額不可為空")
    private Integer paidETagAmt;
    @Schema(description = "收款方式")
    @JsonProperty("eTagPayment")
    private ETagPayment eTagPayment;
    @Schema(description = "收據是否含通行明細")
    private boolean existETagDetail;
    @Schema(description = "備註")
    private String remark;
    @Schema(description = "不收款原因")
    private NotPayableReason notPayableReason;
    @Schema(description = "信用卡刷卡方式")
    private Integer creditBankAuto;
    @Schema(description = "EDC結帳批號")
    private String edcBatchNo;
    @Schema(description = "Etag出車時間")
    private Instant departDate;
    @Schema(description = "Etag還車時間")
    private Instant returnDate;
}
