package com.carplus.subscribe.model.etag;

import lombok.Data;

import java.util.List;

@Data
public class ETagResult {

    /**
     * 總筆數 提供DETAIL總筆數
     */
    private Integer totalCount;
    /**
     * 總金額 提供DETAIL總金額
     */
    private Double totalAmount;
    /**
     * 總差別費率 提供DETAIL總差別費率，有可能為負值
     */
    private Double totalOffset;
    /**
     * 契約已繳總金額 提供此契約客戶之已繳交易總金額
     */
    private Double payTotalAmount;
    /**
     * 契約未繳總金額 提供此契約客戶之未繳交易總金額
     */
    private Double unPayTotalAmount;
    /**
     * 處理結果 S:成功 F:失敗
     */
    private String status;
    /**
     * 處理描述 Error Code(4碼)+Error Message(失敗才會有值)
     */
    private String description;
    /**
     * 通行費總金額 包含4碼小數點
     */
    private Double rentAmount;
    /**
     * 通行費總差別費率 包含4碼小數點，且有可能為負值
     */
    private Double offsetAmount;
    /**
     * 遠通系統時間 業者當下查詢交易的遠通系統時間
     */
    private String queryTime;

    private List<QueryDetail> details;
}
