package com.carplus.subscribe.model.etag;

import carplus.common.utils.BeanUtils;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.enums.CarPlusFleet;
import com.carplus.subscribe.enums.ETagPayment;
import com.carplus.subscribe.enums.NotPayableReason;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.Instant;
import java.util.Optional;

@Data
@Schema(description = "etag 相關資訊")
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class EtagInfoResponse {


    private Integer id;
    @Schema(description = "應收金額")
    private Integer eTagAmt;
    @Schema(description = "遠通金額")
    private int fetcAmt;
    @Schema(description = "差別費率")
    private int fetcOffSetAmt;
    @Schema(description = "是否為經銷商&格上車")
    private Boolean dealerAndETagAccount = false;
    @Schema(description = "付款銀行別")
    private String eTagBankUnitCode;
    @Schema(description = "已付實收金額, null=門市沒有登打到")
    private Integer paidETagAmt;
    @Schema(description = "車號判斷是否為合法通路(查資料庫有無建檔)")
    private Boolean isValid = false;
    @Schema(description = "是否出/還車遠通回傳 status F")
    private Boolean isSuccess;
    @Schema(description = "收款方式")
    private ETagPayment eTagPayment;
    @Schema(description = "收據含通行明細")
    private boolean existETagDetail;
    @Schema(description = "不收款原因")
    private NotPayableReason notPayableReason;
    @Schema(description = "備註")
    private String remark;
    @Schema(description = "信用卡刷卡方式")
    private Integer creditBankAuto = 3; //參考mrms_sit.CreditBank
    @Schema(description = "EDC結帳批號")
    private String edcBatchNo;
    @Schema(description = "是否要收款")
    private boolean payabled;
    @Schema(description = "銀行交易序號")
    private String transactionNumber;
    @Schema(description = "鎖定編輯eTag金額")
    private boolean lockETagAmt = false;
    @Schema(description = "是否略過遠通還車")
    private boolean passReturnCar = false;
    @Schema(description = "出還車流程節點")
    private Integer eTagFlow;
    @Schema(description = "付款判別")
    private Integer eTagPayFlow;
    @Schema(description = "格上車籍")
    private CarPlusFleet carPlusFleet;
    @Schema(description = "是否立賬")
    private boolean uploaded = false;

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "Etag款項資訊")
    private OrderPriceInfo orderPriceInfo;
    @Schema(description = "款項明細編號")
    private Integer orderPriceInfoId;

    @Schema(description = "經銷商Etag款項資訊")
    private DealerOrderPriceInfo dealerOrderPriceInfo;
    @Schema(description = "經銷商款項明細編號")
    private String dealerOrderPriceInfoId;
    @Schema(description = "期數")
    private Integer stage;
    @Schema(description = "出車之車牌號碼")
    private String plateNo;
    @Schema(description = "出車時間")
    private Instant departDate;
    @Schema(description = "還車時間")
    private Instant returnDate;

    @JsonIgnore
    private boolean returnAndSetSuccess;

    public EtagInfoResponse(ETagInfo etagInfo) {
        BeanUtils.copyProperties(etagInfo, this);
        this.stage = Optional.ofNullable(orderPriceInfo).map(OrderPriceInfo::getStage).orElse(stage);
    }

    public EtagInfoResponse(ETagInfo etagInfo, boolean returnAndSetSuccess) {
        this(etagInfo);
        this.returnAndSetSuccess = returnAndSetSuccess;
    }

    public boolean isPaid() {
        int receiveAmt = Optional.ofNullable(orderPriceInfo).map(OrderPriceInfo::getReceivedAmount).orElse(0);
        return receiveAmt > 0;
    }
}
