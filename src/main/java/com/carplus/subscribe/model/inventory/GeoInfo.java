package com.carplus.subscribe.model.inventory;

import com.carplus.subscribe.enums.CarDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Set;

@Data
@Builder
@Schema(description = "區域資訊")
public class GeoInfo {
    @Schema(description = "車輛類別")
    private CarDefine.CarState carState;

    @Schema(description = "車輛廠牌")
    private Set<CarBrand> carBrands;
}
