package com.carplus.subscribe.model.inventory.resp;


import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.model.inventory.GeoInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class InventoryResponse {

    @JsonIgnore
    private GeoDefine.GeoRegion geoRegionEnum;

    @Schema(description = "區域代號")
    private String geoRegion;

    @Schema(description = "區域名稱")
    private String geoRegionName;

    @Schema(description = "區域車輛庫存數")
    private int geoInventory;

    @Schema(description = "區域資訊")
    private List<GeoInfo> geoInfos;
}