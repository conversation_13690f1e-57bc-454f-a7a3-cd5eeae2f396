package com.carplus.subscribe.model;

import carplus.common.response.BaseJsonView;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@JsonPropertyOrder({"total", "list", "skip", "limit"})
@JsonView(BaseJsonView.class)
public class CustomizePage<E> {


    private long total;

    private long skip;

    private int limit;

    private List<E> list;

    public CustomizePage() {
    }

    private CustomizePage(long total, @Nullable List<E> list, long skip, int limit) {
        super();
        this.total = total;
        this.skip = skip;
        this.limit = limit;
        this.list = Optional.ofNullable(list).orElseGet(ArrayList::new);
    }

    public static <E> CustomizePage<E> of(long total, @Nullable List<E> list, long skip, int limit) {
        return new CustomizePage<>(total, list, skip, limit);
    }
}
