package com.carplus.subscribe.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 紀錄車損資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccidentInfo {
    @Schema(description = "是否還車議價中")
    private String returnNego = "N";
    @Schema(description = "是否車損")
    private boolean carDamaged;
    @Deprecated
    @Schema(description = "備註")
    private String remark;
    @Deprecated
    @Schema(description = "應收自付額")
    private int aRCarLossAmt;
    @Deprecated
    @Schema(description = "實收自付額")
    private int carLossAmt;

    @Schema(description = "主管是否同意")
    private boolean isAgree;
    @Schema(description = "營業人員")
    private String adminId;
    @Schema(description = "主管人員")
    private String managerId;
    @Schema(description = "結案備註")
    private String closeRemark;
    @Schema(description = "結案日期")
    private Date closeDate;
}