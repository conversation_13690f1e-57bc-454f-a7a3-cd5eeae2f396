package com.carplus.subscribe.model.coupon;

import com.carplus.subscribe.enums.coupon.CalendarType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Schema(description = "優惠券折抵")
public class Coupon {

    @Schema(description = "優惠券序號")
    private String sequenceId;

    @Schema(description = "優惠券主檔代碼")
    public String couponId;

    @Schema(description = "優惠券名稱")
    private String couponName;

    @Schema(description = "說明")
    public String description;

    @Schema(description = "適用通路")
    public String scope;

    @Schema(description = "日別限制(假日、平日、大假日)")
    private List<CalendarType> calendarTypes;

    @Schema(description = "折扣方式[1 = 折價,2 = 折扣,3 = 滿額折價,4 = 滿額折扣]")
    public String discountType;

    @Schema(description = "滿額折抵限制金額")
    private int overTotalPrice;

    @Schema(description = "最高折抵金額")
    private int discount;

    @Schema(description = "車款")
    private List<CarSeries> carSeries;

    @Schema(description = "適用車型代碼")
    private List<String> carModelCodes;

    @Schema(description = "適用廠牌代碼")
    private List<String> carBrandCodes;

    @Schema(description = "使用期限日-起", example = "1628838160000")
    public Date startTime;

    @Schema(description = "使用期限日-迄", example = "1628838160000")
    public Date endTime;
}