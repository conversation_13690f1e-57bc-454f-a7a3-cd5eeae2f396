package com.carplus.subscribe.model.coupon;

import com.carplus.subscribe.enums.coupon.CalendarType;
import com.carplus.subscribe.enums.coupon.CarLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;

@Data
public class SearchMemberCouponResponse {

    @Schema(description = "優惠券序號代碼")
    public String sequenceId;

    @Schema(description = "優惠券主檔代碼")
    public String couponId;

    @Schema(description = "優惠券名稱")
    public String couponName;

    @Temporal(TemporalType.TIME)
    @Schema(description = "使用期限日-起", example = "1628838160000")
    public Date startTime;

    @Temporal(TemporalType.TIME)
    @Schema(description = "使用期限日-迄", example = "1628838160000")
    public Date endTime;

    @Temporal(TemporalType.TIME)
    @Schema(description = "建立日期", example = "1628838160000")
    public Date createdTime;

    @Schema(description = "適用通路")
    public String scope;

    @Schema(description = "說明")
    public String description;

    @Schema(description = "折扣方式[1 = 折價,2 = 折扣,3 = 滿額折價,4 = 滿額折扣]")
    public String discountType;

    @Schema(description = "折扣(百分比)/折價金額")
    public String discount;

    @Schema(description = "最高限折")
    public String maxDiscount;

    @Schema(description = "超出總額總金額")
    public String overTotalPrice;

    @Schema(description = "可折扣百分比/可折價金額")
    public String offsetPrice;

    @Schema(description = "最高限折金額")
    public String maxPrice;

    @Schema(description = "與其他優惠券搭配使用")
    public Boolean otherCoupon;

    @Schema(description = "與其他活動搭配使用")
    public Boolean otherActivity;

    @Schema(description = "可否使用紅利")
    public Boolean bonus;

    @Schema(description = "使用時段限制")
    public Boolean availableTimeLimit;

    @Schema(description = "付款限制[1=現金, 2=信用卡]")
    public String paymentLimit;

    @Schema(description = "使用日別(假日、平日、大假日)")
    private List<CalendarType> calendarTypes;

    @Schema(description = "是否車型種類限制")
    private Boolean carLevelLimit;

    @Schema(description = "車型種類")
    private List<CarLevel> carLevels;

    @Schema(description = "是否車款限制")
    private Boolean carSeriesLimit;

    @Schema(description = "車款")
    private List<CarSeries> carSeries;
}
