package com.carplus.subscribe.model.payment;

import lombok.Data;

@Data
public class TradeHistory {
    /**
     * 交易種類  付款 退款
     */
    private int action;
    /**
     * 金額
     */
    private int amount;
    /**
     * 是否成功
     */
    private boolean success;
    /**
     * 退款編號
     */
    private String refundId;
    /**
     * 是否仍在進行中
     */
    private boolean isPending;
    /**
     * 銀行回傳結果
     */
    private String bankResultCode;
    /**
     * 銀行回傳結果
     */
    private String bankResultMsg;
    /**
     * 觸發該狀態的時間
     */
    private Long millis;
}