package com.carplus.subscribe.model.payment.resp;

import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AccountSettlementResponse {
    @Schema(description = "帳務登打")
    private PaymentRes paymentRes;

    @Schema(description = "發票清單")
    private List<Invoices> invoicesList;
}
