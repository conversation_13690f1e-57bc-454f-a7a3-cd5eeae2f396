package com.carplus.subscribe.model.payment.resp;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaymentRes {

    @Schema(description = "訂單檔")
    private Orders orders;

    @Schema(description = "收支明細")
    private List<Account> accounts;
}
