package com.carplus.subscribe.model.payment;


import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.PaymentCategory;
import com.carplus.subscribe.enums.PaymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AccountDetail {

    @Schema(description = "交易明細表流水號")
    private Integer paymentId;
    @Schema(description = "支付目的")
    private PayFor payFor;
    @Schema(description = "付款/退款/取消退款")
    private PaymentCategory paymentCategory;
    @Schema(description = "支付方式")
    private PaymentType paymentType;
    @Schema(description = "TapPay交易序號")
    private String recTradeId;
    @Schema(description = "交易狀態")
    private Integer status;
}
