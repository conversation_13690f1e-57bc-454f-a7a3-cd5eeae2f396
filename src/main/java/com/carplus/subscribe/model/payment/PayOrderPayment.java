package com.carplus.subscribe.model.payment;

import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.util.Date;
import java.util.List;

@Data
@FieldNameConstants
public class PayOrderPayment {

    private Integer paymentId;

    private PayOrderHead payOrderHead;

    private Integer status;
    private String respCode;
    private String respMsg;
    private String paymentCategory;
    private String paymentType;
    private String recTradeId;
    private String transactionNumber;
    private String cardLast4digit;
    private String cardNumber;
    private String authCode;
    private String cardIssuer;
    private String bankId;
    private Integer cardType;
    private Integer issuerType;
    private String remember;
    private String acquirer;
    private Integer originalAmount;
    private Integer refundAmount;
    private String payFor;
    private String payFrom;
    private String platForm;
    private String refundId;
    private String bankResultCode;
    private String bankResultMsg;
    private String payId;
    private String paymentAgent;
    private Integer paymentChannelsId;
    private String additionalData;
    private Date createDate = new Date();
    private Date updateDate = new Date();
    private Date paymentDealDate;
    private Integer delayCaptureInDays;
    private Date applyCaptureDate;
    private Boolean isSupplementary;
    private List<String> affiliateCodes;
    private PayInfo payInfo;

    @Data
    public static class PayInfo {
        private String method;
        private Integer amount;
    }
}
