package com.carplus.subscribe.model.payment.req;

import com.carplus.subscribe.model.payment.PayAuthCardHolder;
import com.carplus.subscribe.model.payment.PayAuthResultUrl;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

@Data
public class PayAuthRequest {

    @Schema(description = "tappay pay by prime/card", allowableValues = "prime, card")
    @NotNull(message = "付款類型不可為空")
    private PayType type;
    // 已綁卡交易 pay by card
    @Schema(description = "卡片安全金鑰")
    private String cardKey;
    // 新卡片付款 pay by prime
    @Schema(description = "用卡號所換得的字串")
    private String prime;
    @Schema(description = "持卡人或購買人資訊")
    private PayAuthCardHolder cardholder;
    @Schema(description = "是否記憶卡號，預設為 false")
    private boolean remember = false;
    @Schema(description = "是否設為預設卡片[預設=Y|不預設=N]")
    private String defaultCard = "N";
    // 共用 model
    @Schema(description = "交易金額")
    @NotNull(message = "交易金額不可為空")
    private Integer amount;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Schema(description = "訂單編號")
    private String orderNo;
    //    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Schema(description = "支付目的[PrePay:預授權|Depart:出車款|Return:還車款|Accident:車損款|Other:其他款項|SecurityDeposit:保證金]")
    private String payFor;
    @Schema(description = "消費者付款方式[Sms:簡訊刷卡|Internet:網路刷卡]")
    private String payFrom;
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Schema(description = "是否開啟 3D 驗證，預設為 false")
    private boolean threeDomainSecure = false;
    @Schema(description = "threeDomainSecure 為 true 時必填")
    private PayAuthResultUrl resultUrl;
    @Schema(description = "支付方式[CreditCard|LinePay|JKOPAY|ApplePay]")
    private String paymentType;
    @Schema(description = "自特定收付渠道[GOSMART|SMART2GO|TWTAXI],不分大小寫,有值則優先於X-SystemKind")
    private String businessType;
    @Schema(description = "額外資訊,需為Json字串")
    private String additionalData;
    @Schema(description = "帳目明細編號")
    private Set<Integer> orderPriceInfoIds;
    @JsonIgnore
    private Integer acctId;

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
        this.threeDomainSecure = "CreditCard".equalsIgnoreCase(paymentType);
    }

    public enum PayType {
        prime,
        card,
    }
}
