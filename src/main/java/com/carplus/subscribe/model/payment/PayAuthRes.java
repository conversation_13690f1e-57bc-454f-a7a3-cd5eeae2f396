package com.carplus.subscribe.model.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PayAuthRes {

    @Schema(description = "TapPay交易序號")
    private String recTradeId;
    @Schema(description = "銀行端的訂單編號")
    private String bankTransactionId;
    @Schema(description = "銀行授權碼")
    private String authCode;
    @Schema(description = "交易金額")
    private int amount;
    @Schema(description = "卡片資訊")
    private CardInfo cardInfo;
    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "付款頁面網址，將此網址回傳至前端跳轉")
    private String paymentUrl;
    @Schema(description = "收單銀行")
    private String acquirer;
}
