package com.carplus.subscribe.model.payment.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SecurityDepositInfoResponse {

    @Schema(description = "保證金應收")
    private int realSecurityDeposit;
    @Schema(description = "保證金已收")
    private int paidSecurityDeposit;
    @Schema(description = "保證金退款交易狀態")
    private Integer paymentStatus;
    @Schema(description = "保證金應退金額")
    private int refundSecurityDeposit;
}
