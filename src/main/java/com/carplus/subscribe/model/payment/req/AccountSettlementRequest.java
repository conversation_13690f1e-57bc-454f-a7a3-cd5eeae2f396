package com.carplus.subscribe.model.payment.req;

import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoiceUpdateRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class AccountSettlementRequest {

    @Schema(description = "帳務登打")
    @NotNull(message = "帳務資訊不可為空")
    private PaymentRequest paymentRequest;

    @Schema(description = "發票作廢/折讓清單")
    private List<InvoiceUpdateRequest> invoiceUpdateRequestList;

    @Schema(description = "開立發票清單")
    private InvoiceNewRequest invoiceNewRequest;
}
