package com.carplus.subscribe.model.payment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CardInfo {

    @Schema(description = "發卡銀行")
    private String issuer;
    @Schema(description = "卡片類別[0 = 信用卡, 1 = 簽帳卡, 2 = 預付卡]")
    private int cardCategory;
    @Schema(description = "卡片種類[-1 = Unknown, 1 = VISA, 2 = MasterCard, 3 = JCB, 4 = Union Pay, 5 = AMEX]")
    private int cardType;
    @Schema(description = "卡片等級")
    private String level;
    @Schema(description = "發卡行國家")
    private String country;
    @Schema(description = "卡片後四碼")
    private String lastFour;
    @Schema(description = "卡片前六碼")
    private String binCode;
    @Schema(description = "發卡銀行中文名稱")
    private String cardName;
    @Schema(description = "發卡銀行代碼")
    private String bankCode;
    @Schema(description = "發卡行國家碼")
    private String countryCode;
    @Schema(description = "卡片到期時間")
    private String expiryDate;
}
