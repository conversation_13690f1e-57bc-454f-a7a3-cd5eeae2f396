package com.carplus.subscribe.model.payment.resp;

import com.carplus.subscribe.model.payment.AdditionalData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RefundRequest {

    @Schema(description = "TapPay交易序號,businessType = smart2go必填", required = false, example = "D202105115AQP4l")
    private String tradeId;

    @Schema(description = "訂單編號", required = true, example = "A202105060001")
    private String orderNo;

    @Schema(description = "退款金額，全額退款可不用填此參數", required = false, example = "")
    private int amount;

    @Schema(description = "自特定收付渠道[GOSMART|SMART2GO|TWTAXI],不分大小寫,有值則優先於X-SystemKind", required = false, example = "SUB")
    private String businessType = "SUBV2";

    @Schema(description = "額外資訊,需為Json字串", required = false, example = "'{\"A\":\"1\",\"B\":[\"2\",\"3\"]}'")
    private String additionalData;

    private AdditionalData additionalDataObject;

}
