package com.carplus.subscribe.model.payment;

import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@JsonIdentityInfo(generator = ObjectIdGenerators.IntSequenceGenerator.class, property = "@headId")
@FieldNameConstants
public class PayOrderHead implements Serializable {

    private int headId;

    private String orderId;
    private String memberId;
    private String payerName;
    private String payerPhone;
    private String email;
    private String tradeDetails;
    private int tradeAmount;
    private String orderFrom;
    private String status;
    private Date createDate = new Date();
    private Date updateDate = new Date();

    private List<PayOrderPayment> payOrderPayments;

    public List<PaymentInfo> toPaymentInfos() {
        List<PaymentInfo> paymentInfos = new ArrayList<>();
        for (PayOrderPayment payOrderPayment : payOrderPayments) {
            PaymentInfo paymentInfo = new PaymentInfo();
            paymentInfo.setPaymentId(payOrderPayment.getPaymentId());
            paymentInfo.setOrderId(orderId);
            paymentInfo.setTradeId(payOrderPayment.getRecTradeId());
            paymentInfo.setOrderFrom(orderFrom);
            paymentInfo.setAuthCode(payOrderPayment.getAuthCode());
            paymentInfo.setAmount(payOrderPayment.getRefundAmount() != null ? payOrderPayment.getRefundAmount() : payOrderPayment.getOriginalAmount());
            paymentInfo.setPaymentType(payOrderPayment.getPaymentType() == null ? null : PaymentType.valueOf(payOrderPayment.getPaymentType()));
            paymentInfo.setPaymentCategory(payOrderPayment.getPaymentCategory() == null ? null : PaymentCategory.valueOf(payOrderPayment.getPaymentCategory()));
            paymentInfo.setCardNumber(payOrderPayment.getCardNumber());
            paymentInfo.setStatus(payOrderPayment.getStatus());
            paymentInfo.setMsg(payOrderPayment.getRespMsg());
            paymentInfo.setAcquirer(payOrderPayment.getAcquirer() == null ? null : Acquirer.valueOf(payOrderPayment.getAcquirer()));
            paymentInfo.setPayFor(payOrderPayment.getPayFor() == null ? null : PayFor.valueOf(payOrderPayment.getPayFor()));
            paymentInfo.setPayFrom(payOrderPayment.getPayFrom() == null ? null : PayFrom.valueOf(payOrderPayment.getPayFrom()));
            paymentInfo.setRefundId(payOrderPayment.getRefundId());
            paymentInfo.setTransactionNumber(payOrderPayment.getTransactionNumber());
            paymentInfo.setAdditionalData(payOrderPayment.getAdditionalData());
            paymentInfo.setAffiliateCodes(payOrderPayment.getAffiliateCodes());
            paymentInfo.setPaymentDealDate(payOrderPayment.getPaymentDealDate());
            paymentInfo.setBankResultMsg(payOrderPayment.getBankResultMsg());
            paymentInfo.setBankResultCode(payOrderPayment.getBankResultCode());
            if (payOrderPayment.getAcquirer() == Acquirer.TW_TAISHIN.name()) {
                paymentInfo.setChargeType(ChargeType.Tappay_TAISHIN.getCreditBankAuto());
            } else if (payOrderPayment.getAcquirer() == Acquirer.TW_CTBC.name()) {
                paymentInfo.setChargeType(ChargeType.Tappay_CTBC.getCreditBankAuto());
            } else if (payOrderPayment.getAcquirer() == Acquirer.TW_NCCC.name()) {
                paymentInfo.setChargeType(ChargeType.Tappay_NCCC.getCreditBankAuto());
            }
            if (paymentInfo.getPaymentType() == PaymentType.CreditCard) {
                paymentInfo.setAccountType(AccountType.Credit);
            }
            paymentInfos.add(paymentInfo);
        }
        return paymentInfos;
    }
}
