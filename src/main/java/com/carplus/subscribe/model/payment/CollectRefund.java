package com.carplus.subscribe.model.payment;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

@NoArgsConstructor
@Data
public class CollectRefund {

    /**
     * 計價序號
     */
    private Integer pricingAuto;
    /**
     * 收/退款站所代號 Login 站所
     */
    private String stationCode = "";
    /**
     * 刪除
     */
    private Integer btnDelFlg = 0;
    /**
     * 收/退款序號
     */
    private Integer keyNo = 0;
    /**
     * 方式
     */
    private String paymentType = "";
    /**
     * 收退款金額
     */
    private Integer realIncomeAmt;

    /**
     * 刷卡
     */
    private Integer chkCredit = 0;
    /**
     * 交易序號
     */
    private String transactionNumber;
    /**
     * 刷卡金額
     */
    private Integer creditAmt = 0;
    /**
     * 發卡銀行名稱
     */
    private String issueBankCode = "";
    /**
     * 刷卡卡卡種
     */
    private String cardType = "";
    /**
     * 刷卡方式
     */
    private Integer chargeType;
    /**
     * 信用卡號後四碼
     */
    private String cardNoLast4 = "";
    private String cardNo1 = "";
    private String cardNo2 = "";
    private String cardNo3 = "";
    /**
     * 信用卡有效年月
     */
    private String cardVaild = "";
    /**
     * 信用卡授權碼
     */
    private String authCode = "";

    /**
     * 匯款
     */
    private Integer chkRemit = 0;
    /**
     * 匯款金額
     */
    private Integer remitAmt = 0;
    /**
     * 匯款人
     */
    private String remitter = "";
    /**
     * 匯款帳號
     */
    private String remitAccCode = "";
    /**
     * 匯款編號
     */
    private Long remitNo = 0L;

    /**
     * 支票
     */
    private Integer chkCheck = 0;
    /**
     * 支票總額金額
     */
    private Integer checkAmtTtl = 0;
    /**
     * 支票金額
     */
    private Integer checkAmt = 0;
    /**
     * 支票號碼
     */
    private String checkNo = "";
    /**
     * 開票人
     */
    private String drawer = "";
    /**
     * 票據指定銀行(銀行名稱)
     */
    private String checkAppointBack = "";
    /**
     * 甲存帳號
     */
    private String checkingAccNo = "";
    /**
     * 票據到期日(民國年)
     */
    private String checkDueDate = "";

    /**
     * 現金
     */
    private Integer chkCash = 0;
    /**
     * 現金金額
     */
    private Integer cashAmt = 0;

    /**
     * 退款 0.現金 1.刷卡 2.匯款
     */
    private Integer rdoReturnType = -1;
    /**
     * 退款 現金 退款金額
     */
    private Integer cashAmtR = 0;
    /**
     * 退款 刷卡 刷退金額
     */
    private Integer creditAmtR = 0;
    /**
     * 退款 匯款 匯款金額
     */
    private Integer remitAmtR = 0;
    /**
     * 退款 匯款 匯款銀行
     */
    private String remitBankR = "";
    /**
     * 退款 匯款 匯款分行
     */
    private String remitBranchBankR = "";
    /**
     * 退款 匯款 匯款帳號
     */
    private String remitAccCodeR = "";
    /**
     * 退款 匯款 戶名
     */
    private String remitAcctNameR = "";

    private String belongStatus = "";
    private String lastUserCode;
    private String lastUpdateIp;
    private String lastUpdatePgmId;

    public CollectRefund(@NonNull Integer pricingAuto, @NonNull Orders orders) {
        this.pricingAuto = pricingAuto;
        realIncomeAmt = 0; // check
//        lastUserCode = orders.getLastUserCode(); // come from order
//        lastUpdateIp = orders.getLastUpdateIp(); // come from order
        lastUpdatePgmId = "J02"; // check
    }
}
