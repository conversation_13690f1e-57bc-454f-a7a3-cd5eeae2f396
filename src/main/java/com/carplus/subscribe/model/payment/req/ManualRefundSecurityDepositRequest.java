package com.carplus.subscribe.model.payment.req;

import com.carplus.subscribe.enums.ManualRefundStatus;
import com.carplus.subscribe.enums.ManualRefundMethod;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

@Data
public class ManualRefundSecurityDepositRequest {

    @Schema(description = "申請日期")
    private Instant applyDate;

    @Schema(description = "人工退款狀態")
    private ManualRefundStatus manualRefundStatus = ManualRefundStatus.APPLY;

    @Schema(description = "退款類別")
    private ManualRefundMethod refundMethod = ManualRefundMethod.Credit;
}
