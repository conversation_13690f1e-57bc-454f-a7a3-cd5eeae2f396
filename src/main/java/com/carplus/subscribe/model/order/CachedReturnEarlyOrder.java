package com.carplus.subscribe.model.order;

import com.carplus.subscribe.db.mysql.dto.ETagInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrderPriceInfoDTO;
import com.carplus.subscribe.db.mysql.dto.OrdersDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CachedReturnEarlyOrder {

    private OrdersDTO order;
    private List<OrderPriceInfoDTO> orderPriceInfos;
    private ETagInfoDTO etagInfo;
    private List<OrderPriceInfoDTO> orderPriceInfoDTOsToDelete;

}
