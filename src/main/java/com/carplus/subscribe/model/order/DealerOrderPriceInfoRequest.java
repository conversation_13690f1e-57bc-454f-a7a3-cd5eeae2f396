package com.carplus.subscribe.model.order;

import carplus.common.enums.HeaderDefine;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.TransactionItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class DealerOrderPriceInfoRequest {

    @Schema(description = "經銷商名稱")
    @NotNull(message = "經銷商名稱不可為空")
    private String dealerName;

    @Schema(description = "經銷商訂單編號")
    private String orderNo;

    @Schema(description = "交易ID")
    private String tradeId;

    @Schema(description = "付款日期")
    private Instant paymentDate;

    @Schema(description = "費用目的")
    private String payFor;

    @Schema(description = "交易項目")
    private Integer transactionItem;

    @Schema(description = "交易金額")
    @NotNull(message = "交易金額不可為空")
    private Integer transactionAmt = 0;

    @Schema(description = "計算公式")
    private String formula;

    @Schema(description = "是否分潤")
    @NotNull(message = "是否分潤不可為空")
    private Boolean isProfitSharing;

    @Schema(description = "分潤比例")
    private Double profitSharingRatio;

    public static DealerOrderPriceInfoRequest getDealerOrderPriceInfoRequest(ETagInfo etaginfo) {
        DealerOrderPriceInfoRequest dealerOrderPriceInfoRequest = new DealerOrderPriceInfoRequest();
        dealerOrderPriceInfoRequest.setDealerName(HeaderDefine.SystemKind.SEALAND);
        dealerOrderPriceInfoRequest.setOrderNo(etaginfo.getOrderNo());
        dealerOrderPriceInfoRequest.setPaymentDate(etaginfo.getReturnDate());
        dealerOrderPriceInfoRequest.setPayFor(PayFor.ETag.getName());
        dealerOrderPriceInfoRequest.setTransactionItem(TransactionItem.ETAG.getCode());
        dealerOrderPriceInfoRequest.setTransactionAmt(etaginfo.getETagAmt());
        dealerOrderPriceInfoRequest.setIsProfitSharing(false);
        return dealerOrderPriceInfoRequest;
    }
}
