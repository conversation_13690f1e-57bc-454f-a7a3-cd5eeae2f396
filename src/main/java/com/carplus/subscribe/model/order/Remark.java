package com.carplus.subscribe.model.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

@FieldNameConstants
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "訂單備註")
public class Remark {

    @Schema(description = "備註內容")
    private String content;

    @Schema(description = "備註者工號")
    private String remarkerId;

    @Schema(description = "備註者名稱")
    private String remarkerName;

    @Schema(description = "備註建立時間")
    private Date createTime;

    @Schema(description = "備註更新時間")
    private Date updateTime;

}
