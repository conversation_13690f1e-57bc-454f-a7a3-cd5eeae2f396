package com.carplus.subscribe.model.order;

import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.RenewableType;
import com.carplus.subscribe.enums.SubscribeStatus;
import com.carplus.subscribe.model.PriceInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainContractV2Response {
    @Schema(description = "合約編號")
    private String mainContractNo;

    @Schema(description = "訂閱狀態")
    private SubscribeStatus status;

    @Schema(description = "預計開始時間")
    private Instant expectStartDate;

    @Schema(description = "實際開始時間")
    private Instant startDate;

    @Schema(description = "預計結束時間")
    private Instant expectEndDate;

    @Schema(description = "實際結束時間")
    private Instant endDate;

    @Schema(description = "出發站所")
    private String departStationCode;

    @Schema(description = "出發站所名稱")
    private String departStationName;

    @Schema(description = "還車站所")
    private String returnStationCode;

    @Schema(description = "還車站所名稱")
    private String returnStationName;

    @Schema(description = "車型")
    private CarModel carModel;

    @Schema(description = "是否需付款")
    private boolean isNeedPaid;

    @Schema(description = "是否可續約")
    private RenewableType isRenewable;

    @Schema(description = "價格資訊")
    private PriceInfo priceInfo;

    @Schema(description = "訂單合約月份總和")
    private int sumMonth;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "訂單數量")
    private long orderCount;

    @Schema(description = "備註")
    private String memo;

    public String getStatusName() {
        return Optional.ofNullable(status).map(SubscribeStatus::getName).orElse(null);
    }

    public MainContractV2Response(MainContractResponse mainContractResponse) {
        List<OrderResponse> ordersList = mainContractResponse.getContracts().stream()
            .flatMap(contract -> contract.getOrderResponses().stream())
            .collect(Collectors.toList());
        this.mainContractNo = mainContractResponse.getMainContractNo();
        this.status = SubscribeStatus.getByMainContract(mainContractResponse);
        this.expectStartDate = mainContractResponse.getExpectStartDate();
        this.startDate = mainContractResponse.getStartDate();
        this.expectEndDate = mainContractResponse.getExpectEndDate();
        this.endDate = mainContractResponse.getEndDate();
        this.departStationCode = mainContractResponse.getDepartStationCode();
        this.departStationName = mainContractResponse.getDepartStationName();
        this.returnStationCode = mainContractResponse.getReturnStationCode();
        this.returnStationName = mainContractResponse.getReturnStationName();
        this.carModel = mainContractResponse.getCarModel();
        this.isNeedPaid = mainContractResponse.isNeedPaid();
        this.isRenewable = mainContractResponse.getIsRenewable();
        this.priceInfo = mainContractResponse.getOriginalPriceInfo();
        this.plateNo = mainContractResponse.getPlateNo();
        this.sumMonth = ordersList.stream().filter(orders -> OrderStatus.of(orders.getStatus()).isSumOrder()).mapToInt(Orders::getMonth).sum();
        this.orderCount = ordersList.size();
        if (CollectionUtils.isNotEmpty(mainContractResponse.getMsgForCust())) {
            this.memo = mainContractResponse.getMsgForCust().get(0).getContent();
        }
        if (ordersList.size() == 1 && (ordersList.get(0).getStatus() == OrderStatus.CANCEL.getStatus()
            || ordersList.get(0).getStatus() == OrderStatus.CREDIT_REJECT.getStatus()
            || ordersList.get(0).getStatus() == OrderStatus.CREDITED.getStatus()
            || ordersList.get(0).getStatus() == OrderStatus.CREDIT_PENDING.getStatus())) {
            this.expectEndDate = ordersList.get(0).getExpectEndDate();
        } else {
            Orders processingOrder = ordersList.stream().filter(o -> o.getStatus() == OrderStatus.BOOKING.getStatus()).findFirst().orElse(null);
            if (processingOrder != null) {
                this.expectEndDate = processingOrder.getExpectEndDate();
            } else {
                ordersList.stream().filter(o -> o.getStatus() == OrderStatus.DEPART.getStatus()).findFirst().ifPresent(o -> this.expectEndDate = o.getExpectEndDate());
            }
        }
    }

}
