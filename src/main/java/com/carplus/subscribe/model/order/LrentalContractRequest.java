package com.carplus.subscribe.model.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class LrentalContractRequest {
    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "0.無 1.出險 2.失竊 3.接送 4.洪水 5.維修 6.安心用車專案(出險代步牽送)")
    private List<String> replaceCodes;

    @Schema(description = "備註")
    private String memo;

    @Schema(description = "預期領牌照日")
    private Date licenseExpDate;

}
