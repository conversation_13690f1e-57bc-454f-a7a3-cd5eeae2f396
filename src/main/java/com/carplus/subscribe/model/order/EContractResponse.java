package com.carplus.subscribe.model.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EContractResponse {
    private Integer id;
    private String name;
    private String userName;
    private Instant createDate;
    private Instant updateDate;
    private String memberName;
    private String urlPath;
}
