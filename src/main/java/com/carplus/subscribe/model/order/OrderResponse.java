package com.carplus.subscribe.model.order;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.enums.RenewableType;
import com.carplus.subscribe.enums.SubOrderStatus;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderResponse extends Orders {

    /**
     * 款項摘要
     */
    private CommonOrderPriceInfoResponse priceInfoResponse;

    /**
     * 車型圖片
     */
    private CarResponse car;

    /**
     * 是否可續約
     */
    private RenewableType isRenewable;

    private double mileageFee;

    private SubOrderStatus subOrderStatus;

    @Schema(description = "保免責險費用")
    private Integer disclaimerFee;
    @Schema(description = "代步車費用")
    private Integer replacementCarFee;

    public SubOrderStatus getSubOrderStatus() {
        if (subOrderStatus == null && getStatus() != null) {
            subOrderStatus = SubOrderStatus.getByOrder(this);
        }
        return subOrderStatus;
    }

    public String getSubOrderStatusName() {
        return Optional.ofNullable(getSubOrderStatus()).map(SubOrderStatus::getName).orElse(null);
    }

    /**
     * 是否需要付款
     */
    @JsonProperty("isNeedPay")
    private boolean isNeedPay() {
        if (getStatus() != null && !OrderStatus.of(getStatus()).isNeedPay()) {
            return false;
        }
        return Optional.ofNullable(priceInfoResponse)
            .map(CommonOrderPriceInfoResponse::getAmount)
            .filter(amount -> amount > 0)
            .isPresent();
    }

}
