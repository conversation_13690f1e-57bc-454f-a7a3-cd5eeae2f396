package com.carplus.subscribe.model.order;

import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.enums.CarReady;
import com.carplus.subscribe.enums.RenewableType;
import com.carplus.subscribe.model.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainContractResponse {
    @Schema(description = "合約編號")
    private String mainContractNo;

    @Schema(description = "合約狀態")
    private Integer status;

    @Schema(description = "訂車客戶編號")
    private Integer acctId;

    @Schema(description = "訂車身分證號")
    private String idNo;

    @Schema(description = "車型代碼")
    private String carModelCode;

    @Schema(description = "實際排車方案")
    private Integer givenCarLevel;

    @Schema(description = "實際排車的車型代碼")
    private String givenCarModelCode;

    @Schema(description = "駕駛人")
    private Driver driver;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "車型")
    private CarModel carModel;

    @Schema(description = "預計開始時間")
    private Instant expectStartDate;

    @Schema(description = "實際開始時間")
    private Instant startDate;

    @Schema(description = "預計結束時間")
    private Instant expectEndDate;

    @Schema(description = "出車後預計結束時間")
    private Instant newExpectEndDate;

    @Schema(description = "實際結束時間")
    private Instant endDate;

    @Schema(description = "出發站所")
    private String departStationCode;

    @Schema(description = "出發站所名稱")
    private String departStationName;

    @Schema(description = "還車站所")
    private String returnStationCode;

    @Schema(description = "還車站所名稱")
    private String returnStationName;

    @Schema(description = "租車備註")
    private String remark;

    @Schema(description = "客戶備註")
    private String custRemark;

    @Schema(description = "對客戶留言")
    private List<MsgForCust> msgForCust;

    @Type(type = "json")
    @Schema(description = "線上訂單初始金額明細")
    private PriceInfo originalPriceInfo;

    @Schema(description = "事故類別 1:車損 2:失竊")
    private Integer accidentType;

    @Type(type = "json")
    @Schema(description = "車損資訊")
    private AccidentInfo accidentInfo;

    @Schema(description = "結案人員")
    private String closeUser;

    @Schema(description = "結案日期")
    private Instant closeDate;

    @Schema(description = "結案備註")
    private String closeMemo;

    @Schema(description = "UTM活動")
    private String utmCampaign;

    @Schema(description = "UTM來源")
    private String utmSource;

    @Schema(description = "UTM裝置")
    private String utmMedium;

    @Schema(description = "介紹人資訊")
    @Type(type = "json")
    private ReferInfo referInfo;

    @Schema(description = "訂單來源")
    private Integer custSource;

    @Schema(description = "法人承租人")
    private CompanyDriver companyDriver;

    @Schema(description = "備車註記")
    private CarReady carReady;

    @Schema(description = "合約")
    private List<ContractInfo> contracts;

    @Schema(description = "Srental母單號碼")
    private String parentNo;

    @Schema(description = "是否需付款")
    private boolean isNeedPaid;

    @Schema(description = "車型圖片")
    private List<CarModelImage> carModelImages;

    @Schema(description = "是否可續約")
    private RenewableType isRenewable;

}
