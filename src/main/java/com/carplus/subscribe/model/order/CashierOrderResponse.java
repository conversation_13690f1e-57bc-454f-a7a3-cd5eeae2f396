package com.carplus.subscribe.model.order;

import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.LegalOperationReason;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CashierOrderResponse extends Orders {

    private String securityDepositPaymentCardNo;

    private BuChangeLog buChangeLog;

    @Schema(description = "是否執行過法務作業")
    private Boolean isLegalOperationExecuted;

    @Schema(description = "執行法務作業事由")
    private LegalOperationReason executedLegalOperationReason;

    @Schema(description = "訂單建立者名稱")
    private String orderCreatorName;
}
