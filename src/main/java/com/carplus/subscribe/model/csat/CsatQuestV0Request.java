package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CsatQuestV0Request extends CsatQuestGenerateRequest {

    public CsatQuestV0Request(CsatQuestGenerateRequest request) {
        super(request.getCsatId(),
            request.getCsatMemo(),
            request.getQ0(),
            request.getQ1(),
            request.getQ2(),
            request.getQ3(),
            request.getQ4(),
            request.getQ5(),
            request.getQ6(),
            request.getQ7(),
            request.getVersion(),
            request.isFinish());
    }

    @Override
    public CsatQuest toCsatQuest() {
        CsatQuest csatQuest = new CsatQuest();
        csatQuest.setCsatId(getCsatId());
        csatQuest.setQ0(getQ0());
        csatQuest.setQ1(getQ1());
        csatQuest.setQ2(getQ2());
        csatQuest.setQ3(getQ3());
        csatQuest.setQ4(getQ4());
        csatQuest.setQ5(getQ5());
        csatQuest.setQ6(getQ6());
        csatQuest.setQ7(getQ7());
        csatQuest.setVersion(getVersion());
        return csatQuest;
    }

}
