package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.carplus.subscribe.model.csat.CsatQuestV0.CsatQuestV0Constant.UNANSWERED;

@Data
public class CsatQuestV0 extends GenerateCsatQuest {

    private Integer csatId;
    @Schema(description = "是否接受滿意度調查")
    private String q0;
    @Schema(description = "是否接受滿意度調查")
    private Integer q0Code;
    @Schema(description = "交車時，是否有車輛配件或證件缺件?")
    private String q1;
    @Schema(description = "交車時，是否有車輛配件或證件缺件?")
    private Integer q1Code;
    @Schema(description = "交車至今，車輛功能是否一切正常?")
    private String q2;
    @Schema(description = "交車至今，車輛功能是否一切正常?")
    private Integer q2Code;
    @Schema(description = "需求車輛時間?")
    private String q3;
    @Schema(description = "需求車輛時間?")
    private Integer q3Code;
    @Schema(description = "租車主要用途?")
    private String q4;
    @Schema(description = "租車主要用途?")
    private Integer q4Code;
    @Schema(description = "對於業務員服務是否滿意(新)?")
    private Integer q5;
    @Schema(description = "對於產品品質是否滿意(新)?")
    private Integer q6;
    @Schema(description = "對於本次交車過程，您是否有其他意見反應?")
    private String q7;


    public CsatQuestV0(CsatQuest quest) {
        this.csatId = quest.getCsatId();
        setQ0(quest.getQ0());
        setQ1(quest.getQ1());
        setQ2(quest.getQ2());
        setQ3(quest.getQ3());
        setQ4(quest.getQ4());
        this.q5 = quest.getQ5();
        this.q6 = quest.getQ6();
        this.q7 = quest.getQ7();
        super.setVersion(quest.getVersion());
    }

    private void setQ0(Integer q0) {
        String result = UNANSWERED;
        if (q0 != null) {
            result = CsatQuestV0Constant.q0Map.getOrDefault(q0, UNANSWERED);
        }
        this.q0 = result;
        this.q0Code = q0 == null ? 0 : q0;
    }

    private void setQ1(Integer q1) {
        String result = null;
        if (q1 != null) {
            result = CsatQuestV0Constant.q1Map.getOrDefault(q1, UNANSWERED);
        }
        this.q1 = result;
        this.q1Code = q1;
    }

    /**
     * 交車至今，車輛功能是否一切正常?
     */
    private void setQ2(Integer q2) {
        String result = null;
        if (q2 != null) {
            result = CsatQuestV0Constant.q2Map.getOrDefault(q2, UNANSWERED);
        }
        this.q2 = result;
        this.q2Code = q2;
    }

    /**
     * 需求車輛時間?
     */
    private void setQ3(Integer q3) {
        String result = null;
        if (q3 != null) {
            result = CsatQuestV0Constant.q3Map.getOrDefault(q3, UNANSWERED);
        }
        this.q3 = result;
        this.q3Code = q3;
    }

    /**
     * 租車主要用途?
     */
    private void setQ4(Integer q4) {
        String result = null;
        if (q4 != null) {
            result = CsatQuestV0Constant.q4Map.getOrDefault(q4, UNANSWERED);
        }
        this.q4 = result;
        this.q4Code = q4;
    }

    @Override
    public String questMemo() {
        return q7;
    }

    public boolean isRefuse() {
        return Objects.equals(q0, "1") || Objects.equals(q0Code, 1);
    }

    public static class CsatQuestV0Constant {

        public static final String YES = "Y";
        public static final String NO = "N";
        public static final String UNANSWERED = "未填答";
        public static final String NO_MISSING_PARTS = "無缺件";
        public static final String HAD_MISSING_PARTS = "曾缺件";
        public static final String NOT_COMPLETED = "尚未補齊";
        public static final String NORMAL = "一切正常";
        public static final String FIXED_ANOMALY = "異常已修復";
        public static final String UNFIXED_ANOMALY = "異常，尚未修復";
        public static final String LONG_TERM = "長期使用";
        public static final String SHORT_TERM = "短期使用";
        public static final String COMMUTE = "上下班代步";
        public static final String TRAVEL = "旅遊";
        public static final String SHORT_TERM_USE = "回國短期使用";
        public static final String PRACTICE_DRIVING = "練車";
        public static final String OTHER = "其他";


        private static final Map<Integer, String> q0Map = new HashMap<>();
        private static final Map<Integer, String> q1Map = new HashMap<>();
        private static final Map<Integer, String> q2Map = new HashMap<>();
        private static final Map<Integer, String> q3Map = new HashMap<>();

        private static final Map<Integer, String> q4Map = new HashMap<>();

        static {
            q0Map.put(0, NO);
            q0Map.put(1, YES);
            q1Map.put(0, UNANSWERED);
            q1Map.put(1, NO_MISSING_PARTS);
            q1Map.put(2, HAD_MISSING_PARTS);
            q1Map.put(3, NOT_COMPLETED);
            q2Map.put(1, NORMAL);
            q2Map.put(2, FIXED_ANOMALY);
            q2Map.put(3, UNFIXED_ANOMALY);
            q3Map.put(1, LONG_TERM);
            q3Map.put(2, SHORT_TERM);
            q4Map.put(1, COMMUTE);
            q4Map.put(2, TRAVEL);
            q4Map.put(3, SHORT_TERM_USE);
            q4Map.put(4, PRACTICE_DRIVING);
            q4Map.put(5, OTHER);
        }
    }
}
