package com.carplus.subscribe.model.csat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

@Data
public class CsatAssignYearMonthUpdateRequest {
    
    @Schema(description = "訂單編號列表")
    @NotEmpty(message = "訂單編號列表不可為空")
    private List<String> orderNos;

    @Schema(description = "指派年月(YYYYMM)", example = "202509")
    @NotNull(message = "指派年月不可為空")
    @Pattern(regexp = "^[1-9]\\d{3}(0[1-9]|1[0-2])$", 
            message = "指派年月格式錯誤，須為YYYYMM格式")
    private String assignYearMonth;
}