package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;

import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import static com.carplus.subscribe.constant.CarPlusConstant.TIME_ZONE;

public class CsatCsv {
    public static String[] HEADS = Arrays.stream(CsatCsv.class.getDeclaredFields())
        .filter(field -> field.isAnnotationPresent(Parsed.class))
        .map(field -> field.getAnnotation(Parsed.class))
        .map(parsed -> parsed.field()[0])
        .toArray(String[]::new);
    @Parsed(field = "訂單編號")
    private String orderNo;
    @Parsed(field = "車牌號碼")
    private String plateNo;
    @Parsed(field = "廠牌")
    private String carBrand;
    @Parsed(field = "車型")
    private String carModel;
    @Parsed(field = "出廠年月")
    private String mfgYear;
    @Parsed(field = "訂閱類別")
    private String carState;
    @Parsed(field = "出車站點")
    private String departStationName;
    private String departMemberId;
    @Parsed(field = "出車人員")
    private String departMemberName;
    @Parsed(field = "實際出車時間")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date departDate;
    @Parsed(field = "新單")
    private String isNewOrder;
    @Parsed(field = "客戶姓名")
    private String userName;
    @Parsed(field = "客戶電話")
    private String mainCell;
    @Parsed(field = "是否拒訪")
    private Boolean isRefuse = false;
    //    @Parsed(field = "拒訪原因")
    private String refusedMemo;
    @Parsed(field = "派工月份")
    private Integer assignYearMonth;
    @Parsed(field = "電訪人員編號")
    private String assignMemberId;
    @Parsed(field = "電訪人員")
    private String assignMemberName;
    @Parsed(field = "電訪日期")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date assignDate;
    @Parsed(field = "電訪狀態")
    private String status;
    @Parsed(field = "電訪備註")
    private String csatMemo;
    @Parsed(field = "滿意度調查狀態")
    private String csatQuestStatus;
    @Parsed(field = "Q1配件證件")
    private String q1;
    @Parsed(field = "Q2車輛功能")
    private String q2;
    @Parsed(field = "Q7備註")
    private String q7;
    @Parsed(field = "Q5業代滿意度")
    private Integer q5;
    @Parsed(field = "Q6產品滿意度")
    private Integer q6;

    public CsatCsv(CsatResponse response, Csat csat, GenerateCsatQuest quest, CarBrandModelDTO dto) {
        if (response != null) {
            this.orderNo = response.getOrderNo();
            this.userName = response.getUserName();
            this.mainCell = response.getMainCell();
            this.plateNo = response.getPlateNo();
            this.carBrand = response.getCarBrand();
            this.carModel = response.getCarModel();
            this.refusedMemo = response.getRefusedMemo();
            this.isRefuse = response.getIsRefuse();
            this.isNewOrder = Objects.equals(Boolean.TRUE, response.getIsNewOrder()) ? "是" : "否";
            this.departMemberId = response.getDepartMemberId();
            this.departMemberName = response.getDepartMemberName();
            this.assignMemberId = response.getAssignMemberId();
            this.assignMemberName = response.getAssignMemberName();
            this.assignDate = response.getAssignDate();
            this.status = response.getStatus().getDescription();
            this.departDate = Date.from(response.getDepartDate());
            this.departStationName = response.getDepartStationName();

        }
        if (csat != null) {
            this.csatQuestStatus = CsatQuestStatus.of(csat.getQuestStatus()).getDescription();
            this.csatMemo = csat.getCsatMemo();
            this.assignYearMonth = csat.getAssignYearMonth();
        }

        if (quest != null) {
            if (quest instanceof CsatQuestV0) {
                CsatQuestV0 v0 = (CsatQuestV0) quest;
                this.q1 = v0.getQ1();
                this.q2 = v0.getQ2();
                this.q7 = v0.getQ7();
                this.q5 = v0.getQ5();
                this.q6 = v0.getQ6();
            }
        }
        if (dto != null) {
            this.mfgYear = dto.getCar().getMfgYear();
            this.carState = Optional.ofNullable(dto.getCar().getCarState()).map(CarDefine.CarState::getName).orElse("");
        }
    }


}
