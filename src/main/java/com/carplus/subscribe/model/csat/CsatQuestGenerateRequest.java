package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CsatQuestGenerateRequest implements ICsatQuestGenerateRequest {

    @NotNull(message = "電訪編號不可為空")
    private Integer csatId;

    private String csatMemo;

    private Integer q0;

    private Integer q1;

    private Integer q2;

    private Integer q3;

    private Integer q4;

    private Integer q5;

    private Integer q6;

    private String q7;

    private Integer version;

    private boolean finish;

    public CsatQuest toCsatQuest() {
        switch (version) {
            case 0:
                return new CsatQuestV0Request(this).toCsatQuest();
            default:
                throw new SubscribeException(SubscribeHttpExceptionCode.CSAT_QUEST_NEED_IMPLEMENTS);
        }


    }

    public GenerateCsatQuest toVersionCsatQuest() {
        switch (version) {
            case 0:
                return new CsatQuestV0(new CsatQuestV0Request(this).toCsatQuest());
            default:
                throw new SubscribeException(SubscribeHttpExceptionCode.VERSION_CSAT_QUEST_NEED_IMPLEMENTS);
        }
    }
}
