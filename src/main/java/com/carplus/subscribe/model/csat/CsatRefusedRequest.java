package com.carplus.subscribe.model.csat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class CsatRefusedRequest {
    @Schema(description = "身分證")
    @NotBlank(message = "身分證不可為空")
    private String loginId;

    @Schema(description = "是否拒訪")
    private Boolean isRefused;

    @Schema(description = "拒訪日期")
    private Date refuseDate;

    @Schema(description = "拒訪原因")
    private String refuseMemo;

    @Schema(description = "來源")
    @NotNull(message = "來源不可為空")
    private Integer source;
}
