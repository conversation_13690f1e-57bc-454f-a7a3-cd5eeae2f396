package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.enums.CsatOrderSource;
import com.carplus.subscribe.enums.csat.CsatStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Date;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CsatResponse {


    private Integer id;

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "用戶姓名")
    private String userName;

    @Schema(description = "電話")
    private String mainCell;

    @Schema(description = "身分證字號")
    private String idNo;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "拒訪原因")
    private String refusedMemo;

    @Schema(description = "是否拒訪")
    private Boolean isRefuse = false;

    @Schema(description = "出車人員編號")
    private String departMemberId;

    @Schema(description = "出車人員")
    private String departMemberName;

    @Schema(description = "電訪狀態 NOT_CALLED(0, \"未電訪\"), IN_PROGRESS(1, \"電訪中\"), COMPLETED(2, \"已電訪\");")
    private CsatStatus status;

    @Schema(description = "電訪人員編號")
    private String assignMemberId;

    @Schema(description = "電訪人員名稱")
    private String assignMemberName;

    @Schema(description = "電訪日期")
    private Date assignDate;

    @Schema(description = "出車時間")
    private Instant departDate;

    @Schema(description = "出車站點")
    private String departStationName;

    @Schema(description = "車型")
    private String carModel;

    @Schema(description = "車廠")
    private String carBrand;

    @Schema(description = "來源")
    private int source;

    @Schema(description = "是否新單")
    private Boolean isNewOrder;

    /**
     * 是否經銷商
     */
    public boolean isDealer() {
        return !Objects.equals(CsatOrderSource.SUBSCRIBE.getCode(), source);
    }
}
