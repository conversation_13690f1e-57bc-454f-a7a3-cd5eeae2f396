package com.carplus.subscribe.model.csat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class CsatRefusedResponse {

    @Schema(description = "格上會員名稱")
    private String authUserName;

    @Schema(description = "SL會員名稱")
    private String dealerUserName;

    @Schema(description = "身分證")
    private String loginId;

    @Schema(description = "是否拒訪")
    private Boolean isRefused = false;

    @Schema(description = "拒訪日期")
    private Date refuseDate;

    @Schema(description = "拒訪原因")
    private String refuseMemo;

}
