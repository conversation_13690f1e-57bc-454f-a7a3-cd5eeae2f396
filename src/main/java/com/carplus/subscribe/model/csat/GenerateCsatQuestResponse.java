package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.enums.CsatOrderSource;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

public class GenerateCsatQuestResponse {

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "身分證")
    private String loginId;

    @Schema(description = "來源")
    private Integer source;

    @Schema(description = "電訪備註")
    private String csatMemo;

    @Schema(description = "電訪問券")
    private GenerateCsatQuest quest;

    @Schema(description = "電訪狀態")
    private CsatStatus status;

    @Schema(description = "問券狀態")
    private CsatQuestStatus questStatus;

    @Schema(description = "版本")
    private Integer version;

    public GenerateCsatQuestResponse(Csat csat, GenerateCsatQuest quest, String loginId) {
        this.orderNo = csat.getOrderNo();
        this.loginId = loginId;
        this.source = csat.getSource();
        this.csatMemo = csat.getCsatMemo();
        this.status = CsatStatus.of(csat.getStatus());
        this.questStatus = CsatQuestStatus.of(csat.getQuestStatus());
        this.quest = quest;
        this.version = quest.getVersion();
    }

    @Schema(description = "是否經銷商")
    public boolean isDealer() {
        return !Objects.equals(CsatOrderSource.SUBSCRIBE.getCode(), source);
    }

}
