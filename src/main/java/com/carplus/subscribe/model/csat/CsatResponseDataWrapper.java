package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.db.mysql.dto.CarBrandModelDTO;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.db.mysql.entity.csat.CsatRefused;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import lombok.Data;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
public class CsatResponseDataWrapper {

    private Set<String> orderNos = new HashSet<>();

    private Set<String> dealerOrders = new HashSet<>();

    private Set<String> plateNos = new HashSet<>();

    private Set<Integer> acctIds = new HashSet<>();

    private Set<String> memberIds = new HashSet<>();

//    private Set<String> loginId = new HashSet<>();

    private Map<Integer, Csat> csatMap;

    private Map<String, Orders> ordersMap;

    private Map<String, DealerOrderQueryResponse> dealerOrderMap;

    private Map<String, CarBrandModelDTO> carBrandModelDTOMap;

    private Map<Integer, AuthUser> authUserMap = new HashMap<>();

    private Map<Integer, AuthDealerUserResponse> dealerUserMap = new HashMap<>();

    private Map<String, CsatRefused> refusedMap;

    private Map<String, Stations> stationsMap;
    private Map<String, MemberInfo> memberInfoMap;
}
