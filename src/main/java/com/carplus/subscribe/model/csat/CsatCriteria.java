package com.carplus.subscribe.model.csat;

import com.carplus.subscribe.enums.csat.CsatStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CsatCriteria {

    @Schema(description = "員工編號")
    private String memberId;

    @Schema(description = "狀態清單")
    private List<CsatStatus> status;

    @Schema(description = "車牌號碼")
    private String plateNo;

    @Schema(description = "起日")
    private Date from;

    @Schema(description = "迄日")
    private Date to;

    @Schema(description = "電訪單來源")
    private List<Integer> source;
}
