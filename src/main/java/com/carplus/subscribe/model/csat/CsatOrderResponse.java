package com.carplus.subscribe.model.csat;


import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CsatOrderResponse {
    private String orderNo;

    private String plateNo;

    private CsatStatus status;

    @Schema(description = "電訪人員編號")
    private String assignMemberId;

    @Schema(description = "電訪人員名稱")
    private String assignMemberName;

    @Schema(description = "電訪日期")
    private Date assignDate;

    @Schema(description = "出車時間")
    private Date departDate;

    @Schema(description = "滿意度調查狀態[ 0 未調查 (status < 2) ;1 已調查 (status = 2) ;2 拒調查 (status = 2 且 q0 = false)]")
    private CsatQuestStatus csatQuestStatus;

    @Schema(description = "電訪備註")
    private String csatMemo;

    @Schema(description = "調查備註")
    private String questMemo;

    public CsatOrderResponse(Csat csat) {
        this.setOrderNo(csat.getOrderNo());
        this.setPlateNo(csat.getPlateNo());
        this.setAssignDate(Optional.ofNullable(csat.getContractDate()).map(Date::from).orElse(null));
        this.setStatus(CsatStatus.of(csat.getStatus()));
        this.setCsatQuestStatus(CsatQuestStatus.of(csat.getQuestStatus()));
        this.setCsatMemo(csat.getCsatMemo());

    }
}
