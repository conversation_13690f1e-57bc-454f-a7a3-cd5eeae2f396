package com.carplus.subscribe.model.finbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 發票
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceReq {

    @Schema(description = "發票類型", example = "新開立=NEW, 作廢=VOID, 折讓=ALLOWANCE")
    @NotNull(message = "發票類型不能為空")
    @Pattern(regexp = "^NEW$|^VOID$|^ALLOWANCE$", message = "無效的發票類型錯誤")
    private String type; // 發票類型: 新開立=NEW, 作廢=VOID, 折讓=ALLOWANCE

    @Schema(description = "發票號碼")
    @NotBlank(message = "發票號碼不能為空")
    private String number;

    @Schema(description = "發票金額")
    @NotNull(message = "發票金額不能為空")
    @Min(value = 1, message = "發票金額最少為 1")
    private Integer amount;

    @Schema(description = "公司統編")
    private String businessIdNumber; // 公司統編

    @Schema(description = "交易項目列表")
    @NotEmpty(message = "transactionItems 不能為空")
    @Valid
    private List<TransactionItemReq> transactionItems; // 交易項目列表

}
