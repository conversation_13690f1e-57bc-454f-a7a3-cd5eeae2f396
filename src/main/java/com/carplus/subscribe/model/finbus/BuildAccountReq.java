package com.carplus.subscribe.model.finbus;

import com.carplus.subscribe.constant.CarPlusConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 立帳請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuildAccountReq {

    @Schema(description = "部門代碼")
    @NotNull(message = "部門代碼 不能為空")
    private String departmentCode = CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE;

    @Schema(description = "訂單編號")
    @NotBlank(message = "orderNumber 不能為空")
    private String orderNumber; // 訂單編號

    @Schema(description = "主約編號")
    private String contractNumber; // 主約編號

    @Schema(description = "車號")
    private String carNumber; // 車號

    @Schema(description = "客戶類型", example = "個人=NATURAL, 法人=JURIDICAL")
    @NotNull(message = "customerType 不能為空")
    @Pattern(regexp = "^NATURAL$|^JURIDICAL$", message = "無效的 customerType: NATURAL or JURIDICAL")
    private String customerType = "NATURAL"; // 客戶類型: 個人=NATURAL, 法人=JURIDICAL

    @Schema(description = "客戶名稱: 個人=姓名, 法人=公司名稱")
    private String customerName; // 客戶名稱: 個人=姓名, 法人=公司名稱(目前可不填)

    @Schema(description = "客戶身分編號: 個人=身份證字號, 法人=統編")
    private String customerIdNumber; // 客戶身分編號: 個人=身份證字號, 法人=統編

    @Schema(description = "交易時間")
    @NotNull(message = "transactionTime 不能為空")
    private Date transactionTime; // 交易時間

    @Schema(description = "訂單起始日")
    @NotNull(message = "startDate 不能為空")
    private Date startDate; // 訂單起始日

    @Schema(description = "訂單結束日")
    @NotNull(message = "endDate 不能為空")
    private Date endDate; // 訂單結束日

    @Schema(description = "訂單是否期滿結束(false表示提前解約)")
    @NotNull(message = "isFinish 不能為空")
    private Boolean isFinish; // 訂單是否期滿結束(false表示提前解約)

    @Schema(description = "交易項目列表")
    @NotEmpty(message = "transactionItems 不能為空")
    @Valid
    private List<TransactionItemReq> transactionItems; // 交易項目列表

    @Schema(description = "付款資訊列表")
    @NotEmpty(message = "paymentMethods 不能為空")
    @Valid
    private List<PaymentMethodReq> paymentMethods; // 付款資訊列表

    @Schema(description = "發票列表")
    @Valid
    private List<InvoiceReq> invoices = new ArrayList<>(); // 發票列表


}
