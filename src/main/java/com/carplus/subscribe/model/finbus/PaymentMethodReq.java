package com.carplus.subscribe.model.finbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * 付款方式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentMethodReq {

    @Schema(description = "付款類型", example = "收款=CHARGE, 退款=REFUND")
    @NotNull(message = "付款類型不能為空")
    @Pattern(regexp = "^CHARGE|^REFUND$", message = "無效的付款類型: CHARGE, REFUND")
    private String type; // 類型: 收款=CHARGE, 退款=REFUND

    @Schema(description = "付款方式代碼", example = "TapPay台新=TAPPAY_TSIB, 匯款=REMIT, 保證金=MARGIN")
    @NotBlank(message = "付款方式代碼不能為空")
    private String code; // 付款方式代碼

    @Schema(description = "付款金額")
    @NotNull(message = "付款金額不能為空")
    @Min(value = 1, message = "付款金額最少為 1")
    private Integer amount;

    @Schema(description = "金流服務對應的鍵值", example = "TapPay的訂單編號, 匯款編號")
    private String referenceKey; // 和金流服務對應的鍵值: 如 tappay 的訂單編號, 匯款編號

    @Schema(description = "交易項目列表")
    @NotEmpty(message = "transactionItems 不能為空")
    @Valid
    private List<TransactionItemReq> transactionItems; // 交易項目列表

}
