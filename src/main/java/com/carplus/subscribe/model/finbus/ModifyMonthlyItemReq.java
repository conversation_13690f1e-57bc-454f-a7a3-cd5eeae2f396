package com.carplus.subscribe.model.finbus;

import com.carplus.subscribe.constant.CarPlusConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Date;
import java.util.List;

@NoArgsConstructor
@Data
public class ModifyMonthlyItemReq {
    @Schema(description = "交易時間")
    private Date transactionTime;

    @Schema(description = "分攤日期區間列表")
    private List<Period> periods;

    private String orderNumber;

    private String departmentCode = CarPlusConstant.SUBSCRIBE_MANAGEMENT_DEPT_CODE;

    public ModifyMonthlyItemReq(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Period {

        @Schema(description = "分攤起日")
        private Instant apportionStartDate;

        @Schema(description = "分攤迄日")
        private Instant apportionEndDate;

        @Schema(description = "金額")
        private Integer amount;

    }

}
