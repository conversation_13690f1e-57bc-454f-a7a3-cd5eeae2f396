package com.carplus.subscribe.model.finbus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SecurityChangeCarReq {

    private String carNumber;

    private Integer clientId;

    @Schema(description = "部門代碼")
    @NotNull(message = "部門代碼 不能為空")
    private String departmentCode = "B13000";

    private String orderNumber;

    private String transactionItemCode;

}
