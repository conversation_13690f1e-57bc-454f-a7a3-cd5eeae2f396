package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class DealerSubscriptionInfoForClose implements DealerSubscriptionInfoInterface {

    @Schema(description = "保證金")
    private Integer securityDeposit;

    @Schema(description = "月費租金")
    private Integer monthlyFee;

    @Schema(description = "里程費率(實際)")
    private Double actualMileageRate;

    @Schema(description = "里程費率(原價)")
    private Double originalMileageRate;

    @Schema(description = "預定出車站點")
    private String expectDepartStation;

    @Schema(description = "預定還車站點")
    private String expectReturnStation;

    @Schema(description = "預定出車時間")
    private Instant expectDepartDate;

    @Schema(description = "預定還車時間")
    private Instant expectReturnDate;

    @Schema(description = "訂閱租期")
    private Integer subscribeMonth;

    @Schema(description = "訂單應收總金額")
    private Integer totalAmt;

    @Schema(description = "里程費率折數")
    private Double mileageRateDiscount;

    @Schema(description = "實際還車站點")
    @NotNull(message = "實際還車站點不可為空")
    private String returnStation;

    @Schema(description = "實際還車時間")
    @NotNull(message = "實際還車時間不可為空")
    private Instant returnDate;

    @Schema(description = "訂單實收總金額")
    private Integer paidAmt;

    @Schema(description = "是否實際還車")
    @NotNull(message = "是否實際還車不可為空")
    private Boolean isReturned;

    @Schema(description = "迄租金額")
    @NotNull(message = "迄租金額不可為空")
    private Integer closeAmt;
}
