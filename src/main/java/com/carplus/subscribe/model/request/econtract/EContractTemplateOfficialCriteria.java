package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateOfficialCriteria {

    private Integer skip;

    private Integer limit;

    @Schema(description = "訂單來源")
    private Integer conditionOrderSource;

    @Schema(description = "車輛所屬")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌")
    private String conditionCarBrand;

    @Schema(description = "訂閱類別")
    private String conditionCarState;

    @Schema(description = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @Schema(description = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "訂閱租期")
    private Integer conditionOrderMonth;

}
