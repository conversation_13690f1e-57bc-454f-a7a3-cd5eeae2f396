package com.carplus.subscribe.model.request.station;

import com.carplus.subscribe.enums.StationDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StationQueryRequest {

    @Schema(description = "站所名稱")
    private String stationName;
    @Schema(description = "地理所在區域")
    private List<String> geoRegion;
    @Schema(description = "組織所屬區域", example = "N1(北一區)、N2(北二區)、C(中區)、S(南區)、E(東區)")
    private List<String> locateGeoRegion;
    @Schema(description = "是否啟用電子出租單")
    private List<Boolean> forceOnlineRForm;
    @Schema(description = "站所業務別", example = "SHORT_RENT(短租)、PREOWNED(中古)")
    private List<StationDefine.CarplusService> carplusService;
    @Schema(description = "收銀台顯示", example = "true(顯示)、false(隱藏)")
    private List<Boolean> visible;
}
