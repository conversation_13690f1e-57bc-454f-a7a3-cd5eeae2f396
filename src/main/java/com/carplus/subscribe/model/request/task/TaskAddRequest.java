package com.carplus.subscribe.model.request.task;

import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "新增出車/還車任務")
public class TaskAddRequest {

    @Schema(description = "出租單編號")
    private String orderNo;

    @Schema(description = "系統別")
    private String systemKind;

    @Schema(description = "駕駛資訊")
    private Driver driver;

    @Schema(description = "車籍")
    private CarInfo carInfo;

    @Schema(description = "任務型別")
    @Enumerated(EnumType.STRING)
    private TaskType eContractType;

    @Schema(description = "預計出車執行時間")
    private Instant departExecuteDate;

    @Schema(description = "預計還車執行時間")
    private Instant returnExecuteDate;

    @Schema(description = "出車站點ID")
    private String departStationId;

    @Schema(description = "出車站點")
    private String departStationName;

    @Schema(description = "還車站點ID")
    private String returnStationId;

    @Schema(description = "還車站點")
    private String returnStationName;

    @Schema(description = "車型種類[sedan(0, \"轎車\"), suv(1, \"休旅車\"), truck(2, \"貨車\"), moto(8, \"機車\")]")
    private CarDefine.CarKind eContractCarKind;

    @Schema(description = "出還車備註")
    private String remark;

    @Builder
    public static class CarInfo {
        @Schema(description = "車型名稱")
        private String externalName;

        @Schema(description = "車牌號碼")
        private String plateNo;

        @Schema(description = "顏色")
        private String color;

        @Schema(description = "里程費率")
        private Double mileageFee;
    }

    @Builder
    public static class Driver {
        @Schema(description = "身分證/居留證/護照號碼")
        private String id;

        @Schema(description = "名稱")
        private String name;

        @Schema(description = "汽車駕照管轄編號")
        private String licenseNo;

        @Schema(description = "會員編號")
        private Integer acctId;
    }
}
