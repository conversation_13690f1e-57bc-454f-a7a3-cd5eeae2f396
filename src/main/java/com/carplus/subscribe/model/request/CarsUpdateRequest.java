package com.carplus.subscribe.model.request;

import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CarsUpdateRequest {

    @Schema(description = "車況")
    private CarDefine.CarState carState;
    @Schema(description = "是否格上車")
    private Boolean carPlusCar;
    @Schema(description = "車型編號")
    private String carModelCode;
    @Schema(description = "座位數")
    private Integer seat;
    @Schema(description = "燃料種類")
    private CarDefine.FuelType fuelType;
    @Schema(description = "排氣量")
    private BigDecimal displacement;
    @Schema(description = "里程數")
    private Integer currentMileage;
    @Schema(description = "配備")
    private List<Integer> equipIds;
    @Schema(description = "訂閱車方案")
    @Positive(message = "訂閱車方案須為正整數")
    private Integer subscribeLevel;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "etag型式")
    private ETagModelEnum etagModel;
    @Schema(description = "etag序號 (etag 照片 url)")
    private String etagNo;
    @Schema(description = "車體介紹")
    private String cnDesc;
    @Schema(description = "車型類別")
    private CarDefine.CarType type;
    @Schema(description = "排檔類別")
    private CarDefine.GearType gearType;
    @Schema(description = "出廠年份")
    @Length(min = 4, max = 4, message = "出廠年份字元長度須為 4")
    private String mfgYear;
    @Schema(description = "出廠月份")
    @Length(min = 2, max = 2, message = "出廠月份字元長度須為 2")
    private String mfgMonth;
    @Schema(description = "車色")
    private String colorDesc;
    @Schema(description = "是否SeaLand上架")
    private Boolean isSealandLaunched;
    @Schema(description = "能源別")
    private CarDefine.EnergyType energyType;
    @Schema(description = "車撥申請單")
    private Integer buChangeMasterId;
    @Schema(description = "車籍統編")
    private String vatNo;
    @Min(value = 1, message = "準備工作天數不可小於 1")
    @Max(value = 127, message = "準備工作天數不可大於 127")
    @Schema(description = "準備工作天數")
    private Integer prepWorkdays;
}
