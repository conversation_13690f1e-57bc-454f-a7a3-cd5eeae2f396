package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CarModelImageRequest {

    @Schema(description = "年份")
    @NotNull(message = "年份不可為空")
    private Integer year;

    @Schema(description = "車型圖URL")
    @NotNull(message = "車型圖URL不可為空")
    private List<String> paths;
}
