package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateGetAllVersion {

    private Integer skip;

    private Integer limit;

    @Schema(description = "範本代碼")
    private String templateCode;

}
