package com.carplus.subscribe.model.request.dealer;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class DealerCustomerInfoForCreate extends BaseDealerCustomerInfo {

    @NotBlank(message = "訂車人姓名不可為空")
    private String userName;

    @NotBlank(message = "訂車人身分ID不可為空")
    private String idNo;

    @NotBlank(message = "訂車人國碼不可為空")
    private String nationalCode;

    @NotBlank(message = "訂車人電話不可為空")
    private String mainCell;

    @NotBlank(message = "訂車人生日不可為空")
    private String birthDay;

    @NotBlank(message = "訂車人信箱不可為空")
    private String email;

    @NotNull(message = "訂車人戶籍縣市不可為空")
    private Integer city;

    @NotNull(message = "訂車人戶籍區域不可為空")
    private Integer area;

    @NotBlank(message = "訂車人戶籍地址不可為空")
    private String address;
}
