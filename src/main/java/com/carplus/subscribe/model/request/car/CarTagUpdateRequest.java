package com.carplus.subscribe.model.request.car;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import lombok.Data;

@Data
public class CarTagUpdateRequest extends CarTagCreateRequest {
    private Integer id;


    public CarTag toEntity(CarTag carTag) {
        if (StringUtils.isNotBlank(getCnName())) {
            carTag.setCnName(getCnName());
        }
        if (getIsShow() != null) {
            carTag.setIsShow(getIsShow());
        }
        if (getSeqNo() != null) {
            carTag.setSeqNo(getSeqNo());
        }
        return carTag;
    }
}
