package com.carplus.subscribe.model.request.order;

import com.carplus.subscribe.enums.LegalOperationReason;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 法務作業請求
 */
@Data
public class LegalOperationRequest {

    @NotNull(message = "法務事由不能為空")
    @Schema(description = "法務事由")
    private LegalOperationReason reason;

    @Schema(description = "還車時間")
    private Date returnDate;

    @Schema(description = "還車里程")
    private Integer returnMileage;

    @Schema(description = "還車人員編號")
    private String returnMemberId;

    @Schema(description = "保證金沒收金額，將作為租金金額使用，預設為已收保證金的 100%")
    private Integer forfeitedForRentalAmount;

    @Schema(description = "發票備註")
    @Size(max = 300, message = "發票備註最多300字元")
    private String memo;

    @Schema(description = "欲沖銷 etag 款項 id 列表")
    private List<Integer> etagPriceInfoIdsToWriteOff;

    @Schema(description = "是否強制從母單執行法務作業")
    private Boolean forceExecute = false;
}
