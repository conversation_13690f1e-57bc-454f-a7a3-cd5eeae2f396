package com.carplus.subscribe.model.request.priceinfo;

import com.carplus.subscribe.model.EmpMileageDiscount;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MileageDiscountRequest {
    @Schema(description = "季、里程優惠, 取消優惠則給值為0")
    Map<Integer, EmpMileageDiscount> mileageDiscounts;
}
