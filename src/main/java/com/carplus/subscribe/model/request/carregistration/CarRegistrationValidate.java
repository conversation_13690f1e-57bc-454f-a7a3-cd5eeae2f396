package com.carplus.subscribe.model.request.carregistration;

import carplus.common.redis.serializer.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 車籍驗證資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Version(1)
public class CarRegistrationValidate {

    private List<CarRegistrationCSV> rows;
    private List<CarRegistrationCSV> errorRows;
    private List<CarRegistrationValidateError> errorList;
}
