package com.carplus.subscribe.model.request.carwishlist;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@Data
public class CarWishlistReportRequest {
    
    @Schema(description = "客戶手機號碼")
    private String phone;
    
    @Schema(description = "車型代碼")
    private List<String> carModelCodes;

    @NotNull(message = "加入收藏起始時間不可為空")
    @Schema(description = "加入收藏起始時間")
    private Instant startTime;

    @NotNull(message = "加入收藏結束時間不可為空")
    @Schema(description = "加入收藏結束時間")
    private Instant endTime;

    @AssertTrue(message = "加入收藏起始時間必須小於等於結束時間")
    @Schema(hidden = true)
    public boolean isTimeRangeValid() {
        if (startTime == null || endTime == null) {
            return true;
        }
        return startTime.isBefore(endTime) || startTime.equals(endTime);
    }
}