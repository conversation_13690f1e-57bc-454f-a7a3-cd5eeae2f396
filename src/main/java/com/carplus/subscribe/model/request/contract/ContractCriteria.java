package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.enums.ContractStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ContractCriteria {

    @Schema(description = "主約狀態")
    private List<ContractStatus> contractStatuses;

    @Schema(description = "使用者編號")
    private Integer acctId;

    @Schema(description = "是否拿取訂單訊")
    private boolean isDetail = false;

    @Schema(description = "主約編號")
    private String mainContractNo;


}
