package com.carplus.subscribe.model.request.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CarReplaceRequest {
    @Schema(defaultValue = "替換日期")
    private Date replaceDate;
    @Schema(defaultValue = "替代車備註")
    private String replaceMemo;
    @Schema(defaultValue = "替代車車牌號碼")
    private String inCarPlateNo;
    @Schema(defaultValue = "替代車出車里程數")
    private Integer inCarStartMileage;
    @Schema(defaultValue = "汰換車還車里程數")
    private Integer outCarEndMileage;
    @Schema(defaultValue = "替代車契約代步車方案", description = "0.無 1.出險 2.失竊 3.接送 4.洪水 5.維修 6.安心用車專案(出險代步牽送)")
    private List<String> lrContractReplaceCode;
    @Schema(description = "備註")
    private String lrContractMemo;
}
