package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TemplateValidateReq {


    @NotNull(message = "訂單來源不可為空, 0:格上; 1:SeaLand")
    @Schema(description = "訂單來源")
    private Integer conditionOrderSource;

    @NotNull(message = "訂閱租期不可為空,1-12月")
    @Schema(description = "訂閱租期")
    private Integer conditionOrderMonth;

    @NotNull(message = "車輛所屬不可為空, 1:長租; 4:訂閱")
    @Schema(description = "車輛所屬")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌")
    private String conditionCarBrand;

    @Schema(description = "車型")
    private String conditionCarModel;

    @NotNull(message = "訂閱類別不可為空, NEW:新車; OLD:中古車")
    @Schema(description = "訂閱類別")
    private String conditionCarState;

    @NotNull(message = "車輛牌價起始不可為空")
    @Schema(description = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @NotNull(message = "車輛牌價結束不可為空")
    @Schema(description = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "是否有免責費用")
    private boolean isDisclaimerFee;

    @Schema(description = "是否冰宇車")
    private boolean isSeaLandCar;

}
