package com.carplus.subscribe.model.request.priceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;

@Data
public class FineDiscountAgreeRequest {

    @Schema(description = "是否同意")
    @NotNull(message = "是否同意不可為空")
    private Boolean isAgree;

    @Schema(description = "裁決金額")
    @NotNull(message = "裁決金額不可為空")
    @PositiveOrZero(message = "折扣金額需大於等於0")
    private Integer discount;

    @Schema(description = "裁決說明")
    private String decideRemark;


}
