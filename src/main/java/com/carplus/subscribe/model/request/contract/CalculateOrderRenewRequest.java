package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.config.mapper.SubscribeRenewMonthDeserializer;
import com.carplus.subscribe.enums.SubscribeRenewMonth;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CalculateOrderRenewRequest {

    @JsonDeserialize(using = SubscribeRenewMonthDeserializer.class)
    @Schema(description = "續約訂閱月份")
    private SubscribeRenewMonth month;

    @Schema(description = "使用者編號")
    private int acctId;
}
