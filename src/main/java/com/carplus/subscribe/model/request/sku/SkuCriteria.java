package com.carplus.subscribe.model.request.sku;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@Schema(description = "汽車用品查詢條件")
public class SkuCriteria {
    @Min(0)
    private Integer skip = 0;
    
    @Min(1) 
    @Max(100)
    private Integer limit = 10;

    @Schema(description = "商品編號")
    private String code;

    @Schema(description = "商品類型")
    private String type;

    @Schema(description = "商品名稱")
    private String name;

    @Schema(description = "商品單價")
    private Integer unitPrice;

    @Schema(description = "是否顯示於官網")
    private Boolean isOfficial;

    @Schema(description = "是否顯示於收銀台")
    private Boolean isCashier;
}
