package com.carplus.subscribe.model.request.paymentinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class PaymentInfoQueryRequest {

    private Integer limit;
    private Integer skip;

    @Schema(description = "交易日期起")
    private Instant payDateStart;
    @Schema(description = "交易日期迄")
    private Instant payDateEnd;

    @Schema(description = "主約下單日期起")
    private Instant mainDateStart;
    @Schema(description = "主約下單日期迄")
    private Instant mainDateEnd;


    @Schema(description = "取還車站所")
    private String stationCode;
    @Schema(description = "主約編號")
    private String mainContractNo;
    @Schema(description = "訂單編號")
    private String orderNo;
    @Schema(description = "交易序號")
    private Integer paymentId;
    @Schema(description = "交易狀態")
    private Integer paymentStatus;
    @Schema(description = "交易狀態(已申請 未申請 全部)")
    private Boolean manualRefundStatus;
    @Schema(description = "訂單交易序號")
    private String transactionNumber;
    @Schema(description = "收單行")
    private String acquirer;
}
