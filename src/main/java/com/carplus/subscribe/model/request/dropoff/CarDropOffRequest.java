package com.carplus.subscribe.model.request.dropoff;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 收銀台還車資料確認
 */
@Data
public class CarDropOffRequest {

    @Schema(description = "還車備註")
    private String returnRemark;
    @Schema(description = "預計還車時間")
    @NotNull(message = "預計還車時間不可為空")
    private Date returnDate;
    @Schema(description = "還車里程")
    @NotNull(message = "還車里程不可為空")
    private Integer returnMileage;
    @Schema(description = "車色")
    private String color;
}
