package com.carplus.subscribe.model.request.carwishlist;


import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class CommonCarWishlistCriteria {

    private int skip = 0;

    private int limit = 10;

    @JsonIgnore
    private Integer acctId;

    @JsonIgnore
    private List<String> plateNos;

    @JsonIgnore
    private boolean includeDeleted = false;
}
