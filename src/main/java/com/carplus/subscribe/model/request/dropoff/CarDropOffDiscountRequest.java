package com.carplus.subscribe.model.request.dropoff;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CarDropOffDiscountRequest {

    @Schema(description = "類型")
    private PriceInfoDefinition.PriceInfoCategory category;
    @Schema(description = "預收應收金額")
    private Integer originAmount;
    @Schema(description = "實際應收金額")
    private Integer afterDiscountAmount;
    @Schema(description = "租金折扣")
    private Integer discount;
    @Schema(description = "原因")
    private String reason;
    @Schema(description = "發送給主管id")
    private String managerId;
    @NotNull
    @Schema(description = "付款明細ID")
    private Integer priceInfoPayId;
    @Schema(description = "是否主管同意")
    private boolean isAgree;
    @JsonIgnore
    @Schema(description = "裁決說明")
    private String decideRemark;
    @JsonIgnore
    @Schema(description = "原始申請折扣金額")
    private Integer originalDiscount;
    @JsonIgnore
    @Schema(description = "原始UID")
    private String uid;
    @Schema(description = "強制匯款退款")
    private boolean forceRemitRefund;


}
