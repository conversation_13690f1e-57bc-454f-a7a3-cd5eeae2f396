package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractSignReq {

    @Schema(description = "合約id")
    private Integer templateId;

    @Schema(description = "簽名檔ID")
    private Integer signFileId;

    @Schema(description = "檔案名稱")
    private String fileDisplayName;

    @Schema(description = "合約編號")
    private String contractNo;

}
