package com.carplus.subscribe.model.request.econtract;

import com.carplus.subscribe.enums.UploadFileKindEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UploadPresignedInternalReq {

    @Enumerated(EnumType.STRING)
    @Schema(description = "上傳類型: CUSTOM_CONTRACT:合約檔案, CONTRACT_TEMPLATE:合約範本", example = "CUSTOM_CONTRACT")
    private UploadFileKindEnum kind;

//    @Schema(description = "會員編號(", example = "acctId")
//    private Integer acctId;

    @Schema(description = "顯示用檔案名稱(包含附檔名)", example = "abc.pdf")
    private String filename;
}
