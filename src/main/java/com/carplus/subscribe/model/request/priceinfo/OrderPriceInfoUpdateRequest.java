package com.carplus.subscribe.model.request.priceinfo;

import com.carplus.subscribe.exception.SubscribeException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPriceInfoUpdateRequest {

    @Schema(description = "明細ID")
    private Integer id;

    @Schema(description = "最後付款日期")
    private Instant lastPayDate;

    @Schema(description = "應收日期")
    private Instant receivableDate;


    /**
     * 網頁查詢檢驗
     */
    public void validate() {
        if (id == null) {
            throw new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND);
        }
        if (receivableDate == null || lastPayDate == null) {
            throw new SubscribeException(SUBSCRIBE_UPDATE_DATE_NOT_EMPTY);
        }
        if (lastPayDate.isBefore(receivableDate)) {
            throw new SubscribeException(SUBSCRIBE_RECEIVE_DATE_INVALIDATE);
        }
    }

}
