package com.carplus.subscribe.model.request.carregistration;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.*;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.utils.CarsUtil;
import com.google.common.collect.Lists;
import com.univocity.parsers.annotations.Parsed;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Schema(description = "車籍匯入/匯出CSV")
public class CarRegistrationCSV implements CarPropertyProvider {
    @Getter(AccessLevel.NONE)
    private static final String[] csvDefaultColumn = new String[] {"carState", "plateNo", "carModelCode", "currentMileage",
        "equips", "locationStationCode", "subscribeLevel", "launched", "tags", "stdPrice", "isSealandLaunched", "gearType", "carType", "cnDesc", "vatNo", "prepWorkdays"};

    @Getter
    private static final String[] csvOutputColumn = new String[] {"brandNameEn", "carModelName", "carNo", "carState", "plateNo",
        "brandCode", "carModelCode", "displacement", "seat", "fuelTypeName", "mfgYear", "currentMileage", "equips",
        "locationStationCode", "subscribeLevel", "launched", "carStatus", "processOrders", "tags", "crsCarNo", "stdPrice", "isSealandLaunched",
        "energyType", "ownerName", "vatNo", "prepWorkdays"};
    private static final String TW_PLATE_NO_PATTERN = "\\w{1,3}-\\w{1,4}";
    /**
     * 匯入CSV表頭/範例擋表頭
     */
    @Getter(AccessLevel.NONE)
    private static List<String> carCsvInputHead = Arrays.asList(csvDefaultColumn);
    @Parsed(field = {"車況", "carState"})
    private String carState;
    @Parsed(field = {"車號", "plateNo"})
    private String plateNo;
    @Parsed(field = {"crs車輛編號", "crsCarNo"})
    private Integer crsCarNo;
    @Parsed(field = {"車輛編號", "carNo"})
    private String carNo;
    @Parsed(field = {"廠牌名稱", "brandNameEn"})
    private String brandNameEn;
    @Parsed(field = {"廠牌編號", "brandCode"})
    private String brandCode;
    @Parsed(field = {"車型編號", "carModelCode"})
    private String carModelCode;
    @Parsed(field = {"車型名稱", "carModelName"})
    private String carModelName;
    @Parsed(field = {"排氣量", "displacement"})
    private BigDecimal displacement;
    @Parsed(field = {"座位數", "seat"})
    private int seat;
    @Parsed(field = {"燃料種類", "fuelTypeName"})
    private String fuelTypeName;
    @Parsed(field = {"出廠年份", "mfgYear"})
    private String mfgYear;
    @Parsed(field = {"出廠月份", "mfgMonth"})
    private String mfgMonth;
    @Parsed(field = {"里程數", "currentMileage"})
    private Integer currentMileage;
    @Parsed(field = {"配備", "equips"})
    private String equips;
    private List<Integer> equipIds;
    @Parsed(field = {"目前站點", "locationStationCode"})
    private String locationStationCode;
    @Parsed(field = {"訂閱車方案", "subscribeLevel"})
    private Integer subscribeLevel;
    @Parsed(field = {"標籤", "tags"})
    private String tags;
    @Parsed(field = {"錯誤訊息", "errorMessage"})
    private String errorMessage;
    @Parsed(field = {"上下架", "launched"})
    private String launchedName;
    @Parsed(field = {"車籍狀態", "carStatus"})
    private String carStatus;
    @Parsed(field = {"進行中訂單", "processOrders"})
    private String processOrders;
    @Parsed(field = {"是否在SL上架", "isSealandLaunched"})
    private String sealandLaunchedName = CarDefine.Launched.close.name();
    @Parsed(field = {"牌價", "stdPrice"})
    private Integer stdPrice;
    @Parsed(field = {"車輛能源別", "energyType"})
    private String energyTypeName;
    @Parsed(field = {"排檔類別", "gearType"})
    private CarDefine.GearType gearType;
    @Parsed(field = {"車型類別", "carType"})
    private CarDefine.CarType carType;
    @Parsed(field = {"顏色說明", "colorDesc"})
    private String colorDesc;
    @Parsed(field = {"中文說明", "cnDesc"})
    private String cnDesc;
    @Parsed(field = {"車籍統編", "vatNo"})
    private String vatNo;
    @Parsed(field = {"車籍公司所屬名稱", "ownerName"})
    private String ownerName;
    @Parsed(field = {"準備工作天數", "prepWorkdays"})
    private Integer prepWorkdays;

    private CarDefine.EnergyType energyType;
    private List<Integer> tagIds;
    private CarDefine.FuelType fuelType;
    private List<CarDefine.RentType> rentTypes = Lists.newArrayList(CarDefine.RentType.subscribe);
    private CarDefine.Launched launched;

    private Boolean isSealandLaunched;

    @Parsed(field = {"是否格上車"})
    public Boolean isCarPlusCar() {
        return CarsUtil.isCarPlusCar(vatNo);
    }


    /**
     * 取得匯入CSV表頭/範例擋表頭
     */
    public static List<String> getCarCsvInputHead() {
        return carCsvInputHead;
    }

    /**
     * 取得CSV匯入錯誤表頭
     */
    public static List<String> getCarCsvErrorOutputHead() {
        List<String> carCsvErrorOutputHead = new ArrayList<>(carCsvInputHead);
        carCsvErrorOutputHead.add("errorMessage");
        return carCsvErrorOutputHead;
    }

    public String getPlateNo() {
        return Optional.ofNullable(plateNo).map(String::toUpperCase).orElse(null);

    }

    public void setPlateNo(String plateNo) {
        Optional.ofNullable(plateNo).map(String::toUpperCase).ifPresent(s -> this.plateNo = s);
    }

    public List<String> validate(Map<String, CarModel> subscribeCarModelMap, Map<Integer, CarEquip> equipMap, Map<Integer, CarTag> tagsMap, List<String> stationList, Map<String, CarRegistration> carRegistrationMap) {
        List<String> errorMessage = new ArrayList<>();
        if (!plateNo.matches(TW_PLATE_NO_PATTERN)) {
            errorMessage.add("車號格式錯誤!");
        }
        //檢查車況
        try {
            CarDefine.CarState.valueOf(this.carState);
        } catch (IllegalArgumentException e) {
            errorMessage.add("車況資料錯誤!");
        } catch (NullPointerException e) {
            errorMessage.add("缺少車況資料!");
        }
        try {
            launched = CarDefine.Launched.valueOf(launchedName);
        } catch (IllegalArgumentException | NullPointerException e) {
            String s = Arrays.stream(CarDefine.Launched.values())
                .map(CarDefine.Launched::name)
                .collect(Collectors.joining(","));
            errorMessage.add(String.format("上下架資料錯誤!，請輸入[%s]", s));
        }
        setIsSealandLaunched(sealandLaunchedName.equals(CarDefine.Launched.open.name()));
        if (!subscribeCarModelMap.containsKey(carModelCode)) {
            errorMessage.add("[車型]無效：" + carModelCode);
        }
        if (equips != null && !equips.isEmpty()) {
            equipIds = Arrays.stream(equips.split(";")).map(Integer::valueOf).collect(Collectors.toList());
            if (equipIds.stream().anyMatch(equipId -> !equipMap.containsKey(equipId))) {
                errorMessage.add("配備資訊有誤，請再確認");
            }
        }
        if (tags != null && !tags.isEmpty()) {
            tagIds = Arrays.stream(tags.split(";")).map(Integer::valueOf).collect(Collectors.toList());
            if (tagIds.stream().anyMatch(tag -> !tagsMap.containsKey(tag))) {
                errorMessage.add(String.format("備註資訊(%s)有誤，請再確認", tags));
            }
        }
        if (StringUtils.isEmpty(locationStationCode)) {
            errorMessage.add("請輸入目前站點!");
        }
        if (!stationList.contains(locationStationCode)) {
            errorMessage.add("站點並不存在!");
        }
        if (currentMileage == null || currentMileage < 0) {
            errorMessage.add("里程數為必填且不可為負值");
        }
        if (StringUtils.isBlank(vatNo)) {
            errorMessage.add("車籍統編為必填");
        }
        if (vatNo.length() != 8) {
            errorMessage.add("車籍統編長度為8碼");
        }
        if (!carRegistrationMap.containsKey(vatNo)) {
            errorMessage.add("不存在系統的車籍統編");
        }
        if (prepWorkdays != null && (prepWorkdays < 1 || prepWorkdays > 127)) {
            errorMessage.add("準備工作天數必須介於1~127天");
        }
        return errorMessage;
    }
}
