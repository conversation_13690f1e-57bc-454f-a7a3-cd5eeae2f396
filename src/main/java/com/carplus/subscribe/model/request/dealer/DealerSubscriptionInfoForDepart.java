package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class DealerSubscriptionInfoForDepart implements DealerSubscriptionInfoInterface {

    @Schema(description = "保證金")
    private Integer securityDeposit;

    @Schema(description = "月費租金")
    private Integer monthlyFee;

    @Schema(description = "里程費率(實際)")
    private Double actualMileageRate;

    @Schema(description = "里程費率(原價)")
    private Double originalMileageRate;

    @Schema(description = "預定出車站點")
    private String expectDepartStation;

    @Schema(description = "預定還車站點")
    private String expectReturnStation;

    @Schema(description = "預定出車時間")
    private Instant expectDepartDate;

    @Schema(description = "預定還車時間")
    private Instant expectReturnDate;

    @Schema(description = "訂閱租期")
    private Integer subscribeMonth;

    @Schema(description = "訂單應收總金額")
    private Integer totalAmt;

    @Schema(description = "起租金額")
    @NotNull(message = "起租金額不可為空")
    private Integer beginAmt;

    @Schema(description = "月租金折抵")
    private Integer monthlyFeeDiscount;

    @Schema(description = "里程費率折數")
    private Double mileageRateDiscount;

    @Schema(description = "預收月數")
    private Integer prepaidMonths;

    @Schema(description = "每月預收里程")
    private Integer prepaidMileage;

    @Schema(description = "預收里程折抵")
    private Integer prepaidMileageDiscount;

    @Schema(description = "彈性里程")
    private Integer flexibleMileage;

    @Schema(description = "實際使用里程")
    private Integer actualMileageUsed;

    @Schema(description = "折抵里程1")
    private Integer offsetMileage1;

    @Schema(description = "折抵里程2")
    private Integer offsetMileage2;

    @Schema(description = "預收里程")
    private Integer prepaidMileageFee;

    @Schema(description = "實際出車站點")
    @NotBlank(message = "實際出車站點不可為空")
    private String departStation;

    @Schema(description = "實際出車時間")
    @NotNull(message = "實際出車時間不可為空")
    private Instant departDate;

    @Schema(description = "實際還車站點")
    private String returnStation;

    @Schema(description = "實際還車時間")
    private Instant returnDate;

    @Schema(description = "訂單實收總金額")
    private Integer paidAmt;

    @Schema(description = "是否取消")
    private Boolean isCancel;

    @Schema(description = "取消備註")
    private String cancelRemark;
}
