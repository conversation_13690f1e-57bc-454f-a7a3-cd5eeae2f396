package com.carplus.subscribe.model.request.priceinfo;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.utils.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SUBSCRIBE_QUERY_DATE_NOT_EMPTY;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SUBSCRIBE_QUERY_DATE_TOO_LONG;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPriceInfoCriteriaRequest {

    @Schema(description = "明細ID")
    private Integer id;

    @Schema(description = "期數")
    private Integer stage;

    @Schema(description = "費用類別")
    private List<PriceInfoDefinition.PriceInfoCategory> category;

    @Schema(description = "費用型態")
    private Integer type;

    @Schema(description = "關聯費用資訊")
    private Integer refPriceInfoNo;

    @Schema(description = "異動起始日")
    private Instant dateFrom;

    @Schema(description = "異動結束日")
    private Instant dateTo;

    @Schema(description = "是否包含未審核/不通過的費用")
    private boolean isCredit = true;

    @Schema(description = "是否包含保證金")
    private boolean isSecurityDeposit = false;

    private Integer skip;

    private Integer limit;


    /**
     * 網頁查詢檢驗
     */
    public void validateForWeb() {
        if (dateFrom == null || dateTo == null) {
            throw new SubscribeException(SUBSCRIBE_QUERY_DATE_NOT_EMPTY);
        }
        if (DateUtil.calculateDiffDate(dateFrom, dateTo, ChronoUnit.MONTHS) > 3) {
            throw new SubscribeException(SUBSCRIBE_QUERY_DATE_TOO_LONG);
        }
        if (skip == null) {
            skip = 0;
        }
        if (limit == null) {
            limit = 10;
        }
    }

}
