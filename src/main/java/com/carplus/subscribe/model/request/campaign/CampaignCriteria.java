package com.carplus.subscribe.model.request.campaign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CampaignCriteria extends CommonCampaignCriteria {

    @Schema(description = "是否僅篩選當前有效的活動 (活動開始時間 ≤ 現在 ≤ 活動結束時間)")
    private boolean onlyActive = true;

    @Schema(description = "是否排除已刪除活動")
    private boolean excludeDeleted = true;
}
