package com.carplus.subscribe.model.request.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class InternalContractCreateReq extends ContractCreateReq implements AutoCreditAware {

    @NotNull(message = "使用者編號不可為空")
    @Schema(description = "使用者編號")
    private Integer acctId;

    @Schema(description = "是否需要自動授信")
    private boolean needAutoCredit = true;

    @Schema(description = "不需要自動授信的原因")
    private String autoCreditBypassReason;
}