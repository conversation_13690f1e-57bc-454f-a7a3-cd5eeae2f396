package com.carplus.subscribe.model.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderRemarkRequest {

    @Schema(description = "訂單備註內容")
    @NotBlank(message = "訂單備註內容不可為空")
    private String content;
}
