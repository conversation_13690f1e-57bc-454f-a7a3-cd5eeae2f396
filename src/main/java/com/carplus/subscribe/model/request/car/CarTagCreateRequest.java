package com.carplus.subscribe.model.request.car;


import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CarTagCreateRequest {

    @Schema(description = "名稱")
    private String cnName;

    @Schema(description = "是否在官網顯示")
    private Boolean isShow;

    @Schema(description = "排序")
    private Integer seqNo;


    public CarTag toEntity() {
        CarTag carTag = new CarTag();
        carTag.setCnName(cnName);
        carTag.setIsShow(isShow);
        carTag.setSeqNo(seqNo);
        return carTag;
    }
}
