package com.carplus.subscribe.model.request.campaign;

import com.carplus.subscribe.enums.CarDefine;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarsCondition {

    @Schema(description = "車輛所在區域")
    private String geoRegion;

    @Schema(description = "廠牌代號")
    private List<String> brandCode;

    @Schema(description = "車型代號")
    private List<String> carModelCode;

    @Schema(description = "訂閱類別")
    private CarDefine.CarState carState;

    @Schema(description = "能源類別")
    private List<CarDefine.EnergyType> energyType;

    @Schema(description = "月費起始")
    private Integer monthFeeStart;
    @Schema(description = "月費結束")
    private Integer monthFeeEnd;

    @Min(value = 1900, message = "出廠年份起必須大於或等於 1900")
    @Max(value = 2100, message = "出廠年份起不能超過 2100")
    @Schema(description = "出廠年份起，範圍應為 1900 至當前年份")
    private Integer mfgYearFrom;
    @Min(value = 1900, message = "出廠年份迄必須大於或等於 1900")
    @Max(value = 2100, message = "出廠年份迄不能超過 2100")
    @Schema(description = "出廠年份迄，範圍應為 1900 至當前年份，且不得小於出廠年份起")
    private Integer mfgYearTo;

    @Schema(description = "標籤")
    private List<Integer> tagIds;

    @Schema(description = "車籍統編")
    private List<String> vatNo;

    /**
     * 檢查是否有任何非必填欄位被設置值
     *
     * @return true 如果有任何非必填欄位不為 null，對於集合類型則檢查是否不為空集合
     */
    public boolean hasAnyFieldSet() {
        return geoRegion != null
            || (brandCode != null && !brandCode.isEmpty())
            || (carModelCode != null && !carModelCode.isEmpty())
            || carState != null
            || (energyType != null && !energyType.isEmpty())
            || monthFeeStart != null
            || monthFeeEnd != null
            || mfgYearFrom != null
            || mfgYearTo != null
            || (tagIds != null && !tagIds.isEmpty())
            || (vatNo != null && !vatNo.isEmpty());
    }
}
