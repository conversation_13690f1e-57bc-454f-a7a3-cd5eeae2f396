package com.carplus.subscribe.model.request.econtract;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 電子合約主檔
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EContractFileUpdateReq {

    /**
     * 會員編號
     */
    @JsonProperty(value = "acctId")
    private Integer acctId;

    /**
     * 檔案顯示名稱
     */
    @JsonProperty(value = "uploadFilename")
    private String displayFilename;

    /**
     * 檔案編號
     */
    @JsonProperty(value = "uploadFileId")
    private Integer uploadFileId;

    /**
     * 備註
     */
    @JsonProperty(value = "remark")
    private String remark;
}