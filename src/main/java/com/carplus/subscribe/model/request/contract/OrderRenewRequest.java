package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.enums.OrderPlatform;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class OrderRenewRequest extends CalculateOrderRenewRequest implements AutoCreditAware {

    @Schema(description = "訂單平台來源")
    private OrderPlatform orderPlatform;

    @Schema(description = "是否需要自動授信")
    private boolean needAutoCredit = true;

    @Schema(description = "不需要自動授信的原因")
    private String autoCreditBypassReason;

    @Valid
    @Schema(description = "加購汽車用品列表")
    private List<MerchandiseReq> merchList;
}
