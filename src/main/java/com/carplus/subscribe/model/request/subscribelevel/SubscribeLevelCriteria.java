package com.carplus.subscribe.model.request.subscribelevel;

import com.carplus.subscribe.enums.SubscribeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.PositiveOrZero;
import java.util.List;

@Data
public class SubscribeLevelCriteria {

    @Schema(description = "訂閱車方案")
    private Integer level;

    @Schema(description = "訂閱車方案名稱")
    private String name;

    @PositiveOrZero(message = "最低保證金必須大於等於0")
    @Schema(description = "最低保證金")
    private Integer securityDepositMin;

    @PositiveOrZero(message = "最高保證金必須大於等於0")
    @Schema(description = "最高保證金")
    private Integer securityDepositMax;

    @PositiveOrZero(message = "最低基本月費必須大於等於0")
    @Schema(description = "最低基本月費")
    private Integer monthlyFeeMin;

    @PositiveOrZero(message = "最高基本月費必須大於等於0")
    @Schema(description = "最高基本月費")
    private Integer monthlyFeeMax;

    @PositiveOrZero(message = "最低里程費率必須大於等於0")
    @Schema(description = "最低里程費率")
    private Double mileageFeeMin;

    @PositiveOrZero(message = "最高里程費率必須大於等於0")
    @Schema(description = "最高里程費率")
    private Double mileageFeeMax;

    @Schema(description = "方案類型(可複選)")
    private List<SubscribeType> types;

    @Schema(description = "是否自動授信(null=不限, true=是, false=否)")
    private Boolean autoCredit;

    private Integer skip = 0;
    private Integer limit = 100;

    @AssertTrue(message = "最低保證金不可大於最高保證金")
    @Schema(hidden = true)
    public boolean isSecurityDepositValid() {
        if (securityDepositMin != null && securityDepositMax != null) {
            return securityDepositMin <= securityDepositMax;
        }
        return true;
    }

    @AssertTrue(message = "最低基本月費不可大於最高基本月費")
    @Schema(hidden = true)
    public boolean isMonthlyFeeValid() {
        if (monthlyFeeMin != null && monthlyFeeMax != null) {
            return monthlyFeeMin <= monthlyFeeMax;
        }
        return true;
    }

    @AssertTrue(message = "最低里程費率不可大於最高里程費率")
    @Schema(hidden = true)
    public boolean isMileageFeeValid() {
        if (mileageFeeMin != null && mileageFeeMax != null) {
            return mileageFeeMin <= mileageFeeMax;
        }
        return true;
    }
}