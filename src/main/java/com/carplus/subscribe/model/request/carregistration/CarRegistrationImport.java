package com.carplus.subscribe.model.request.carregistration;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.entity.cars.CarEquip;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.server.cars.model.Da41Entity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@Schema(description = "車籍匯入請求")
public class CarRegistrationImport {

    private static final String TW_PLATE_NO_PATTERN = "\\w{1,3}-\\w{1,4}";

    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    @NotBlank(message = "車號不可為空")
    private String plateNo;
    @Schema(description = "車況")
    @NotNull(message = "車況不可為空")
    private String carStat;
    @Schema(description = "車型編號")
    @NotNull(message = "車型編號不可為空")
    private String carModelCode;
    @Schema(description = "座位數")
    @NotNull(message = "座位數不可為空")
    private int seat;
    @Schema(description = "燃料種類")
    @NotNull(message = "燃料種類不可為空")
    private CarDefine.FuelType fuelType;
    @Schema(description = "里程數")
    @NotNull(message = "里程數不可為空")
    private int currentMileage;
    @Schema(description = "配備")
    private List<Integer> equipIds;
    @Schema(description = "目前站點")
    @NotNull(message = "目前站點不可為空")
    private String locationStationCode;
    @Schema(description = "訂閱車方案")
    @NotNull(message = "訂閱車方案不可為空")
    private Integer subscribeLevel;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "車籍統編")
    private String vatNo;
    @Min(value = 1, message = "準備工作天數不可小於 1")
    @Max(value = 127, message = "準備工作天數不可大於 127")
    @Schema(description = "準備工作天數")
    private Integer prepWorkdays;

    @JsonIgnore
    private List<CarDefine.RentType> rentTypes = Lists.newArrayList(CarDefine.RentType.subscribe);

    /**
     * 長租車籍資料
     */
    @JsonIgnore
    private Da41Entity da41Entity;

    public String getPlateNo() {
        return Optional.ofNullable(plateNo).map(String::toUpperCase).orElse(null);

    }

    public void setPlateNo(String plateNo) {
        Optional.ofNullable(plateNo).map(String::toUpperCase).ifPresent(s -> this.plateNo = s);
    }

    public void validate(List<String> carModelCodes, Map<Integer, CarEquip> equipMap, Map<Integer, CarTag> tagsMap) {
        if (!getPlateNo().matches(TW_PLATE_NO_PATTERN)) {
            throw new BadRequestException("車號格式錯誤!");
        }
        Optional.of(carModelCodes.contains(carModelCode)).map(b -> b ? true : null).orElseThrow(() -> new BadRequestException("[車型]無效：" + carModelCode));
        if (equipIds != null && equipIds.stream().anyMatch(equipId -> !equipMap.containsKey(equipId))) {
            throw new BadRequestException("配備資訊有誤，請再確認");
        }
        if (tagIds != null && tagIds.stream().anyMatch(tag -> !tagsMap.containsKey(tag))
            && (tagIds.contains(CarDefine.CarTag.MONTHLY_DISCOUNTED.getId()) && tagIds.contains(CarDefine.CarTag.LEVEL_DISCOUNTED.getId()))) {
            throw new BadRequestException("備註資訊有誤，請再確認");
        }
    }
}
