package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CarModelAddRequest {
    @Schema(description = "廠牌代碼")
    @NotNull(message = "廠牌代碼不可為空")
    private String brandCode;
    @Schema(description = "車型名稱")
    @NotNull(message = "車型名稱不可為空")
    private String carModelName;
    @Schema(description = "車型種類 0:轎車, 1:休旅車, 2:貨車, 8:電動機車")
    @NotNull(message = "車型種類不可為空")
    private int carKind;
    @Schema(description = "車型圖")
    @NotNull(message = "車型圖不可為空")
    private List<CarModelImageRequest> images;
}
