package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CarBrandUpdateRequest {

    @Schema(description = "廠牌代碼")
    @NotNull(message = "廠牌代碼不可為空")
    private String brandCode;

    @Schema(description = "廠牌名稱")
    @NotNull(message = "廠牌名稱不可為空")
    private String brandName;

    @Schema(description = "廠牌英文名稱")
    @NotNull(message = "廠牌英文名稱不可為空")
    private String brandNameEn;

    @Schema(description = "車廠牌是否呈現官網介紹")
    @NotNull(message = "車廠牌是否呈現官網介紹不可為空")
    private boolean isAppearOnOfficial;
}
