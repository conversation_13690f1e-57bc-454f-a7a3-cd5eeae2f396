package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "經銷商訂單異動客戶與長租契約編號請求")
public class DealerOrderUpdateUserAndLrentalContractNoRequest {

    @NotNull(message = "經銷商訂單編號不可為空")
    @NotBlank(message = "經銷商訂單編號不可為空")
    @Schema(description = "經銷商訂單編號")
    private String orderNo;

    @NotNull(message = "經銷商訂單客戶編號不可為空")
    @Schema(description = "經銷商訂單客戶編號")
    private Long dealerUserId;

    @Schema(description = "長租契約編號")
    private String lrentalContractNo;
} 