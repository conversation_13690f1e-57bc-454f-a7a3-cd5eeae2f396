package com.carplus.subscribe.model.request.sku;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "更新汽車用品資料")
public class SkuUpdateRequest {
    
    @Min(value = 0, message = "商品單價不可小於0")
    @Schema(description = "商品單價")
    private Integer unitPrice;

    @Schema(description = "商品描述")
    private String description;

    @Schema(description = "商品圖片路徑")
    private String imgPath;

    @Schema(description = "是否顯示於官網")
    private Boolean isOfficial;

    @Schema(description = "是否顯示於收銀台")
    private Boolean isCashier;
}
