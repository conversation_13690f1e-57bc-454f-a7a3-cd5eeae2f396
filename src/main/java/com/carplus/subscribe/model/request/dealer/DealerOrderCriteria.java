package com.carplus.subscribe.model.request.dealer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DealerOrderCriteria {

    private Integer skip;

    private Integer limit;

    @Schema(description = "查看單位")
    @NotNull(message = "站點不可為空")
    @NotEmpty(message = "站點不可為空")
    private List<String> stationCode;

    @Schema(description = "訂單狀態")
    private List<Integer> orderStatus;

    @Schema(description = "經銷商訂單編號")
    private String orderNo;

    @Schema(description = "訂車人姓名")
    private String userName;

    @Schema(description = "身分證號")
    private String idNo;

    @Schema(description = "訂車人電話國碼")
    private String nationalCode;

    @Schema(description = "訂車人電話")
    private String phone;

    @Schema(description = "車號")
    private List<String> plateNo;

    @Schema(description = "保證金支付時間起")
    private Date securityDepositDateFrom;

    @Schema(description = "保證金支付時間訖")
    private Date securityDepositDateTo;

    @Schema(description = "訂單建立時間起")
    private Date createDateFrom;

    @Schema(description = "訂單建立時間訖")
    private Date createDateTo;

    @Schema(description = "[預定]出車站點")
    private String expectDepartStation;

    @Schema(description = "[預定]還車站點")
    private String expectReturnStation;

    @Schema(description = "[實際]出車站點")
    private String departStation;

    @Schema(description = "[實際]還車站點")
    private String returnStation;

    @Schema(description = "[預定]出車日期起")
    private Date expectDepartDateFrom;

    @Schema(description = "[預定]出車日期訖")
    private Date expectDepartDateTo;

    @Schema(description = "[預定]還車日期起")
    private Date expectReturnDateFrom;

    @Schema(description = "[預定]還車日期訖")
    private Date expectReturnDateTo;

    @Schema(description = "[實際]出車日期起")
    private Date departDateFrom;

    @Schema(description = "[實際]出車日期訖")
    private Date departDateTo;

    @Schema(description = "[實際]還車日期起")
    private Date returnDateFrom;

    @Schema(description = "[實際]還車日期訖")
    private Date returnDateTo;

    @JsonIgnore
    private List<Integer> acctId;


}
