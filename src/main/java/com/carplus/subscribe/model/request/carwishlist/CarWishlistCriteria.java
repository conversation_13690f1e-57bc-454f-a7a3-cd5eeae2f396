package com.carplus.subscribe.model.request.carwishlist;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CarWishlistCriteria extends CommonCarWishlistCriteria {

    @Schema(description = "廠牌代號")
    private List<String> brandCodes;

    @Schema(description = "車型代號")
    private List<String> carModelCodes;
}
