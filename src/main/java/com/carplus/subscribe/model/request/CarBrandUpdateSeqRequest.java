package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CarBrandUpdateSeqRequest {

    @NotNull(message = "廠牌代碼列表不可為空")
    @NotEmpty(message = "廠牌代碼列表不可為空")
    @Schema(description = "廠牌代碼列表")
    private List<String> brandCodes;
}
