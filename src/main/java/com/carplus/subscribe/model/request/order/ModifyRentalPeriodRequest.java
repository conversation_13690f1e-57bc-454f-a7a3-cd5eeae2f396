package com.carplus.subscribe.model.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 續約訂單出車前異動租期請求
 */
@Data
@Schema(description = "續約訂單出車前異動租期請求")
public class ModifyRentalPeriodRequest {

    @NotNull(message = "新租期月數不可為空")
    @Min(value = 1, message = "租期月數最少為1個月")
    @Max(value = 12, message = "租期月數最多為12個月")
    @Schema(description = "新租期月數", example = "6", required = true)
    private Integer newSubscribeMonth;
}
