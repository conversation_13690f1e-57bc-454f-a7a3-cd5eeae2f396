package com.carplus.subscribe.model.request.dealer;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class DealerOrderDepartRequest extends BaseDealerOrderRequest {

    @NotBlank(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼", example = "ABC-1234")
    private String plateNo;

    @Schema(description = "經銷商名稱")
    private String dealerName;

    @Schema(description = "訂單狀態")
    private Integer orderStatus = ContractStatus.GOING.getCode();

    @Schema(description = "是否新訂單")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信")
    private Boolean isAudit;

    @Schema(description = "是否支付保證金")
    private Boolean isPaySecurityDeposit;

    @Schema(description = "母約編號")
    private String parentOrderNo;

    @Schema(description = "期數")
    private String stage;

    @Schema(description = "訂車人資訊")
    private DealerCustomerInfoAllOptional customerInfo;

    @Schema(description = "訂車方案資訊")
    @Valid
    @NotNull(message = "訂車方案資訊不可為空")
    private DealerSubscriptionInfoForDepart subscriptionInfo;

    public DealerOrderDepartRequest(DealerOrder dealerOrder, DealerOrderExcel dealerOrderExcel, Map<String, String> stationsNameCodeMap) {
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setOrderStatus(ContractStatus.GOING.getCode());
        // updatable
        this.setPlateNo(Optional.ofNullable(dealerOrderExcel.getPlateNo()).orElse(dealerOrder.getPlateNo()));
        DealerSubscriptionInfoForDepart subscriptionInfo = new DealerSubscriptionInfoForDepart();
        subscriptionInfo.setDepartStation(stationsNameCodeMap.get(dealerOrderExcel.getDepartStationName()));
        subscriptionInfo.setDepartDate(dealerOrderExcel.getDepartDate().toInstant());
        subscriptionInfo.setBeginAmt(dealerOrderExcel.getBeginAmt());
        subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt() + dealerOrderExcel.getBeginAmt());
        subscriptionInfo.setPaidAmt(dealerOrder.getPaidAmt() + dealerOrderExcel.getBeginAmt());
        this.subscriptionInfo = subscriptionInfo;
    }

    public DealerOrderDepartRequest(DealerOrder dealerOrder) {
        this.setOrderNo(dealerOrder.getEntityNo());
        this.setOrderStatus(ContractStatus.GOING.getCode());
        this.setPlateNo(dealerOrder.getPlateNo());

        DealerSubscriptionInfoForDepart subscriptionInfo = new DealerSubscriptionInfoForDepart();
        subscriptionInfo.setDepartStation(dealerOrder.getDepartStationCode());
        subscriptionInfo.setDepartDate(dealerOrder.getStartDate());
        subscriptionInfo.setBeginAmt(0);
        subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt());
        subscriptionInfo.setPaidAmt(dealerOrder.getPaidAmt());
        this.setSubscriptionInfo(subscriptionInfo);
    }
}
