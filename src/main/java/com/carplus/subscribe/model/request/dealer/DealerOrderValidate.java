package com.carplus.subscribe.model.request.dealer;

import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealerOrderValidate {

    private List<DealerOrderExcel> rows;
    private List<DealerOrderExcel> errorRows;
    private List<DealerOrderValidateError> errorList;
}
