package com.carplus.subscribe.model.request.econtract;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EContractRequest {
    // EContract

    // UploadFiles
    @JsonProperty("uploadFileId")
    @Schema(name = "uploadFileId", description = "11")
    private Integer uploadFileId;
    @JsonProperty("displayFilename")
    @Schema(name = "displayFilename", description = "顯示用合約範本檔案名稱", example = "格上汽車訂閱式租賃契約")
    private String displayFilename;
    @JsonProperty("uploadFileRemark")
    @Schema(name = "uploadFileRemark", description = "上傳檔案備註")
    private String uploadFileRemark;
}
