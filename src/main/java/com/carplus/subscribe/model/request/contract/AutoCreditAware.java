package com.carplus.subscribe.model.request.contract;

import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.AssertTrue;

public interface AutoCreditAware {

    @Schema(description = "是否需要自動授信")
    boolean isNeedAutoCredit();

    @Schema(description = "不需要自動授信的原因")
    String getAutoCreditBypassReason();

    void setNeedAutoCredit(boolean needAutoCredit);

    void setAutoCreditBypassReason(String autoCreditBypassReason);

    @Schema(hidden = true)
    @AssertTrue(message = "不需要自動授信時，必填不需要自動授信的原因")
    default boolean isAutoCreditBypassReasonValid() {
        return isNeedAutoCredit() || (getAutoCreditBypassReason() != null && !getAutoCreditBypassReason().trim().isEmpty());
    }
}