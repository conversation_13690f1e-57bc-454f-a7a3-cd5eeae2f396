package com.carplus.subscribe.model.request.campaign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class CampaignRequest {

    @NotBlank(message = "活動標題不可為空")
    @Schema(description = "活動標題")
    private String title;

    @NotBlank(message = "活動說明不可為空")
    @Schema(description = "活動說明")
    private String description;

    @NotNull(message = "車輛顯示條件不可為空")
    @Valid
    @Schema(description = "車輛顯示條件")
    private CarsCondition carsCondition;

    @NotNull(message = "橫幅類別編號不可為空")
    @Schema(description = "橫幅類別編號")
    private Integer bannerCategoryId;

    @NotNull(message = "活動開始時間不可為空")
    @Schema(description = "活動開始時間")
    private Instant startDate;

    @NotNull(message = "活動結束時間不可為空")
    @Schema(description = "活動結束時間")
    private Instant endDate;
}
