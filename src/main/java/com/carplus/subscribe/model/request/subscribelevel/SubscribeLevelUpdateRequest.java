package com.carplus.subscribe.model.request.subscribelevel;

import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.util.List;

import static com.carplus.subscribe.enums.SubscribeType.SEASON;

@Data
public class SubscribeLevelUpdateRequest {

    @Schema(description = "主鍵")
    @NotNull(message = "主鍵不可為空")
    @Positive(message = "主鍵須大於 0")
    private Integer id;

    @NotBlank(message = "訂閱車方案名稱不可為空")
    @Schema(description = "訂閱車方案名稱")
    private String name;

    @Schema(description = "對照超激優惠方案")
    @Positive(message = "對照優惠方案須大於 0")
    private Integer discountLevel;

    @Schema(description = "保證金")
    @NotNull(message = "保證金不可為空")
    @PositiveOrZero(message = "保證金不可小於 0")
    private Integer securityDeposit;

    @Schema(description = "基本月費")
    @NotNull(message = "基本月費不可為空")
    @PositiveOrZero(message = "基本月費不可小於 0")
    private Integer monthlyFee;

    @Schema(description = "里程費")
    @NotNull(message = "里程費不可為空")
    @PositiveOrZero(message = "里程費不可小於 0")
    private Double mileageFee;

    @Schema(description = "是否自動授信")
    @NotNull(message = "是否自動授信不可為空")
    private Boolean autoCredit;

    @Schema(description = "里程優惠")
    private List<MileageDiscount> mileageDiscount = Lists.newArrayList();

    @Schema(description = "優惠價")
    @NotNull(message = "優惠價不可為空")
    @PositiveOrZero(message = "優惠價不可小於 0")
    private Integer discountMonthlyFee;

    @Schema(description = "方案類型")
    @NotNull(message = "方案類型不可為空")
    private SubscribeType type = SEASON;

    public SubscribeLevelUpdateRequest() {
    }
}
