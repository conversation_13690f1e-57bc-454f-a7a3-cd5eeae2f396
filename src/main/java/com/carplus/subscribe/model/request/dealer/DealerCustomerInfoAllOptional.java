package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DealerCustomerInfoAllOptional extends BaseDealerCustomerInfo {

    @Schema(description = "訂車人身分ID")
    private String idNo;

    @Schema(description = "訂車人姓名")
    private String userName;

    @Schema(description = "訂車人國碼")
    private String nationalCode;

    @Schema(description = "訂車人電話")
    private String mainCell;

    @Schema(description = "訂車人生日")
    private String birthDay;

    @Schema(description = "訂車人信箱")
    private String email;

    @Schema(description = "訂車人戶籍縣市")
    private Integer city;

    @Schema(description = "訂車人戶籍區域")
    private Integer area;

    @Schema(description = "訂車人戶籍地址")
    private String address;
}
