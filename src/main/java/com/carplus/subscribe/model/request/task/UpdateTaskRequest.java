package com.carplus.subscribe.model.request.task;

import com.carplus.subscribe.enums.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class UpdateTaskRequest {
    private Integer id;

    @Schema(description = "任務狀態")
    private Integer status;

    @Schema(description = "被分派者")
    private String assignedId;

    @Schema(description = "被分派者名稱")
    private String assignedName;

    @Schema(description = "站點編號")
    private String stationId;

    @Schema(description = "站點名稱")
    private String stationName;

    @Schema(description = "任務預計開始時間")
    private Date expectExecuteDate;

    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(type = "出/還車任務類別; DEPART,RETURN")
    private TaskType taskType;

    @Schema(description = "里程")
    private Integer mileage;

}
