package com.carplus.subscribe.model.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class UpdateDepartMileageRequest {
    @NotNull(message = "異動出車里程數不可為空")
    @Schema(description = "異動出車里程數")
    private Integer newDepartMileage;

    @Schema(description = "留言給客戶")
    private String msgForCustomer;
}
