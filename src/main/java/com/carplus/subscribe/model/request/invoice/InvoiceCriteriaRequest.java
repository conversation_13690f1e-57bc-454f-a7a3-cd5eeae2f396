package com.carplus.subscribe.model.request.invoice;

import com.carplus.subscribe.exception.SubscribeException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SUBSCRIBE_QUERY_DATE_NOT_EMPTY;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceCriteriaRequest {

    @Schema(description = "異動起始日")
    private Instant dateFrom;

    @Schema(description = "異動結束日")
    private Instant dateTo;

    private Integer skip;

    private Integer limit;

    /**
     * 網頁查詢檢驗
     */
    public void validateForWeb() {
        if (dateFrom == null || dateTo == null) {
            throw new SubscribeException(SUBSCRIBE_QUERY_DATE_NOT_EMPTY);
        }
    }
}
