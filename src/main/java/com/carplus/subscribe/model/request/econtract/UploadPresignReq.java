package com.carplus.subscribe.model.request.econtract;

import com.carplus.subscribe.enums.UploadFileKindEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UploadPresignReq {

    @Enumerated(EnumType.STRING)
    @Schema(description = "上傳類型 SIGN: 簽名圖檔(PNG)", example = "SIGN")
    private UploadFileKindEnum kind;
}
