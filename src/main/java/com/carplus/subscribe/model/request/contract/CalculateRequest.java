package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.config.mapper.SubscribeMonthDeserializer;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.enums.SubscribeMonth;
import com.carplus.subscribe.model.EmpMileageDiscount;
import com.carplus.subscribe.model.config.YesChargingPoint;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
public class CalculateRequest {
    @NotNull(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼")
    private String plateNo;
    @NotNull(message = "方案不可為空")
    @Schema(description = "方案")
    private Integer carLevel;
    @NotNull(message = "出車站點不可為空")
    @Schema(description = "出車站點")
    private String departStationCode;
    @NotNull(message = "合約預計開始時間不可為空")
    @Schema(description = "合約預計開始時間")
    private Instant expectStartDate;
    @NotNull(message = "訂閱月份不可為空")
    @Schema(description = "訂閱月份")
    @JsonDeserialize(using = SubscribeMonthDeserializer.class)
    private SubscribeMonth month;
    @Schema(description = "主約編號")
    private String mainContractNo;
    @Schema(description = "是否投保免責險")
    private boolean disclaimer;
    @Schema(description = "是否投保溢價險")
    private boolean premium;
    @Schema(description = "是否代步車")
    private boolean replacement;
    @Schema(description = "每季里程數折扣")
    private Map<Integer, EmpMileageDiscount> mileageDiscounts;
    @Schema(description = "車電充值金")
    private YesChargingPoint yesChargingPoint;
    @Valid
    @Schema(description = "加購汽車用品列表")
    private List<MerchandiseReq> merchList;
    @Schema(description = "訂單平台來源")
    private OrderPlatform orderPlatform;
}
