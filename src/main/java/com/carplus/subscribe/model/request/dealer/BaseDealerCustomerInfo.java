package com.carplus.subscribe.model.request.dealer;

import com.carplus.subscribe.model.auth.AuthDealerUser;
import com.carplus.subscribe.model.auth.req.AuthDealerUserSaveRequest;
import com.carplus.subscribe.model.response.dealer.DealerUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_VAT_NO;

@Data
public class BaseDealerCustomerInfo implements DealerUser {

    @JsonIgnore
    private Long dealerUserId;

    @Schema(description = "身份 0: 本國人(含居留), 1: 外國人", allowableValues = "0,1", example = "1")
    private Integer isForeigner;

    @Schema(description = "法人共同承租人統一編號", example = CARPLUS_COMPANY_VAT_NO)
    private String vatNumber;

    @Schema(description = "法人共同承租人公司抬頭", example = "格上汽車租賃股份有限公司")
    private String companyName;

    @Schema(description = "法人共同承租人公司地址", example = "臺北市大安區敦化南路2段2號11樓")
    private String companyLocation;

    @Schema(description = "訂車人身分ID", example = "A123456789")
    protected String idNo;

    @Schema(description = "訂車人姓名", example = "王小明")
    protected String userName;

    @Schema(description = "訂車人國碼", example = "886")
    protected String nationalCode;

    @Schema(description = "訂車人電話", example = "0912345678")
    protected String mainCell;

    @Schema(description = "訂車人生日", example = "1990-01-01")
    protected String birthDay;

    @Schema(description = "訂車人信箱", example = "<EMAIL>")
    protected String email;

    @Schema(description = "訂車人戶籍縣市", example = "1")
    protected Integer city;

    @Schema(description = "訂車人戶籍區域", example = "5")
    protected Integer area;

    @Schema(description = "訂車人戶籍地址", example = "和平東路二段96巷17弄20號2F")
    protected String address;

    public AuthDealerUserSaveRequest buildAuthDealerUserSaveRequest() {
        AuthDealerUser authDealerUser = AuthDealerUser.builder()
            .idNo(getIdNo())
            .userName(getUserName())
            .nationalCode(getNationalCode())
            .mainCell(getMainCell())
            .birthDay(getBirthDay())
            .email(getEmail())
            .hhcityId(getCity())
            .hhareaId(getArea())
            .hhaddress(getAddress())
            .vatNumber(getVatNumber())
            .companyName(getCompanyName())
            .companyLocation(getCompanyLocation())
            .build();
        return new AuthDealerUserSaveRequest(Collections.singletonList(authDealerUser));
    }
}
