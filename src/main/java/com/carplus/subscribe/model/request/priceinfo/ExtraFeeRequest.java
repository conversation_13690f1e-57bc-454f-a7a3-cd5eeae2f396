package com.carplus.subscribe.model.request.priceinfo;

import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtraFeeRequest {
    @Schema(description = "是否車損")
    private boolean carDamaged;
    @Schema(description = "是否還車議價中")
    private String returnNego = "N";

    private List<ExtraFee> extraFeeList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtraFee {
        @Schema(description = "費用明細ID")
        private Integer priceInfoId;

        @Schema(description = "額外費用 / 實收自付額(carLossAmt)")
        @NotNull(message = "額外費用 / 實收自付額 不可為空")
        private Integer amount;

        @Schema(description = "應收自付額")
        @JsonProperty("arcarLossAmt")
        @NotNull(message = "應收自付額不可為空")
        private Integer aRCarLossAmt;

        @Schema(description = "折扣原因")
        @NotEmpty(message = "額外費用原因不可為空")
        private String reason;

        @Schema(description = "是否刪除")
        private boolean isDelete;

        @Schema(description = "期數")
        private Integer stage;

        @Schema(description = "款項類別=[Others(其他費用),Dispatch(跨區調度費),Insurance(其他駕駛保障),CarAccident(車損自負額)]")
        private PriceInfoDefinition.PriceInfoCategory category;

        @Schema(description = "儲值金")
        private Integer point;
    }
}
