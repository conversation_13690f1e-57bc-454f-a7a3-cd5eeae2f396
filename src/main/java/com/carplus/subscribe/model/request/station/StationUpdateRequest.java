package com.carplus.subscribe.model.request.station;

import com.carplus.subscribe.enums.StationDefine;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StationUpdateRequest {

    @Schema(description = "站所代碼")
    private String stationCode;

    /**
     * 站所性質 GENERAL:一般門市, COURIER:驛站, VSHOP:V-SHOP, DEALER:經銷商, VIRTUAL:虛擬站, WAREHOUSE:倉庫
     */
    @Schema(description = "站所性質")
    private StationDefine.StationCategory stationCategory;

    @Schema(description = "是否官網顯示(是否為訂閱站點)")
    private Boolean isSubscribe;

    @Schema(description = "站所狀態", example = "A:啟用, D:停用")
    private String status;

    @Schema(description = "是否啟用電子出租單")
    private Boolean forceOnlineRForm;
}
