package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EContractTemplateCriteria {

    private Integer skip;

    private Integer limit;

    @Schema(description = "範本代碼/名稱(營業)")
    private String templateIdNameSales;

    @Schema(description = "啟用時間")
    private Instant enableDate;

    @Schema(description = "啟用狀況")
    private Boolean isEnabled;

    @Schema(description = "訂單來源")
    private Integer conditionOrderSource;

    @Schema(description = "車輛所屬")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌")
    private String conditionCarBrand;

    @Schema(description = "訂閱類別")
    private String conditionCarState;

    @Schema(description = "車輛牌價起始")
    private Integer conditionCarPriceStart;

    @Schema(description = "車輛牌價結束")
    private Integer conditionCarPriceEnd;

    @Schema(description = "訂閱租期")
    private Integer conditionOrderMonth;

    @Schema(description = "是否冰宇車")
    private Boolean isSeaLandCar;

    @Schema(description = "車型")
    private String conditionCarModel;

    @Schema(description = "是否有免責費用")
    private Boolean isDisclaimerFee;

}
