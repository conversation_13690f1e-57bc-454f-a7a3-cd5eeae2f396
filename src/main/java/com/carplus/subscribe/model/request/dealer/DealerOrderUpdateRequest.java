package com.carplus.subscribe.model.request.dealer;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.model.auth.resp.AuthDealerUserResponse;
import com.carplus.subscribe.model.request.depart.CarDepartRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class DealerOrderUpdateRequest extends BaseDealerOrderRequest {

    @NotBlank(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼", example = "ABC-1234")
    private String plateNo;

    @Schema(description = "經銷商名稱")
    private String dealerName;

    @Schema(description = "保證金支付時間")
    private Instant securityDepositDate;

    @Schema(description = "母約編號")
    private String parentOrderNo;

    @Schema(description = "續約編號")
    private String nextStageOrderNo;

    @Schema(description = "期數")
    private String stage;

    @Schema(description = "是否投保免責險")
    private boolean disclaimer;

    @Schema(description = "訂車人資訊")
    @NotNull(message = "訂車人資訊不可為空")
    private DealerCustomerInfoAllOptional customerInfo;

    @Schema(description = "訂車方案資訊")
    @NotNull(message = "訂車方案資訊不可為空")
    private DealerSubscriptionInfoForUpdate subscriptionInfo;

    public DealerOrderUpdateRequest(DealerOrder dealerOrder, DealerOrderExcel dealerOrderExcel, AuthDealerUserResponse existingUser, Map<String, String> stationsNameCodeMap) {
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setDealerName(dealerOrder.getDealerName());
        this.setSecurityDepositDate(dealerOrder.getSecurityDepositDate());
        this.setParentOrderNo(dealerOrder.getParentOrderNo());
        this.setNextStageOrderNo(dealerOrder.getNextStageOrderNo());
        this.setStage(dealerOrder.getStage());
        // updatable
        this.setPlateNo(StringUtils.isNotBlank(dealerOrderExcel.getPlateNo()) ? dealerOrderExcel.getPlateNo() : dealerOrder.getPlateNo());
        DealerCustomerInfoAllOptional customerInfo = buildCustomerInfo(dealerOrderExcel, existingUser);
        this.setCustomerInfo(customerInfo);
        DealerSubscriptionInfoForUpdate subscriptionInfo = buildSubscriptionInfo(dealerOrder, dealerOrderExcel, stationsNameCodeMap);
        this.setSubscriptionInfo(subscriptionInfo);
    }

    private DealerCustomerInfoAllOptional buildCustomerInfo(DealerOrderExcel dealerOrderExcel, AuthDealerUserResponse existingUser) {
        DealerCustomerInfoAllOptional customerInfo = new DealerCustomerInfoAllOptional();
        customerInfo.setUserName(StringUtils.isNotBlank(dealerOrderExcel.getUserName()) ? dealerOrderExcel.getUserName() : existingUser != null ? existingUser.getUserName() : null);
        customerInfo.setIdNo(existingUser != null ? existingUser.getIdNo() : StringUtils.isNotBlank(dealerOrderExcel.getIdNo()) ? dealerOrderExcel.getIdNo() : null);
        customerInfo.setNationalCode(StringUtils.isNotBlank(dealerOrderExcel.getNationalCode()) ? dealerOrderExcel.getNationalCode() : existingUser != null ? existingUser.getNationalCode() : null);
        customerInfo.setMainCell(StringUtils.isNotBlank(dealerOrderExcel.getMainCell()) ? dealerOrderExcel.getMainCell() : existingUser != null ? existingUser.getMainCell() : null);
        customerInfo.setBirthDay(StringUtils.isNotBlank(dealerOrderExcel.getBirthDay()) ? dealerOrderExcel.getBirthDay() : existingUser != null ? existingUser.getBirthDay() : null);
        customerInfo.setEmail(StringUtils.isNotBlank(dealerOrderExcel.getEmail()) ? dealerOrderExcel.getEmail() : existingUser != null ? existingUser.getEmail() : null);
        customerInfo.setCity(Optional.ofNullable(dealerOrderExcel.getCity()).orElse(existingUser != null ? existingUser.getHhcityId() : null));
        customerInfo.setArea(Optional.ofNullable(dealerOrderExcel.getArea()).orElse(existingUser != null ? existingUser.getHhareaId() : null));
        customerInfo.setAddress(StringUtils.isNotBlank(dealerOrderExcel.getAddress()) ? dealerOrderExcel.getAddress() : existingUser != null ? existingUser.getHhaddress() : null);
        customerInfo.setVatNumber(StringUtils.isNotBlank(dealerOrderExcel.getVatNumber()) ? dealerOrderExcel.getVatNumber() : existingUser != null ? existingUser.getVatNumber() : null);
        customerInfo.setCompanyName(StringUtils.isNotBlank(dealerOrderExcel.getCompanyName()) ? dealerOrderExcel.getCompanyName() : existingUser != null ? existingUser.getCompanyName() : null);
        customerInfo.setCompanyLocation(StringUtils.isNotBlank(dealerOrderExcel.getCompanyLocation()) ? dealerOrderExcel.getCompanyLocation() : existingUser != null ? existingUser.getCompanyLocation() : null);
        return customerInfo;
    }

    private DealerSubscriptionInfoForUpdate buildSubscriptionInfo(DealerOrder dealerOrder, DealerOrderExcel dealerOrderExcel, Map<String, String> stationsNameCodeMap) {
        DealerSubscriptionInfoForUpdate subscriptionInfo = new DealerSubscriptionInfoForUpdate();
        subscriptionInfo.setExpectDepartStation(StringUtils.isNotBlank(dealerOrderExcel.getExpectDepartStationName()) ? stationsNameCodeMap.get(dealerOrderExcel.getExpectDepartStationName()) : dealerOrder.getExpectDepartStation());
        subscriptionInfo.setExpectReturnStation(StringUtils.isNotBlank(dealerOrderExcel.getExpectReturnStationName()) ? stationsNameCodeMap.get(dealerOrderExcel.getExpectReturnStationName()) : dealerOrder.getExpectReturnStation());
        subscriptionInfo.setExpectDepartDate(Optional.ofNullable(dealerOrderExcel.getExpectDepartDate()).map(Date::toInstant).orElse(dealerOrder.getExpectDepartDate()));
        subscriptionInfo.setExpectReturnDate(Optional.ofNullable(dealerOrderExcel.getExpectReturnDate()).map(Date::toInstant).orElse(dealerOrder.getExpectReturnDate()));
        subscriptionInfo.setSecurityDeposit(Optional.ofNullable(dealerOrderExcel.getSecurityDeposit()).orElse(dealerOrder.getSecurityDeposit()));
        subscriptionInfo.setMonthlyFee(Optional.ofNullable(dealerOrderExcel.getMonthlyFee()).orElse(dealerOrder.getMonthlyFee()));
        subscriptionInfo.setActualMileageRate(Optional.ofNullable(dealerOrderExcel.getActualMileageRate()).orElse(dealerOrder.getActualMileageRate()));
        subscriptionInfo.setOriginalMileageRate(Optional.ofNullable(dealerOrderExcel.getOriginalMileageRate()).orElse(dealerOrder.getOriginalMileageRate()));
        subscriptionInfo.setSubscribeMonth(Optional.ofNullable(dealerOrderExcel.getSubscribeMonth()).orElse(dealerOrder.getSubscribeMonth()));
        subscriptionInfo.setPrepaidMonths(Optional.ofNullable(dealerOrderExcel.getPrepaidMonths()).orElse(dealerOrder.getPrepaidMonths()));
        return subscriptionInfo;
    }

    public DealerOrderUpdateRequest(DealerOrder dealerOrder, AuthDealerUserResponse dealerUser, CarDepartRequest departRequest) {
        // 設置訂單基本資訊
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setDealerName(dealerOrder.getDealerName());
        this.setSecurityDepositDate(dealerOrder.getSecurityDepositDate());
        this.setParentOrderNo(dealerOrder.getParentOrderNo());
        this.setNextStageOrderNo(dealerOrder.getNextStageOrderNo());
        this.setStage(dealerOrder.getStage());
        
        // 設置車牌號碼，優先使用 departRequest 中的車牌
        this.setPlateNo(departRequest.getPlateNo());

        DealerCustomerInfoAllOptional customerInfo = new DealerCustomerInfoAllOptional();
        customerInfo.setUserName(dealerUser.getUserName());
        customerInfo.setIdNo(dealerUser.getIdNo());
        customerInfo.setNationalCode(dealerUser.getNationalCode());
        customerInfo.setMainCell(dealerUser.getMainCell());
        customerInfo.setBirthDay(dealerUser.getBirthDay());
        customerInfo.setEmail(dealerUser.getEmail());
        customerInfo.setCity(dealerUser.getHhcityId());
        customerInfo.setArea(dealerUser.getHhareaId());
        customerInfo.setAddress(dealerUser.getHhaddress());
        customerInfo.setVatNumber(dealerUser.getVatNumber());
        customerInfo.setCompanyName(dealerUser.getCompanyName());
        customerInfo.setCompanyLocation(dealerUser.getCompanyLocation());
        this.setCustomerInfo(customerInfo);

        DealerSubscriptionInfoForUpdate subscriptionInfo = new DealerSubscriptionInfoForUpdate();
        subscriptionInfo.setExpectDepartStation(dealerOrder.getExpectDepartStation());
        subscriptionInfo.setExpectReturnStation(dealerOrder.getExpectReturnStation());
        subscriptionInfo.setExpectDepartDate(Optional.ofNullable(departRequest.getDepartDate()).map(Date::toInstant).orElse(dealerOrder.getExpectDepartDate()));
        subscriptionInfo.setExpectReturnDate(dealerOrder.getExpectReturnDate());
        subscriptionInfo.setSecurityDeposit(dealerOrder.getSecurityDeposit());
        subscriptionInfo.setMonthlyFee(dealerOrder.getMonthlyFee());
        subscriptionInfo.setActualMileageRate(dealerOrder.getActualMileageRate());
        subscriptionInfo.setOriginalMileageRate(dealerOrder.getOriginalMileageRate());
        subscriptionInfo.setSubscribeMonth(dealerOrder.getSubscribeMonth());
        subscriptionInfo.setPrepaidMonths(dealerOrder.getPrepaidMonths());
        subscriptionInfo.setMonthlyFeeDiscount(dealerOrder.getMonthlyFeeDiscount());
        subscriptionInfo.setMileageRateDiscount(dealerOrder.getMileageRateDiscount());
        subscriptionInfo.setPrepaidMileage(dealerOrder.getPrepaidMileage());
        subscriptionInfo.setPrepaidMileageDiscount(dealerOrder.getPrepaidMileageDiscount());
        subscriptionInfo.setFlexibleMileage(dealerOrder.getFlexibleMileage());
        subscriptionInfo.setActualMileageUsed(dealerOrder.getActualMileageUsed());
        subscriptionInfo.setOffsetMileage1(dealerOrder.getOffsetMileage1());
        subscriptionInfo.setOffsetMileage2(dealerOrder.getOffsetMileage2());
        subscriptionInfo.setPrepaidMileageFee(dealerOrder.getPrepaidMileageFee());
        this.setSubscriptionInfo(subscriptionInfo);
    }
}
