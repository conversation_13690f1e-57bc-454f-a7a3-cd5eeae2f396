package com.carplus.subscribe.model.request.order;

import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.RenewType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrdersCriteria {

    private Integer skip;
    private Integer limit;
    @Schema(description = "查看單位")
    private List<String> stationCode;
    @Schema(description = "主約編號")
    private String mainContractNo;
    @Schema(description = "合約編號")
    private String contractNo;
    @Schema(description = "訂單狀態")
    private List<Integer> status;
    @Schema(description = "訂單編號")
    private String orderNo;

    @Schema(description = "發票號碼")
    private String invNo;

    @Schema(description = "訂車人姓名")
    private String acctName;
    @Schema(description = "訂車人電話國碼")
    private String nationalCode;
    @Schema(description = "訂車人電話")
    private String phone;
    @Schema(description = "身分證號")
    private String idNo;
    @Schema(description = "[預定]出車日期起")
    private Date expectDepartFrom;
    @Schema(description = "[預定]出車日期迄")
    private Date expectDepartTo;
    @Schema(description = "[預定]還車日期起")
    private Date expectReturnFrom;
    @Schema(description = "[預定]還出車日期迄")
    private Date expectReturnTo;
    @Schema(description = "[實際]出車日期起")
    private Date departFrom;
    @Schema(description = "[實際]出車日期迄")
    private Date departTo;
    @Schema(description = "[實際]還車日期起")
    private Date returnFrom;
    @Schema(description = "[實際]還出車日期迄")
    private Date returnTo;
    @Schema(description = "訂單建立時間起")
    private Date createFrom;
    @Schema(description = "訂單建立時間迄")
    private Date createTo;
    @Schema(description = "是否已排車")
    private Boolean carDispatched;
    @Schema(description = "車號")
    private Collection<String> plateNo;
    @Schema(description = "訂單續約狀態")
    private RenewType renewType;
    @Schema(description = "訂單租期")
    private List<Integer> month;
    @Schema(description = "是否新訂單")
    private Boolean isNewOrder;
    @Schema(description = "是否有未繳款項")
    private Boolean isUnpaid;
    @Schema
    private boolean isQueryPaid;
    @Schema(description = "車籍統編")
    private List<String> carVatNo;

    @JsonIgnore
    private List<Integer> acctId;

    private static final List<Integer> ALLOWED_MONTHS = Arrays.asList(1, 2, 3, 6, 9, 12);

    /**
     * validate after check acctId
     */
    public void validate() {
        if (CollectionUtils.isEmpty(status)) {
            status = Arrays.stream(OrderStatus.values()).map(OrderStatus::getStatus).collect(Collectors.toList());
        }
        if (status.contains(OrderStatus.CLOSE.getStatus())) {
            if (StringUtils.isBlank(orderNo)
                && (acctId == null || acctId.isEmpty())
                && (plateNo != null && plateNo.isEmpty())
                && isUnpaid == null
                && theDateRangeOverTheTargetMonth(expectDepartFrom, expectDepartTo, 3)
                && theDateRangeOverTheTargetMonth(expectReturnFrom, expectReturnTo, 3)
                && theDateRangeOverTheTargetMonth(departFrom, departTo, 3)
                && theDateRangeOverTheTargetMonth(returnFrom, returnTo, 3)
                && theDateRangeOverTheTargetMonth(createFrom, createTo, 3)) {
                throw new BadRequestException("查詢狀態為全選或包含[已還車時]，需提供[訂單編號or訂車人姓名or出租單號or身分證or手機or車號or日期條件且區間介於3個月之間]條件");
            }
        }
        validateMonths();
    }

    private void validateMonths() {
        if (CollectionUtils.isNotEmpty(month)) {
            for (Integer m : month) {
                if (!ALLOWED_MONTHS.contains(m)) {
                    throw new BadRequestException("訂單租期只允許 1, 2, 3, 6, 9, 12 月");
                }
            }
        }
    }

    /**
     * 起始結束日是否超過指定月數
     */
    private boolean theDateRangeOverTheTargetMonth(Date startDate, Date endDate, int month) {
        if (startDate == null || endDate == null) {
            return true;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(startDate.getTime());
        cal.add(Calendar.MONTH, month);
        return endDate.getTime() > cal.getTimeInMillis();
    }
}
