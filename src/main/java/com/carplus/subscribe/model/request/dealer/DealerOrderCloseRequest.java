package com.carplus.subscribe.model.request.dealer;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.request.dropoff.CarDropOffRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class DealerOrderCloseRequest extends BaseDealerOrderRequest {

    @NotBlank(message = "車牌號碼不可為空")
    @Schema(description = "車牌號碼", example = "ABC-1234")
    private String plateNo;

    @Schema(description = "經銷商名稱")
    private String dealerName;

    @Schema(description = "訂單狀態")
    private Integer orderStatus = ContractStatus.COMPLETE.getCode();

    @Schema(description = "是否新訂單")
    private Boolean isNewOrder;

    @Schema(description = "是否通過授信")
    private Boolean isAudit;

    @Schema(description = "是否支付保證金")
    private Boolean isPaySecurityDeposit;

    @Schema(description = "母約編號")
    private String parentOrderNo;

    @Schema(description = "期數")
    private String stage;

    @Schema(description = "訂車人資訊")
    private DealerCustomerInfoAllOptional customerInfo;

    @Schema(description = "訂閱方案資訊")
    @Valid
    @NotNull(message = "訂閱方案資訊不可為空")
    private DealerSubscriptionInfoForClose subscriptionInfo;

    public DealerOrderCloseRequest(DealerOrder dealerOrder, DealerOrderExcel dealerOrderExcel, Map<String, String> stationsNameCodeMap) {
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setPlateNo(dealerOrder.getPlateNo());
        this.setOrderStatus(ContractStatus.COMPLETE.getCode());
        // updatable
        DealerSubscriptionInfoForClose subscriptionInfo = new DealerSubscriptionInfoForClose();
        subscriptionInfo.setReturnStation(stationsNameCodeMap.get(dealerOrderExcel.getReturnStationName()));
        subscriptionInfo.setReturnDate(dealerOrderExcel.getReturnDate().toInstant());
        subscriptionInfo.setCloseAmt(dealerOrderExcel.getCloseAmt());
        subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt() + dealerOrderExcel.getCloseAmt());
        subscriptionInfo.setPaidAmt(dealerOrder.getPaidAmt() + dealerOrderExcel.getCloseAmt());
        subscriptionInfo.setIsReturned(dealerOrderExcel.getIsReturned());
        this.subscriptionInfo = subscriptionInfo;
    }

    public DealerOrderCloseRequest(DealerOrder dealerOrder, CarDropOffRequest request) {
        this.setOrderNo(dealerOrder.getOrderNo());
        this.setPlateNo(dealerOrder.getPlateNo());
        this.setOrderStatus(ContractStatus.COMPLETE.getCode());
        
        DealerSubscriptionInfoForClose subscriptionInfo = new DealerSubscriptionInfoForClose();
        subscriptionInfo.setReturnStation(dealerOrder.getReturnStation());
        subscriptionInfo.setReturnDate(request.getReturnDate().toInstant());
        subscriptionInfo.setCloseAmt(0);
        subscriptionInfo.setTotalAmt(dealerOrder.getTotalAmt());
        subscriptionInfo.setPaidAmt(dealerOrder.getPaidAmt());
        subscriptionInfo.setIsReturned(true);
        this.subscriptionInfo = subscriptionInfo;
    }
}
