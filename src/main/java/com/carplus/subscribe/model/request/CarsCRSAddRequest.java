package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CarsCRSAddRequest {

    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    @NotBlank(message = "車號不可為空")
    private String plateNo;

    @Schema(description = "申請單單號")
    private Integer buChangeMasterId;

    @Schema(description = "申請事由")
    private String changeMemo;
}
