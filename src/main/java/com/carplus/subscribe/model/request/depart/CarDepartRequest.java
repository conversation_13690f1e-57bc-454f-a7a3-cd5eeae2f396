package com.carplus.subscribe.model.request.depart;

import com.carplus.subscribe.enums.ETagModelEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Optional;

/**
 * [訂閱車]收銀台出車資料確認
 */
@Data
public class CarDepartRequest {

    @NotNull(message = "實際出車時間不可為空")
    @Schema(description = "實際出車時間")
    private Date departDate;
    @NotNull(message = "出車里程不可為空")
    @Schema(description = "出車里程")
    private Integer departMileage;
    @Schema(description = "出車備註")
    private String departRemark;
    @NotBlank(message = "車號不可為空")
    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    private String plateNo;
    @Schema(description = "車色")
    private String color;
    @Schema(description = "etag型式")
    private ETagModelEnum etagModel;
    @Schema(description = "etag序號")
    private String etagNo;

    public String getPlateNo() {
        return Optional.ofNullable(plateNo).map(String::toUpperCase).orElse(null);

    }

    public void setPlateNo(String plateNo) {
        Optional.ofNullable(plateNo).map(String::toUpperCase).ifPresent(s -> this.plateNo = s);
    }
}
