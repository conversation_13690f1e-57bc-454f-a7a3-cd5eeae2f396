package com.carplus.subscribe.model.request;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.enums.CarDefine;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Optional;

@Data
@Schema(description = "短租車籍匯入請求")
public class CarsImportOriginalRequest {

    private static final String TW_PLATE_NO_PATTERN = "\\w{1,3}-\\w{1,4}";

    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    @NotBlank(message = "車號不可為空")
    private String plateNo;
    @Schema(description = "出租類別")
    private List<CarDefine.RentType> rentTypes = Lists.newArrayList(CarDefine.RentType.subscribe);

    public void validate() {
        if (!plateNo.matches(TW_PLATE_NO_PATTERN)) {
            throw new BadRequestException("車號格式錯誤!");
        }
    }

    public String getPlateNo() {
        return Optional.ofNullable(plateNo).map(String::toUpperCase).orElse(null);

    }

    public void setPlateNo(String plateNo) {
        Optional.ofNullable(plateNo).map(String::toUpperCase).ifPresent(s -> this.plateNo = s);
    }
}
