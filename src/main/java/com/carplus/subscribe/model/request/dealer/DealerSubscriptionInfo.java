package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class DealerSubscriptionInfo implements DealerSubscriptionInfoInterface {

    @Schema(description = "保證金", example = "10000")
    @NotNull(message = "保證金不可為空")
    private Integer securityDeposit;

    @Schema(description = "月費租金", example = "16800")
    @NotNull(message = "月費租金不可為空")
    private Integer monthlyFee;

    @Schema(description = "里程費率(實際)", example = "7")
    @NotNull(message = "里程費率(實際)不可為空")
    private Double actualMileageRate;

    @Schema(description = "里程費率(原價)", example = "14")
    @NotNull(message = "里程費率(原價)不可為空")
    private Double originalMileageRate;

    @Schema(description = "預定出車站點", example = "71")
    @NotBlank(message = "預定出車站點不可為空")
    private String expectDepartStation;

    @Schema(description = "預定還車站點", example = "71")
    @NotBlank(message = "預定還車站點不可為空")
    private String expectReturnStation;

    @Schema(description = "預定出車時間", example = "2024-06-01T00:00:00Z")
    @NotNull(message = "預定出車時間不可為空")
    private Instant expectDepartDate;

    @Schema(description = "預定還車時間", example = "2024-09-01T00:00:00Z")
    @NotNull(message = "預定還車時間不可為空")
    private Instant expectReturnDate;

    @Schema(description = "訂閱租期", example = "3")
    @NotNull(message = "訂閱租期不可為空")
    private Integer subscribeMonth;

    @Schema(description = "訂單應收總金額", example = "26820")
    private Integer totalAmt;

    @Schema(description = "月租金折抵")
    private Integer monthlyFeeDiscount;

    @Schema(description = "里程費率折數", example = "0.5")
    @NotNull(message = "里程費率折數不可為空")
    private Double mileageRateDiscount;

    @Schema(description = "預收月數")
    private Integer prepaidMonths;

    @Schema(description = "每月預收里程")
    private Integer prepaidMileage;

    @Schema(description = "預收里程折抵")
    private Integer prepaidMileageDiscount;

    @Schema(description = "彈性里程")
    private Integer flexibleMileage;

    @Schema(description = "實際使用里程")
    private Integer actualMileageUsed;

    @Schema(description = "折抵里程1")
    private Integer offsetMileage1;

    @Schema(description = "折抵里程2")
    private Integer offsetMileage2;

    @Schema(description = "預收里程")
    private Integer prepaidMileageFee;
}
