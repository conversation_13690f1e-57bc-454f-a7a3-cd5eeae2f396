package com.carplus.subscribe.model.request.priceinfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MerchandiseInfoRequest {

    @NotEmpty(message = "汽車用品明細不可為空")
    private List<@Valid MerchandiseInfo> merchandiseList;

    @Data
    public static class MerchandiseInfo {
        @Schema(description = "費用明細ID")
        private Integer priceInfoId;

        @Schema(description = "汽車用品實際單價")
        @NotNull(message = "汽車用品實際單價不可為空")
        @Min(value = 1, message = "汽車用品實際單價必須大於0")
        private Integer actualUnitPrice;

        @Schema(description = "汽車用品數量")
        @NotNull(message = "汽車用品數量不可為空")
        @Min(value = 1, message = "汽車用品數量必須大於0")
        private Integer quantity;

        @Schema(description = "是否刪除")
        private boolean isDelete;

        @Schema(description = "期數")
        private Integer stage;

        @Schema(description = "汽車用品代碼")
        @NotBlank(message = "汽車用品代碼不可為空")
        private String skuCode;
    }
}
