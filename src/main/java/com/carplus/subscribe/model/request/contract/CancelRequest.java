package com.carplus.subscribe.model.request.contract;

import com.carplus.subscribe.model.invoice.InvoiceRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 取消訂單請求
 */
@Data
public class CancelRequest {

    @Schema(description = "說明")
    @NotEmpty(message = "說明不可為空")
    private String remark;
    @Valid
    @Schema(description = "開立發票")
    private List<@Valid InvoiceRequest> invoices;

    @Schema(description = "是否強制退款")
    private boolean forceRemitRefund;
}
