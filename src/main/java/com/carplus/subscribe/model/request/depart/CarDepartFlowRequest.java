package com.carplus.subscribe.model.request.depart;

import com.carplus.subscribe.enums.RentalFormType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CarDepartFlowRequest {

    @Schema(description = "員工編號")
    @NotNull(message = "員工編號不可為空")
    private String departMemberId;

    @Schema(description = "出租單類型 (出車任務完成因帳不平而出車失敗後，再次出車時，給 ELECTRONIC；出車任務尚未成功完成前，欲改成走紙本出租單，則給 PAPER)")
    private RentalFormType rentalFormType;
}
