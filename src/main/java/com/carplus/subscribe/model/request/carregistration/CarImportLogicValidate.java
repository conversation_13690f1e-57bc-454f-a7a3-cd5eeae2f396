package com.carplus.subscribe.model.request.carregistration;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.model.crs.CarBase;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.crs.CarSpecInfoResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarImportLogicValidate {

    private List<String> errorMessages;

    private CarRegistrationCSV csv;

    private List<Integer> levels;

    private CarBaseInfoSearchResponse carBaseInfo;

    private List<String> existPlateNos;

    private Boolean changeLevel;


    public void validate() {
        checkDuplicatePlateNo();
        checkSubscribeLeveChange();
        checkSubscribeLevel();
        logicValidate();
    }

    private void checkDuplicatePlateNo() {
        //檢查車號在CSV是否重複
        if (existPlateNos.contains(csv.getPlateNo())) {
            errorMessages.add("車號重複匯入");
        }
    }

    private void checkSubscribeLeveChange() {
        //檢查方案異動時，是否有進行中訂單
        if (!changeLevel) {
            errorMessages.add("有進行中訂單不可異動方案");
        }
    }


    private void checkSubscribeLevel() {
        //檢查訂閱方案
        if (csv.getSubscribeLevel() == null) {
            errorMessages.add("沒有訂閱車方案等級");
        } else if (!levels.contains(csv.getSubscribeLevel())) {
            errorMessages.add("訂閱車方案 level:" + csv.getSubscribeLevel() + " 不存在");
        }

    }

    private void logicValidate() {
        if (carBaseInfo != null) {
            checkMfg();
            checkStdPrice();
            Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarSpecInfoResponse).map(CarSpecInfoResponse::getCylinder).map(BigDecimal::valueOf).ifPresent(csv::setDisplacement);
            Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarSpecInfoResponse).map(CarSpecInfoResponse::getSeats).ifPresent(csv::setSeat);
            Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarBase).map(CarBase::getKm).ifPresent(csv::setCurrentMileage);
            Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarNo).ifPresent(csv::setCrsCarNo);
            Optional.of(carBaseInfo).map(CarBaseInfoSearchResponse::getCarSpecInfoResponse).map(CarSpecInfoResponse::getCarPlusEnergyCode).ifPresent(energyCode -> csv.setEnergyType(CarDefine.EnergyType.of(energyCode)));
        } else if (csv.isCarPlusCar()) {
            errorMessages.add("此車號/CRS車號不存在CRS");
        }
    }


    private void checkMfg() {
        String mfg = Optional.ofNullable(carBaseInfo).map(CarBaseInfoSearchResponse::getCarBase)
            .map(CarBase::getPublishDate).map(StringUtils::trim).map(mfgYear -> mfgYear.substring(0, 4)).orElse(null);
        if (mfg == null && !csv.isVirtualCar()) {
            errorMessages.add("CRS無出廠年份");
        } else {
            csv.setMfgYear(mfg);
        }
    }

    private void checkStdPrice() {
        Integer stdPrice = Optional.ofNullable(carBaseInfo).map(CarBaseInfoSearchResponse::getCarBase)
            .map(CarBase::getStdPrice).orElse(null);
        if (stdPrice == null) {
            if (csv.getStdPrice() == null) {
                errorMessages.add("CRS無車輛牌價");
            }
        } else {
            csv.setStdPrice(stdPrice);
        }
    }
}