package com.carplus.subscribe.model.request.econtract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@Data
public class TemplateUpdateReq {

    //@NotNull(message = "範本代碼不可為空")
    @Schema(description = "範本代碼")
    private String templateCode;

    @Schema(description = "範本名稱-營業", example = "格上中古_3M")
    private String templateNameSales;

    @Schema(description = "範本名稱-官網", example = "格上汽車訂閱式租賃契約")
    private String templateNameCust;

    @Schema(description = "訂單來源", example = "0")
    private Integer conditionOrderSource;

    @Schema(description = "訂閱租期", example = "3")
    private Integer conditionOrderMonth;

    @Schema(description = "車輛所屬", example = "1")
    private String conditionCarBu;

    @Schema(description = "車輛廠牌", example = "00T03")
    private String conditionCarBrand;

    @Schema(description = "訂閱類別", example = "OLD")
    private String conditionCarState;

    @NotNull(message = "車輛牌價起始不可為空")
    @Schema(description = "車輛牌價起始", example = "100")
    private Integer conditionCarPriceStart;

    @NotNull(message = "車輛牌價結束不可為空")
    @Schema(description = "車輛牌價結束", example = "200")
    private Integer conditionCarPriceEnd;

    @Schema(description = "是否有免責費用")
    private boolean isDisclaimerFee;

    @Schema(description = "是否冰宇車")
    private boolean isSeaLandCar;

    @Schema(description = "車型")
    private String conditionCarModel;

    @Schema(description = "啟用時間", example = "1713858334000")
    private Instant enableDate;

    @Schema(description = "修訂說明", example = "memo")
    private String reviseMemo;

    @Schema(description = "檔案ID")
    private Integer UploadFileId;

    @Schema(description = "顯示用檔案名稱")
    private List<String> UploadFileName;

}
