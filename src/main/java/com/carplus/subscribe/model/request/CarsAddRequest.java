package com.carplus.subscribe.model.request;

import com.carplus.subscribe.db.mysql.entity.cars.CarPropertyProvider;
import com.carplus.subscribe.enums.CarDefine;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CarsAddRequest implements CarPropertyProvider {

    @Schema(description = "車號 (RAA 開頭且長度為 7 碼為虛擬車)")
    @NotBlank(message = "車號不可為空")
    private String plateNo;
    @Schema(description = "訂閱類別, NEW:新車; OLD:中古車", example = "OLD")
    @NotNull(message = "訂閱類別不可為空")
    private CarDefine.CarState carState;
    @Schema(description = "車型編號")
    @NotBlank(message = "車型編號不可為空")
    private String carModelCode;
    @Schema(description = "座位數")
    private int seat;
    @Schema(description = "燃料種類")
    private CarDefine.FuelType fuelType;
    @Schema(description = "里程數")
    private Integer currentMileage;
    @Schema(description = "配備")
    private List<Integer> equipIds;
    @Schema(description = "目前站點")
    private String locationStationCode;
    @Schema(description = "訂閱車方案")
    @NotNull(message = "訂閱車方案不可為空")
    @Positive(message = "訂閱車方案須為正整數")
    private Integer subscribeLevel;
    @Schema(description = "標籤")
    private List<Integer> tagIds;
    @Schema(description = "排氣量")
    private BigDecimal displacement;
    @Schema(description = "能源類別")
    private CarDefine.EnergyType energyType;
    @Schema(description = "出廠年份")
    private String mfgYear;
    @Schema(description = "車輛編號 (若為虛擬車須輸入 18{車牌後四碼數字}0101)")
    private String carNo;
    @Schema(description = "crs車輛編號")
    private Integer crsCarNo;
    @Schema(description = "牌價")
    private Integer stdPrice;
    @Schema(description = "車色")
    private String colorDesc;
    @Schema(description = "車體介紹")
    private String cnDesc;
    @Schema(description = "車籍統編")
    @NotBlank(message = "車籍統編不可為空")
    private String vatNo;
    @Min(value = 1, message = "準備工作天數不可小於 1")
    @Max(value = 127, message = "準備工作天數不可大於 127")
    @Schema(description = "準備工作天數")
    private Integer prepWorkdays;
}
