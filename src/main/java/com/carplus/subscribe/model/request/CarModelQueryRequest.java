package com.carplus.subscribe.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class CarModelQueryRequest {

    @Schema(description = "廠牌代碼")
    private String[] brandCodes;

    @Schema(description = "車型代碼")
    private String[] carModelCodes;
}
