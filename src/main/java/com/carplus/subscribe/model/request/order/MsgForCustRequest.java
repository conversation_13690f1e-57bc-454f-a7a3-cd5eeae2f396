package com.carplus.subscribe.model.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MsgForCustRequest {

    @NotBlank(message = "留言內容不可為空")
    @Schema(description = "給客戶留言內容")
    private String content;
}
