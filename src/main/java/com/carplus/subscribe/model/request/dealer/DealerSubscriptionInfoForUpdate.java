package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

@Data
public class DealerSubscriptionInfoForUpdate implements DealerSubscriptionInfoInterface {

    @Schema(description = "保證金")
    private Integer securityDeposit;

    @Schema(description = "月費租金")
    private Integer monthlyFee;

    @Schema(description = "里程費率(實際)")
    private Double actualMileageRate;

    @Schema(description = "里程費率(原價)")
    private Double originalMileageRate;

    @Schema(description = "預定出車站點")
    private String expectDepartStation;

    @Schema(description = "預定還車站點")
    private String expectReturnStation;

    @Schema(description = "預定出車時間")
    private Instant expectDepartDate;

    @Schema(description = "預定還車時間")
    private Instant expectReturnDate;

    @Schema(description = "訂閱租期")
    private Integer subscribeMonth;

    @Schema(description = "訂單應收總金額")
    private Integer totalAmt;

    @Schema(description = "月租金折抵")
    private Integer monthlyFeeDiscount;

    @Schema(description = "里程費率折數")
    private Double mileageRateDiscount;

    @Schema(description = "預收月數")
    private Integer prepaidMonths;

    @Schema(description = "每月預收里程")
    private Integer prepaidMileage;

    @Schema(description = "預收里程折抵")
    private Integer prepaidMileageDiscount;

    @Schema(description = "彈性里程")
    private Integer flexibleMileage;

    @Schema(description = "實際使用里程")
    private Integer actualMileageUsed;

    @Schema(description = "折抵里程1")
    private Integer offsetMileage1;

    @Schema(description = "折抵里程2")
    private Integer offsetMileage2;

    @Schema(description = "預收里程")
    private Integer prepaidMileageFee;
}
