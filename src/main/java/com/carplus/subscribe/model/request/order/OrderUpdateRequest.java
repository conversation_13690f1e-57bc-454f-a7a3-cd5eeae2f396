package com.carplus.subscribe.model.request.order;

import com.carplus.subscribe.model.ReferInfo;
import com.carplus.subscribe.model.invoice.Invoice;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
public class OrderUpdateRequest {

    @Schema(description = "訂單出車時間")
    @NotNull(message = "訂單出車時間不可為空")
    private Instant expectDepartDate;
    @Schema(description = "預定出車站所")
    @NotEmpty(message = "預定出車站所不可為空")
    private String departStationCode;
    @Schema(description = "預定還車站所")
    @NotEmpty(message = "預定還車站所不可為空")
    private String returnStationCode;
    @Schema(description = "訂單備註")
    private String remark;
    @Schema(description = "車牌號碼")
    @NotEmpty(message = "車牌號碼不可為空")
    private String plateNo;
    @Schema(description = "發票資訊")
    private Invoice invoice;
    /**
     * 訂閱訂單異動判斷(出車時間、出車&還車站所), 由validate給值
     **/
    @JsonIgnore
    private boolean isOrderChanged;

    @Schema(description = "departMemberId")
    private String departMemberId;

    @Schema(description = "returnMemberId")
    private String returnMemberId;

    @Schema(description = "介紹人")
    private ReferInfo referInfo;


    @JsonIgnore
    @Schema(description = "原預定出車站所")
    private String oriDepartStationCode;
    @JsonIgnore
    @Schema(description = "原預定還車站所")
    private String oriReturnStationCode;

    @JsonIgnore
    @Schema(description = "原訂單出車時間")
    private Instant oriExpectDepartDate;

    @JsonIgnore
    @Schema(description = "原訂單還車時間")
    private Instant oriExpectReturnDate;

    @JsonIgnore
    @Schema(description = "原車牌號碼")
    private String oriPlateNo;

    @Schema(description = "是否投保免責險")
    private Boolean disclaimer;

    @Schema(description = "是否代步車")
    private Boolean replacement;

    @Schema(description = "訂閱月份")
    @NotNull(message = "訂閱月份不可為空")
    private Integer month;
}
