package com.carplus.subscribe.model.request.econtract;

import com.carplus.subscribe.enums.UploadFileKindEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DownloadPresignReq {
    @Schema(description = "會員編號", example = "1234")
    private Integer acctId;

    @Enumerated(EnumType.STRING)
    @Schema(description = "上傳類型", example = "SIGN")
    private UploadFileKindEnum kind;

    @Schema(description = "檔案ID", example = "1")
    private Integer fileId;
}
