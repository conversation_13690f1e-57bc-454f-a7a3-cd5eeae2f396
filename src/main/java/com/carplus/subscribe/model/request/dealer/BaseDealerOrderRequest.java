package com.carplus.subscribe.model.request.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public abstract class BaseDealerOrderRequest {

    @Schema(description = "訂單編號", example = "24063012345")
    @NotBlank(message = "訂單編號不可為空")
    private String orderNo;

    @Schema(description = "前約訂單編號")
    private String previousOrderNo;

    protected BaseDealerCustomerInfo customerInfo;

    protected DealerSubscriptionInfoInterface subscriptionInfo;
}
