package com.carplus.subscribe.model.request.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "人工授信通過請求")
public class CreditApprovalRequest {

    @NotEmpty(message = "授信通過原因不可為空")
    @Schema(description = "授信通過原因")
    private String remark;
}
