package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "查詢發票主檔結果")
public class InvoiceMasterSearchResponse {
    /**
     * 發票號碼
     */
    @Schema(description = "發票號碼(10碼)")
    private String invoiceNo;

    /**
     * 發票開立日期
     */
    @Schema(description = "發票開立日期")
    private Instant invoiceDate;

    /**
     * 發票開立日期(GMT+8) YYYY-MM-DD
     */
    @Schema(description = "發票開立日期(GMT+8) YYYY-MM-DD")
    private String invoiceDateYYYYMMDD;

    /**
     * 字軌年度(西元yyyy)
     */
    @Schema(description = "字軌年度(西元yyyy)")
    private String periodYear;

    /**
     * 字軌期別 serviceCode=total_period-period
     */
    @Schema(description = "字軌期別 serviceCode=total_period-period")
    private String period;

    /**
     * 字軌期別名稱
     */
    @Schema(description = "字軌期別名稱")
    private String periodName;

    /**
     * 發票狀態 serviceCode=invoice_master-invoicestatus
     */
    @Schema(description = "發票狀態 serviceCode=invoice_master-invoicestatus")
    private String invoiceStatus;

    /**
     * 發票狀態名稱
     */
    @Schema(description = "發票狀態名稱")
    private String invoiceStatusName;

    /**
     * 發票類型 serviceCode=invoice_master-invoicetype
     */
    @Schema(description = "發票類型 serviceCode=invoice_master-invoicetype")
    private String invoiceType;

    /**
     * 發票類型名稱
     */
    @Schema(description = "發票類型名稱")
    private String invoiceTypeName;

    /**
     * 隨機碼(for B2C發票)
     */
    @Schema(description = "隨機碼(for B2C發票)")
    private String randomCode;

    /**
     * 業務別 serviceCode=business_info-businesstype
     */
    @Schema(description = "業務別 serviceCode=business_info-businesstype")
    private String businessType;

    /**
     * 業務別名稱
     */
    @Schema(description = "業務別名稱")
    private String businessTypeName;

    /**
     * 站別代碼(各系統自行定義)
     */
    @Schema(description = "站別代碼(各系統自行定義)")
    private String businessSubType;

    /**
     * 站別名稱(各系統自行定義)
     */
    @Schema(description = "站別名稱(各系統自行定義)")
    private String businessSubTypeName;

    /**
     * 總公司統編 serviceCode=total_period-vatno
     */
    @Schema(description = "總公司統編 serviceCode=total_period-vatno")
    private String vatNo;

    /**
     * 總公司名稱
     */
    @Schema(description = "總公司名稱")
    private String vatNoName;

    /**
     * 營業人統編(站別統編)
     */
    @Schema(description = "營業人統編(站別統編)")
    private String sellerVatNo;

    /**
     * 買受人統編
     */
    @Schema(description = "買受人統編")
    private String buyerVatNo;

    /**
     * 買受人名稱
     */
    @Schema(description = "買受人名稱")
    private String buyerName;

    /**
     * 稅別 serviceCode=invoice_master-taxType
     */
    @Schema(description = "稅別 serviceCode=invoice_master-taxType")
    private String taxType;

    /**
     * 稅別名稱
     */
    @Schema(description = "稅別名稱")
    private String taxTypeName;

    /**
     * 發票總金額(含稅)
     */
    @Schema(description = "發票總金額(含稅)")
    private Integer totalVATAmount;

    /**
     * 發票總稅額
     */
    @Schema(description = "發票總稅額")
    private Integer totalTax;

    /**
     * 發票總金額(未稅)
     */
    @Schema(description = "發票總金額(未稅)")
    private Integer totalAmount;

    /**
     * 會員代碼or身分證號
     */
    @Schema(description = "會員代碼or身分證號")
    private String memberID;

    /**
     * 發票備註
     */
    @Schema(description = "發票備註")
    private String memo;

    /**
     * 列印註記 serviceCode=invoice_master-printstatus
     */
    @Schema(description = "列印註記 serviceCode=invoice_master-printstatus")
    private String printStatus;

    /**
     * 列印註記名稱
     */
    @Schema(description = "列印註記名稱")
    private String printStatusName;

    /**
     * 發票開立通知方式 serviceCode=invoice_master-notifybuyertype
     */
    @Schema(description = "發票開立通知方式 serviceCode=invoice_master-notifybuyertype")
    private String notifyBuyerType;

    /**
     * 發票開立通知方式名稱
     */
    @Schema(description = "發票開立通知方式名稱")
    private String notifyBuyerTypeName;

    /**
     * 發票收件人姓名
     */
    @Schema(description = "發票收件人姓名")
    private String notifyBuyerName;

    /**
     * 發票收件地址郵遞區號
     */
    @Schema(description = "發票收件地址郵遞區號")
    private String notifyBuyerPost;

    /**
     * 發票收件地址
     */
    @Schema(description = "發票收件地址")
    private String notifyBuyerAddress;

    /**
     * 發票收件人 EMail
     */
    @Schema(description = "發票收件人 EMail")
    private String notifyBuyerEmail;

    /**
     * 發票收件人手機
     */
    @Schema(description = "發票收件人手機")
    private String notifyBuyerMobile;

    /**
     * 發票是否捐贈(for B2C發票)
     */
    @Schema(description = "發票是否捐贈(for B2C發票)")
    private Boolean isDonate;

    /**
     * 發票捐贈對象(愛心碼)(for B2C發票)
     */
    @Schema(description = "發票捐贈對象(愛心碼)(for B2C發票)")
    private String donateCode;

    /**
     * 發票載具類型 (載具類別編號)(for B2C發票)，serviceCode=invoice_master-carriertype
     */
    @Schema(description = "發票載具類型 (載具類別編號)(for B2C發票)，serviceCode=invoice_master-carriertype")
    private String carrierType;

    /**
     * 發票載具類型名稱
     */
    @Schema(description = "發票載具類型名稱")
    private String carrierTypeName;

    /**
     * 發票載具編號顯碼(for B2C發票)
     */
    @Schema(description = "發票載具編號顯碼(for B2C發票)")
    private String carrierID1;

    /**
     * 發票載具編號隱碼(for B2C發票)
     */
    @Schema(description = "發票載具編號隱碼(for B2C發票)")
    private String carrierID2;

    /**
     * 發票作廢日期
     */
    @Schema(description = "發票作廢日期")
    private Instant cancelDate;

    /**
     * 發票作廢備註
     */
    @Schema(description = "發票作廢備註")
    private String cancelMemo;

    /**
     * 發票開立transactionID
     */
    @Schema(description = "發票開立transactionID")
    private String issueTransactionID;

    /**
     * 發票作廢transactionID
     */
    @Schema(description = "發票作廢transactionID")
    private String cancelTransactionID;

    /**
     * 傳送關貿的發票開立檔FileName
     */
    @Deprecated
    @Schema(description = "傳送關貿的發票開立檔FileName")
    private String invoiceIssueFileName;

    /**
     * 傳送關貿的發票作廢檔FileName
     */
    @Deprecated
    @Schema(description = "傳送關貿的發票作廢檔FileName")
    private String invoiceCancelFileName;

    /**
     * 關貿回傳發票開立Ack紀錄流水號
     */
    @Schema(description = "關貿回傳發票開立Ack紀錄流水號")
    private Integer invoiceAckID;

    /**
     * 關貿回傳發票作廢Ack紀錄流水號
     */
    @Schema(description = "關貿回傳發票作廢Ack紀錄流水號")
    private Integer invoiceCancelAckID;

    /**
     * 申報格式代碼，電子發票固定帶35
     */
    @Schema(description = "申報格式代碼，電子發票固定帶35")
    private String invoiceFormate = "35";

    /**
     * 建檔人員
     */
    @Schema(description = "建檔人員")
    private String createUserId;

    /**
     * 建檔日期
     */
    @Schema(description = "建檔日期")
    private Instant createDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Instant updateDate;
}
