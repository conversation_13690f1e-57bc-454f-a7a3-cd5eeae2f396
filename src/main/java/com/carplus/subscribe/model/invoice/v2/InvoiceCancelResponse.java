package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "作廢發票結果")
public class InvoiceCancelResponse {

    /**
     * 發票作廢 transactionID
     */
    @Schema(description = "發票作廢 transactionID")
    private String cancelTransactionID;

    /**
     * 發票作廢日期
     */
    @Schema(description = "發票作廢日期")
    private Instant cancelDate;

    /**
     * 錯誤訊息
     */
    @Schema(description = "錯誤訊息")
    private String errorMsg;
}
