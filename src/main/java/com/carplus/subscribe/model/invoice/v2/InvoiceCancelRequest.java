package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "作廢發票")
public class InvoiceCancelRequest {

    /**
     * 發票作廢 transactionID
     */
    @Schema(description = "發票作廢 transactionID")
    private String cancelTransactionID;

    /**
     * 發票號碼
     */
    @Schema(description = "發票號碼", required = true)
    @NotEmpty(message = "發票號碼，不可為空")
    private String invoiceNo;

    /**
     * 發票開立日期
     */
    @Schema(description = "發票開立日期", required = true)
    @NotNull(message = "發票開立日期，不可為空")
    private Instant invoiceDate;

    /**
     * 發票作廢日期
     */
    @Schema(description = "發票作廢日期")
    private Instant cancelDate;

    /**
     * 發票作廢備註
     */
    @Schema(description = "發票作廢備註")
    private String cancelMemo;

    /**
     * 發票字軌年度
     */
    @Schema(description = "發票字軌年度")
    private String periodYear;
}
