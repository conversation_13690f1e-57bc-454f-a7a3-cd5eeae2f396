package com.carplus.subscribe.model.invoice.v2;

import com.carplus.subscribe.enums.inv.AllowanceDataEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "依整張發票，開立折讓")
public class AllowanceIssueByInvoiceNoRequest {

    /**
     * 折讓開立日期
     */
    @Schema(description = "折讓開立日期")
    private Instant allowanceDate;

    /**
     * 折讓狀態 serviceCode=allowance_master-allowancestatus
     */
    @Schema(description = "折讓狀態 serviceCode=allowance_master-allowancestatus", required = true)
    @NotNull(message = "折讓類型，不可為空")
    private AllowanceDataEnum.AllowanceStatus allowanceStatus;

    /**
     * 折讓類型 serviceCode=allowance_master-allowancetype
     */
    @Schema(description = "折讓類型 serviceCode=allowance_master-allowancetype", required = true)
    @NotNull(message = "折讓類型，不可為空")
    private AllowanceDataEnum.AllowanceType allowanceType;

    /**
     * 發票號碼
     */
    @Schema(description = "發票號碼", required = true)
    @NotEmpty(message = "發票號碼，不可為空")
    private String invoiceNo;

    /**
     * 發票字軌年度(西元yyyy)
     */
    @Schema(description = "發票字軌年度(西元yyyy)", required = true)
    @NotEmpty(message = "發票字軌年度，不可為空")
    private String periodYear;

    /**
     * 折讓備註
     */
    @Schema(description = "折讓備註")
    private String memo;

    /**
     * 買受人是否簽回
     */
    @Schema(description = "買受人是否已簽回", required = true)
    @NotNull(message = "買受人是否已簽回，不可為空")
    private Boolean isSigned;

    /**
     * 折讓開立transactionID
     */
    @Schema(description = "折讓開立transactionID")
    private String issueTransactionID;

}
