package com.carplus.subscribe.model.invoice;

import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import carplus.common.utils.TaiwanIDUtils;
import com.carplus.subscribe.enums.Donate;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.enums.InvoiceDefine.InvCategory;
import com.carplus.subscribe.enums.InvoiceDefine.InvType;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.util.Objects;
import java.util.Optional;

/**
 * 發票開立
 */
@Data
@NoArgsConstructor
public class Invoice {

    /**
     * 發票種類 InvType 2：二聯式 , 3：三聯式
     */
    @NotNull
    private Integer type;
    /**
     * 發票類別 InvCategory 1:紙本, 2：自然人憑證, 3：手機載具, 4：捐贈發票, 5：會員載具
     */
    @NotNull
    private Integer category;
    /**
     * 發票載具
     */
    private String carrierID;
    /**
     * 三聯式統編、捐贈單位代號
     */
    private String id;
    /**
     * 三聯式抬頭、捐贈單位名稱
     */
    private String title;

    /**
     * 預設訂閱發票格式
     */
    public static Invoice defaultInvoice() {
        Invoice invoice = new Invoice();
        invoice.setType(InvoiceDefine.InvType.Person.getType());
        invoice.setCategory(InvoiceDefine.InvCategory.MEMBER.getCategory());

        return invoice;
    }

    /**
     * @param userDefaultInvoice 檢查是否需要向 Finance 驗證
     */
    public void validate(@Nullable Invoice userDefaultInvoice) {
        InvoiceDefine.InvType invType = Optional.ofNullable(InvoiceDefine.InvType.of(type)).orElseThrow(() -> new BadRequestException("不存在的發票種類：" + type));
        InvoiceDefine.InvCategory invCategory = Optional.ofNullable(InvoiceDefine.InvCategory.of(category)).orElseThrow(() -> new BadRequestException("不存在的發票類型：" + category));
        // 三聯
        if (invType == InvType.Biz) {
            if (invCategory == InvCategory.DONATE) {
                throw new BadRequestException("三聯式不可選擇捐贈");
            }
            if (StringUtils.isBlank(id) || StringUtils.isBlank(title)) {
                throw new BadRequestException("三聯式統編與抬頭不可為空");
            }
            if (!TaiwanIDUtils.validateBID(id)) {
                throw new BadRequestException("與財政部統編規則不符");
            }
        }
        // 二聯
        if (invType == InvType.Person) {
            if (invCategory != InvCategory.DONATE) {
                if (userDefaultInvoice == null) {
//                    title = null;
                }
            }
        }

        // 自然人/手機載具
        if (invCategory == InvCategory.CA || invCategory == InvCategory.MOBILE) {
            if (StringUtils.isBlank(carrierID)) {
                throw new BadRequestException(invCategory.getTitle() + "號碼不可為空");
            }
        } else {
            carrierID = null;
        }
        // 捐贈
        if (invCategory == InvCategory.DONATE) {
            id = Donate.HSAPF.getId();
            title = Donate.HSAPF.getTitle();
        }
    }

    public void validate() {
        validate(null);
    }


    /**
     * 檢查是否需要呼叫 Finance 驗證
     *
     * @param userDefaultInvoice User 預設的發票內容於建立訂單時已驗證
     */
    public boolean needFinanceValidate(@Nullable Invoice userDefaultInvoice) {
        InvoiceDefine.InvCategory invCategory = Optional.ofNullable(InvoiceDefine.InvCategory.of(category)).orElseThrow(() -> new BadRequestException("不存在的發票類型：" + category));

        return (invCategory == InvCategory.CA || invCategory == InvCategory.MOBILE) && (userDefaultInvoice == null
            || !Objects.equals(carrierID, userDefaultInvoice.getCarrierID()));
    }

    /**
     * 1:二聯式-手機載具; 2:二聯式-會員載具; 3:三聯式-手機載具; 4:三聯式-會員載具; 5:發票捐贈
     */
    public int getDefaultInvoiceType() {
        if (type == InvType.Person.getType() && category == InvCategory.MOBILE.getCategory()) {
            return 1;
        } else if (type == InvType.Person.getType() && category == InvCategory.MEMBER.getCategory()) {
            return 2;
        } else if (type == InvType.Biz.getType() && category == InvCategory.MOBILE.getCategory()) {
            return 3;
        } else if (type == InvType.Biz.getType() && category == InvCategory.MEMBER.getCategory()) {
            return 4;
        } else if (category == InvCategory.DONATE.getCategory()) {
            return 5;
        } else {
            return 0;
        }
    }

    public String getCompanyId() {
        return InvType.Biz.getType() == type ? id : null;
    }

    public String getCompanyTitle() {
        return InvType.Biz.getType() == type ? title : null;
    }

    public String getDonateCompanyId() {
        return InvCategory.DONATE.getCategory() == category ? id : null;
    }

    public String getDonateCompanyTitle() {
        return InvCategory.DONATE.getCategory() == category ? title : null;
    }

    /**
     * 是否紙本發票
     */
    public boolean isPrintInvoice() {
        return InvCategory.PAPER.getCategory() == category;
    }
}
