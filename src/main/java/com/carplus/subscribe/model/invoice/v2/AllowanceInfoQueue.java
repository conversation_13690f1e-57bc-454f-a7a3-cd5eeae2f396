package com.carplus.subscribe.model.invoice.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

/**
 * 折讓資訊 queue
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class AllowanceInfoQueue {

    /**
     * 折讓單號碼(ALW+yyyy+mmdd+3碼流水號)
     */
    @Schema(description = "折讓單號碼(ALW+yyyy+mmdd+3碼流水號)")
    @JsonProperty("ALLOWANCE_NO")
    private String allowanceNo;

    /**
     * 折讓開立日期
     */
    @JsonProperty("ALLOWANCE_DATE")
    @Schema(description = "折讓開立日期")
    private Instant allowanceDate;

    /**
     * 折讓開立transactionID
     */
    @Schema(description = "折讓開立transactionID")
    private String issueTransactionID;

    /**
     * deque折讓單號的Service name
     */
    @Schema(description = "deque折讓單號的Service name")
    private String dequeService;

    /**
     * 錯誤訊息
     */
    @Schema(description = "錯誤訊息")
    private String errMsg;

}
