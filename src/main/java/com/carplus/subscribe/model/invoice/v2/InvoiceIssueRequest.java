package com.carplus.subscribe.model.invoice.v2;

import com.carplus.subscribe.enums.inv.BusinessInfoEnum;
import com.carplus.subscribe.enums.inv.InvoiceDataEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "開立發票")
public class InvoiceIssueRequest {

    /**
     * 發票開立日期
     */
    @Schema(description = "發票開立日期")
    private Instant invoiceDate;

    /**
     * 發票類型 serviceCode=invoice_master-invoicetype
     */
    @Schema(description = "發票類型 serviceCode=invoice_master-invoicetype", required = true)
    @NotNull(message = "發票開立transactionID，不可為空")
    private InvoiceDataEnum.InvoiceType invoiceType;

    /**
     * 業務別 serviceCode=business_info-businesstype
     */
    @Schema(description = "業務別 serviceCode=business_info-businesstype", required = true)
    @NotNull(message = "業務別，不可為空")
    private BusinessInfoEnum.BusinessType businessType;

    /**
     * 站別代碼(各系統自行定義)
     */
    @Schema(description = "站別代碼(各系統自行定義)", required = true)
    @NotEmpty(message = "站別代碼，不可為空")
    private String businessSubType;

//    /**
//     * 站別名稱(各系統自行定義)
//     */
//    @Schema(description ="站別名稱(各系統自行定義)")
//    private String businessSubTypeName;

    /**
     * 總公司統編 serviceCode=total_period-vatno
     */
    @Schema(description = "總公司統編 serviceCode=total_period-vatno", required = true)
    @NotEmpty(message = "總公司統編，不可為空")
    private String vatNo;

    /**
     * 營業人統編(站別統編)
     */
    @Schema(description = "營業人統編(站別統編)", required = true)
    @NotEmpty(message = "營業人統編(站別統編)，不可為空")
    private String sellerVatNo;

    /**
     * 買受人統編
     */
    @Schema(description = "買受人統編")
    private String buyerVatNo;

    /**
     * 買受人名稱
     */
    @Schema(description = "買受人名稱")
    private String buyerName;

    /**
     * 稅別 serviceCode=invoice_master-taxType
     */
    @Schema(description = "稅別 serviceCode=invoice_master-taxType", required = true)
    @NotNull(message = "稅別，不可為空")
    private InvoiceDataEnum.TaxType taxType;

    /**
     * 發票總金額(含稅)
     */
    @Schema(description = "發票總金額(含稅)", required = true)
    @NotNull(message = "發票總金額(含稅)，不可為空")
    private Integer totalVATAmount;

    /**
     * 發票總稅額
     */
    @Schema(description = "發票總稅額")
    private Integer totalTax;

    /**
     * 發票總金額(未稅)
     */
    @Schema(description = "發票總金額(未稅)")
    private Integer totalAmount;

    /**
     * 會員代碼or身分證號
     */
    @Schema(description = "會員代碼or身分證號")
    private String memberID;

    /**
     * 發票備註
     */
    @Schema(description = "發票備註")
    private String memo;

    /**
     * 列印註記 serviceCode=invoice_master-printstatus
     */
    @Schema(description = "列印註記 serviceCode=invoice_master-printstatus", example = "0")
    @Builder.Default
    private String printStatus = "0";

    /**
     * 發票開立通知方式 serviceCode=invoice_master-notifybuyertype
     */
    @Schema(description = "發票開立通知方式 serviceCode=invoice_master-notifybuyertype", example = "2")
    @Builder.Default
    private String notifyBuyerType = "2";

    /**
     * 發票收件人姓名
     */
    @Schema(description = "發票收件人姓名")
    private String notifyBuyerName;

    /**
     * 發票收件地址郵遞區號
     */
    @Schema(description = "發票收件地址郵遞區號")
    private String notifyBuyerPost;

    /**
     * 發票收件地址
     */
    @Schema(description = "發票收件地址")
    private String notifyBuyerAddress;

    /**
     * 發票收件人 EMail
     */
    @Schema(description = "發票收件人 EMail")
    private String notifyBuyerEmail;

    /**
     * 發票收件人手機
     */
    @Schema(description = "發票收件人手機")
    private String notifyBuyerMobile;

    /**
     * 發票是否捐贈(for B2C發票)
     */
    @Schema(description = "發票是否捐贈(for B2C發票)")
    private Boolean isDonate;

    /**
     * 發票捐贈對象(愛心碼)(for B2C發票)，isDonate =Ｙ時必填
     */
    @Schema(description = "發票捐贈對象(愛心碼)(for B2C發票)，isDonate =Ｙ時必填")
    private String donateCode;

    /**
     * 發票載具類型(for B2C發票)，serviceCode=invoice_master-carriertype，isDonate =N，且printStatus=0時，必填
     */
    @Schema(description = "發票載具類型(for B2C發票)，serviceCode=invoice_master-carriertype，isDonate =N，且printStatus=0時，必填")
    private String carrierType;

    /**
     * 發票載具編號顯碼(for B2C發票)
     */
    @Schema(description = "發票載具編號顯碼(for B2C發票)")
    private String carrierID1;

    /**
     * 發票載具編號隱碼(for B2C發票)
     */
    @Schema(description = "發票載具編號隱碼(for B2C發票)")
    private String carrierID2;

    /**
     * 發票開立transactionID
     */
    @Schema(description = "發票開立transactionID")
    private String issueTransactionID;

    /**
     * 開立發票明細List
     */
    @Schema(description = "開立發票明細List", required = true)
    private List<InvoiceIssueDetailRequest> invoiceIssueDetailList;

}
