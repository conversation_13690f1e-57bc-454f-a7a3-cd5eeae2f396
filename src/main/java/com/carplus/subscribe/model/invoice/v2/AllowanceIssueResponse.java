package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "開立折讓結果")
public class AllowanceIssueResponse {

    /**
     * 折讓開立transactionID
     */
    @Schema(description = "折讓開立transactionID")
    private String issueTransactionID;

    /**
     * 錯誤訊息
     */
    @Schema(description = "錯誤訊息")
    private String errorMsg;

}
