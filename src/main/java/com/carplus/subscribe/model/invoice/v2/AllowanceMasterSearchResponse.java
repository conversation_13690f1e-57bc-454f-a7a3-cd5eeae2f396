package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "查詢折讓單主檔結果")
public class AllowanceMasterSearchResponse {
    /**
     * 折讓單號碼(ALW+yyyy+mmdd+3碼流水號)
     */
    @Schema(description = "折讓單號碼(ALW+yyyy+mmdd+3碼流水號)")
    private String allowanceNo;

    /**
     * 折讓開立日期
     */
    @Schema(description = "折讓開立日期")
    private Instant allowanceDate;

    /**
     * 折讓狀態 serviceCode=allowance_master-allowancestatus
     */
    @Schema(description = "折讓狀態 serviceCode=allowance_master-allowancestatus")
    private String allowanceStatus;

    /**
     * 折讓類型 serviceCode=allowance_master-allowancetype
     */
    @Schema(description = "折讓類型 serviceCode=allowance_master-allowancetype")
    private String allowanceType;

    /**
     * 總公司統編 serviceCode=total_period-vatno
     */
    @Schema(description = "總公司統編 serviceCode=total_period-vatno")
    private String vatNo;

    /**
     * 營業人統編(站別統編)
     */
    @Schema(description = "營業人統編(站別統編)")
    private String sellerVatNo;

    /**
     * 營業人名稱
     */
    @Schema(description = "營業人名稱")
    private String sellerName;

    /**
     * 營業人地址
     */
    @Schema(description = "營業人地址")
    private String sellerAddress;

    /**
     * 買受人統編
     */
    @Schema(description = "買受人統編")
    private String buyerVatNo;

    /**
     * 買受人名稱
     */
    @Schema(description = "買受人名稱")
    private String buyerName;

    /**
     * 折讓總金額(含稅)
     */
    @Schema(description = "折讓總金額(含稅)")
    private Integer totalVATAmount;

    /**
     * 折讓總稅額
     */
    @Schema(description = "折讓總稅額")
    private Integer totalTax;

    /**
     * 折讓總金額(未稅)
     */
    @Schema(description = "折讓總金額(未稅)")
    private Integer totalAmount;

    /**
     * 折讓備註
     */
    @Schema(description = "折讓備註")
    private String memo;

    /**
     * 折讓作廢日期
     */
    @Schema(description = "折讓作廢日期")
    private Instant cancelDate;

    /**
     * 折讓作廢備註
     */
    @Schema(description = "折讓作廢備註")
    private String cancelMemo;

    /**
     * 列印日期
     */
    @Schema(description = "列印日期")
    private Instant printDate;

    /**
     * 買受人是否簽回
     */
    @Schema(description = "買受人是否簽回")
    private Boolean isSigned;

    /**
     * 折讓開立transactionID
     */
    @Schema(description = "折讓開立transactionID")
    private String issueTransactionID;

    /**
     * 折讓作廢transactionID
     */
    @Schema(description = "折讓作廢transactionID")
    private String cancelTransactionID;

    /**
     * 關貿回傳折讓開立Ack紀錄流水號
     */
    @Schema(description = "關貿回傳折讓開立Ack紀錄流水號")
    private Integer allowanceIssueAckID;

    /**
     * 關貿回傳折讓作廢Ack紀錄流水號
     */
    @Schema(description = "關貿回傳折讓作廢Ack紀錄流水號")
    private Integer allowanceCancelAckID;

    /**
     * deque折讓單號的Service name
     */
    @Schema(description = "deque折讓單號的Service name")
    private String dequeService;

    /**
     * 建檔人員
     */
    @Schema(description = "建檔人員")
    private String createUserId;

    /**
     * 建檔日期
     */
    @Schema(description = "建檔日期")
    private Instant createDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Instant updateDate;

    /**
     * 折讓狀態名稱
     */
    @Schema(description = "折讓狀態名稱")
    private String allowanceStatusName;

    /**
     * 折讓類型名稱
     */
    @Schema(description = "折讓類型名稱")
    private String allowanceTypeName;
}
