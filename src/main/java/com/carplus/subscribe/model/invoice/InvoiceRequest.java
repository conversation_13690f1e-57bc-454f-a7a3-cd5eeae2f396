package com.carplus.subscribe.model.invoice;

import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.lang.NonNull;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
public class InvoiceRequest implements InvoiceRequestInterface {
    /**
     * 發票內容
     */
    @NotNull
    private Invoice invoice;
    /**
     * 發票金額
     */
    @NotNull
    private Integer amount;

    @Schema(description = "關聯費用ID編號")
    private List<Integer> refPriceInfoIds;

    @Schema(description = "發票備註")
    @Size(max = 300, message = "發票備註最多300字元")
    private String memo;

    public Invoices toEntity(@NonNull String stationCode, @NonNull String payFor, @NonNull String invoiceNo, @NonNull String orderNo) {
        Invoices unit = new Invoices();

        int unTax = (int) Math.round((float) amount / 1.05);
        int tax = amount - unTax;

        unit.setStatus(InvoiceDefine.InvStatus.CREATE.name());
        unit.setInvoice(this.invoice);
        unit.setStationCode(stationCode);
        unit.setStationVATNo("");
        unit.setPayFor(payFor);
        unit.setMemo(this.memo);
        unit.setAmount(this.amount);
        unit.setTax(tax);
        unit.setUnTax(unTax);
        unit.setInvNo(invoiceNo);
        unit.setCreatedAt(new Date());
        unit.setOrderNo(orderNo);
        unit.setRefPriceInfoIds(refPriceInfoIds);
        return unit;
    }

    @JsonIgnore
    private boolean isMerchandiseRelated;
}
