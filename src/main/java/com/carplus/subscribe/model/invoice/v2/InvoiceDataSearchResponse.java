package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldNameConstants
@Schema(description = "查詢發票完整資訊結果")
public class InvoiceDataSearchResponse extends InvoiceMasterSearchResponse {
    /**
     * 站別名稱(各系統自行定義)
     */
    @Schema(description = "站別名稱(各系統自行定義)")
    private String businessSubTypeName;

    /**
     * 第三方加值中心 serviceCode=business_info-thirdpratyservice
     */
    @Schema(description = "第三方加值中心 serviceCode=business_info-thirdpratyservice")
    private String thirdPratyService;

    /**
     * 第三方加值中心名稱
     */
    @Schema(description = "第三方加值中心名稱")
    private String thirdPratyServiceName;

    /**
     * 折讓狀態名稱
     */
    @Schema(description = "折讓狀態名稱")
    private String allowanceStatusName;

    /**
     * 發票明細資料
     */
    @Schema(description = "發票明細資料")
    private List<InvoiceDetailSearchResponse> invoiceDetailSearchResponseList;
}
