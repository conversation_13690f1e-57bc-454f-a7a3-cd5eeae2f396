package com.carplus.subscribe.model.invoice;

import carplus.common.response.exception.BadRequestException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
public class InvoicePriceInfoUpdateRequest implements InvoiceRequestInterface {

    @NotNull
    @JsonIgnore
    private Integer amount;

    @NotNull
    @Schema(description = "關聯費用ID編號")
    private List<Integer> refPriceInfoIds;


    private String invoiceNo;

    public Integer getAmount() {
        if (amount == null) {
            throw new BadRequestException("請先設定發票金額");
        }
        return amount;
    }

}
