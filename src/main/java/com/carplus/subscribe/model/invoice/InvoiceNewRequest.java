package com.carplus.subscribe.model.invoice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
public class InvoiceNewRequest {

    @Schema(description = "發票開立站所代碼")
    private String stationCode;
    @Schema(description = "支付目的 - Depart:出車,Return:還車,Accident:車損")
    @NotEmpty(message = "支付目的不可為空")
    private String payFor;
    @Valid
    @NotNull
    @Schema(description = "發票開立")
    private List<InvoiceRequest> invoices = new ArrayList<>();
}
