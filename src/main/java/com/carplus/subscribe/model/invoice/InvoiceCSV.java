package com.carplus.subscribe.model.invoice;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.univocity.parsers.annotations.Format;
import com.univocity.parsers.annotations.Parsed;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

@Data
public class InvoiceCSV {

    public static String[] HEADS = Arrays.stream(InvoiceCSV.class.getDeclaredFields())
        .filter(field -> field.isAnnotationPresent(Parsed.class))
        .map(field -> field.getAnnotation(Parsed.class))
        .map(parsed -> parsed.field()[0])
        .toArray(String[]::new);

    private static final String TIME_ZONE = "Asia/Taipei";

    @Parsed(field = "發票號碼")
    private String invNo;

    @Parsed(field = "訂單編號")
    private String orderNo;

    @Parsed(field = "發票異動日期")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date updateDate;

    @Parsed(field = "狀態")
    private String status;

    @Parsed(field = "發票抬頭")
    private String title;

    @Parsed(field = "買受人統一編號")
    private String id;

    @Parsed(field = "發票開立日期")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date createdAt;

    @Parsed(field = "發票作廢/折讓日期")
    @Format(formats = "yyyy/MM/dd", options = "timeZone=" + TIME_ZONE)
    private Date deletedAt;

    @Parsed(field = "銷售額")
    private Integer unTax;

    @Parsed(field = "稅額")
    private Integer tax;

    @Parsed(field = "發票金額")
    private Integer amount;

    public InvoiceCSV(InvoiceDTO invoiceDTO) {
        this.invNo = invoiceDTO.getInvNo();
        this.orderNo = invoiceDTO.getOrderNo();
        this.updateDate = Optional.ofNullable(invoiceDTO.getDeletedAt()).orElse(invoiceDTO.getCreatedAt());
        this.status = InvoiceDefine.InvStatus.getName(invoiceDTO.getStatus());
        this.title = Optional.ofNullable(invoiceDTO.getInvoice())
            .map(Invoice::getTitle)
            .filter(s -> !s.equalsIgnoreCase("null"))
            .map(s -> StringUtils.mask(s, '*'))
            .orElse("");
        this.id = Optional.ofNullable(invoiceDTO.getInvoice())
            .map(Invoice::getId)
            .filter(s -> !s.equalsIgnoreCase("null"))
            .map(s -> StringUtils.mask(s, '*', 2))
            .orElse("");
        this.createdAt = invoiceDTO.getCreatedAt();
        this.deletedAt = invoiceDTO.getDeletedAt();
        this.unTax = invoiceDTO.getUnTax();
        this.tax = invoiceDTO.getTax();
        this.amount = invoiceDTO.getAmount();
    }
}
