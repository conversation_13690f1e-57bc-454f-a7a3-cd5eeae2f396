package com.carplus.subscribe.model.invoice;

import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.enums.PayFor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 已開立發票
 */
@Data
public class InvoiceInfo {

    private List<Invoices> invoices;

    public InvoiceInfo(List<Invoices> list) {
        this.invoices = list;
    }

    public InvoiceInfo() {
        this.invoices = new ArrayList<>();
    }

    /**
     * 開立
     *
     * @param invoices 發票資訊
     */
    public void create(Invoices invoices) {
        if (this.invoices.stream().noneMatch(invoice ->
            invoice.getInvNo().equals(invoices.getInvNo()))
        ) {
            this.invoices.add(invoices);
        }
    }

    /**
     * 作廢、折讓 更新原發票內容
     */
    public void replace(Invoices invoices) {
        this.invoices.stream().filter(invoice ->
            invoice.getInvNo().equals(invoices.getInvNo())
        ).findAny().ifPresent(inv ->
            inv.update(invoices)
        );
    }

    /**
     * 已開立總額
     */
    public int getTotalAmount() {
        int amount = 0;

        for (Invoices unit : this.invoices) {
            if (unit.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name())) {
                amount += unit.getAmount();
            }
        }

        return amount;
    }

    /**
     * 發票開立數量
     */
    public int getCount(PayFor payFor) {
        int count = 0;

        for (Invoices unit : this.invoices) {
            if (unit.getStatus().equals(InvoiceDefine.InvStatus.CREATE.name()) && unit.getPayFor().equals(payFor.name())) {
                count++;
            }
        }

        return count;
    }
}
