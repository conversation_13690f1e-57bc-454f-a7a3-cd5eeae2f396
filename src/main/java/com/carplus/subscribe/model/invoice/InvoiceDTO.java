package com.carplus.subscribe.model.invoice;

import lombok.Data;

import java.util.Date;

@Data
public class InvoiceDTO {
    private String invNo;
    private String orderNo;
    private String status;
    private Invoice invoice;
    private Date createdAt;
    private Date deletedAt;
    private Integer unTax;
    private Integer tax;
    private Integer amount;

    public InvoiceDTO(String invNo, String orderNo, String status, Object invoice, Date createdAt, Date deletedAt, Integer unTax, Integer tax, Integer amount) {
        this.invNo = invNo;
        this.orderNo = orderNo;
        this.status = status;
        this.invoice = (Invoice) invoice;
        this.createdAt = createdAt;
        this.deletedAt = deletedAt;
        this.unTax = unTax;
        this.tax = tax;
        this.amount = amount;
    }
}
