package com.carplus.subscribe.model.invoice.v2;

import com.carplus.subscribe.enums.inv.InvoiceDataEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.NotNull;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@FieldNameConstants
@Schema(description = "開立發票明細")
public class InvoiceIssueDetailRequest {

    /**
     * 項次
     */
    @Schema(description = "項次", required = true)
    @NotNull(message = "項次，不可為空")
    private Integer itemNo;

    /**
     * 商品代碼
     */
    @Schema(description = "商品代碼")
    private String itemCode;

    /**
     * 商品名稱
     */
    @Schema(description = "商品名稱")
    private String itemName;

    /**
     * 商品數量
     */
    @Schema(description = "商品數量", required = true)
    @NotNull(message = "商品數量，不可為空")
    private Integer qty;

    /**
     * 稅別 serviceCode=invoice_master-taxType
     */
    @Schema(description = "稅別 serviceCode=invoice_master-taxType", required = true)
    @NotNull(message = "稅別，不可為空")
    private InvoiceDataEnum.TaxType taxType;

    /**
     * 商品單價(含稅)
     */
    @Schema(description = "商品單價(含稅)", required = true)
    @NotNull(message = "商品單價(含稅)，不可為空")
    private Integer itemVatPrice;

    /**
     * 商品金額(未稅)
     */
    @Schema(description = "商品金額(未稅)")
    private Integer itemAmount;

    /**
     * 商品稅額
     */
    @Schema(description = "商品稅額")
    private Integer itemTax;

    /**
     * 商品金額(含稅)
     */
    @Schema(description = "商品金額(含稅)", required = true)
    @NotNull(message = "商品單價(含稅)，不可為空")
    private Integer itemVatAmount;

    /**
     * 明細備註
     */
    @Schema(description = "明細備註")
    private String memo;

    /**
     * 開立發票憑證類型 serviceCode=invoice_detail-depencedoctype
     */
    @Schema(description = "開立發票憑證類型 serviceCode = invoice_detail-depencedoctype")
    private InvoiceDataEnum.DepenceDocType depenceDocType;

    /**
     * 開立發票憑證單號
     */
    @Schema(description = "開立發票憑證單號")
    private String depenceDocNo;

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 目前牌照號碼
     */
    @Schema(description = "目前牌照號碼")
    private String plateNo;

}
