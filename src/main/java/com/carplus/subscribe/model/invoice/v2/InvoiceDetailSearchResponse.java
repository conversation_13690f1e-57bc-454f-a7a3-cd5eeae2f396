package com.carplus.subscribe.model.invoice.v2;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@FieldNameConstants
@Schema(description = "查詢發票明細檔結果")
public class InvoiceDetailSearchResponse extends AllowanceMasterSearchResponse {
    /**
     * 發票號碼(10碼)
     */
    @Schema(description = "發票號碼(10碼)")
    private String invoiceNo;

    /**
     * 字軌年度(西元yyyy)
     */
    @Schema(description = "字軌年度(西元yyyy)")
    private String periodYear;

    /**
     * 項次
     */
    @Schema(description = "項次")
    private Integer itemNo;

    /**
     * 商品代碼
     */
    @Schema(description = "商品代碼")
    private String itemCode;

    /**
     * 商品名稱
     */
    @Schema(description = "商品名稱")
    private String itemName;

    /**
     * 商品數量
     */
    @Schema(description = "商品數量")
    private Integer qty;

    /**
     * 稅別 serviceCode=invoice_master-taxtype
     */
    @Schema(description = "稅別 serviceCode=invoice_master-taxtype")
    private String taxType;

    /**
     * 稅別名稱
     */
    @Schema(description = "稅別名稱")
    private String taxTypeName;

    /**
     * 商品單價(含稅)
     */
    @Schema(description = "商品單價(含稅)")
    private Integer itemVatPrice;

    /**
     * 商品金額(未稅)
     */
    @Schema(description = "商品金額(未稅)")
    private Integer itemAmount;

    /**
     * 商品稅額
     */
    @Schema(description = "商品稅額")
    private Integer itemTax;

    /**
     * 商品金額(含稅)
     */
    @Schema(description = "商品金額(含稅)")
    private Integer itemVatAmount;

    /**
     * 發票明細備註
     */
    @Schema(description = "發票明細備註")
    private String itemMemo;

    /**
     * 開立發票憑證類型 serviceCode=invoice_detail-depencedoctype
     */
    @Schema(description = "開立發票憑證類型 serviceCode=invoice_detail-depencedoctype")
    private String depenceDocType;

    /**
     * 開立發票憑證類型名稱
     */
    @Schema(description = "開立發票憑證類型名稱")
    private String depenceDocTypeName;

    /**
     * 開立發票憑證單號
     */
    @Schema(description = "開立發票憑證單號")
    private String depenceDocNo;

    /**
     * 車輛編號
     */
    @Schema(description = "車輛編號")
    private Integer carNo;

    /**
     * 目前牌照號碼
     */
    @Schema(description = "目前牌照號碼")
    private String plateNo;

    /**
     * 折讓單號
     */
    @Schema(description = "折讓單號")
    private String allowanceNo;

    /**
     * 明細折讓狀態，serviceCode=allowance_master-allowancestatus
     * 此參數棄用，改用 AllowanceMasterSearchResponse.allowanceStatus
     */
    @Deprecated
    @Schema(description = "明細折讓狀態，serviceCode=allowance_master-allowancestatus")
    private String itemAllowanceStatus;

    /**
     * 明細折讓狀態
     */
    @Schema(description = "明細折讓狀態")
    private String itemAllowanceStatusName;

    /**
     * 建檔人員
     */
    @Schema(description = "建檔人員")
    private String createUserId;

    /**
     * 建檔日期
     */
    @Schema(description = "建檔日期")
    private Instant createDate;

    /**
     * 異動人員
     */
    @Schema(description = "異動人員")
    private String updateUserId;

    /**
     * 異動日期
     */
    @Schema(description = "異動日期")
    private Instant updateDate;
}
