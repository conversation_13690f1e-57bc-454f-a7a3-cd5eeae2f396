package com.carplus.subscribe.model.invoice.v2;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.time.Instant;


/**
 * 發票資訊 queue
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class InvoiceInfoQueue {

    /**
     * 發票號碼
     */
    @JsonProperty("INVOICE_NO")
    @Schema(description = "發票號碼")
    private String invoiceNo;

    /**
     * 發票開立日期
     */
    @JsonProperty("INVOICE_DATE")
    @Schema(description = "發票開立日期")
    private Instant invoiceDate;

}
