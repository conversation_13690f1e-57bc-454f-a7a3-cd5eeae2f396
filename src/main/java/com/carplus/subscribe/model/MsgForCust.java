package com.carplus.subscribe.model;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor
@Schema(description = "對客戶留言")
public class MsgForCust extends GeneralEntity {

    @Schema(description = "留言內容")
    private String content;

    @Schema(description = "留言者工號")
    private String commenterId;

    @Schema(description = "留言者姓名")
    private String commenterName;
}
