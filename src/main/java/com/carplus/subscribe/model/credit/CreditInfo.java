package com.carplus.subscribe.model.credit;

import com.carplus.subscribe.enums.CreditMechanismType;
import com.carplus.subscribe.enums.CreditRemarkType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "授信審核資訊")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreditInfo {

    @Schema(description = "自動授信資訊")
    private List<AutoCreditInfo> autoCreditInfo;

    @Schema(description = "人工授信資訊")
    private ManualCreditInfo manualCreditInfo;

    public CreditInfo update(Auditor auditor, String manualCreditRemark, CreditRemarkType type) {
        this.manualCreditInfo = new ManualCreditInfo(auditor, manualCreditRemark, type);
        return this;
    }

    public CreditInfo updateAutoCredit(@NonNull CreditRemarkType type, @NonNull CreditMechanismType creditMechanismType, List<String> autoCreditFailMessages) {
        if (autoCreditInfo == null) {
            autoCreditInfo = new ArrayList<>();
        }
        autoCreditInfo.add(new AutoCreditInfo(creditMechanismType, type, autoCreditFailMessages));
        return this;
    }
}
