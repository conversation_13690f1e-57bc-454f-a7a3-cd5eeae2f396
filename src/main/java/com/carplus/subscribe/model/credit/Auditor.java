package com.carplus.subscribe.model.credit;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Schema(description = "授信審核人資訊")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Auditor {
    @Schema(description = "名稱")
    private String name;
    @Schema(description = "員工編號")
    private String memberId;
    @Schema(description = "稽核日期")
    private Date auditDate;
    @Schema(description = "信箱")
    private String email;

    public Auditor(String name, String memberId, String email) {
        this.name = name;
        this.memberId = memberId;
        this.email = email;
        this.auditDate = new Date();
    }
}
