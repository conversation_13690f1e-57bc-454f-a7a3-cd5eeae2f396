package com.carplus.subscribe.model.credit.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CreditCheckRes {

    @Schema(description = "傳送成功回傳1000，其它則為異常")
    private String ResultCode;
    @Schema(description = "執行結果說明")
    private String ResultMessage;
    @Schema(description = "授信結果")
    private CreditCheckResult Result;

    @Data
    public static class CreditCheckResult {

        @Schema(description = "異常代碼")
        private String RtnCode;
        @Schema(description = "異常說明")
        private String RtnMessage;
    }
}
