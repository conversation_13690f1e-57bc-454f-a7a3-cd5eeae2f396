package com.carplus.subscribe.model.credit.req;

import carplus.common.enums.HeaderDefine;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.model.auth.AuthUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.NonNull;

import java.text.ParseException;
import java.text.SimpleDateFormat;


@Setter
@NoArgsConstructor
@ToString
@SuppressWarnings("unused")
public class CreditCheckRequest {

    @Schema(description = "被查詢角色。自然人=1；法人=2")
    private final String Role = "1";
    @Schema(description = "查詢系統")
    private final String System = HeaderDefine.SystemKind.SUB;
    @Schema(description = "會員編號")
    private int AcctId;
    @Schema(description = "條件")
    private Condition Condition;
    @Schema(description = "個人資訊")
    private Personal Personal;

    public CreditCheckRequest(String orderNo, AuthUser user) {
        this.AcctId = user.getAcctId().intValue();
        this.Personal = new Personal(user);
        this.Condition = new Condition(orderNo, user);
    }

    @Setter
    @NoArgsConstructor
    @ToString
    public static class Personal {

        @Schema(description = "發證日期(日,1~31)")
        private String ApplyDD;
        @Schema(description = "發證日期(月,1~12)")
        private String ApplyMM;
        @Schema(description = "領補換類別(1:初發、2:補發、3:換發)")
        private String ApplyReason;
        @Schema(description = "發證日期(年,94~103)")
        private String ApplyTWY;
        @Schema(description = "生日(民國+月+日 ex:0490504)")
        private String Birthday;
        @Schema(description = "姓名")
        private String Name;
        @Schema(description = "發證地點(北縣:10001…)")
        private String SiteId;

        public Personal(@NonNull AuthUser user) {
            if (user.getBirthday() != null) {
                try {
                    this.Birthday = DateUtils.toROCDateString(new SimpleDateFormat("yyyy-MM-dd").parse(user.getBirthday()), "yyyMMdd");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            this.Name = StringUtils.trim(user.getAcctName());
        }
    }

    @Setter
    @NoArgsConstructor
    @ToString
    public static class Condition {

        @Schema(description = "聯絡單號")
        private String QueryNumber;
        @Schema(description = "統編/身分證")
        private String SN;

        public Condition(@NonNull String orderNo, AuthUser user) {
            this.QueryNumber = orderNo;
            this.SN = user.getLoginId();
        }
    }
}
