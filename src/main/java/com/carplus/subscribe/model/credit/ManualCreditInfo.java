package com.carplus.subscribe.model.credit;

import com.carplus.subscribe.enums.CreditRemarkType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

@Data
@Schema(description = "人工授信審核資訊")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManualCreditInfo {
    @Schema(description = "人工授信備註")
    private String manualCreditRemark;

    @Schema(description = "自動授信時間")
    private Date creditDate;

    @Schema(description = "授信審核人員資訊")
    private Auditor auditor;

    @Schema(description = "授信類別")
    private CreditRemarkType creditRemarkType;

    public ManualCreditInfo(Auditor auditor, String manualCreditFailMessages, CreditRemarkType creditRemarkType) {
        this(auditor, manualCreditFailMessages, creditRemarkType, null);
    }

    public ManualCreditInfo(Auditor auditor, String manualCreditRemark, CreditRemarkType creditRemarkType, Date autoCreditDate) {
        Optional.ofNullable(auditor).ifPresent(this::setAuditor);
        Optional.ofNullable(manualCreditRemark).ifPresent(this::setManualCreditRemark);
        Optional.ofNullable(creditRemarkType).ifPresent(this::setCreditRemarkType);
        this.creditDate = Optional.ofNullable(autoCreditDate).orElseGet(Date::new);
    }

    public String getCreditRemark() {
        return Optional.ofNullable(creditRemarkType).map(CreditRemarkType::getDescription).orElse(null);
    }

}