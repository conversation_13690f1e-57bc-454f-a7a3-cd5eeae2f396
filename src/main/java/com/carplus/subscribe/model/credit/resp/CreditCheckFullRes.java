package com.carplus.subscribe.model.credit.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class CreditCheckFullRes {
    /**
     * 整體是否通過
     */
    @Schema(description = "整體是否通過")
    private Pass pass = Pass.FAIL;
    /**
     * 是否透過歷史資料判定
     */
    @Schema(description = "是否透過歷史資料判定")
    private Boolean isInHistroy = false;
    /**
     * 所有授信檢查結果
     */
    @Schema(description = "所有授信檢查結果")
    private List<DetailRes> details = new ArrayList<>();

    @Getter
    @AllArgsConstructor
    public enum Pass {
        /**
         * 通過
         **/
        @Schema(description = "通過")
        PASS,
        /**
         * 人工授信
         **/
        @Schema(description = "人工授信")
        MANUALLY,
        /**
         * 不通過
         **/
        @Schema(description = "不通過")
        FAIL,
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailRes {
        @Schema(description = "是否通過檢核")
        private Boolean isValided;

        @Schema(description = "檢核項目代碼")
        private String code;

        @Schema(description = "檢核項目說明")
        private String message;
    }
}
