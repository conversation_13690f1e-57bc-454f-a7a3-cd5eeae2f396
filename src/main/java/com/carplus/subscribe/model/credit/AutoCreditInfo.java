package com.carplus.subscribe.model.credit;

import com.carplus.subscribe.enums.CreditMechanismType;
import com.carplus.subscribe.enums.CreditRemarkType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
@Schema(description = "自動授信審核資訊")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AutoCreditInfo {
    @Schema(description = "自動授信失敗訊息清單")
    private List<String> autoCreditFailMessages;

    @Schema(description = "自動授信時間")
    private Date creditDate;

    @Schema(description = "授信機構")
    private CreditMechanismType creditMechanismType;

    @Schema(description = "授信類別")
    private CreditRemarkType creditRemarkType;

    public AutoCreditInfo(CreditMechanismType creditMechanismType, CreditRemarkType creditRemarkType, List<String> autoCreditFailMessages) {
        this(creditMechanismType, creditRemarkType, autoCreditFailMessages, null);
    }

    public AutoCreditInfo(CreditMechanismType creditMechanismType, CreditRemarkType creditRemarkType, List<String> autoCreditFailMessages, Date creditDate) {
        Optional.ofNullable(creditMechanismType).ifPresent(this::setCreditMechanismType);
        Optional.ofNullable(creditRemarkType).ifPresent(this::setCreditRemarkType);
        Optional.ofNullable(autoCreditFailMessages).ifPresent(this::setAutoCreditFailMessages);
        this.creditDate = Optional.ofNullable(creditDate).orElseGet(Date::new);
    }

    public String getCreditRemark() {
        return Optional.ofNullable(creditRemarkType).map(CreditRemarkType::getDescription).orElse(null);
    }
}