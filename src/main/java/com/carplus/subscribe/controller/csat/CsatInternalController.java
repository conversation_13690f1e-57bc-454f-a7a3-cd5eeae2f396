package com.carplus.subscribe.controller.csat;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.csat.*;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.service.CsatService;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 電訪API")
public class CsatInternalController {

    @Autowired
    private CsatService csatService;

    @Operation(summary = "電訪單查詢")
    @GetMapping(value = "subscribe/csat/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<CsatResponse> search(@Validated CsatCriteria criteria,
                                             @RequestParam(defaultValue = "0") Integer skip,
                                             @RequestParam(defaultValue = "10") Integer limit) {

        return PageResponse.of(csatService.searchPage(new PageRequest(limit, skip), criteria));
    }

    @Operation(summary = "電訪單查詢匯出")
    @GetMapping(value = "subscribe/csat/csv/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public byte[] csvExport(HttpServletResponse res, @Validated CsatCriteria criteria) {

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = csatService.csatExport(criteria);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=SUB_CSAT_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }


    @Operation(summary = "查詢電訪單人員")
    @GetMapping(value = "subscribe/csat/members", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> getCsatTaskAllMembers() {
        return csatService.getCsatTaskAllMembers();
    }


    @Operation(summary = "查詢指定電訪單問券")
    @GetMapping(value = "subscribe/csat/{csatId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public GenerateCsatQuestResponse getCsatQuest(@PathVariable Integer csatId) {
        return csatService.getCsatQuest(csatId);
    }

    @Operation(summary = "異動電訪單問券")
    @PutMapping(value = "subscribe/csat/upsert", produces = MediaType.APPLICATION_JSON_VALUE)
    public void upsetCsatQuest(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
        @RequestBody CsatQuestGenerateRequest request) {
        csatService.upsetCsatQuest(memberId, request);
    }

    @Operation(summary = "查詢拒訪資訊")
    @GetMapping(value = "subscribe/csat/refused/{loginId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CsatRefusedResponse getRefusedCsat(@PathVariable String loginId) {
        return csatService.getCsatRefused(loginId);
    }

    @Operation(summary = "異動拒訪資訊")
    @PutMapping(value = "subscribe/csat/refused/upsert", produces = MediaType.APPLICATION_JSON_VALUE)
    public void upsetRefused(@RequestBody CsatRefusedRequest request) {
        csatService.upsetRefused(request);
    }


    @Operation(summary = "查詢電訪訂單資訊 by 身分證")
    @GetMapping(value = "subscribe/csat/orders/{loginId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CsatOrderResponse> getCsatOrderByLoginId(@PathVariable String loginId) {
        return csatService.getCsatOrderByLiginId(loginId);
    }

    @Operation(summary = "建立電訪任務")
    @PostMapping(value = "subscribe/csat", produces = MediaType.APPLICATION_JSON_VALUE)
    public void createCsat(@RequestBody CsatCreateRequest request) {
        csatService.createCsatByOrders(request.getOrderNos());
    }

    @Operation(summary = "將已完成電訪的電訪單狀態重新設置為電訪中")
    @PatchMapping(value = "subscribe/csat/{csatId}/reopen", produces = MediaType.APPLICATION_JSON_VALUE)
    public CsatResponse reopenCompletedCsat(@PathVariable Integer csatId) {
        return csatService.reopenCompletedCsat(csatId);
    }

    @Operation(summary = "批次更新電訪單指派年月")
    @PatchMapping(value = "subscribe/csat/assignYearMonth", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateCsatAssignYearMonth(@Valid @RequestBody CsatAssignYearMonthUpdateRequest request) {
        csatService.updateCsatAssignYearMonth(request);
    }
}
