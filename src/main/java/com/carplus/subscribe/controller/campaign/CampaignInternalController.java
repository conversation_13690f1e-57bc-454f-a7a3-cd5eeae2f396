package com.carplus.subscribe.controller.campaign;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.model.request.campaign.CampaignCriteria;
import com.carplus.subscribe.model.request.campaign.CampaignRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.campaign.CampaignCommonBaseResponse;
import com.carplus.subscribe.model.response.campaign.CampaignInternalDetailResponse;
import com.carplus.subscribe.service.CampaignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 活動資料 接口")
public class CampaignInternalController {

    @Autowired
    private CampaignService campaignService;

    @Operation(summary = "新增活動")
    @PostMapping(value = "subscribe/campaign", produces = MediaType.APPLICATION_JSON_VALUE)
    public Campaign createCampaign(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                   @Validated @RequestBody CampaignRequest request) {
        return campaignService.create(request, memberId);
    }

    @Operation(summary = "取得活動清單")
    @GetMapping(value = "subscribe/campaigns", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CampaignCommonBaseResponse> getCampaigns(CampaignCriteria criteria) {
        return PageResponse.of(campaignService.searchByPage(new PageRequest(criteria.getLimit(), criteria.getSkip()), criteria));
    }

    @Operation(summary = "取得活動明細")
    @GetMapping(value = "subscribe/campaign/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CampaignInternalDetailResponse getCampaign(@PathVariable Integer id) {
        return campaignService.get(id, true);
    }

    @Operation(summary = "編輯活動")
    @PatchMapping(value = "subscribe/campaign/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CampaignInternalDetailResponse updateCampaign(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                         @PathVariable Integer id, @Validated @RequestBody CampaignRequest request) {
        return campaignService.update(id, request, memberId);
    }

    @Operation(summary = "刪除活動")
    @DeleteMapping(value = "subscribe/campaign/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteCampaign(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @PathVariable Integer id) {
        campaignService.delete(id, memberId);
    }
}