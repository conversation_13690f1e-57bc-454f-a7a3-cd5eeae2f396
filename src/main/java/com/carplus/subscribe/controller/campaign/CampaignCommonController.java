package com.carplus.subscribe.controller.campaign;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.campaign.CommonCampaignCriteria;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.campaign.CampaignCommonBaseResponse;
import com.carplus.subscribe.service.CampaignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 活動資料")
public class CampaignCommonController {

    @Autowired
    private CampaignService campaignService;

    @Operation(summary = "取得活動清單")
    @GetMapping(value = "subscribe/campaigns", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CampaignCommonBaseResponse> getCampaigns(CommonCampaignCriteria criteria) {
        return PageResponse.of(campaignService.searchByPage(new PageRequest(criteria.getLimit(), criteria.getSkip()), criteria));
    }

    @Operation(summary = "取得活動明細")
    @GetMapping(value = "subscribe/campaign/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CampaignCommonBaseResponse getCampaign(@PathVariable Integer id) {
        return campaignService.get(id, false);
    }
}
