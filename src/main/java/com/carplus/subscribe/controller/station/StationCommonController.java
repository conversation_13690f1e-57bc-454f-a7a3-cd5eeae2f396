package com.carplus.subscribe.controller.station;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.station.StationResponse;
import com.carplus.subscribe.service.StationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 站點資訊")
public class StationCommonController {

    @Autowired
    private StationService stationService;

    @Operation(summary = "拿取所有訂閱站點")
    @GetMapping(value = "subscribe/stations", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<StationResponse> getStations() {
        return stationService.getAllSubscribeAvailableStations();
    }


}