package com.carplus.subscribe.controller.station;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.aspects.LogEntity;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.request.station.StationQueryRequest;
import com.carplus.subscribe.model.request.station.StationUpdateRequest;
import com.carplus.subscribe.model.station.*;
import com.carplus.subscribe.service.StationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 站點資訊")
public class StationInternalController {

    @Autowired
    private StationService stationService;

    @Operation(summary = "收銀台站所查詢")
    @GetMapping(value = "subscribe/stations/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<StationResponse> getStations(@RequestHeader(required = false, name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                             @Parameter(hidden = true) @Nullable AdminUser adminUser,
                                             StationQueryRequest request) {
        return stationService.getAllSubscribeAvailableStations(adminUser, request);
    }

    @Operation(summary = "拿取所有訂閱可視站點")
    @GetMapping(value = "subscribe/stations", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<StationResponse> getAllAvailableStations(@RequestHeader(required = false, name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                         @Parameter(hidden = true) @Nullable AdminUser adminUser) {
        return stationService.getAllSubscribeAvailableStations(adminUser);
    }

    @Operation(summary = "站所明細")
    @GetMapping(value = "subscribe/stations/{stationCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public StationResponseWithChangeLogs findByStationCode(@RequestHeader(required = false, name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                           @Parameter(hidden = true) @Nullable AdminUser adminUser,
                                                           @PathVariable("stationCode") String stationCode) {
        return stationService.findByStationCode(adminUser, stationCode);
    }

    @LogEntity(mainEntity = Stations.class)
    @Operation(summary = "修改站點資訊")
    @PatchMapping(value = "subscribe/stations", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateStation(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                              @RequestBody @Validated StationUpdateRequest request) {

        stationService.updateStation(request, memberId);
    }

    @Operation(summary = "取得區域 & 站點")
    @GetMapping(value = "subscribe/car/level", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarAreaInfoListResponse getLocationInfosForCrs() {
        List<CarAreaInfo> carAreaInfoList = stationService.getCarAreaInfosForCrs();
        return new CarAreaInfoListResponse(carAreaInfoList);
    }

    @Operation(description = "取得各站點車輛數")
    @GetMapping(value = "subscribe/car/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarAreaInfoListWithCount getCarCountForCrs() {

        return stationService.getCarCountForCrs();
    }
}