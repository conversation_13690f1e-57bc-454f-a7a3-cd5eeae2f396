package com.carplus.subscribe.controller.station;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.station.SealandStationResponse;
import com.carplus.subscribe.service.StationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Partner 站點資訊")
public class StationPartnerController {

    @Autowired
    private StationService stationService;

    @Operation(summary = "拿取所有訂閱可視站點")
    @GetMapping(value = "subscribe/partner/stations", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SealandStationResponse> getAllAvailableStations() {
        return stationService.getSealandStations();
    }
}
