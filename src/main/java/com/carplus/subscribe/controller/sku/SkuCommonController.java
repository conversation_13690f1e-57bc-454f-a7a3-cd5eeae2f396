package com.carplus.subscribe.controller.sku;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.sku.SkuCriteria;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.sku.SkuCommonResponse;
import com.carplus.subscribe.service.SkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 汽車用品API")
public class SkuCommonController {

    @Autowired
    private SkuService skuService;

    @Operation(summary = "取得汽車用品分頁列表")
    @GetMapping(value = "/subscribe/sku", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<SkuCommonResponse> getSkus(@Validated SkuCriteria criteria) {
        criteria.setIsOfficial(true);
        return PageResponse.of(skuService.searchByPage(new PageRequest(criteria.getLimit(), criteria.getSkip()), criteria, SkuCommonResponse::new));
    }
}
