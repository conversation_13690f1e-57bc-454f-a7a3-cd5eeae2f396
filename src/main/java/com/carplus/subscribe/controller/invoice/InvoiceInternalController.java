package com.carplus.subscribe.controller.invoice;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.model.invoice.InvoiceAcctIdQueryRequest;
import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoicePriceInfoUpdateRequest;
import com.carplus.subscribe.model.invoice.InvoiceUpdateRequest;
import com.carplus.subscribe.model.request.invoice.InvoiceCriteriaRequest;
import com.carplus.subscribe.model.response.invoice.InvoicesWithAcctId;
import com.carplus.subscribe.service.InvoiceServiceV2;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 發票API")
public class InvoiceInternalController {

    @Autowired
    private InvoiceServiceV2 invoiceServiceV2;

    @Operation(summary = "發票清單")
    @GetMapping("subscribe/v1/invoice/{orderNo}")
    public List<Invoices> getInvoice(@PathVariable("orderNo") String orderNo) {
        return invoiceServiceV2.getInvoice(orderNo);
    }

    @Operation(summary = "查詢某 N 個 acctId 最新一筆發票開立方式")
    @PostMapping("subscribe/v1/invoice/latest")
    public List<InvoicesWithAcctId> getLatestInvoiceByAcctIds(@RequestBody @Validated InvoiceAcctIdQueryRequest invoiceAcctIdQueryRequest) {
        return invoiceServiceV2.getLatestInvoiceByAcctIds(invoiceAcctIdQueryRequest.getAcctIds());
    }

    @Operation(summary = "開立發票")
    @PostMapping("subscribe/v1/invoice/{orderNo}")
    public List<Invoices> createInvoice(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @PathVariable("orderNo") String orderNo,
                                        @RequestBody @Validated InvoiceNewRequest invoiceRequest) {
        return invoiceServiceV2.createInvoice(orderNo, invoiceRequest.getPayFor(), invoiceRequest.getInvoices(), memberId);
    }

    @Operation(summary = "發票作廢、折讓")
    @DeleteMapping(value = "subscribe/v1/invoice/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Invoices> updateInvoice(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                        @PathVariable("orderNo") String orderNo, @RequestBody @Validated InvoiceUpdateRequest invoiceRequest) {
        return invoiceServiceV2.updateInvoice(orderNo, invoiceRequest.getInvoiceNo(), invoiceRequest.getReason(), memberId);
    }

    @Operation(summary = "設定發票與費應明細對應")
    @PatchMapping("subscribe/v1/invoice/{orderNo}")
    public void updateInvoiceRefPriceInfoIds(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @PathVariable("orderNo") String orderNo,
                                             @RequestBody @Validated List<InvoicePriceInfoUpdateRequest> invoiceRequest) {
        invoiceServiceV2.updateInvoiceRefPriceInfoIds(orderNo, invoiceRequest, memberId);
    }

    @Operation(summary = "發票明細 csv")
    @GetMapping(value = "subscribe/v1/invoice/csv/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] invoiceCsvExport(HttpServletResponse res, InvoiceCriteriaRequest request) {
        request.validateForWeb();
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = invoiceServiceV2.generateCsv(request);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=invoices_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "收入明細 csv")
    @GetMapping(value = "subscribe/v1/invoice/income/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] invoiceCsvExport(HttpServletResponse res, @RequestParam(required = false) Date date) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = invoiceServiceV2.incomeReport(date);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=income_" + sf.format(new Date()) + ".csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }



    @Operation(summary = "取得因有退款而須作廢/折讓發票")
    @GetMapping(value = "subscribe/v1/invoice/cancel/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Invoices> getNeedCancelInvoice(@PathVariable("orderNo") String orderNo) {
        return invoiceServiceV2.getNeedCancelInvoice(orderNo);
    }

    @Operation(summary = "使用者訂單款項資訊可開立發票清單")
    @GetMapping(value = "/subscribe/invoice/priceInfo/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> getCanCreateInvoicePriceInfo(
        @PathVariable("orderNo") String orderNo) {
        return invoiceServiceV2.getCanCreateInvoicePriceInfo(orderNo);
    }

}
