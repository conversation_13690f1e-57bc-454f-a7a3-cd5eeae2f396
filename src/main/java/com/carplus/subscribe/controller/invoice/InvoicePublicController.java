package com.carplus.subscribe.controller.invoice;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.service.InvoiceServiceV2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@CarPlusRestController
@Tag(name = "發票API")
public class InvoicePublicController {

    @Autowired
    private InvoiceServiceV2 invoiceService;

    @Operation(summary = "發票清單")
    @GetMapping("subscribe/v1/invoice/{orderNo}")
    public List<Invoices> getInvoice(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo) {
        return invoiceService.getInvoice(acctId, orderNo);
    }

}
