package com.carplus.subscribe.controller.carwishlist;

import carplus.common.response.CarPlusRestController;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistCriteria;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistReportRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponse;
import com.carplus.subscribe.service.CarWishlistService;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車輛收藏清單 接口")
@RequiredArgsConstructor
public class CarWishlistInternalController {

    private final CarWishlistService carWishlistService;

    @Operation(summary = "取得用戶車輛收藏清單")
    @GetMapping(value = "subscribe/carWishlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CarWishlistResponse> getWishlist(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                                   CarWishlistCriteria criteria) {
        criteria.setAcctId(acctId);
        return PageResponse.of(carWishlistService.searchByPage(criteria));
    }

    @Operation(summary = "匯出車輛收藏清單報表")
    @GetMapping(value = "subscribe/carWishlist/csv/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] downloadWishlistReport(HttpServletResponse response,
                                         @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                         @Validated CarWishlistReportRequest request) {
        
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = carWishlistService.generateWishlistReportCsv(request);
        
        // Set CSV file name with current date
        String fileName = "SUB_car_wishlist_report_" + DateUtils.toDateString(new Date(), "yyyyMMdd", DateUtils.ZONE_TPE) + ".csv";
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        
        return out.toByteBuffer().array();
    }
}
