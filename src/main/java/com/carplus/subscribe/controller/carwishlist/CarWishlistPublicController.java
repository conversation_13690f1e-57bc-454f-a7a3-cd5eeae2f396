package com.carplus.subscribe.controller.carwishlist;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.carwishlist.CarWishlistResponse;
import com.carplus.subscribe.service.CarWishlistService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


@CarPlusRestController
@Tag(name = "車輛收藏清單API")
@RequiredArgsConstructor
public class CarWishlistPublicController {

    private final CarWishlistService carWishlistService;

    @Operation(summary = "加入車輛到收藏清單")
    @PostMapping(value = "subscribe/carWishlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public void add(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                    @RequestBody CarWishlistRequest request) {
        carWishlistService.add(acctId, request.getPlateNo());
    }

    @Operation(summary = "從收藏清單移除車輛")
    @DeleteMapping(value = "subscribe/carWishlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public void remove(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                       @RequestBody CarWishlistRequest request) {
        carWishlistService.remove(acctId, request.getPlateNo());
    }

    @Operation(summary = "取得用戶車輛收藏清單")
    @GetMapping(value = "subscribe/carWishlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CarWishlistResponse> getWishlist(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                                   CommonCarWishlistCriteria criteria) {
        criteria.setAcctId(acctId);
        return PageResponse.of(carWishlistService.searchByPage(criteria));
    }
}
