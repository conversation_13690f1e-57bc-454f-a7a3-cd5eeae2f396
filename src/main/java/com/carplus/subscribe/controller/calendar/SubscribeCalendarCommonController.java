package com.carplus.subscribe.controller.calendar;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.service.SubscribeCalendarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 訂閱月曆 接口")
public class SubscribeCalendarCommonController {

    @Autowired
    private SubscribeCalendarService subscribeCalendarService;

    @Operation(summary = "取得不可使用訂閱日曆")
    @GetMapping(value = "subscribe/calendar", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SubscribeCalendarDto> getCalendar(@RequestParam(value = "date", required = false) Long date) {
        if (date == null) {
            date = System.currentTimeMillis();
        }
        return subscribeCalendarService.getUnavailableCalendarList(new Date(date));
    }
}
