package com.carplus.subscribe.controller.calendar;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.aspects.LogEntity;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.model.calendar.CalendarDeleteRequest;
import com.carplus.subscribe.model.calendar.CalendarUpdateRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.service.SubscribeCalendarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂閱月曆 接口")
public class SubscribeCalendarInternalController {

    @Autowired
    private SubscribeCalendarService subscribeCalendarService;

    @LogEntity(mainEntity = SubscribeCalendar.class)
    @Operation(summary = "新增不可使用時間")
    @PostMapping(value = "subscribe/calendar/unavailable", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SubscribeCalendar> unavailable(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                               @RequestBody @Validated CalendarUpdateRequest request) {
        return subscribeCalendarService.setUnavailableSubscribeCalendar(request);
    }

    @LogEntity(mainEntity = SubscribeCalendar.class)
    @Operation(summary = "編輯不可使用時間")
    @PatchMapping(value = "subscribe/calendar/unavailable", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SubscribeCalendar> updateUnavailable(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                                     @RequestBody @Validated CalendarUpdateRequest request) {
        return subscribeCalendarService.updateUnavailableSubscribeCalendar(request);
    }

    @LogEntity(mainEntity = SubscribeCalendar.class)
    @Operation(summary = "刪除不可使用時間")
    @DeleteMapping(value = "subscribe/calendar/unavailable", produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteUnavailable(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @RequestBody @Validated CalendarDeleteRequest request) {
        subscribeCalendarService.deleteUnavailableSubscribeCalendar(request);
    }

    @Operation(summary = "取得不可使用訂閱日曆")
    @GetMapping(value = "subscribe/calendar", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<SubscribeCalendarDto> getCalendar(@RequestParam(value = "date", required = false) Long date,
                                                          @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
                                                          @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit) {
        if (date == null) {
            date = System.currentTimeMillis();
        }
        return PageResponse.of(subscribeCalendarService.getUnavailableCalendarList(new Date(date), new PageRequest(limit, skip)));
    }

    @Operation(summary = "取得行事曆異動歷程(分頁)")
    @GetMapping(value = "subscribe/calendar/changeLogs", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<EntityChangeLog> getCalendarChangeLogs(
        @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
        @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit
    ) {
        return PageResponse.of(subscribeCalendarService.getCalendarChangeLogs(new PageRequest(limit, skip)));
    }
}
