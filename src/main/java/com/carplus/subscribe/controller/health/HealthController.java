package com.carplus.subscribe.controller.health;

import carplus.common.response.CarPlusRestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;

@Tag(name = "Health API")
@CarPlusRestController
public class HealthController {

    @GetMapping(value = "/subscribe/v1/health", produces = MediaType.APPLICATION_JSON_VALUE)
    public void health() {
    }
}
