package com.carplus.subscribe.controller.cars;

import carplus.common.model.Option;
import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.cars.CarEquip;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.model.cars.req.CommonCarCriteria;
import com.carplus.subscribe.model.cars.resp.CarCommonResponse;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.inventory.resp.InventoryResponse;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.service.CarEquipService;
import com.carplus.subscribe.service.CarTagService;
import com.carplus.subscribe.service.CarsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 車籍資料")
@Slf4j
public class CarsCommonController {

    @Autowired
    private CarsService carsService;

    @Autowired
    private CarTagService carTagService;

    @Autowired
    private CarEquipService carEquipService;

    @Operation(summary = "車籍明細 by carNo")
    @GetMapping(value = "subscribe/cars/carNo/{carNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarCommonResponse getCarInfoByCarNo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT, required = false) Integer acctId,
        @Schema(description = "車號", required = true) @PathVariable("carNo") String carNo) {

        return carsService.getCommonCarInfoByCarNo(carNo, acctId);
    }

    @Operation(summary = "車籍庫存")
    @GetMapping(value = "subscribe/cars/inventory", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<InventoryResponse> getCarsRegionInventory(CarsCondition carsCondition, Integer skip, Integer limit) {
        return carsService.getCarsRegionInventory(carsCondition, skip, limit);
    }

    @Operation(summary = "車籍查詢")
    @GetMapping(value = "subscribe/car", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CarResponse> car(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT, required = false) Integer acctId,
        @Validated CommonCarCriteria criteria) {

        return PageResponse.of(carsService.searchByPage(new PageRequest(Integer.MAX_VALUE, 0), criteria, acctId));
    }


    @Operation(summary = "拿取車輛標籤列表")
    @GetMapping(value = "subscribe/car/tags", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CarTag> carTags(@RequestParam(required = false) Boolean isShow, CarsCondition carsCondition) {
        return carTagService.findAllCarTags(isShow, carsCondition);
    }

    @Operation(summary = "拿取車輛配件列表")
    @GetMapping(value = "subscribe/car/equips", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CarEquip> carEquips(@RequestParam(required = false) Boolean isShow) {
        return carEquipService.findAllCarEquips(isShow);
    }

    @Operation(summary = "拿取車況列表")
    @GetMapping(value = "subscribe/car/carState", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Option<String>> carState() {
        return Arrays.stream(CarDefine.CarState.values()).map(carState -> new Option<>(carState.name(), carState.getName())).collect(Collectors.toList());
    }

    @Operation(summary = "拿取車輛上下架列表")
    @GetMapping(value = "subscribe/car/carLunched", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Option<String>> getCarLaunched() {
        return Arrays.stream(CarDefine.Launched.values()).map(launched -> new Option<>(launched.name(), launched.getName())).collect(Collectors.toList());
    }

    @Operation(summary = "拿取能源別列表")
    @GetMapping(value = "subscribe/car/energyType", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Option<String>> getEnergyType() {
        return Arrays.stream(CarDefine.EnergyType.values()).map(energyType -> new Option<>(energyType.name(), energyType.getName())).collect(Collectors.toList());
    }
}
