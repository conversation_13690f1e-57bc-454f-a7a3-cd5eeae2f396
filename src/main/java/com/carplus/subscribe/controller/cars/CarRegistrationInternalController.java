package com.carplus.subscribe.controller.cars;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.cars.CarRegistration;
import com.carplus.subscribe.model.request.car.CarRegistrationRequest;
import com.carplus.subscribe.service.CarRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車籍公司所屬 接口")
public class CarRegistrationInternalController {

    @Autowired
    private CarRegistrationService carRegistrationService;

    @Operation(summary = "新增車籍公司所屬")
    @PostMapping(value = "subscribe/carRegistration", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCarRegistration(@RequestBody CarRegistrationRequest request) {
        carRegistrationService.addCarRegistration(request.getVatNo(), request.getName(), request.getShortName());
    }

    @Operation(summary = "修改車籍公司所屬")
    @PatchMapping(value = "subscribe/carRegistration", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateCarRegistration(@RequestBody CarRegistrationRequest request) {
        carRegistrationService.updateCarRegistration(request.getVatNo(), request.getName(), request.getShortName());
    }

    @Operation(summary = "刪除車籍公司所屬")
    @DeleteMapping(value = "subscribe/carRegistration/{vatNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteCarRegistration(@PathVariable String vatNo) {
        carRegistrationService.deleteCarRegistration(vatNo);
    }

    @Operation(summary = "檢視車籍公司所屬")
    @GetMapping(value = "subscribe/carRegistration/{vatNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarRegistration getCarRegistration(@PathVariable String vatNo) {
        return carRegistrationService.getCarRegistration(vatNo);
    }

    @Operation(summary = "檢視所有車籍公司所屬")
    @GetMapping(value = "subscribe/carRegistration", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CarRegistration> getAllCarRegistration() {
        return carRegistrationService.getAllCarRegistrations();
    }
}
