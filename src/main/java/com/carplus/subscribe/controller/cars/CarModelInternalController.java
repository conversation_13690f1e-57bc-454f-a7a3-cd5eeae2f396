package com.carplus.subscribe.controller.cars;

import carplus.common.response.CarPlusRestController;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.excel.CarModelCSV;
import com.carplus.subscribe.model.request.CarModelAddRequest;
import com.carplus.subscribe.model.request.CarModelQueryRequest;
import com.carplus.subscribe.model.request.CarModelUpdateRequest;
import com.carplus.subscribe.model.response.CarModelResponse;
import com.carplus.subscribe.service.CarModelService;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.List;
import java.util.stream.Collectors;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車型資料 接口")
public class CarModelInternalController {

    @Autowired
    private CarModelService carModelService;

    @Operation(summary = "新增車型")
    @PostMapping(value = "subscribe/carModel", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarModelResponse addCarModel(@RequestBody @Validated CarModelAddRequest request) {
        return carModelService.addCardModel(request);
    }

    @Operation(summary = "修改車型")
    @PatchMapping(value = "subscribe/carModel/{carModelCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarModelResponse updateCarModel(@RequestBody @Validated CarModelUpdateRequest request) {
        return carModelService.updateCarModel(request);
    }

    @Operation(summary = "刪除車型")
    @DeleteMapping(value = "subscribe/carModel/{carModelCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void delete(@Schema(description = "車型代碼") @PathVariable("carModelCode") String carModelCode) {
        carModelService.updateIsDeleted(carModelCode, Integer.valueOf(1));
    }

    @Operation(summary = "檢視車型")
    @GetMapping(value = "subscribe/carModel/{carModelCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarModelResponse getInfo(@Schema(description = "車型代碼", required = true) @PathVariable("carModelCode") String carModelCode) {
        return carModelService.getInfo(carModelCode);
    }

    @Operation(summary = "搜尋車型")
    @GetMapping(value = "subscribe/carModel", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CarModelResponse> queryCarModel(@Validated CarModelQueryRequest request) {
        return carModelService.findBySearch(request);
    }

    @Operation(summary = "匯出車型")
    @GetMapping(value = "subscribe/carModel/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public @ResponseBody byte[] exportCarModel(
        @Schema(description = "廠牌代碼") @RequestParam(required = false) String brandCodes,
        @Schema(description = "車型代碼") @RequestParam(required = false) String carModelCodes,
        HttpServletResponse res) {

        String[] brandCodeArray = null;
        if (StringUtils.isNotEmpty(brandCodes)) {
            brandCodeArray = brandCodes.split(",");
        }
        String[] carModelCodeArray = null;
        if (StringUtils.isNotEmpty(carModelCodes)) {
            carModelCodeArray = carModelCodes.split(",");
        }

        CarModelQueryRequest queryRequest = new CarModelQueryRequest(brandCodeArray, carModelCodeArray);
        List<CarModelResponse> dataList = carModelService.findBySearch(queryRequest);
        List<CarModelCSV> rows = dataList.stream().map(data -> {
            CarModelCSV row = new CarModelCSV();
            BeanUtils.copyProperties(data, row);
            row.setIsDeleted(data.getIsDeleted() == 0 ? "否" : "是");
            return row;
        }).collect(Collectors.toList());

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            rows,
            new String[] {"廠牌代碼", "廠牌名稱", "廠牌英文名稱", "車型名稱", "車型代碼", "車型種類名稱", "已刪除"},
            true,
            ',',
            out,
            Charset.forName("big5"),
            CarModelCSV.class
        );

        res.setHeader("Content-Disposition", "attachment; filename=carModel_" + System.currentTimeMillis() + ".csv");

        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }
}
