package com.carplus.subscribe.controller.cars;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.cars.resp.CarBrandResponse;
import com.carplus.subscribe.model.request.CarBrandRequest;
import com.carplus.subscribe.model.request.CarBrandUpdateSeqRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.service.CarBrandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車廠資料 接口")
public class CarBrandInternalController {

    private final CarBrandService carBrandService;

    @Operation(summary = "新增車廠品牌")
    @PostMapping(value = "subscribe/carBrand/add", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarBrandResponse addCarBrand(@RequestBody @Validated CarBrandRequest request) {
        return carBrandService.add(request);
    }

    @Operation(summary = "更新車廠品牌")
    @PatchMapping(value = "subscribe/carBrand/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarBrandResponse updateCarBrand(@RequestBody @Validated CarBrandRequest request) {
        return carBrandService.update(request);
    }

    @Operation(summary = "車廠品牌分頁列表")
    @GetMapping(value = "/subscribe/carBrand/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<CarBrandResponse> listCarBrand(
        @RequestParam(value = "brands", required = false) List<String> brands,
        @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
        @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit
    ) {
        return PageResponse.of(carBrandService.searchByPage(new PageRequest(limit, skip), brands));
    }

    @Operation(summary = "批次更新車廠排序")
    @PatchMapping(value = "subscribe/carBrand/updateSeq", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateCarBrandSeq(@RequestBody @Validated CarBrandUpdateSeqRequest carBrandUpdateSeqRequest) {
        carBrandService.updateSeq(carBrandUpdateSeqRequest.getBrandCodes());
    }
}
