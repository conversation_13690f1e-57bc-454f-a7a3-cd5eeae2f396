package com.carplus.subscribe.controller.cars;


import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.car.CarSortUpdateRequest;
import com.carplus.subscribe.model.request.car.CarTagCreateRequest;
import com.carplus.subscribe.model.request.car.CarTagUpdateRequest;
import com.carplus.subscribe.service.CarTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車籍標籤資料 接口")
@Slf4j
public class CarTagInternalController {

    @Autowired
    private CarTagService carTagService;

    @Operation(summary = "新增車籍標籤")
    @PostMapping(value = "subscribe/cars/carTag", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCars(@RequestBody @Validated CarTagCreateRequest request,
                        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {

        carTagService.createCarTag(request, memberId);
    }


    @Operation(summary = "異動車籍標籤")
    @PatchMapping(value = "subscribe/cars/carTag", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCars(@RequestBody @Validated CarTagUpdateRequest request,
                        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        carTagService.updateCarTag(request, memberId);
    }


    @Operation(summary = "異動車籍標籤")
    @PatchMapping(value = "subscribe/cars/carTag/sort", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCars(@RequestBody @Validated CarSortUpdateRequest request,
                        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        carTagService.updateSort(request.getCarIds(), memberId);
    }

}
