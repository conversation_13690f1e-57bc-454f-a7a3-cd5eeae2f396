package com.carplus.subscribe.controller.cars;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.cars.resp.CarBrandResponse;
import com.carplus.subscribe.service.CarBrandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@RequiredArgsConstructor
@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 車廠資料")
public class CarBrandCommonController {

    private final CarBrandService carBrandService;

    @Operation(summary = "品牌專區")
    @GetMapping(value = "/subscribe/carBrand/describe", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<CarBrandResponse> listCarBrand() {
        return carBrandService.getDescribeCarBrand();
    }
}
