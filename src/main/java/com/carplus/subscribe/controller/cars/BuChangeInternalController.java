package com.carplus.subscribe.controller.cars;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import com.carplus.subscribe.service.BuChangeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 撥車查詢")
@Slf4j
public class BuChangeInternalController {

    @Autowired
    @Lazy
    private BuChangeService buChangeService;


    @Operation(summary = "車輛撥車紀錄")
    @GetMapping(value = "subscribe/v1/buChangelog/{plateNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BuChangeLog> carChange(@PathVariable("plateNo") String plateNo) {
        return buChangeService.getLastPlateNoLog(plateNo);
    }


    @Operation(summary = "撥回車")
    @PostMapping(value = "subscribe/v1/changeReturn/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void changeReturn(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @PathVariable("orderNo") String orderNo) {
        buChangeService.changeReturn(orderNo, memberId);
    }
}
