package com.carplus.subscribe.controller.cars;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.cars.resp.SeaLandCarResponse;
import com.carplus.subscribe.service.CarsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Partner 車籍資料 接口")
@Slf4j
public class CarsPartnerController {

    @Autowired
    private CarsService carsService;

    @Operation(summary = "SeaLand車籍資料")
    @GetMapping(value = "subscribe/partner/cars", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SeaLandCarResponse> getSeaLandCars() {
        return carsService.getSeaLandCars();
    }
}