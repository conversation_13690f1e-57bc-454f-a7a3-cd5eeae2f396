package com.carplus.subscribe.controller.cars;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.aspects.EntityLoggingContext;
import com.carplus.subscribe.aspects.LogEntity;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.model.cars.req.*;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.cars.resp.CarResponseWithChangeLogs;
import com.carplus.subscribe.model.cars.resp.PlateNoQueryResponse;
import com.carplus.subscribe.model.crs.CarBaseInfoToAddResponse;
import com.carplus.subscribe.model.request.CarsAddRequest;
import com.carplus.subscribe.model.request.CarsAddSingleRequest;
import com.carplus.subscribe.model.request.CarsCRSAddRequest;
import com.carplus.subscribe.model.request.CarsUpdateRequest;
import com.carplus.subscribe.model.request.carregistration.*;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.carregistration.CarRegistrationCSVResponse;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.CrsService;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.DownloadUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 車籍資料 接口")
@Slf4j
public class CarsInternalController {

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private EntityLoggingContext entityLoggingContext;

    @Value("${gcs.url}")
    private String gcsUrl;

    @Operation(summary = "新增車籍")
    @PostMapping(value = "subscribe/cars", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCars(@RequestBody @Validated CarsAddRequest request,
                        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {

        //TODO 檢查項目

        // 新增 cars 車籍
        carsService.add(request, memberId);
    }

    @LogEntity(mainEntity = Cars.class, memo = "採購", changedBy = "CRS")
    @Operation(summary = "新增車籍from CRS")
    @PostMapping(value = "subscribe/cars/crs", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addCars(@RequestBody @Validated CarsCRSAddRequest request) {

        // 記錄申請單單號和申請事由，在 EntityListener.onPostInsert 會判斷 request 來決定是否覆寫 memo
        entityLoggingContext.setCarsCRSAddRequest(request);

        // 新增 crs 車籍
        carsService.addCarFromCrsByPlateNo(request.getPlateNo());
    }

    @LogEntity(mainEntity = Cars.class)
    @Operation(summary = "修改車籍")
    @PatchMapping(value = "subscribe/cars/{plateNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateCars(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER, required = false) String memberId,
                           @RequestHeader(name = CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, required = false) String systemKind,
                           @PathVariable("plateNo") String plateNo, @RequestBody @Validated CarsUpdateRequest request) {

        // 修改 cars 車籍
        carsService.update(plateNo, request);
    }

    @Operation(summary = "刪除車籍")
    @DeleteMapping(value = "subscribe/cars/{plateNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void delete(@Schema(description = "車型代碼") @PathVariable("plateNo") String plateNo) {
        carsService.updateIsDeleted(plateNo, 1);
    }

    @Operation(summary = "查詢車牌號碼是否已存在並查詢 CRS 車輛資訊")
    @GetMapping(value = "subscribe/cars/check/{plateNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarBaseInfoToAddResponse checkCar(@Schema(description = "車牌號碼", required = true) @PathVariable("plateNo") String plateNo) {

        // 檢查車牌號碼是否已存在
        carsService.checkCarExistence(plateNo);

        // 檢查車牌號碼是否已存在於 CRS 並返回車輛資訊
        CarBaseInfoToAddResponse carBaseInfoToAdd = crsService.getCarBaseInfoToAdd(plateNo);

        // 檢查 carBaseInfoToAdd crsCarNo 是否存在於車籍表，若存在則將車籍資料複製到 carBaseInfoToAdd
        carsService.populateCarBaseInfoWithOriginalCarIfExists(carBaseInfoToAdd);

        return carBaseInfoToAdd;
    }

    @LogEntity(mainEntity = Cars.class, memo = "人工單筆新增")
    @Operation(summary = "車籍管理：單台新增")
    @PostMapping(value = "subscribe/cars/single", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addSingleCars(@RequestBody @Validated CarsAddSingleRequest request,
                              @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {

        // 新增 cars 車籍
        request.validate();
        carsService.addSingle(request, memberId);
    }

    @Operation(summary = "車籍分頁列表")
    @GetMapping(value = "subscribe/cars", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<? extends CarResponse> searchByPage(
        @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
        @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit,
        @Validated CarCriteria criteria
    ) {
        return PageResponse.of(carsService.searchByPage(new PageRequest(limit, skip), criteria, null));
    }

    @Operation(summary = "取得車輛與站點資訊")
    @PostMapping(value = "subscribe/car", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<PlateNoQueryResponse> getCarAndLocation(@RequestBody @Validated PlateNoQueryRequest request) {
        return carsService.getCarAndLocation(request);
    }

    @Operation(summary = "車籍明細")
    @GetMapping(value = "subscribe/cars/{plateNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarResponseWithChangeLogs getCarInfo(@Schema(description = "車號", required = true) @PathVariable("plateNo") String plateNo
    ) {
        return carsService.getCarInfoWithChangeLogs(plateNo);
    }

    @Operation(summary = "車籍明細 by carNo")
    @GetMapping(value = "subscribe/cars/carNo/{carNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarResponseWithChangeLogs getCarInfoByCarNo(@Schema(description = "車號", required = true) @PathVariable("carNo") String carNo
    ) {
        return carsService.getCarInfoByCarNoWithChangeLogs(carNo);
    }

    @LogEntity(mainEntity = Cars.class)
    @Operation(summary = "修改舊短租及訂閱車籍所在站所")
    @PatchMapping(value = "subscribe/cars/{plateNo}/locationStationCode", produces = MediaType.APPLICATION_JSON_VALUE)
    public int updateLocationStation(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER, required = false) String memberId,
                                     @Schema(description = "車號", required = true) @PathVariable("plateNo") String plateNo,
                                     @RequestBody @Validated CarRegistrationUpdateNewStationCodeRequest modifyLocationStationCode) {
        return carsService.updateLocationStation(plateNo, modifyLocationStationCode.getNewStationCode());
    }

    @Operation(summary = "修改舊短租及訂閱車籍里程")
    @PatchMapping(value = "subscribe/cars/{plateNo}/currentMileage", produces = MediaType.APPLICATION_JSON_VALUE)
    public int updateCurrentMileage(@Schema(description = "車號", required = true) @PathVariable("plateNo") String plateNo,
                                    @RequestBody @Validated CarRegistrationUpdateCurrentMileageRequest updateCurrentMileageRequest) {
        return carsService.updateCurrentMileage(plateNo, updateCurrentMileageRequest.getCurrentMileage());
    }


    @Operation(summary = "查詢長租資料匯入舊短租車籍和訂閱車籍")
    @PostMapping(value = "subscribe/cars/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public void carImport(@RequestBody @Validated CarRegistrationImport carImport) {
        try {
            CarDefine.CarState.valueOf(carImport.getCarStat());
        } catch (IllegalArgumentException e) {
            throw new BadRequestException("車況資料錯誤!");
        }
        if (StringUtils.isBlank(carImport.getLocationStationCode())) {
            throw new BadRequestException("請輸入目前站點");
        }
        // 寫入 cars 車籍
        carsService.importCars(carImport);
    }

    @LogEntity(mainEntity = Cars.class, memo = "人工批量匯入")
    @Operation(summary = "批次查詢長租資料匯入舊短租車籍和訂閱車籍")
    @PostMapping(value = "subscribe/cars/importCSV", produces = MediaType.APPLICATION_JSON_VALUE)
    public CarRegistrationCSVResponse cars(
        @RequestBody @Validated CarRegistrationCSVImportUrl url,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {

        String uuid = UUID.randomUUID().toString();

        //將CSV從雲抓下來
        List<CarRegistrationCSV> list = DownloadUtils.getCSVListByURL(String.format("%s/%s", gcsUrl, url.getUrl()), CarRegistrationCSV.class, ",", Charset.forName("big5"));

        //驗證車籍資料正確性
        CarRegistrationValidate carValidate = carsService.validateCarsDataAndSaveCSVCarsAndCarRegistration(list, uuid, memberId);

        return CarRegistrationCSVResponse.builder()
            .rowsCount(carValidate.getRows().size() + carValidate.getErrorList().size())
            .uuid(uuid)
            .successCount(carValidate.getRows().size())
            .failCount(carValidate.getErrorList().size())
            .errMessageList(carValidate.getErrorList()).build();
    }

    @Operation(summary = "車籍CSV下載")
    @GetMapping(value = "subscribe/cars/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] csv(
        HttpServletResponse res,
        @Validated CarCriteria criteria
    ) {

        criteria.setIsDelete(false);
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = carsService.carsCsvGenerate(criteria);
        res.setHeader("Content-Disposition", "attachment; filename=cars_" + DateUtils.toDateString(new Date(), "yyyy-MM-dd", DateUtils.ZONE_TPE) + ".csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "下載錯誤車籍CSV")
    @GetMapping(value = "subscribe/cars/csv/error", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public @ResponseBody byte[] carCsv(@RequestParam String uuid, HttpServletResponse res,
                                       @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {

        CarRegistrationValidate carValidate = carsService.validateCarsDataAndSaveCSVCarsAndCarRegistration(null, uuid, memberId);
        if (carValidate == null) {
            throw new BadRequestException("請提供正確的UUID，或在時效內下載錯誤資訊");
        }
        List<CarRegistrationCSV> rows = carValidate.getErrorRows();
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = new CsvUtil.ByteArrayOutputStream2ByteBuffer();
        CsvUtil.createCsv(
            rows,
            CarRegistrationCSV.getCarCsvErrorOutputHead().toArray(new String[0]),
            true,
            ',',
            out,
            Charset.forName("big5"),
            CarRegistrationCSV.class
        );

        res.setHeader("Content-Disposition", "attachment; filename=ErrorCars_" + DateUtils.toDateString(new Date(), "yyyy-MM-dd", DateUtils.ZONE_TPE) + ".csv");

        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "車籍資料範例檔下載")
    @GetMapping(value = "subscribe/cars/csv/example", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public ResponseEntity exportExample(HttpServletResponse response) {
        try {
            byte[] arr = carsService.exportExampleZip();
            InputStreamResource streamResource = new InputStreamResource(new ByteArrayInputStream(arr));
            return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=example.zip")
                .contentType(MediaType.parseMediaType("application/zip"))
                .body(streamResource);
        } catch (Exception e) {
            log.error("匯出Zip範例失敗", e);
            throw new BadRequestException("匯出Zip範例失敗");
        }
    }

    @LogEntity(mainEntity = Cars.class)
    @Operation(summary = "修改車輛上下架")
    @PatchMapping(value = "subscribe/v1/car/{plateNo}/launched", produces = MediaType.APPLICATION_JSON_VALUE)
    public void carLaunched(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER, required = false) String memberId,
                            @Schema(description = "車號", required = true) @PathVariable("plateNo") String plateNo,
                            @RequestBody @Validated ModifyLaunchedRequest modifyLaunched) {
        switch (modifyLaunched.getLaunched()) {
            case open:
                carsService.launchOpen(plateNo);
                break;
            case close:
                carsService.launchClose(plateNo);
                break;
            case deprecate:
                carsService.launchDeprecate(plateNo);
                break;
            case accident:
                carsService.launchAccident(plateNo);
                break;


            default:
        }
    }

    @Operation(summary = "修改SL車輛上下架")
    @PatchMapping(value = "subscribe/v1/car/{plateNo}/SL/launched", produces = MediaType.APPLICATION_JSON_VALUE)
    public void carLaunched(@Schema(description = "車號", required = true) @PathVariable("plateNo") String plateNo,
                            @RequestBody @Validated ModifySLLaunch modifyLaunched) {
        carsService.updateSealandLaunch(plateNo, modifyLaunched.isLaunched());
    }


    @Operation(summary = "自動撥車申請")
    @PostMapping(value = "subscribe/v1/car/changeBu", produces = MediaType.APPLICATION_JSON_VALUE)

    public Integer autoChangeBu(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @RequestHeader(name = CarPlusConstant.HEADER_COMPANY_CODE) String companyId,
                                @RequestBody @Validated AutoChangeBuRequest autoChangeBuRequest) {
        return carsService.changeBu(autoChangeBuRequest, companyId, memberId);
    }

    @Deprecated
    @Operation(summary = "自動撥還車申請")
    @PostMapping(value = "subscribe/v1/car/changeBuReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    public Integer autoChangeReturnBu(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                      @RequestBody @Validated AutoChangeBuRequest autoChangeBuRequest) {
        return carsService.autoChangeReturnBu(autoChangeBuRequest.getOrderNo(), autoChangeBuRequest.getPlateNo(), memberId);
    }

    @Operation(summary = "車輛管制")
    @PostMapping(value = "subscribe/v1/car/carChangeLimit", produces = MediaType.APPLICATION_JSON_VALUE)
    public void carChange(@RequestBody @Valid CarChangeLimitRequest request) {
        carsService.launchChangeAndNotify(request.getCrsCarNo(), request.isLaunched(), request.getChangeType(), request.getChangeMemo());
    }

    @Operation(summary = "賣車申請")
    @PostMapping(value = "subscribe/v1/car/{plateNo}/sellToPreowned", produces = MediaType.APPLICATION_JSON_VALUE)
    public Integer sellToPreowned(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @PathVariable("plateNo") String plateNo,
                                  @RequestBody @Validated SellToPreownedRequest sellToPreownedRequest) {
        return carsService.sellToPreowned(plateNo, sellToPreownedRequest, memberId);
    }
}
