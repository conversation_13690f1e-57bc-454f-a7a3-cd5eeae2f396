package com.carplus.subscribe.controller.config;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.config.ConfigRequest;
import com.carplus.subscribe.model.config.ConfigResponse;
import com.carplus.subscribe.service.ConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 參數設定API")
public class ConfigController {

    @Autowired
    private ConfigService configService;

    @Operation(summary = "新增config")
    @RequestMapping(value = "subscribe/v1/config", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ConfigResponse addConfig(@RequestBody ConfigRequest request) {
        return configService.addConfig(request);
    }

    @Operation(summary = "修改config")
    @RequestMapping(value = "subscribe/v1/config", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public ConfigResponse updateConfig(@RequestBody ConfigRequest request) {
        return configService.updateConfig(request);
    }

    @Operation(summary = "取得全部config")
    @RequestMapping(value = "subscribe/v1/config", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ConfigResponse> getAllConfig() {
        return configService.getAllConfig();
    }

    @Operation(summary = "取得config")
    @RequestMapping(value = "subscribe/v1/config/{code}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ConfigResponse getConfigByCode(@PathVariable String code) {
        return configService.getConfig(code);
    }
}
