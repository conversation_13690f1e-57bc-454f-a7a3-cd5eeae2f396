package com.carplus.subscribe.controller.payment;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.service.PaymentServiceV2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@Slf4j
@Tag(name = "訂閱帳務收/支 API")
@CarPlusRestController
@Validated
public class PublicPaymentV1Controller {

    @Autowired
    private PaymentServiceV2 paymentService;

    @Operation(summary = "查詢訂單TradeId付款資訊")
    @GetMapping(value = "subscribe/v1/payment/{orderNo}/tradId/{tradeId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public PaymentInfo getUserOrderPayment(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo,
        @PathVariable("tradeId") String tradeId) {
        return paymentService.getUserOrderPayment(acctId, orderNo, tradeId);
    }
}
