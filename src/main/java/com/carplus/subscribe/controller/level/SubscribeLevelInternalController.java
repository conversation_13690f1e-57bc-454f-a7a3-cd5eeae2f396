package com.carplus.subscribe.controller.level;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelAddRequest;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelCriteria;
import com.carplus.subscribe.model.request.subscribelevel.SubscribeLevelUpdateRequest;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.subscribelevel.SubscribeLevelCommonResponse;
import com.carplus.subscribe.model.response.subscribelevel.SubscribeLevelInternalResponse;
import com.carplus.subscribe.service.SubscribeLevelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 訂閱方案 接口")
public class SubscribeLevelInternalController {

    @Autowired
    private SubscribeLevelService subscribeLevelService;

    @Operation(summary = "新增訂閱方案")
    @PostMapping(value = "subscribe/subscribeLevel", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addSubscribeLevel(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                  @RequestBody @Validated SubscribeLevelAddRequest request) {
        subscribeLevelService.add(request, memberId);
    }

    @Operation(summary = "修改訂閱方案")
    @PatchMapping(value = "subscribe/subscribeLevel", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateSubscribeLevel(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                     @RequestBody @Validated SubscribeLevelUpdateRequest request) {
        subscribeLevelService.update(request, memberId);
    }

    @Operation(summary = "刪除訂閱方案")
    @DeleteMapping(value = "subscribe/subscribeLevel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void delete(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                       @Schema(description = "訂閱方案編號") @PathVariable("id") Integer id) {
        subscribeLevelService.updateIsDeleted(id, memberId);
    }

    @Operation(summary = "檢視訂閱方案")
    @GetMapping(value = "subscribe/subscribeLevel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public SubscribeLevelCommonResponse getInfo(@Schema(description = "訂閱方案編號", required = true) @PathVariable("id") Integer id) {
        return subscribeLevelService.findById(id, true);
    }

    @Operation(summary = "訂閱方案分頁列表")
    @GetMapping(value = "subscribe/subscribeLevel", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<SubscribeLevelInternalResponse> listSubscribeLevel(@Validated SubscribeLevelCriteria criteria) {
        return PageResponse.of(subscribeLevelService.searchByPage(criteria));
    }
}
