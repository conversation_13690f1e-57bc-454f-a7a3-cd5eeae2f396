package com.carplus.subscribe.controller.level;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.subscribelevel.SubscribeLevelCommonResponse;
import com.carplus.subscribe.model.subscribelevel.LevelPriceRange;
import com.carplus.subscribe.service.SubscribeLevelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 訂閱方案")
public class SubscribeLevelCommonController {

    @Autowired
    private SubscribeLevelService subscribeLevelService;

    @Operation(summary = "檢視訂閱方案")
    @GetMapping(value = "subscribe/subscribeLevel/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public SubscribeLevelCommonResponse getInfo(@Schema(description = "訂閱方案編號", required = true) @PathVariable("id") Integer id) {
        return subscribeLevelService.findById(id, false);
    }

    @Operation(summary = "訂閱方案分頁列表")
    @GetMapping(value = "subscribe/subscribeLevel", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<SubscribeLevelCommonResponse> listCarBrand(
        @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
        @RequestParam(value = "limit", defaultValue = "100", required = false) Integer limit
    ) {
        return PageResponse.of(subscribeLevelService.searchByPage(new PageRequest(limit, skip)));
    }

    @Operation(summary = "檢視訂閱方案最高／低金額")
    @GetMapping(value = "subscribe/subscribeLevel/priceRange", produces = MediaType.APPLICATION_JSON_VALUE)
    public LevelPriceRange getPriceRange() {
        return subscribeLevelService.getPriceRange();
    }
}
