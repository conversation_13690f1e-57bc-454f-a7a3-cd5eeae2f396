package com.carplus.subscribe.controller.dealer;

import carplus.common.enums.etag.ETagPayFlow;
import carplus.common.model.PageRequest;
import carplus.common.redis.cache.Lock;
import carplus.common.response.CarPlusRestController;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.etag.ETagInfoRequest;
import com.carplus.subscribe.model.etag.EtagCloseRequest;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.order.DealerOrderPriceInfoRequest;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.request.dealer.*;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcelResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.model.response.dealer.DealerOrderResponse;
import com.carplus.subscribe.service.DealerOrderPriceInfoService;
import com.carplus.subscribe.service.DealerOrderService;
import com.carplus.subscribe.service.ETagService;
import com.carplus.subscribe.utils.CsvUtil;
import com.carplus.subscribe.utils.DownloadUtils;
import com.carplus.subscribe.utils.ExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 經銷商訂單API")
public class DealerOrderInternalController {

    @Autowired
    private DealerOrderService dealerOrderService;

    @Autowired
    private DealerOrderPriceInfoService dealerOrderPriceInfoService;

    @Autowired
    private ETagService etagService;

    @Value("${gcs.url}")
    private String gcsUrl;

    @Operation(summary = "建立經銷商訂單")
    @PostMapping("subscribe/dealerOrder")
    public DealerOrderQueryResponse createDealerOrder(@Validated @RequestBody DealerOrderCreateRequest req) {
        return dealerOrderService.createDealerOrder(req, false);
    }

    @Operation(summary = "編輯經銷商訂單")
    @PatchMapping("subscribe/dealerOrder")
    public DealerOrderQueryResponse updateDealerOrder(@Validated @RequestBody DealerOrderUpdateRequest req) {
        return dealerOrderService.updateDealerOrder(req);
    }

    @Operation(summary = "建立經銷商訂單費用資訊")
    @PostMapping("subscribe/dealerOrder/orderPriceInfo")
    public DealerOrderPriceInfo createDealerOrderPriceInfo(@Validated @RequestBody DealerOrderPriceInfoRequest req) {
        return dealerOrderPriceInfoService.createDealerOrderPriceInfo(req);
    }

    @Operation(summary = "經銷商訂單出車作業異動")
    @PatchMapping("subscribe/dealerOrder/depart")
    public DealerOrder departDealerOrder(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                         @RequestHeader(name = CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, required = false) String systemKind,
                                         @Validated @RequestBody DealerOrderDepartRequest req) {
        return dealerOrderService.departDealerOrder(req, memberId, systemKind);
    }

    @Operation(summary = "經銷商訂單結案")
    @PatchMapping("subscribe/dealerOrder/close")
    public DealerOrder returnDealerOrder(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                                         @Validated @RequestBody DealerOrderCloseRequest req) {
        return dealerOrderService.closeDealerOrder(req, headerMemberId);
    }

    @Operation(summary = "經銷商訂單取消")
    @PatchMapping("subscribe/dealerOrder/cancel")
    public DealerOrder cancelDealerOrder(@Validated @RequestBody DealerOrderCancelRequest req) {
        return dealerOrderService.cancelDealerOrder(req);
    }

    @Operation(summary = "經銷商訂單查詢")
    @GetMapping(value = "subscribe/dealerOrder/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<DealerOrderQueryResponse> queryDealerOrder(@Validated DealerOrderCriteria queryRequest) {
        return PageResponse.of(dealerOrderService.searchByPage(new PageRequest(queryRequest.getLimit(), queryRequest.getSkip()), queryRequest));
    }

    @Operation(summary = "經銷商訂單明細查詢")
    @GetMapping(value = "subscribe/dealerOrder/{orderNo}")
    public DealerOrderResponse queryDealerOrderDetail(@PathVariable("orderNo") String orderNo) {
        return dealerOrderService.queryDealerOrderDetail(orderNo);
    }

    @Lock(group = DealerOrderService.class, key = "'import_excel'", ttl = 60 * 3, errorMsg = "匯入經銷商訂單 Excel 處理中，請稍候再試")
    @Operation(summary = "匯入 Excel 批量新增經銷商訂單")
    @PostMapping(value = "subscribe/dealerOrder/excel/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public DealerOrderExcelResponse createDealerOrderFromExcel(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                                                               @RequestBody @Validated DealerOrderExcelImportUrl url) {

        String uuid = UUID.randomUUID().toString();

        //將 Excel 從雲抓下來
        String fileUrl = String.format("%s/%s", gcsUrl, url.getPath());
        Workbook workbook = DownloadUtils.getWorkBookFileByURL(fileUrl);

        DealerOrderValidate dealerOrderValidate = dealerOrderService.validateDealerOrderWorkbookAndSave(workbook, uuid, headerMemberId);

        return DealerOrderExcelResponse.builder()
            .rowsCount(dealerOrderValidate.getRows().size() + dealerOrderValidate.getErrorRows().size())
            .successCount(dealerOrderValidate.getRows().size())
            .failCount(dealerOrderValidate.getErrorList().size())
            .errMessageList(dealerOrderValidate.getErrorList())
            .uuid(uuid)
            .build();
    }

    @Deprecated
    @Operation(summary = "經銷商訂單CSV下載")
    @GetMapping(value = "subscribe/dealerOrder/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] dealerOrderCsv(HttpServletResponse res,
                                 @Validated DealerOrderCriteria queryRequest) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = dealerOrderService.generateDealerOrderCsv(queryRequest);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=SUB_dealerOrder_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "經銷商訂單 xlsx 匯入範例檔下載")
    @GetMapping(value = "subscribe/dealerOrder/excel/example", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] dealerOrderXlsxExampleDownload(HttpServletResponse res) {
        try {
            res.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("匯入Sealand訂單資料_範例檔.xlsx", StandardCharsets.UTF_8.toString()));
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        return dealerOrderService.generateImportedXlsxExample();
    }

    @Operation(summary = "經銷商訂單Excel下載")
    @GetMapping(value = "subscribe/dealerOrder/excel", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public byte[] dealerOrderExcel(HttpServletResponse res,
                                   @Validated DealerOrderCriteria queryRequest) {
        res.setHeader("Content-Disposition", "attachment; filename=SUB_dealerOrder_" + DateUtils.toDateString("yyyyMMdd") + ".xlsx");
        return dealerOrderService.generateDealerOrderExcel(queryRequest);
    }

    @Operation(summary = "下載錯誤經銷商訂單 Excel")
    @GetMapping(value = "subscribe/dealerOrder/excel/error", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public @ResponseBody byte[] dealerOrderExcelError(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                                                      @RequestParam String uuid, HttpServletResponse res) {

        DealerOrderValidate dealerOrderValidate = dealerOrderService.validateDealerOrderWorkbookAndSave(null, uuid, headerMemberId);

        ExcelUtil.SheetData sheetData = dealerOrderService.generateErrorDealerOrderExport(dealerOrderValidate);

        String fileName = "ErrorDealerOrders_" + DateUtils.toDateString("yyyy-MM-dd") + ".xlsx";
        res.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
        res.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);

        return new ExcelUtil().builder()
                .setfileName(fileName)
                .addSheetData(sheetData)
                .build()
                .toByte();
    }

    @Operation(summary = "建立長租契約")
    @PostMapping(value = "subscribe/dealerOrder/addLrentalContract")
    public DealerOrder addLrentalContract(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                          @RequestBody LrentalContractRequest lrentalContractRequest) {
        return dealerOrderService.createLrentalContract(lrentalContractRequest, memberId);
    }

    @Operation(summary = "人工設定Etag費用")
    @PatchMapping(value = "subscribe/dealerOrder/{orderNo}/etagAmt", produces = MediaType.APPLICATION_JSON_VALUE)
    public void manualSetEtagAmt(@PathVariable("orderNo") String orderNo,
                                 @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                 @RequestBody @Validated ETagInfoRequest request) {
        etagService.manualSetDealerOrderEtagAmt(orderNo, memberId, request);
    }

    @Operation(summary = "經銷商訂單 eTag 通行費用查詢")
    @GetMapping(value = "subscribe/dealerOrder/{orderNo}/etagAmt", produces = MediaType.APPLICATION_JSON_VALUE)
    public EtagInfoResponse queryEtagAmt(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String headerMemberId,
                                         @PathVariable("orderNo") String orderNo) {
        return dealerOrderService.queryEtagAmt(orderNo, headerMemberId);
    }

    @Operation(summary = "遠通迄租 & 查回 etc 費用")
    @PostMapping(value = "subscribe/dealerOrder/{orderNo}/etagReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    public ETagInfo etagReturn(@PathVariable("orderNo") String orderNo,
                             @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                             @RequestBody @Validated EtagCloseRequest request) {
        ETagInfo eTagInfo = dealerOrderService.etagReturn(orderNo, memberId, request);

        if (ETagPayFlow.CONFIRMING.getCode().equals(eTagInfo.getETagPayFlow())) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_RETURN_CAR_CUSTOM);
        }

        return eTagInfo;
    }

    @Operation(summary = "異動經銷商訂單客戶與長租契約編號")
    @PatchMapping("subscribe/dealerOrder/userAndLrentalContractNo")
    public DealerOrder updateUserIdAndLrentalContractNo(@Validated @RequestBody DealerOrderUpdateUserAndLrentalContractNoRequest request) {
        return dealerOrderService.updateUserIdAndLrentalContractNo(request);
    }
}
