package com.carplus.subscribe.controller.econtract;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.db.mysql.entity.UploadFile;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractMailHistory;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.UploadFileKindEnum;
import com.carplus.subscribe.model.order.EContractTemplateResponse;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.presign.UploadFilePresignedRes;
import com.carplus.subscribe.model.request.econtract.*;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.econtract.ECFilesResponse;
import com.carplus.subscribe.model.response.econtract.EContractTemplateGetResponse;
import com.carplus.subscribe.model.response.econtract.EContractTemplateQueryResponse;
import com.carplus.subscribe.model.response.econtract.UploadFileResponse;
import com.carplus.subscribe.service.EContractLogic;
import com.carplus.subscribe.service.EContractService;
import com.carplus.subscribe.service.GcsService;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 電子合約API")
public class EContractInternalController {

    @Autowired
    private EContractLogic eContractLogic;

    @Autowired
    private EContractService eContractService;

    @Autowired
    private GcsService gcsService;

    @Operation(summary = "建立電子合約範本")
    @PostMapping(value = "subscribe/eContractTemplate", produces = MediaType.APPLICATION_JSON_VALUE)
    public EContractTemplate createTemplate(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @Validated @RequestBody TemplateCreateReq req) {
        return eContractLogic.createTemplate(req, memberId);
    }

    @Operation(summary = "電子合約版本升級")
    @PostMapping(value = "subscribe/eContractTemplate/upgrade", produces = MediaType.APPLICATION_JSON_VALUE)
    public EContractTemplate upgradeTemplate(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId, @Validated @RequestBody TemplateCreateReq req) {
        return eContractLogic.upgradeTemplate(req, memberId);
    }

    @Operation(summary = "檢視電子合約範本")
    @GetMapping(value = "subscribe/eContractTemplate/{templateId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public EContractTemplateGetResponse getTemplate(@Schema(description = "範本代碼", required = true) @PathVariable("templateId") Integer templateId) {
        return eContractLogic.getTemplate(templateId);
    }

    @Operation(summary = "電子合約範本查詢")
    @GetMapping(value = "subscribe/eContractTemplate/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<EContractTemplateQueryResponse> queryTemplate(@Validated EContractTemplateCriteria queryRequest) {
        return PageResponse.of(eContractService.searchByPage(new PageRequest(queryRequest.getLimit(), queryRequest.getSkip()), queryRequest));
    }

    @Operation(summary = "電子合約範本歷程查詢")
    @GetMapping(value = "subscribe/eContractTemplate/{templateCode}/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<EContractTemplateResponse> getAllVersionTemplate(@Validated EContractTemplateGetAllVersion req) {
        return PageResponse.of(eContractService.getByTemplateCode(new PageRequest(req.getLimit(), req.getSkip()), req));
    }

    @Operation(summary = "電子合約範本CSV下載")
    @GetMapping(value = "subscribe/eContractTemplate/csv", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public byte[] templateCsv(HttpServletResponse res,
                              @Validated EContractTemplateCriteria queryRequest) {
        CsvUtil.ByteArrayOutputStream2ByteBuffer out = eContractService.generateEContractTemplateCsv(queryRequest);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        res.setHeader("Content-Disposition", "attachment; filename=SUB_EContractTemplate_{" + sf.format(new Date()) + "}.csv");
        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }

    @Operation(summary = "修改電子合約範本")
    @PatchMapping(value = "subscribe/eContractTemplate/{templateId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public EContractTemplate updateTemplate(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                            @PathVariable Integer templateId,
                                            @Validated @RequestBody TemplateUpdateReq req) {
        return eContractLogic.updateTemplate(templateId, memberId, req);
    }

    @Operation(summary = "電子合約範本範本資料驗證")
    @PostMapping(value = "subscribe/eContractTemplate/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result templateValidate(@Validated @RequestBody TemplateValidateReq req) {
        eContractService.templateCreateRequestValidate(req);
        return Result.success();
    }

    @Operation(summary = "更新合約關聯範本")
    @PutMapping(value = "subscribe/eContract/{contractNo}/template", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result updateContractTemplate(
        @PathVariable(name = "contractNo") @Validated @Parameter(description = "合約編號或經銷商訂單編號") String econtractRefEntityNo,
        @RequestParam(name = "templateId") @Validated @Parameter(description = "新範本編號") Integer templateId,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        eContractService.updateContractTemplate(econtractRefEntityNo, templateId);
        return Result.success();
    }

    @Operation(summary = "寄送自選電子[合約/出租單]附件連結Email(人工寄送)")
    @GetMapping(value = "subscribe/eContract/{contractNo}/mail/send", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result sendContractMail(
        @PathVariable(name = "contractNo") @Validated @Parameter(description = "合約編號") String econtractRefEntityNo,
        @RequestParam(name = "acctId") @Validated @Parameter(description = "會員編號") Integer acctId,
        @RequestParam(name = "fileIds") @Validated @Parameter(description = "檔案編號") List<Integer> fileIds,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        eContractService.sendContractMail(acctId, econtractRefEntityNo, fileIds, memberId);
        return Result.success();
    }

    @Operation(summary = "查詢郵件寄送記錄")
    @GetMapping(value = "subscribe/eContract/{contractNo}/mail/history", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<EContractMailHistory> getMailHistory(
        @PathVariable(name = "contractNo") @Parameter(description = "合約流水號") String econtractRefEntityNo) {
        return eContractService.getMailHistory(econtractRefEntityNo);
    }

    @Operation(summary = "取上傳檔案憑証")
    @GetMapping(value = "subscribe/file/upload/presigned", produces = MediaType.APPLICATION_JSON_VALUE)
    public UploadFilePresignedRes uploadPresigned(
        @RequestParam(name = "kind") @Validated @Parameter(description = "上傳類型: CUSTOM_CONTRACT:合約檔案, CONTRACT_TEMPLATE:合約範本") UploadFileKindEnum kind,
        @RequestParam(name = "filename") @Validated @Parameter(description = "顯示用檔案名稱(包含附檔名)") String filename,
        @RequestParam(name = "acctId", required = false) @Validated @Parameter(description = "會員編號") Integer acctId,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        if (kind.equals(UploadFileKindEnum.CONTRACT_TEMPLATE)) {
            return gcsService.getUploadPresigned(acctId, kind, false, filename, memberId);
        } else {
            return gcsService.getUploadPresigned(acctId, kind, true, filename, memberId);
        }
    }

    @Operation(summary = "取得下載檔案憑証")
    @GetMapping(value = "subscribe/file/download/presigned", produces = MediaType.APPLICATION_JSON_VALUE)
    public GcsUrlRes downloadPresigned(
        @RequestParam(name = "acctId", required = false) @Validated @Parameter(description = "會員編號") Integer acctId,
        @RequestParam(name = "kind") @Validated @Parameter(description = "上傳類型: CUSTOM_CONTRACT:合約檔案, CONTRACT_TEMPLATE:合約範本") UploadFileKindEnum kind,
        @RequestParam(name = "fileId") @Validated @Parameter(description = "檔案編號") Integer fileId) {
        return gcsService.getDownloadPresigned(acctId, kind, fileId);
    }

    @Operation(summary = "查詢指定合約的有效附件")
    @GetMapping(value = "subscribe/eContract/{contractNo}/attachment", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<UploadFile> getEContract(
        @PathVariable(name = "contractNo") @Validated @Parameter(description = "合約編號") String econtractRefEntityNo,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        return eContractService.getEContractFiles(econtractRefEntityNo);
    }

    @Operation(summary = "電子合約附件上傳")
    @PostMapping(value = "subscribe/eContract/{contractNo}/attachment", produces = MediaType.APPLICATION_JSON_VALUE)
    public void addContractAttachment(
        @Parameter(description = "合約流水號")
        @PathVariable(name = "contractNo") String econtractRefEntityNo,
        @Validated @RequestBody EContractFileUpdateReq req,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId
    ) {
        eContractService.addContractAttachment(req, econtractRefEntityNo, memberId);
    }

    @Operation(summary = "電子合約附件刪除")
    @DeleteMapping(value = "subscribe/eContract/{contractNo}/attachment/{fileId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void removeContractAttachment(
        @Parameter(description = "合約流水號") @PathVariable(name = "contractNo") String econtractRefEntityNo,
        @Parameter(description = "檔案編號") @PathVariable(name = "fileId") Integer fileId,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId
    ) {
        eContractService.removeContractAttachment(econtractRefEntityNo, fileId, memberId);
    }

    @Operation(summary = "更新電子合約 - 上傳檔案資料")
    @PatchMapping(value = "subscribe/eContract/{contractNo}/attachment", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<UploadFileResponse> updateContractAttachment(@Parameter(description = "合約流水號") @PathVariable(name = "contractNo") String econtractRefEntityNo,
                                                               @Validated @RequestBody EContractRequest request,
                                                               @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        return Result.success(eContractService.updateEContractUploadFile(econtractRefEntityNo, request, memberId));
    }

    @Operation(summary = "取得主約/經銷商訂單下所有合約和合約檔案資料")
    @GetMapping(value = "subscribe/eContract/mainContract/{mainContractNo}/attachment", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<ECFilesResponse>> getEContractAttachments(
        @PathVariable(name = "mainContractNo") @Validated @Parameter(description = "主合約編號/經銷商訂單編號") String mainContractOrDealerOrderNo,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId) {
        return Result.success(eContractService.getMainContractFiles(mainContractOrDealerOrderNo));
    }


    @Operation(summary = "是否簽約電子合約")
    @GetMapping(value = "subscribe/eContract/rental/sign/check/{contractNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean isSignEContract(@PathVariable("contractNo") String econtractRefEntityNo) {
        return eContractService.isSignEContract(econtractRefEntityNo);
    }
}
