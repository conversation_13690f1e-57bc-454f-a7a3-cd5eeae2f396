package com.carplus.subscribe.controller.econtract;

import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.enums.UploadFileKindEnum;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.presign.UploadFilePresignedRes;
import com.carplus.subscribe.model.request.econtract.*;
import com.carplus.subscribe.model.response.PageResponse;
import com.carplus.subscribe.model.response.econtract.EContractTemplateQueryResponse;
import com.carplus.subscribe.service.EContractLogic;
import com.carplus.subscribe.service.EContractService;
import com.carplus.subscribe.service.GcsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

@CarPlusRestController
@Tag(name = "電子合約API")
public class EContractPublicController {

    @Autowired
    private EContractService eContractService;

    @Autowired
    private EContractLogic eContractTemplateLogic;

    @Autowired
    private GcsService gcsService;

    @Operation(summary = "電子合約範本查詢")
    @GetMapping(value = "subscribe/eContractTemplate/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public PageResponse<EContractTemplateQueryResponse> queryTemplate(@Validated EContractTemplateCriteria queryRequest) {

        String conditionCarBu = eContractTemplateLogic.getConditionCarBu(queryRequest.getConditionCarBrand());
        if (StringUtils.isNotBlank(conditionCarBu)) {
            queryRequest.setConditionCarBu(conditionCarBu);
        }

        return PageResponse.of(eContractService.searchByPage(new PageRequest(queryRequest.getLimit(), queryRequest.getSkip()), queryRequest));
    }

    @Operation(summary = "電子合約PDF加密")
    @GetMapping(value = "subscribe/eContractTemplate/encryptPdf", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public GcsUrlRes encryptPdf(HttpServletResponse res,
        @Validated EContractPdfReq queryRequest,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId) throws Exception {
        return eContractService.generateEContractPdf(queryRequest, acctId);
    }

    @Operation(summary = "電子合約簽署")
    @GetMapping(value = "subscribe/eContractTemplate/sign", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result signPdf(HttpServletResponse res,
        @Validated EContractSignReq req,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId) {
        eContractService.eContractSigning(acctId, req.getTemplateId(), req.getSignFileId(), req.getContractNo());
        return Result.success();
    }

    @Operation(summary = "取得上傳檔案憑証(簽名檔)")
    @GetMapping(value = "subscribe/file/upload/presigned", produces = MediaType.APPLICATION_JSON_VALUE)
    public UploadFilePresignedRes uploadPresigned(
        HttpServletResponse res,
        @Validated UploadPresignReq req,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId) {
        return gcsService.getUploadPresigned(acctId, req.getKind(), true, UUID.randomUUID().toString(), acctId.toString());
    }

    @Operation(summary = "取得下載檔案憑証", hidden = true)
    @GetMapping(value = "subscribe/file/download/presigned", produces = MediaType.APPLICATION_JSON_VALUE)
    public GcsUrlRes downloadPresigned(
        HttpServletResponse res,
        @Validated DownloadContractPresignReq req,
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId) {
        return gcsService.getDownloadPresigned(acctId, UploadFileKindEnum.CUSTOM_CONTRACT, req.getFileId());
    }
}
