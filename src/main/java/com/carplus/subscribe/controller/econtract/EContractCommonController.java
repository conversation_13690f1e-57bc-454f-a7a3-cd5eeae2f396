package com.carplus.subscribe.controller.econtract;

import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.constant.PDFConstant;
import com.carplus.subscribe.enums.EContractSource;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.model.response.econtract.EContractTemplateSignatureResponse;
import com.carplus.subscribe.service.EContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 電子合約API")
public class EContractCommonController {

    @Autowired
    private EContractService eContractService;

    @Operation(summary = "查詢電子簽名檔插入位置及尺寸")
    @GetMapping(value = "subscribe/eContractTemplate/coordinate", produces = MediaType.APPLICATION_JSON_VALUE)
    public EContractTemplateSignatureResponse getTemplateCoordinate() {
        return new EContractTemplateSignatureResponse(PDFConstant.X, PDFConstant.Y, PDFConstant.SignatureWidth, PDFConstant.SignatureHeight, PDFConstant.PageWidth, PDFConstant.PageHeight);
    }


    @Operation(summary = "取得預覽合約編號")
    @GetMapping(value = "subscribe/econtract/template/", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result getPreviewTemplateId(
        @RequestParam Integer month,
        @RequestParam String carNo,
        @RequestParam(required = false, defaultValue = "false") Boolean isDisclaimerFee
    ) {
        return Result.success(eContractService.getTemplateContractIdByCarNo(carNo, EContractSource.CARPLUS, month, isDisclaimerFee));
    }

    @Operation(summary = "取得電子合約下載檔案憑証")
    @GetMapping(value = "subscribe/eContractTemplate/download/presigned", produces = MediaType.APPLICATION_JSON_VALUE)
    public GcsUrlRes eContractTemplateDownloadPresigned(@RequestParam(name = "templateId") @Validated @Parameter(description = "範本編號") Integer templateId) {
        return eContractService.getEContractTemplateDownloadPresigned(templateId);
    }
}
