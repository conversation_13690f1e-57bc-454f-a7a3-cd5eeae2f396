package com.carplus.subscribe.controller.shipment;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.shipment.SkuShipmentDetailResponse;
import com.carplus.subscribe.model.shipment.SkuShipmentResponse;
import com.carplus.subscribe.model.shipment.SkuShipmentCriteria;
import com.carplus.subscribe.model.shipment.SkuShipmentStatusUpdateRequest;
import com.carplus.subscribe.service.SkuShipmentService;
import com.carplus.subscribe.utils.CsvUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 出貨作業 API")
public class SkuShipmentInternalController {

    @Autowired
    private SkuShipmentService skuShipmentService;

    @Operation(summary = "取得出貨清單")
    @GetMapping(value = "subscribe/skuShipments", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<SkuShipmentResponse> getSkuShipmentList(
            SkuShipmentCriteria criteria,
            @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
            @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit) {
        return skuShipmentService.getShipmentList(criteria, new PageRequest(limit, skip));
    }

    @Operation(summary = "取得出貨明細")
    @GetMapping(value = "subscribe/skuShipments/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public SkuShipmentDetailResponse getShipmentDetail(@PathVariable("id") Integer shipmentId) {
        return skuShipmentService.getShipmentDetail(shipmentId);
    }

    @Operation(summary = "新增訂單出貨資料")
    @PostMapping(value = "subscribe/skuShipments/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void createShipments(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
                                @PathVariable("orderNo") String orderNo) {
        skuShipmentService.createShipments(orderNo, memberId);
    }

    @Operation(summary = "更新出貨狀態")
    @PatchMapping(value = "subscribe/skuShipments/{shipmentId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateShipmentStatus(
            @PathVariable("shipmentId") Integer shipmentId,
            @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
            @Validated @RequestBody SkuShipmentStatusUpdateRequest request) {
        skuShipmentService.updateShipmentStatus(shipmentId, request.getStatus(), memberId);
    }

    @Operation(summary = "出貨作業 CSV 匯出")
    @GetMapping(value = "subscribe/skuShipments/csv/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public byte[] exportSkuShipmentCsv(HttpServletResponse response, SkuShipmentCriteria criteria) {

        CsvUtil.ByteArrayOutputStream2ByteBuffer out = skuShipmentService.generateSkuShipmentCsv(criteria);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        response.setHeader("Content-Disposition", "attachment; filename=SUB_sku_shipments_" + sf.format(new Date()) + ".csv");

        return ByteBuffer
            .allocate(out.size())
            .put(out.toByteBuffer())
            .array();
    }
}