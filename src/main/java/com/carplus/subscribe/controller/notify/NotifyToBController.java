package com.carplus.subscribe.controller.notify;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.service.NotifyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 營業通知 接口")
@Slf4j
public class NotifyToBController {

    @Autowired
    private NotifyService notifyService;


    @Operation(summary = "授信需求")
    @GetMapping(value = "subscribe/notify/creditDemand/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyCreditDemand(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyCreditDemand(orderNo);
    }

    @Operation(summary = "保證金付款成功")
    @GetMapping(value = "subscribe/notify/paySecurityDeposit/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyPaySecurityDepositSuccess(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyPaySecurityDepositSuccess(orderNo);
    }

    @Operation(summary = "人工保證金退款通知")
    @GetMapping(value = "subscribe/notify/manualRefundSecurityDeposit/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyManualRefundSecurityDeposit(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyManualRefundSecurityDeposit(orderNo);
    }

    @Operation(summary = "續約成立")
    @GetMapping(value = "subscribe/notify/renewConfirm/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyRenewConfirm(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyRenewConfirm(orderNo);
    }

    @Operation(summary = "取消訂單")
    @GetMapping(value = "subscribe/notify/cancelOrder/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyCancelOrder(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyCancelOrder(orderNo);
    }

    @Operation(summary = "續約提醒")
    @GetMapping(value = "subscribe/notify/renewCall/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyRenewCall(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyRenewCall(orderNo);
    }

    @Operation(summary = "不續約通知")
    @GetMapping(value = "subscribe/notify/notRenewConfirm/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyNotRenewConfirm(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyNotRenewConfirm(orderNo);
    }

    @Operation(summary = "已還車未結案提醒")
    @GetMapping(value = "subscribe/notify/returnNotClose/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyReturnNotClose(@PathVariable("orderNo") String orderNo) {
        notifyService.notifyReturnNotClose(orderNo);
    }
        
}
