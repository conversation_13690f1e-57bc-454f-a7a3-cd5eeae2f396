package com.carplus.subscribe.controller.notify;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.service.NotifyToCService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 客戶通知 接口")
@Slf4j
public class NotifyToCController {

    @Autowired
    private NotifyToCService notifyToCService;


    @Operation(summary = "續約通知")
    @GetMapping(value = "subscribe/notify/renew/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public void notifyRenewOrder(@PathVariable("orderNo") String orderNo) {
        notifyToCService.notifyRenewOrder(orderNo);
    }

}
