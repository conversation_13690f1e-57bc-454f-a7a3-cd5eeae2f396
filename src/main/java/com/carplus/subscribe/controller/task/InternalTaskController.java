package com.carplus.subscribe.controller.task;

import carplus.common.response.CarPlusRestController;
import carplus.common.response.Result;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 任務 接口")
public class InternalTaskController {

    @Autowired
    private TaskService taskService;

    @Operation(summary = "同步站點資料")
    @PostMapping(value = "subscribe/task/syncStationsData", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncStationsDataTask() {
        taskService.syncStationsData();
        return Result.success("OK");
    }

    @Operation(summary = "ETag 還車 查詢 出車")
    @PostMapping(value = "subscribe/task/returnAndQueryAndDepartCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> returnAndQueryAndDepartCar() {
        taskService.returnAndQueryAndDepartCar();
        return Result.success("OK");
    }

    @Operation(summary = "罰金 累加")
    @PostMapping(value = "subscribe/task/accumulateFines", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> accumulateFine() {
        taskService.accumulateFines();
        return Result.success("OK");
    }

    @Operation(summary = "檢查訂單有無未繳款狀態")
    @PostMapping(value = "subscribe/task/checkOrderIsUnpaid", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> checkOrderIsUnpaid() {
        taskService.checkOrderIsUnpaid();
        return Result.success("OK");
    }

    @Operation(summary = "通知每期費用繳款")
    @PostMapping(value = "subscribe/task/notifyPayStageFee", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyPayStageFee() {
        taskService.notifyPayStageFee();
        return Result.success("OK");
    }

    @Operation(summary = "續約提醒")
    @PostMapping(value = "subscribe/task/notifyRenewCall", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyRenewCall() {
        taskService.notifyRenewCall();
        return Result.success("OK");
    }

    @Operation(summary = "還車提醒")
    @PostMapping(value = "subscribe/task/notifyReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyReturn() {
        taskService.notifyReturn();
        return Result.success("OK");
    }

    @Operation(summary = "已出車且ETAG出車失敗的車五天後重新對遠通出車")
    @PostMapping(value = "subscribe/task/recallEtagDepartFail", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> recallEtagDepartFail() {
        taskService.recallEtagDepartFail();
        return Result.success("OK");
    }


    @Operation(summary = "取消未付保證金訂單")
    @PostMapping(value = "subscribe/task/cancelOrderWithoutSecurityDeposit", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> cancelOrderWithoutSecurityDeposit() {
        taskService.cancelOrderWithoutSecurityDeposit();
        return Result.success("OK");
    }

    @Operation(summary = "長租契約自動展期訂閱約")
    @PostMapping(value = "subscribe/task/autoExpendLrentalContract", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> autoExpendLrentalContract() {
        taskService.autoExpendLrentalContract();
        return Result.success("OK");
    }

    @Operation(summary = "經銷商長租契約自動展期訂閱約")
    @PostMapping(value = "subscribe/task/autoExpendLrentalDealerContract", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> autoExpendLrentalDealerContract() {
        taskService.autoExpendLrentalDealerContract();
        return Result.success("OK");
    }

    @Operation(summary = "檢查未日結資訊")
    @PostMapping(value = "subscribe/task/checkoutDaily", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> checkoutDaily() {
        taskService.checkoutTask();
        return Result.success("OK");
    }

    @Operation(summary = "通知已還車未結案")
    @PostMapping(value = "subscribe/task/notifyReturnNotClose", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyReturnNotClose() {
        taskService.notifyReturnNotClose();
        return Result.success("OK");
    }

    @Operation(summary = "通知已出車未還車")
    @PostMapping(value = "subscribe/task/notifyDepartNotReturn", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyDepartNotReturn() {
        taskService.notifyDepartNotReturn();
        return Result.success("OK");
    }

    @Operation(summary = "通知逾期未結算")
    @PostMapping(value = "subscribe/task/notifyMonthlyFeeUnpaid", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyMonthlyFeeUnpaid() {
        taskService.notifyMonthlyFeeUnpaid();
        return Result.success("OK");
    }

    @Operation(summary = "同步車籍牌價")
    @PostMapping(value = "subscribe/task/syncCarStdPrice", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncCarStdPrice() {
        taskService.syncStdPriceToCar();
        return Result.success("OK");
    }

    @Operation(summary = "同步車籍能源別")
    @PostMapping(value = "subscribe/task/syncEnergyTypeToCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncEnergyTypeToCar() {
        taskService.syncEnergyTypeToCar();
        return Result.success("OK");
    }

    @Operation(summary = "收訂尚未建約提醒")
    @PostMapping(value = "subscribe/task/notifyOrderWithoutContract", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyOrderWithoutContract() {
        taskService.notifyOrderWithoutContract();
        return Result.success("OK");
    }

    @Operation(summary = "通知 SEALAND 訂單投保提醒")
    @PostMapping(value = "subscribe/task/notifySealandInsurance", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyDealerOrderInsurance() {
        taskService.notifyDealerOrderInsurance();
        return Result.success("OK");
    }

    @Operation(summary = "通知 SEALAND 訂單出車異常")
    @PostMapping(value = "subscribe/task/notifySealandDepartAbnormal", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyDealerOrderDepartAbnormal() {
        taskService.notifyDealerOrderDepartAbnormal();
        return Result.success("OK");
    }

    @Operation(summary = "從 CRS 同步所有車籍出廠月份")
    @PostMapping(value = "subscribe/task/syncCarsMfgMonthFromCrs", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncCarsMfgMonthFromCrs() {
        taskService.syncCarsMfgMonthFromCrs();
        return Result.success("OK");
    }

    @Operation(summary = "從 CRS 同步車籍CrsNo與上下架(若出售則Deprecate)狀態到車籍表")
    @PostMapping(value = "subscribe/task/syncCrsCarNoAndLaunchedToCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncCrsCarNoAndLaunchedToCar() {
        taskService.syncCrsCarNoAndLaunchedToCar();
        return Result.success("OK");
    }

    @Operation(summary = "從 CRS 同步車籍是否專案車")
    @PostMapping(value = "subscribe/task/syncCrsIsProjectCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> syncCrsIsProjectCar() {
        taskService.syncCrsIsProjectCar();
        return Result.success("OK");
    }

    @Operation(summary = "將屬於訂閱的車撥回各BU或通知營業查詢車籍歸屬")
    @PostMapping(value = "subscribe/task/notifyReturnCar", produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> notifyReturnCar() {
        taskService.notifyReturnCar();
        return Result.success("OK");
    }


}
