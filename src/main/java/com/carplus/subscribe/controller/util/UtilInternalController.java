package com.carplus.subscribe.controller.util;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.enums.FileTypeEnum;
import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.service.GcsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@CarPlusRestController
@RequestMapping(HttpConstant.INTERNAL_URL)
@Tag(name = "Internal 通用API")
public class UtilInternalController {

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private GcsService gcsService;

    @Operation(summary = "GCS公開Bucket OSS")
    @GetMapping(value = "subscribe/util/img/gcs/oss")
    public GcsUrlRes getGcsPublicUploadUrl() {
        GcsGetUploadUrlReq gcsGetUploadUrlReq = goSmartServer.createGcsGetUploadUrlReq(FileTypeEnum.JPG);
        return goSmartServer.getGcsPublicUploadUrl(gcsGetUploadUrlReq);
    }


    @Operation(summary = "將車型圖GCS")
    @GetMapping(value = "subscribe/util/gcs/convert")
    public void convertImg2GCS() {
        gcsService.convertCarModelImg2Gcs();
    }
}
