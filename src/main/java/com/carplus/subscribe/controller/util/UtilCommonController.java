package com.carplus.subscribe.controller.util;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.utils.DateUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.time.Instant;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "Common 通用API")
public class UtilCommonController {

    @GetMapping(value = "subscribe/util/calculateNewEndDate")
    public Instant getExpectEndDate(Instant originalStartDate, Instant newStartDate, Instant originalEndDate) {
        return DateUtil.calculateNewEndDate(originalStartDate, newStartDate, originalEndDate);
    }
}
