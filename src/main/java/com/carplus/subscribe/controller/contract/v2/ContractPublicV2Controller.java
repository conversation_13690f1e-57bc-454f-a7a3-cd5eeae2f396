package com.carplus.subscribe.controller.contract.v2;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.model.order.MainContractV2Response;
import com.carplus.subscribe.model.order.OrderResponse;
import com.carplus.subscribe.model.response.order.UserOrderResponse;
import com.carplus.subscribe.service.ContractService;
import com.carplus.subscribe.service.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

@CarPlusRestController
@Tag(name = "訂閱合約API V2")
public class ContractPublicV2Controller {

    @Autowired
    private OrderService orderService;

    @Autowired
    private ContractService contractService;

    @Operation(summary = "拿取訂單V2")
    @GetMapping("subscribe/order/v2/{orderNo}")
    public OrderResponse findOrdersV2(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                      @PathVariable("orderNo") String orderNo) {
        return orderService.getUserOrderV2(orderNo, acctId);
    }

    @Operation(summary = "查詢主約V2")
    @GetMapping(value = "/subscribe/v2/mainContract", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<MainContractV2Response> getMainContractOrders(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                              @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
                                                              @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit,
                                                              @RequestParam(value = "contractStatuses", required = false) List<ContractStatus> contractStatuses) {
        return contractService.userSearchPage(new PageRequest(limit, skip), acctId, contractStatuses);
    }

    @Operation(summary = "拿取主約V2")
    @GetMapping(value = "/subscribe/v2/mainContract/{mainContractNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public MainContractV2Response getMainContractOrders(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                        @PathVariable String mainContractNo) {
        return contractService.getUserMainContract(mainContractNo, acctId);
    }

    @Operation(summary = "查詢主約底下訂單")
    @GetMapping(value = "/subscribe/v2/mainContract/{mainContractNo}/orders", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<UserOrderResponse> getMainContractOrders(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                         @RequestParam(value = "skip", defaultValue = "0", required = false) Integer skip,
                                                         @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit,
                                                         @PathVariable String mainContractNo) {
        return orderService.getUserOrderResponse(new PageRequest(limit, skip), mainContractNo, acctId, null);
    }


    @Operation(summary = "拿取訂單可續約訂單")
    @GetMapping("subscribe/order/v2/{mainContractNo}/renewable/orders")
    public List<UserOrderResponse> getRenewableOrder(@RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
                                                     @PathVariable("mainContractNo") String mainContractNo) {
        return orderService.getUserOrderResponse(new PageRequest(1000, 0), mainContractNo, acctId, Collections.singletonList(OrderStatus.DEPART.getStatus())).getList();
    }

}
