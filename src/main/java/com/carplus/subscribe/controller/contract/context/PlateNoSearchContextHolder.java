package com.carplus.subscribe.controller.contract.context;

public class PlateNoSearchContextHolder {

    private static final ThreadLocal<Boolean> INCLUDE_REPLACED_PLATE_NO_CONTEXT = new ThreadLocal<>();

    public static void setIncludeReplacedPlateNo(boolean includeReplacedPlateNo) {
        INCLUDE_REPLACED_PLATE_NO_CONTEXT.set(includeReplacedPlateNo);
    }

    public static boolean shouldIncludeReplacedPlateNo() {
        Boolean value = INCLUDE_REPLACED_PLATE_NO_CONTEXT.get();
        return value != null && value;
    }

    public static void clear() {
        INCLUDE_REPLACED_PLATE_NO_CONTEXT.remove();
    }
}
