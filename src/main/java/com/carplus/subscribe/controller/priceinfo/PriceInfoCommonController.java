package com.carplus.subscribe.controller.priceinfo;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.HttpConstant;
import com.carplus.subscribe.model.config.YesChargingPoint;
import com.carplus.subscribe.service.PriceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@CarPlusRestController
@RequestMapping(HttpConstant.COMMON_URL)
@Tag(name = "訂單費用資訊API")
public class PriceInfoCommonController {

    @Autowired
    private PriceInfoService priceInfoService;

    @Operation(summary = "充電金費用範例清單")
    @GetMapping(value = "/subscribe/priceInfo/yes-charging/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<YesChargingPoint> orderPriceInfo() {
        return priceInfoService.getYesChargingDefaultExample();
    }
}
