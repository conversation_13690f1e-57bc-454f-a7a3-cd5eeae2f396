package com.carplus.subscribe.controller.priceinfo;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.service.PriceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@CarPlusRestController
@Tag(name = "訂單費用資訊API")
public class PriceInfoPublicController {

    @Autowired
    private PriceInfoService priceInfoService;

    @Operation(summary = "使用者取消訂單款項資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}/cancel", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfoResponse> cancelOrderPriceInfo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo) {
        return priceInfoService.getCancelPriceInfo(orderNo, acctId);
    }

    @Operation(summary = "訂單付款資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfo> priceInfo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo) {
        return priceInfoService.getPriceInfosByOrderAndAcctId(orderNo, acctId);
    }

    @Operation(summary = "使用者訂單需付款資訊清單")
    @GetMapping(value = "/subscribe/priceInfo/{orderNo}/unpaid", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OrderPriceInfoResponse> orderPriceInfo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo,
        @RequestParam(value = "history", defaultValue = "false", required = false) Boolean history,
        @RequestParam(value = "isCredit", defaultValue = "true", required = false) Boolean isCredit,
        @RequestParam(value = "isLastMileage", defaultValue = "true", required = false) Boolean isLastMileage,
        @RequestParam(value = "sequenceId", required = false) String sequenceId) {
        return priceInfoService.getUserUnPaidPriceInfoByOrderResponseWithCoupon(acctId, orderNo, history, isCredit, isLastMileage, sequenceId);
    }

}
