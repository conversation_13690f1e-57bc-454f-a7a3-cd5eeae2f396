package com.carplus.subscribe.controller.priceinfo.v2;

import carplus.common.response.CarPlusRestController;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.priceinfo.resp.StageOrderPriceInfoResponse;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.carplus.subscribe.service.PriceInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@CarPlusRestController
@Tag(name = "訂單費用資訊API")
public class PriceInfoV2Controller {

    @Autowired
    private PriceInfoService priceInfoService;

    @Operation(summary = "訂單付款資訊清單")
    @GetMapping(value = "/subscribe/v2/priceInfo/{orderNo}", produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<Integer, StageOrderPriceInfoResponse> priceInfo(
        @RequestHeader(name = CarPlusConstant.AUTH_HEADER_ACCT) Integer acctId,
        @PathVariable("orderNo") String orderNo, OrderPriceInfoCriteriaRequest request) {
        return priceInfoService.getUserStageGroupPriceInfoByOrder(orderNo, request, acctId, OrderPriceInfoResponse.class);
    }

}
