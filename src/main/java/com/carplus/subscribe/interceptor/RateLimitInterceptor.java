package com.carplus.subscribe.interceptor;

import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.exception.SubscribeException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.WISHLIST_RATE_LIMIT_EXCEEDED;

@Component
@RequiredArgsConstructor
public class RateLimitInterceptor implements HandlerInterceptor {

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!isRateLimitPath(request.getRequestURI(), request.getMethod())) {
            return true;
        }

        String acctId = request.getHeader(CarPlusConstant.AUTH_HEADER_ACCT);
        if (acctId == null) {
            response.sendError(HttpStatus.BAD_REQUEST.value(), "缺少使用者 ID");
            return false;
        }

        String carWishlistRedisKey = CarPlusConstant.CAR_WISHLIST_REDIS_KEY + acctId;

        // 檢查並更新 Redis 中的計數器
        Long requestCount = redisTemplate.opsForValue().increment(carWishlistRedisKey);

        if (requestCount == 1) {
            // 如果是第一次請求，設置 1 分鐘的過期時間
            redisTemplate.expire(carWishlistRedisKey, 1, TimeUnit.MINUTES);
        }

        // 如果超過 20 次，返回 收藏清單操作過於頻繁，請稍後再試
        if (requestCount > 20) {
            throw new SubscribeException(WISHLIST_RATE_LIMIT_EXCEEDED);
        }

        return true;
    }

    private boolean isRateLimitPath(String uri, String method) {
        return uri.endsWith("/subscribe/carWishlist") && ("POST".equals(method) || "DELETE".equals(method));
    }
}
