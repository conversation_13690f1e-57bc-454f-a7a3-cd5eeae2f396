package com.carplus.subscribe.schedule;

import com.carplus.subscribe.db.mysql.dao.StationsRepository;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.server.SrentalServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StationsTask {

    @Autowired
    private SrentalServer srentalServer;

    @Autowired
    private StationsRepository stationsRepository;

    /**
     * 同步站點資料任務
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void syncStationsDataTask() {
        log.info("syncStationsData 同步站點資料 開始");
        // 舊站點資料
        List<Stations> oldStations = stationsRepository.findAll();
        List<String> oldCodes = oldStations.stream().map(s -> s.getStationCode()).collect(Collectors.toList());
        // 新站點資料(短租)
        List<Stations> allStations = srentalServer.getAllStationList();
        List<String> allCodes = allStations.stream().map(s -> s.getStationCode()).collect(Collectors.toList());

        // 不需要的站點軟刪除
        List<Stations> deleteStations = oldStations.stream().filter(s -> !allCodes.contains(s.getStationCode())).collect(Collectors.toList());
        List<String> deleteIds = deleteStations.stream().filter(s -> !s.isDeleted()).map(s -> s.getStationCode()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            stationsRepository.updateIsDeleted(deleteIds, true);
            log.info("syncStationsData 不需要的站點軟刪除 stationCodes: {}", deleteIds);
        }

        // 已存在的站點改狀態
        List<Stations> existStations = oldStations.stream().filter(s -> allCodes.contains(s.getStationCode())).collect(Collectors.toList());
        List<String> updateToUnDeletedIds = existStations.stream().filter(s -> s.isDeleted()).map(s -> s.getStationCode()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateToUnDeletedIds)) {
            stationsRepository.updateIsDeleted(updateToUnDeletedIds, false);
            log.info("syncStationsData 已存在的站點改狀態 stationCodes: {}", updateToUnDeletedIds);
        }

        // 新站點新增
        List<Stations> saveStations = allStations.stream().filter(s -> !oldCodes.contains(s.getStationCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(saveStations)) {
            stationsRepository.saveAll(saveStations);
            log.info("syncStationsData 新站點新增: {}", saveStations);
        }
    }
}