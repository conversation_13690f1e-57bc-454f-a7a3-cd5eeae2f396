package com.carplus.subscribe.schedule;

import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dao.OrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.NotifyToCService;
import com.carplus.subscribe.service.OrderService;
import com.carplus.subscribe.service.PriceInfoService;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Discount;

@Slf4j
@Component
public class OrderPriceInfoTask {

    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private NotifyToCService notifyToCService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private AuthServer authServer;
    @Autowired
    private EtagInfoRepository etagInfoRepository;

    /**
     * 累計 罰金
     */
    public void accumulateFinesTask() {
        log.info("accumulateFineTask 累加 罰金 開始");

        Map<String, Object> errorMsg = accumulateOrderPriceInfoStagesFines();
        if (!errorMsg.isEmpty()) {
            mattermostServer.notify("[罰金計算錯誤]", errorMsg, null);
        }
    }

    /**
     * 檢查訂單有無未繳款狀態
     */
    public void checkOrderIsUnpaidTask() {
        log.info("checkOrderIsUnpaid 檢查訂單有無未繳款狀態 開始");
        executeCheckOrderIsUnpaid();
        log.info("checkOrderIsUnpaid 檢查訂單有無未繳款狀態 結束");
    }

    /**
     * 檢查訂單有無未繳款狀態
     */
    public void notifyPayStageFee() {
        log.info("notifyPayStageFee 排程通知每期付款費 開始");
        executePayStageFee();
        log.info("notifyPayStageFee 排程通知每期付款費 結束");
    }

    /**
     * 每季繳納里程費或其他款項延遲時罰金計算
     * 當客戶有款項預期未繳時，則產生月租費*0.2 *預期天數的罰金
     */
    public Map<String, Object> accumulateOrderPriceInfoStagesFines() {
        Instant now = Instant.now();
        Instant yesterday = now.minus(1, ChronoUnit.DAYS);
        Instant yesterdayEnd = DateUtil.convertToEndOfInstant(yesterday);
        Map<String, Object> matterMostInfos = new LinkedHashMap<>();

        List<OrderPriceInfo> unpaidList = orderPriceInfoRepository.findAll((Specification<OrderPriceInfo>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.type), PriceInfoDefinition.PriceInfoType.Pay.getCode()));
            predicates.add(builder.notEqual(root.get(OrderPriceInfo.Fields.amount), 0));
            predicates.add(builder.equal(root.get(OrderPriceInfo.Fields.receivedAmount), 0));
            predicates.add(builder.notEqual(root.get(OrderPriceInfo.Fields.category), PriceInfoDefinition.PriceInfoCategory.ETag));
            predicates.add(builder.notEqual(root.get(OrderPriceInfo.Fields.category), PriceInfoDefinition.PriceInfoCategory.PayLate));
            predicates.add(builder.lessThanOrEqualTo(root.get(OrderPriceInfo.Fields.lastPayDate), yesterdayEnd));
            return builder.and(predicates.toArray(new Predicate[0]));
        });

        Map<String, List<OrderPriceInfo>> unpaidMap = unpaidList.stream()
            .collect(Collectors.groupingBy(
                OrderPriceInfo::getOrderNo));

        for (List<OrderPriceInfo> infoList : unpaidMap.values()) {

            String orderPriceInfoJson = "";
            try {
                OrderPriceInfo firstInfo = infoList.stream().min(Comparator.comparing(OrderPriceInfo::getStage)).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_PRICE_INFO_NOT_FOUND));
                orderPriceInfoJson = objectMapper.writeValueAsString(firstInfo);
                int unPaidDays = getUnPaidDays(firstInfo.getLastPayDate(), now);
                Orders orders = orderService.getOrder(firstInfo.getOrderNo());
                if (orders.getStatus() < OrderStatus.DEPART.getStatus()) {
                    continue;
                }
                int monthlyFee = orders.getContract().getMainContract().getOriginalPriceInfo().getUseMonthlyFee();
                int insuranceFee = Optional.ofNullable(orders.getContract()).map(Contract::getDisclaimer).map(disClaimer -> disClaimer ? orders.getContract().getMainContract().getOriginalPriceInfo().getDisclaimerFee() : 0).orElse(0);
                Integer fineAmount = (int) Math.ceil((monthlyFee + insuranceFee) * 0.2 * unPaidDays);

                OrderPriceInfo fineInfo = new OrderPriceInfo();
                fineInfo.setOrderNo(firstInfo.getOrderNo());
                fineInfo.setStage(firstInfo.getStage());
                fineInfo.setReceivableDate(DateUtil.convertToStartOfInstant(Instant.now()));
                fineInfo.setLastPayDate(DateUtil.convertToEndOfInstant(Instant.now()));

                fineInfo.setCategory(PriceInfoDefinition.PriceInfoCategory.PayLate);

                PriceInfoDetail detail = new PriceInfoDetail();
                detail.setDelayDays(unPaidDays);
                detail.setFineFrom(firstInfo.getCategory().name());
                detail.setOriginAmount(fineAmount);
                detail.setMonthlyFee(monthlyFee);
                detail.setInsurance(insuranceFee);
                fineInfo.setInfoDetail(detail);

                fineInfo.setType(PriceInfoDefinition.PriceInfoType.Pay.getCode());
                fineInfo.setAmount(fineAmount);

                addOrUpdateFine(fineInfo);
            } catch (SubscribeException | JsonProcessingException e) {
                log.error("something error. PriceInfo:{} ,Exception:{}", orderPriceInfoJson, e);
                matterMostInfos.put(infoList.get(0).getOrderNo(), e);
            }
        }
        return matterMostInfos;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void addOrUpdateFine(OrderPriceInfo fineInfo) {
        try {
            OrderPriceInfo query = new OrderPriceInfo();
            query.setOrderNo(fineInfo.getOrderNo());
            query.setStage(fineInfo.getStage());
            query.setCategory(PriceInfoDefinition.PriceInfoCategory.PayLate);
            query.setType(PriceInfoDefinition.PriceInfoType.Pay.getCode());
            query.setAmount(null);
            query.setReceivedAmount(null);
            Example<OrderPriceInfo> example = Example.of(query);
            Optional<OrderPriceInfo> optional = orderPriceInfoRepository.findAll(example).stream().filter(opi -> opi.getReceivedAmount() == 0).findAny();
            if (optional.isPresent()) {
                OrderPriceInfo updateInfo = optional.get();

                List<OrderPriceInfo> orderPriceInfoList =
                    orderPriceInfoRepository.getPriceInfos(OrderPriceInfoCriteria.builder()
                        .orderNo(Collections.singletonList(fineInfo.getOrderNo()))
                        .category(Collections.singletonList(PriceInfoDefinition.PriceInfoCategory.PayLate))
                        .type(Discount.getCode())
                        .refPriceInfoNo(updateInfo.getRefPriceInfoNo())
                        .build()).stream().filter(opi -> opi.getReceivedAmount() == 0).collect(Collectors.toList());
                boolean isAlreadyApplyDiscount = orderPriceInfoList.stream().anyMatch(orderPriceInfo -> Optional.ofNullable(orderPriceInfo).map(OrderPriceInfo::getInfoDetail).map(PriceInfoDetail::getIsAgree).orElse(false));
                // 沒有折扣則更新
                if (!isAlreadyApplyDiscount && (Objects.isNull(updateInfo.getInfoDetail()) || !Objects.equals(Boolean.TRUE, updateInfo.getInfoDetail().getIsAgree()))) {
                    BeanUtils.copyProperties(fineInfo, updateInfo, "id");
                    orderPriceInfoRepository.save(updateInfo);
                }
            } else {
                fineInfo.setReceivableDate(Instant.now());
                orderPriceInfoRepository.save(fineInfo);
            }
        } catch (Exception ex) {
            log.error("addOrUpdateFine error", ex);
        }
    }

    /**
     * 訂單狀態小於還車，並且日期大於等於 OrderPrinceInfo 的 receivableDate 與 lastPayDate 大於10天的訂單
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void executeCheckOrderIsUnpaid() {

        List<Orders> ordersList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.lessThan(root.get(Orders.Fields.status), OrderStatus.CLOSE.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });

        List<Orders> updateList = new ArrayList<>();
        for (Orders order : ordersList) {
            priceInfoService.checkIsUnpaid(order);
            updateList.add(order);
        }

        try {
            orderRepository.saveAll(updateList);
        } catch (Exception ex) {
            log.error("executeCheckOrderIsUnpaid error", ex);
        }

    }

    /**
     * 取得未繳天數 (遲交不足1天 算1天)
     */
    private int getUnPaidDays(Instant start, Instant end) {
        Duration duration = Duration.between(start, end);
        return (int) (duration.toDays() + 1);
    }


    /**
     * 訂單狀態小於還車，並且為月費開放日，且費用>0且期數>
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public void executePayStageFee() {

        List<Orders> ordersList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.lessThan(root.get(Orders.Fields.status), OrderStatus.CLOSE.getStatus()));
            predicates.add(builder.greaterThanOrEqualTo(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });

        for (Orders order : ordersList) {
            try {
                List<OrderPriceInfo> unPaidPriceInfos = priceInfoService.getUnPaidPriceInfoByOrder(order.getOrderNo(), false);
                if (unPaidPriceInfos.stream()
                    .anyMatch(orderPriceInfo -> orderPriceInfo.getStage() > 1
                        && orderPriceInfo.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.MonthlyFee)
                        && orderPriceInfo.getReceivedAmount() == 0
                        && DateUtil.convertToStartOfInstant(orderPriceInfo.getReceivableDate()).equals(DateUtil.convertToStartOfInstant(Instant.now())))
                    && // 訂單查詢Etag費用不為0
                    unPaidPriceInfos.stream()
                        .anyMatch(orderPriceInfo -> orderPriceInfo.getCategory().equals(PriceInfoDefinition.PriceInfoCategory.ETag)
                            && orderPriceInfo.getReceivedAmount() == 0
                            && etagInfoRepository.getEtagInfoByOrderPriceId(orderPriceInfo.getId()).getETagAmt() != null
                            && DateUtil.convertToStartOfInstant(orderPriceInfo.getReceivableDate()).isBefore(Instant.now()))) {
                    notifyToCService.notifyPayStageFee(order, authServer.getUserWithRetry(order.getContract().getMainContract().getAcctId()));
                }
            } catch (Exception e) {
                log.error("executePayStageFee error", e);
                mattermostServer.notify("排程通知每期付款費用失敗", null, e);
            }
        }
    }
}
