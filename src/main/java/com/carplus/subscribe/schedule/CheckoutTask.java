package com.carplus.subscribe.schedule;

import carplus.common.response.exception.BadRequestException;
import com.carplus.subscribe.db.mysql.dao.AccountDetailRepository;
import com.carplus.subscribe.db.mysql.dao.AccountRepository;
import com.carplus.subscribe.db.mysql.dao.InvoicesRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.CheckoutService;
import com.carplus.subscribe.service.NotifyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CheckoutTask {

    private final MattermostServer mattermostServer;

    private final CheckoutService checkoutService;
    private final NotifyService notifyService;

    private final InvoicesRepository invoicesRepository;
    private final AccountDetailRepository accountDetailRepository;
    private final AccountRepository accountRepository;
    private final OrderRepository orderRepository;

    public void dailyCheckout() {
        log.info("日結開始");
        List<String> orderIds = new ArrayList<>();
        List<Invoices> invoicesList = invoicesRepository.findInvoicesNeedCheckout();
        List<Long> accountIds = accountDetailRepository.findAllUnCheckOut();
        List<Account> accountList = accountRepository.findAllById(accountIds);
        orderIds.addAll(invoicesList.stream().map(Invoices::getOrderNo).collect(Collectors.toSet()));
        orderIds.addAll(accountList.stream().map(Account::getOrderNo).collect(Collectors.toSet()));

        List<Orders> ordersList = orderRepository.findAllById(orderIds);
        Set<String> errorOrders = new HashSet<>();
        Set<String> errorNotEvenOrders = new HashSet<>();
        Set<String> errorFinanceOrders = new HashSet<>();
        Date transactionDate = new Date(Instant.now().minus(2, ChronoUnit.HOURS).toEpochMilli());
        for (Orders orders : ordersList) {
            try {
                checkoutService.checkOut(orders, transactionDate);
            } catch (Exception e) {
                if (e instanceof BadRequestException
                    || (e instanceof SubscribeException && SubscribeHttpExceptionCode.PRICE_AND_INVOICE_AMOUNT_NOT_EQUAL == ((SubscribeException) e).getCode())) {
                    errorNotEvenOrders.add(orders.getOrderNo());
                } else {
                    errorFinanceOrders.add(orders.getOrderNo());
                }
                errorOrders.add(orders.getOrderNo());
            }
        }

        if (!errorOrders.isEmpty()) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("今日日結訂單編號", orderIds);
            map.put("日結失敗訂單編號", errorOrders);
            map.put("帳務與發票不平編號", errorNotEvenOrders);
            map.put("財務中台立帳失敗", errorFinanceOrders);
            mattermostServer.notify("日結異常", map, null);
            if (!errorNotEvenOrders.isEmpty()) {
                notifyService.notifyCheckOutFail(new ArrayList<>(errorNotEvenOrders));
            }
        }
        log.info("日結結束");
    }
}
