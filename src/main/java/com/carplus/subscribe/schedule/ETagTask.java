package com.carplus.subscribe.schedule;

import carplus.common.enums.etag.ETagFlow;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dao.OrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.dao.OrderRepository;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.server.MattermostServer;
import com.carplus.subscribe.service.ETagService;
import com.carplus.subscribe.service.NotifyService;
import com.carplus.subscribe.service.OrderService;
import com.carplus.subscribe.service.PriceInfoService;
import com.carplus.subscribe.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.SingletonMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.persistence.criteria.Predicate;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.MONTHS;

@Slf4j
@Component
public class ETagTask {
    @Autowired
    private EtagInfoRepository etagInfoRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;
    @Autowired
    private ETagService eTagService;
    @Autowired
    private MattermostServer mattermostServer;
    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private NotifyService notifyService;

    /**
     * 訂單租期大於3個月時，則每季時間到期前10天時，進行還車 查詢 出車
     */
    public void returnAndQueryAndDepartCarTask() {
        log.info("returnAndQueryAndDepartCarTask 開始");
        etagSettlement();
    }

    @Deprecated
    private void execute() {
        List<Orders> orderList = orderRepository.findAll((Specification<Orders>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(Orders.Fields.status), OrderStatus.DEPART.getStatus()));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
        List<String> etagErrorOrderNo = new ArrayList<>();
        for (Orders order : orderList) {
            try {
                Contract contract = order.getContract();
                String plateNo = contract.getMainContract().getPlateNo();
                String adminId = order.getDepartMemberId();
                String returnStationCode = contract.getMainContract().getReturnStationCode();


                ETagInfo eTagInfo = eTagService.getLatestNotReturnETagInfo(order, true);
                if (eTagInfo == null) {
                    // 紀錄Etag有問題的訂單編號
                    etagErrorOrderNo.add(order.getOrderNo());
                } else {
                    // 當開始時間+一個月大於等與當下時間，則進行Etag還車
                    if (!eTagInfo.getDepartDate().atZone(DateUtils.ZONE_TPE).toLocalDate().plus(1, MONTHS).atStartOfDay().isBefore(Instant.now().atZone(DateUtils.ZONE_TPE).toLocalDate().plus(1, MONTHS).atStartOfDay())) {
                        // eTag 遠通還車 && 查詢
                        eTagService.returnCar(order, plateNo, adminId, returnStationCode);
                        ETagInfo eTag = eTagService.close(order, plateNo, adminId, returnStationCode, false);
                        log.info("callEtag orderNo: {} 還車 && 查詢", order.getOrderNo());

                        OrderPriceInfo orderPriceInfo = priceInfoService.generateEtagOrderPriceInfo(eTag, order);
                        orderPriceInfoRepository.saveAndFlush(orderPriceInfo);
                        eTag.setOrderPriceInfoId(orderPriceInfo.getId());
                        etagInfoRepository.save(eTag);
                        // 還車後出車
                        eTagService.rentCar(order, plateNo, adminId);
                    }
                }
            } catch (Exception e) {
                Map<String, Object> resultMap = new LinkedHashMap<>();
                resultMap.put("orderNo", order.getOrderNo());
                mattermostServer.notify("Etag自動月結錯誤，無法自動結算Etag", resultMap, e);
            }
        }
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("orderNo", etagErrorOrderNo);
        mattermostServer.notify("查不到ETag已出車未還車紀錄，無法自動結算Etag", resultMap, null);
    }


    /**
     * etag自結
     */
    public void etagSettlement() {
        List<Orders> ordersList = orderService.getOrdersByStatus(OrderStatus.DEPART).stream().filter(
            orders -> {

                CalculateStage calculateStage = DateUtil.calculateStageAndDateByTargetDate(orders, Instant.now());
                List<CalculateStage> calculateStageList = DateUtil.calculateStageAndDate(orders);
                boolean isLastStage = calculateStage.getStage() == calculateStageList.size();
                return (DateUtil.calculateDiffDate(DateUtil.convertToStartOfInstant(Instant.now()), DateUtil.convertToStartOfInstant(calculateStage.getEndDate()), DAYS) == 10
                    || DateUtil.calculateDiffDate(DateUtil.convertToStartOfInstant(Instant.now()), DateUtil.convertToStartOfInstant(orders.getExpectEndDate()), DAYS) == 10)
                    && (!isLastStage);
            }
        ).collect(Collectors.toList());
        Set<String> etagErrorOrderNo = new HashSet<>();

        for (Orders orders : ordersList) {
            Contract contract = orders.getContract();
            MainContract mainContract = contract.getMainContract();
            try {
                // eTag 遠通還車 && 查詢
                eTagService.returnCar(orders, mainContract.getPlateNo(), orders.getDepartMemberId(), mainContract.getReturnStationCode());
                ETagInfo eTag = eTagService.close(orders, mainContract.getPlateNo(), orders.getDepartMemberId(), mainContract.getReturnStationCode(), false);
                log.info("callEtag orderNo: {} 還車 && 查詢", orders.getOrderNo());

                OrderPriceInfo orderPriceInfo = priceInfoService.generateEtagOrderPriceInfo(eTag, orders);
                orderPriceInfoRepository.saveAndFlush(orderPriceInfo);
                eTag.setOrderPriceInfoId(orderPriceInfo.getId());
                etagInfoRepository.save(eTag);
                CalculateStage calculateStage = DateUtil.calculateStageAndDate(orders).stream().max(Comparator.comparing(CalculateStage::getStage)).get();

                // 是否最後一期，若為最後一期，則不Etag出車，等續約當初車後再Etag出車
                if (!(DateUtil.calculateDiffDate(DateUtil.convertToStartOfInstant(Instant.now()), DateUtil.convertToStartOfInstant(calculateStage.getEndDate()), DAYS) == 10
                    && StringUtils.isNotBlank(orders.getNextStageOrderNo()))) {
                    eTagService.rentCar(orders, mainContract.getPlateNo(), orders.getDepartMemberId());
//                    Orders nextOrders = orderService.getOrder(orders.getNextStageOrderNo());
//                    String departMemberId = Optional.ofNullable(nextOrders.getContract().getDepartMemberId()).orElse(contract.getDepartMemberId());
//                    eTagService.rentCar(nextOrders, mainContract.getPlateNo(),departMemberId);
                }
                if (!Optional.of(ETagFlow.RETURN_SUCCESS.getCode().equals(eTag.getETagFlow())).orElse(false)) {
                    throw new SubscribeException(SubscribeHttpExceptionCode.ETAG_AUTO_SETTLEMENT_FAIL);
                }
            } catch (Exception e) {
                etagErrorOrderNo.add(orders.getOrderNo());
                Map<String, Object> resultMap = new LinkedHashMap<>();
                resultMap.put("orderNo", orders.getOrderNo());
                mattermostServer.notify("Etag自動月結錯誤，無法自動結算Etag", resultMap, e);
            }
        }
        if (!etagErrorOrderNo.isEmpty()) {
            Map<String, Object> resultMap = new LinkedHashMap<>();
            resultMap.put("orderNo", etagErrorOrderNo);
            mattermostServer.notify("查不到ETag已出車未還車紀錄，無法自動結算Etag", resultMap, null);
        }
    }

    /**
     * 訂單Etag出車失敗後嘗試重新出車，失敗後寄信
     */
    @Async
    public void recallEtagDepartFail() {
        List<String> failOrderNos = new ArrayList<>();
        List<Orders> ordersList = orderService.getOrdersByStatus(OrderStatus.DEPART);
        List<String> orderNos = ordersList.stream()
            .map(Orders::getOrderNo).collect(Collectors.toList());
        List<String> eTagDepartFailOrderNos = etagInfoRepository.getAlertETagInfosDepartFailByOrderNos(orderNos).stream().filter(etagInfo -> etagInfo.getReturnDate() == null).map(ETagInfo::getOrderNo).collect(Collectors.toList());
        for (String orderNo : eTagDepartFailOrderNos) {
            ETagInfo eTagInfo = eTagService.rentCar(orderNo, null);
            if (!eTagInfo.getIsSuccess()) {
                failOrderNos.add(orderNo);
            }
        }
        if (!failOrderNos.isEmpty()) {
            mattermostServer.notify("遠通出車失敗清單", new SingletonMap<>("orderNos", failOrderNos), null);
        }
    }
}
