package com.carplus.subscribe.aspects;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
public @interface LogEntity {
    /**
     * 主要異動實體類型，資料表會使用主要異動實體名稱與主要異動實體主鍵作為複合索引
     */
    Class<?> mainEntity();

    /**
     * 記錄內容
     */
    String memo() default "";

    /**
     * 異動者
     */
    String changedBy() default "";
}