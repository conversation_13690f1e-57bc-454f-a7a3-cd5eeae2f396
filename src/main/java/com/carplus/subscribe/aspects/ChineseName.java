package com.carplus.subscribe.aspects;

import com.carplus.subscribe.config.mapper.FieldNameMapping;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ChineseName {

    /**
     * 有使用 @ChineseName 的 Entity 須手動註冊到 {@link FieldNameMapping} 中
     */
    String value();

    /**
     * 若該欄位於 DB 實際上不屬於所在的 class，則須給定此屬性，表示此欄位實際儲存在哪個 entity 所對應的 table 中
     */
    Class<?> belongsTo() default void.class;
}
