package com.carplus.subscribe.aspects;

import com.carplus.subscribe.constant.CarPlusConstant;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

/**
 * 費用資訊更新人員追蹤切面
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class TrackPriceInfoAspect {

    @Around("@annotation(trackPriceInfoUpdator)")
    public Object track(ProceedingJoinPoint pjp, TrackPriceInfoUpdator trackPriceInfoUpdator) throws Throwable {
        // 前置處理，即使發生異常也不影響主邏輯執行
        try {
            String memberId = extractMemberIdFromParameters(pjp);
            if (memberId != null) {
                PriceInfoTrackingContext.setMemberId(memberId);
            }
        } catch (Exception e) {
            // 記錄異常但不拋出，避免影響主邏輯
            log.warn("Failed to extract or set member ID", e);
        }

        try {
            return pjp.proceed();
        } finally {
            PriceInfoTrackingContext.clear();
        }
    }

    /**
     * 從方法參數中提取 memberId
     */
    private String extractMemberIdFromParameters(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];

            // 查找標記了 @RequestHeader 且 name 為 AUTH_HEADER_MEMBER 的參數
            RequestHeader requestHeader = parameter.getAnnotation(RequestHeader.class);
            if (requestHeader != null && CarPlusConstant.AUTH_HEADER_MEMBER.equals(requestHeader.name()) && args[i] instanceof String) {
                return (String) args[i];
            }
        }

        return null;
    }
}
