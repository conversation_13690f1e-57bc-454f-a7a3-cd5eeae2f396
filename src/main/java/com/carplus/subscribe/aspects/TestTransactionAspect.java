package com.carplus.subscribe.aspects;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

@Slf4j
@Aspect
@Component
@ConditionalOnClass(name = "com.carplus.subscribe.controller.contract.ContractInternalControllerTest")
public class TestTransactionAspect {

    private final PlatformTransactionManager mysqlTransactionManager;

    public TestTransactionAspect(@Qualifier("mysqlTransactionManager") PlatformTransactionManager mysqlTransactionManager) {
        this.mysqlTransactionManager = mysqlTransactionManager;
    }

    @Around("execution(* com.carplus.subscribe.service..*.*(..)) && @annotation(transactional)")
    public Object suppressRequiresNewTransaction(ProceedingJoinPoint joinPoint, Transactional transactional) throws Throwable {
        // 創建 TransactionDefinition，不顯式設定隔離級別，依賴預設值
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();

        if (transactional.propagation() == Propagation.REQUIRES_NEW) {
            // 如果是 Propagation.REQUIRES_NEW，仍然抑制為 Propagation.MANDATORY
            transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_MANDATORY);
            String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
            log.info("Transactional 方法 '{}' 的傳播行為從 REQUIRES_NEW 抑制為 MANDATORY", methodName);
        }

        TransactionStatus transactionStatus = mysqlTransactionManager.getTransaction(transactionDefinition);
        Object result;
        try {
            result = joinPoint.proceed();
            if (!transactionStatus.isCompleted()) {
                mysqlTransactionManager.commit(transactionStatus);
            }
        } catch (Throwable e) {
            if (!transactionStatus.isCompleted()) {
                mysqlTransactionManager.rollback(transactionStatus);
            }
            throw e;
        } finally {
            // 交易清理工作 (如果有)
        }
        return result;
    }
}
