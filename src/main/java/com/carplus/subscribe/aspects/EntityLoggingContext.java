package com.carplus.subscribe.aspects;

import com.carplus.subscribe.db.mysql.entity.change.EntityBase;
import com.carplus.subscribe.model.request.CarsCRSAddRequest;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Getter
@Setter
public class EntityLoggingContext {

    private final ThreadLocal<Boolean> apiInsertionLoggingEnabled = ThreadLocal.withInitial(() -> false);
    private final ThreadLocal<Boolean> apiChangeLoggingEnabled = ThreadLocal.withInitial(() -> false);
    private final ThreadLocal<Boolean> apiDeletionLoggingEnabled = ThreadLocal.withInitial(() -> false);
    // Map entityName to List<EntityInsertion>
    private final ThreadLocal<Map<String, List<EntityBase>>> insertedEntities = ThreadLocal.withInitial(HashMap::new);
    // Map entityName to List<EntityChange>
    private final ThreadLocal<Map<String, List<EntityBase>>> changedEntities = ThreadLocal.withInitial(HashMap::new);
    // Map entityName to List<EntityDeletion>
    private final ThreadLocal<Map<String, List<EntityBase>>> deletedEntities = ThreadLocal.withInitial(HashMap::new);
    private String memo;
    private CarsCRSAddRequest carsCRSAddRequest;

    public void clearInsertion() {
        memo = null;
        carsCRSAddRequest = null;
        apiInsertionLoggingEnabled.set(false);
        insertedEntities.remove();
    }

    public void clearChange() {
        apiChangeLoggingEnabled.set(false);
        changedEntities.remove();
    }

    public void clearDeletion() {
        apiDeletionLoggingEnabled.set(false);
        deletedEntities.remove();
    }
}