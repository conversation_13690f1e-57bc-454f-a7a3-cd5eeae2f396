package com.carplus.subscribe.aspects;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.change.EntityBase;
import com.carplus.subscribe.db.mysql.entity.change.RequestInfo;
import com.carplus.subscribe.service.EntityLogger;
import com.carplus.subscribe.utils.HttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.persistence.Id;
import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Aspect
@Component
public class EntityLoggingAspect {

    private static final String PATCH = "PATCH";
    private static final String POST = "POST";
    private static final String DELETE = "DELETE";

    @Autowired
    private EntityLoggingContext entityLoggingContext;

    @Autowired
    private EntityLogger entityLogger;

    @Around("@annotation(logEntity)")
    public Object logEntityChanges(ProceedingJoinPoint joinPoint, LogEntity logEntity) throws Throwable {

        Object result;

        try {
            // 前置處理邏輯
            Optional<HttpServletRequest> httpServletRequest = HttpRequestUtils.getHttpServletRequest();

            if (httpServletRequest.isPresent()) {
                String httpMethod = httpServletRequest.get().getMethod();
                switch (httpMethod) {
                    case PATCH:
                        entityLoggingContext.getApiChangeLoggingEnabled().set(true);
                        break;
                    case POST:
                        entityLoggingContext.getApiInsertionLoggingEnabled().set(true);
                        String memo = logEntity.memo();
                        if (StringUtils.isNotBlank(memo)) {
                            entityLoggingContext.setMemo(memo);
                        }
                        break;
                    case DELETE:
                        entityLoggingContext.getApiDeletionLoggingEnabled().set(true);
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            // 即使前置處理發生異常，也繼續執行
            log.error("Log Entity 前置處理發生異常", e);
        }

        // 確保一定會執行的核心邏輯
        result = joinPoint.proceed();

        try {
            // 後置處理邏輯
            Optional<HttpServletRequest> httpServletRequest = HttpRequestUtils.getHttpServletRequest();
            if (httpServletRequest.isPresent()) {
                String httpMethod = httpServletRequest.get().getMethod();
                Class<?> mainEntity = logEntity.mainEntity();
                RequestInfo requestInfo = getRequestInfo(httpServletRequest.get(), joinPoint, mainEntity);
                switch (httpMethod) {
                    case PATCH:
                        Map<String, List<EntityBase>> changedEntities = entityLoggingContext.getChangedEntities().get();
                        if (!changedEntities.isEmpty()) {
                            entityLogger.logEntities(requestInfo, mainEntity, changedEntities);
                        }
                        break;
                    case POST:
                        Map<String, List<EntityBase>> insertedEntities = entityLoggingContext.getInsertedEntities().get();
                        if (!insertedEntities.isEmpty()) {
                            if (StringUtils.isNotBlank(logEntity.changedBy())) {
                                requestInfo.setChangedBy(logEntity.changedBy());
                            }
                            entityLogger.logEntities(requestInfo, mainEntity, insertedEntities);
                        }
                        break;
                    case DELETE:
                        entityLogger.logEntities(requestInfo, mainEntity, entityLoggingContext.getDeletedEntities().get());
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            // 後置處理發生異常也不影響結果
            log.error("Log Entity 後置處理發生異常", e);
        } finally {
            entityLoggingContext.clearChange();
            entityLoggingContext.clearInsertion();
            entityLoggingContext.clearDeletion();

            return result;
        }
    }

    private RequestInfo getRequestInfo(HttpServletRequest httpServletRequest, JoinPoint joinPoint, Class<?> mainEntity) {
        // 獲取 requestUrl 和 requestMethod
        String requestUrl = httpServletRequest.getRequestURL().toString();
        String requestMethod = httpServletRequest.getMethod();

        // 獲取方法參數
        Object[] args = joinPoint.getArgs();
        // 獲取方法簽名
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        // 獲取方法參數的註解
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();

        // 遍歷參數註解，找到帶有 @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) 和 @RequestBody 註解的參數
        Object requestBody = null;
        String memberId = null;
        String systemKind = null;
        String changedBy = null;
        String mainEntityId = null;
        for (int i = 0; i < parameterAnnotations.length && (requestBody == null || (memberId == null && systemKind == null)); i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                Object arg = args[i];
                if (annotation instanceof RequestHeader) {
                    String annotationName = ((RequestHeader) annotation).name();
                    switch (annotationName) {
                        case CarPlusConstant.AUTH_HEADER_MEMBER:
                            memberId = Optional.ofNullable(arg).map(Object::toString).orElse(null);
                            changedBy = memberId;
                            break;
                        case CarPlusConstant.AUTH_HEADER_SYSTEM_KIND:
                            systemKind = Optional.ofNullable(arg).map(Object::toString).orElse(null);
                            break;
                        default:
                            break;
                    }
                } else if (annotation instanceof RequestBody && requestBody == null) {
                    requestBody = arg;
                    // Find the field in mainEntity annotated with @Id and get its value from requestBody
                    for (Field mainEntityField : mainEntity.getDeclaredFields()) {
                        if (mainEntityField.isAnnotationPresent(Id.class)) {
                            mainEntityField.setAccessible(true);
                            Object value = getMainEntityIdFromRequestBody(mainEntityField, requestBody);

                            if (value != null) {
                                mainEntityId = value.toString(); // Store the value for the mainEntity ID
                            }
                            break;
                        }
                    }
                }
            }
        }
        // 若 memberId 和 systemKind 都不為 null，則優先選擇 memberId，否則選擇 systemKind；若兩者都為 null，則設為 "Unknown"
        changedBy = (changedBy != null) ? changedBy : (systemKind != null ? systemKind : "Unknown");

        return new RequestInfo(requestUrl, requestMethod, requestBody, changedBy, mainEntityId);
    }

    private Object getMainEntityIdFromRequestBody(Field mainEntityField, Object requestBody) {
        // 獲取 requestBody 所有欄位
        Field[] fields = requestBody.getClass().getDeclaredFields();

        // 查找匹配的欄位
        Optional<Field> matchingField = Arrays.stream(fields)
            .filter(field -> field.getName().equals(mainEntityField.getName()))
            .findFirst();

        // 如果找到匹配的欄位，嘗試獲取其值
        if (matchingField.isPresent()) {
            try {
                Field field = matchingField.get();
                field.setAccessible(true);
                return field.get(requestBody);
            } catch (IllegalAccessException e) {
                throw new RuntimeException("無法存取欄位: " + mainEntityField.getName(), e);
            }
        }

        // 如果沒有找到匹配的欄位，返回 null
        return null;
    }

    @AfterThrowing(pointcut = "@annotation(LogEntity)", throwing = "exception")
    public void handleException(JoinPoint joinPoint, Exception exception) {
        entityLoggingContext.clearChange();
        entityLoggingContext.clearInsertion();
        entityLoggingContext.clearDeletion();
    }
}
