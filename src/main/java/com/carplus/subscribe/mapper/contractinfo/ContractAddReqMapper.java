package com.carplus.subscribe.mapper.contractinfo;

import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ContractEnum;
import com.carplus.subscribe.model.lrental.ContractAddReq;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import com.carplus.subscribe.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
public class ContractAddReqMapper {

    private ContractAddReq createCommon(
        String plateNo,
        Instant startDate,
        Instant endDate,
        String userId,
        String orderNo,
        String rentalTaxID,
        Integer monthlyVatRent,
        Integer securityDeposit,
        Double mileageRate,
        List<String> replaceCodes,
        String memo,
        Cars cars) {

        return ContractAddReq.builder()
            .contractType(ContractEnum.ContractType.add_customer_subscribe)
            .plateNo(plateNo)
            .contractStartDate(DateUtil.transferADDateToMinguoDate(startDate))
            .contractEndDate(DateUtil.transferADDateToMinguoDate(endDate))
            .userId(userId)
            .orderNo(orderNo)
            .rentalTaxID(rentalTaxID)
            .monthlyVatRent(monthlyVatRent)
            .monthlyRent((int) Math.round(monthlyVatRent / 1.05))
            .promiseAmount(securityDeposit)
            .mileageRate(mileageRate)
            .fixAmount((int) (mileageRate * 3000))
            .replaceCode(Optional.ofNullable(replaceCodes)
                .map(list -> list.isEmpty() ? "0" : String.join("", replaceCodes))
                .orElse("0"))
            .contractMemo(memo)
            .carSource(CarDefine.CarState.NEW.equals(cars.getCarState()) ? "0" : "1")
            .build();
    }

    public ContractAddReq fromOrders(Orders orders, LrentalContractRequest request, String memberId, Cars cars, Double mileageFee, Date replaceDate) {
        MainContract mainContract = orders.getContract().getMainContract();
        Instant startDate = Optional.ofNullable(replaceDate).map(Date::toInstant)
            .orElse(Optional.ofNullable(orders.getStartDate()).orElse(orders.getExpectStartDate()));

        return createCommon(
            mainContract.getPlateNo(),
            startDate,
            Optional.ofNullable(orders.getEndDate()).orElse(orders.getExpectEndDate()),
            memberId,
            orders.getOrderNo(),
            mainContract.getIdNo(),
            mainContract.getOriginalPriceInfo().getUseMonthlyFee(),
            mainContract.getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit(),
            mileageFee,
            request.getReplaceCodes(),
            request.getMemo(),
            cars
        );
    }

    public ContractAddReq fromOrders(Orders orders, String memberId, Cars inCar, List<String> lrContractReplaceCodes, String lrContractMemo, Double mileageFee, Date replaceDate) {
        LrentalContractRequest lrentalContractRequest = new LrentalContractRequest();
        lrentalContractRequest.setOrderNo(orders.getOrderNo());
        lrentalContractRequest.setReplaceCodes(lrContractReplaceCodes);
        lrentalContractRequest.setMemo(lrContractMemo);
        return fromOrders(orders, lrentalContractRequest, memberId, inCar, mileageFee, replaceDate);
    }

    public ContractAddReq fromDealerOrder(DealerOrderQueryResponse dealerOrder, LrentalContractRequest request, String memberId, Cars cars) {
        return createCommon(
            dealerOrder.getPlateNo(),
            Optional.ofNullable(dealerOrder.getSubscriptionInfo().getDepartDate())
                .orElse(dealerOrder.getSubscriptionInfo().getExpectDepartDate()),
            Optional.ofNullable(dealerOrder.getSubscriptionInfo().getReturnDate())
                .orElse(dealerOrder.getSubscriptionInfo().getExpectReturnDate()),
            memberId,
            dealerOrder.getOrderNo(),
            dealerOrder.getCustomerInfo().getIdNo(),
            dealerOrder.getSubscriptionInfo().getMonthlyFee(),
            dealerOrder.getSubscriptionInfo().getSecurityDeposit(),
            dealerOrder.getSubscriptionInfo().getActualMileageRate(),
            request.getReplaceCodes(),
            request.getMemo(),
            cars
        );
    }
}
