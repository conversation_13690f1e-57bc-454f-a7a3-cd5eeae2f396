package com.carplus.subscribe.mapper.cars;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.model.cars.resp.CarBrandResponse;
import com.carplus.subscribe.model.request.CarBrandRequest;

public class CarBrandMapper {

    private CarBrandMapper() {}

    public static CarBrandResponse toCarBrandResp(CarBrand carBrand) {
        return CarBrandResponse.builder()
                .brandCode(carBrand.getBrandCode())
                .brandName(carBrand.getBrandName())
                .brandNameEn(carBrand.getBrandNameEn())
                .isAppearOnOfficial(carBrand.isAppearOnOfficial())
                .isShowPage(carBrand.isShowPage())
                .logoImgUrl(carBrand.getLogoImgUrl())
                .carImgUrl(carBrand.getCarImgUrl())
                .seqNo(carBrand.getSeqNo())
                .build();
    }

    public static CarBrand reqToCarBrand(CarBrandRequest request) {
        return CarBrand.builder()
                .brandCode(request.getBrandCode())
                .brandName(request.getBrandName())
                .brandNameEn(request.getBrandNameEn())
                .isAppearOnOfficial(request.isAppearOnOfficial())
                .isShowPage(request.isShowPage())
                .logoImgUrl(request.getLogoImgUrl())
                .carImgUrl(request.getCarImgUrl())
                .build();
    }

    public static CarBrand toCarBrandForUpdate(CarBrandRequest request, CarBrand carBrand) {
        carBrand.setBrandCode(request.getBrandCode());
        carBrand.setBrandName(request.getBrandName());
        carBrand.setBrandNameEn(request.getBrandNameEn());
        carBrand.setAppearOnOfficial(request.isAppearOnOfficial());
        carBrand.setShowPage(request.isShowPage());
        carBrand.setLogoImgUrl(request.getLogoImgUrl());
        carBrand.setCarImgUrl(request.getCarImgUrl());
        return carBrand;
    }
}
