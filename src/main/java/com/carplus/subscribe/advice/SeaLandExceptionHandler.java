package com.carplus.subscribe.advice;

import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.enums.CsatOrderSource;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.service.NotifyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.lang.reflect.Method;
import java.util.*;

@RestControllerAdvice(basePackages = "com.carplus.subscribe.controller.dealer")
@Slf4j
public class SeaLandExceptionHandler {

    // 避免循環依賴問題
    @Lazy
    @Autowired
    private NotifyService notifyService;

    @Autowired
    private RequestMappingHandlerMapping handlerMapping;

    @Autowired
    private ObjectMapper objectMapper;

    // 需要處理異常的 API 端點 (HTTP Method + URI Pattern)
    private final Map<String, Set<String>> handledEndpoints = new HashMap<>();

    public SeaLandExceptionHandler() {
        handledEndpoints.put("POST", new HashSet<>(Collections.singletonList(
            "/internal/subscribe/dealerOrder"
        )));
        handledEndpoints.put("PATCH", new HashSet<>(Arrays.asList(
            "/internal/subscribe/dealerOrder",
            "/internal/subscribe/dealerOrder/depart",
            "/internal/subscribe/dealerOrder/close",
            "/internal/subscribe/dealerOrder/cancel"
        )));
    }

    @ExceptionHandler(SubscribeException.class)
    public void handleSeaLandSubscribeException(SubscribeException ex, HttpServletRequest request) {
        String systemKind = request.getHeader(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND);
        String requestURI = request.getRequestURI();

        // 只有 systemKind 為 SEALAND 且為 dealerOrder 相關端點才需要特殊處理
        boolean shouldNotify = CsatOrderSource.SEALAND.name().equals(systemKind)
            && requestURI.contains("/internal/subscribe/dealerOrder")
            && shouldHandleEndpoint(request);

        if (shouldNotify) {
            // 執行 SEALAND 特殊通知邏輯
            String syncType = getOperationSummary(request);
            String orderNo = extractOrderNoFromRequest(request);

            notifyService.notifySeaLandSyncFail(orderNo, syncType, ex.getReason(), systemKind);

            log.info("SEALAND 同步失敗通知已發送，訂單編號: {}, 同步類別: {}, 錯誤訊息: {}", orderNo, syncType, ex.getMessage());
        }

        // 無論是否發送通知，都要重新拋出異常以維持正常的錯誤處理流程
        throw ex;
    }

    /**
     * 檢查是否應該處理此端點的異常
     */
    private boolean shouldHandleEndpoint(HttpServletRequest request) {
        String method = request.getMethod();
        String requestURI = request.getRequestURI();

        Set<String> uriPatterns = handledEndpoints.get(method);
        if (uriPatterns == null) {
            return false;
        }

        return uriPatterns.stream().anyMatch(requestURI::equals);
    }

    /**
     * 通過 Spring HandlerMapping 獲取當前請求對應的 @Operation summary
     */
    private String getOperationSummary(HttpServletRequest request) {
        try {
            // 獲取當前請求的處理器方法
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain == null) {
                return "未知操作";
            }

            Object handler = handlerChain.getHandler();
            if (!(handler instanceof HandlerMethod)) {
                return "未知操作";
            }

            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();

            // 獲取 @Operation 註解
            Operation operation = method.getAnnotation(Operation.class);
            if (operation != null && !operation.summary().isEmpty()) {
                return operation.summary();
            }

            return "未知操作";
        } catch (Exception e) {
            log.warn("無法取得操作方法名稱", e);
            return "未知操作";
        }
    }

    private String extractOrderNoFromRequest(HttpServletRequest request) {
        try {
            String method = request.getMethod();

            // 對於 POST/PATCH 請求，優先從 RequestBody 中獲取
            if ("POST".equals(method) || "PATCH".equals(method)) {
                String orderNoFromBody = extractOrderNoFromRequestBody(request);
                if (orderNoFromBody != null) {
                    return orderNoFromBody;
                }
            }

            // 然後嘗試從路徑變數中獲取 (e.g., /dealerOrder/{orderNo})
            String orderNoFromPath = extractOrderNoFromPath(request);
            if (orderNoFromPath != null) {
                return orderNoFromPath;
            }

            // 最後從查詢參數中獲取
            String orderNoFromParam = request.getParameter("orderNo");
            if (orderNoFromParam != null && !orderNoFromParam.trim().isEmpty()) {
                return orderNoFromParam;
            }
        } catch (Exception e) {
            log.warn("無法從請求中提取 orderNo", e);
        }

        return "未知訂單";
    }

    /**
     * 從 RequestBody 中提取 orderNo
     */
    private String extractOrderNoFromRequestBody(HttpServletRequest request) {
        try {
            // 讀取請求體
            StringBuilder sb = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }

            // 提取 orderNo
            return objectMapper.readTree(sb.toString()).get("orderNo").asText();
        } catch (Exception e) {
            log.debug("無法從請求主體中提取 orderNo", e);
        }
        return null;
    }

    /**
     * 從 URL 路徑中提取 orderNo
     */
    private String extractOrderNoFromPath(HttpServletRequest request) {
        try {
            // 獲取當前請求的處理器方法
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain == null) {
                return null;
            }

            Object handler = handlerChain.getHandler();
            if (!(handler instanceof HandlerMethod)) {
                return null;
            }

            // 從 Spring 的路徑變數中直接獲取 orderNo
            Map<String, String> pathVariables = (Map<String, String>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            if (pathVariables != null && pathVariables.containsKey("orderNo")) {
                String orderNo = pathVariables.get("orderNo");
                if (orderNo != null && !orderNo.trim().isEmpty()) {
                    return orderNo;
                }
            }

        } catch (Exception e) {
            log.debug("無法從路徑變數中提取 orderNo", e);
        }

        return null;
    }
}