package com.carplus.subscribe.listener;

import com.carplus.subscribe.aspects.PriceInfoTrackingContext;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.utils.PriceInfoUtils;
import org.hibernate.event.spi.PreInsertEvent;
import org.hibernate.event.spi.PreInsertEventListener;
import org.hibernate.event.spi.PreUpdateEvent;
import org.hibernate.event.spi.PreUpdateEventListener;
import org.springframework.stereotype.Component;

@Component
public class OrderPriceInfoListener implements PreInsertEventListener, PreUpdateEventListener {

    @Override
    public boolean onPreInsert(PreInsertEvent event) {
        Object entity = event.getEntity();
        if (!(entity instanceof OrderPriceInfo)) {
            return false; // 繼續執行 INSERT
        }

        OrderPriceInfo priceInfo = (OrderPriceInfo) entity;
        String memberId = PriceInfoTrackingContext.getMemberId();
        if (memberId != null) {
            PriceInfoUtils.setUpdater(priceInfo, memberId);
        }
        return false; // 繼續執行 INSERT
    }

    @Override
    public boolean onPreUpdate(PreUpdateEvent event) {
        Object entity = event.getEntity();
        if (!(entity instanceof OrderPriceInfo)) {
            return false; // 繼續執行 UPDATE
        }

        OrderPriceInfo priceInfo = (OrderPriceInfo) entity;
        String memberId = PriceInfoTrackingContext.getMemberId();
        if (memberId != null) {
            PriceInfoUtils.setUpdater(priceInfo, memberId);
        }
        return false; // 繼續執行 UPDATE
    }
}

