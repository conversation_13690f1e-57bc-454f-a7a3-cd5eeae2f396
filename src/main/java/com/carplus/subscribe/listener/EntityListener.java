package com.carplus.subscribe.listener;

import com.carplus.subscribe.aspects.EntityLoggingContext;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.change.*;
import com.carplus.subscribe.model.request.CarsCRSAddRequest;
import com.carplus.subscribe.service.EntityLogger;
import com.carplus.subscribe.utils.HttpRequestUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.event.spi.*;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.persistence.Id;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

@Slf4j
@Component
public class EntityListener implements PostInsertEventListener, PostUpdateEventListener, PostDeleteEventListener {

    @Autowired
    private EntityLoggingContext entityLoggingContext;

    @Autowired
    private List<EntityChangeProcessor> entityChangeProcessors;

    @Autowired
    private EntityLogger entityLogger;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void onPostInsert(PostInsertEvent event) {

        Boolean isApiLoggingEnabled = entityLoggingContext.getApiInsertionLoggingEnabled().get();
        Object entity = event.getEntity();

        // Guard clause：如果未啟用 API 記錄且實體不是 Cars，直接返回
        if (!isApiLoggingEnabled && !(entity instanceof Cars)) {
            return;
        }

        String entityName = entity.getClass().getSimpleName();
        String entityId = getEntityId(entity);

        // 處理 Cars 特殊情況
        if (!isApiLoggingEnabled) {
            EntityInsertion entityInsertion = new EntityInsertion(entityName, entityId, "車籍新增");
            HashMap<String, List<EntityBase>> insertedEntities = new HashMap<>();
            insertedEntities.put(entityName, Collections.singletonList(entityInsertion));
            RequestInfo requestInfo = getCarsLoggingRequestInfo(entityId);
            entityLogger.logEntities(requestInfo, Cars.class, insertedEntities);
            entityLoggingContext.clearInsertion();
            return;
        }

        // 處理 API 記錄啟用的情況
        String insertionMemo = determineInsertionMemo(entity);
        EntityInsertion entityInsertion = new EntityInsertion(entityName, entityId, insertionMemo);
        entityLoggingContext.getInsertedEntities().get().computeIfAbsent(entityName, k -> new ArrayList<>()).add(entityInsertion);
    }

    private RequestInfo getCarsLoggingRequestInfo(String entityId) {
        Optional<HttpServletRequest> httpServletRequest = HttpRequestUtils.getHttpServletRequest();

        // 須為 requestUrl 提供預設值
        String requestUrl = httpServletRequest.map(servletRequest -> servletRequest.getRequestURL().toString()).orElse("SYSTEM");

        // 須為 requestMethod 提供預設值
        String requestMethod = httpServletRequest.map(HttpServletRequest::getMethod).orElse("SYSTEM");

        // 須為 requestBody 提供預設值
        Object requestBody = "SYSTEM";
        if (httpServletRequest.isPresent()) {
            try (ServletInputStream inputStream = httpServletRequest.get().getInputStream()) {
                if (inputStream != null && inputStream.available() > 0) {
                    Object body = objectMapper.readValue(inputStream, Object.class);
                    if (body != null) {
                        requestBody = body;
                    }
                }
            } catch (IOException e) {
                // 發生錯誤時使用預設值，記錄錯誤但不中斷流程
                log.error("讀取請求內容時發生錯誤", e);
            }
        }

        return new RequestInfo(requestUrl, requestMethod, requestBody, "SUB", entityId);
    }

    private String determineInsertionMemo(Object entity) {
        if (isCarAddedForBuChange(entity)) {
            CarsCRSAddRequest request = entityLoggingContext.getCarsCRSAddRequest();
            return String.format("撥車 (單號: %d；事由: %s)", request.getBuChangeMasterId(), request.getChangeMemo());
        }
        return entityLoggingContext.getMemo();
    }

    private boolean isCarAddedForBuChange(Object entity) {
        if (!(entity instanceof Cars)) {
            return false;
        }
        CarsCRSAddRequest request = entityLoggingContext.getCarsCRSAddRequest();
        return request != null && request.getBuChangeMasterId() != null && request.getChangeMemo() != null;
    }

    @Override
    public void onPostUpdate(PostUpdateEvent event) {
        Boolean isApiLoggingEnabled = entityLoggingContext.getApiChangeLoggingEnabled().get();
        Object entity = event.getEntity();

        // Guard clause：如果未啟用 API 記錄且實體不是 Cars，直接返回
        if (!isApiLoggingEnabled && !(entity instanceof Cars)) {
            return;
        }

        String entityName = entity.getClass().getSimpleName();
        String entityId = getEntityId(entity);
        List<FieldChange> fieldChanges = getFieldChanges(event);

        // 如果沒有變更，直接返回
        if (fieldChanges.isEmpty()) {
            return;
        }

        // 處理 Cars 特殊情況
        if (!isApiLoggingEnabled) {
            EntityChange entityChange = new EntityChange(entityName, entityId, fieldChanges);
            HashMap<String, List<EntityBase>> changedEntities = new HashMap<>();
            changedEntities.put(entityName, Collections.singletonList(entityChange));
            RequestInfo requestInfo = getCarsLoggingRequestInfo(entityId);
            entityLogger.logEntities(requestInfo, Cars.class, changedEntities);
            entityLoggingContext.clearChange();
            return;
        }

        // 處理 API 記錄啟用的情況
        EntityChange entityChange = new EntityChange(entityName, entityId, fieldChanges);
        entityLoggingContext.getChangedEntities()
            .get()
            .computeIfAbsent(entityName, k -> new ArrayList<>())
            .add(entityChange);
    }

    private List<FieldChange> getFieldChanges(PostUpdateEvent event) {
        String[] propertyNames = event.getPersister().getPropertyNames();
        Object[] oldState = event.getOldState();
        Object[] newState = event.getState();

        List<FieldChange> fieldChanges = new ArrayList<>();
        for (int i = 0; i < propertyNames.length; i++) {
            if (!Objects.equals(oldState[i], newState[i])) {
                processFieldChange(event.getEntity(), propertyNames[i], oldState[i], newState[i], fieldChanges);
            }
        }
        return fieldChanges;
    }

    private void processFieldChange(Object entity, String propertyName, Object oldValue, Object newValue, List<FieldChange> fieldChanges) {
        for (EntityChangeProcessor processor : entityChangeProcessors) {
            if (processor.supports(entity.getClass(), propertyName)) {
                fieldChanges.addAll(processor.process(propertyName, oldValue, newValue));
                return;
            }
        }
        fieldChanges.add(new FieldChange(propertyName, oldValue, newValue));
    }

    private String getEntityId(Object entity) {
        for (Field field : entity.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(Id.class)) {
                field.setAccessible(true);
                try {
                    return field.get(entity).toString();
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        throw new RuntimeException("Entity does not have an ID field.");
    }

    @Override
    public void onPostDelete(PostDeleteEvent postDeleteEvent) {
        if (!entityLoggingContext.getApiDeletionLoggingEnabled().get()) {
            return;
        }

        Object entity = postDeleteEvent.getEntity();

        entityLoggingContext.getDeletedEntities().get()
            .computeIfAbsent(entity.getClass().getSimpleName(), k -> new ArrayList<>())
            .add(new EntityDeletion(entity.getClass().getSimpleName(), getEntityId(entity), entity));
    }

    @Override
    public boolean requiresPostCommitHanding(EntityPersister entityPersister) {
        return false;
    }
}
