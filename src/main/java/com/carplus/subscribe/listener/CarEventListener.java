package com.carplus.subscribe.listener;

import carplus.common.enums.CRS.CRS;
import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.event.car.CarCalculateMileageEvent;
import com.carplus.subscribe.event.car.CarDepartEvent;
import com.carplus.subscribe.event.car.CarReplaceEvent;
import com.carplus.subscribe.event.car.CarReturnEvent;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.service.CrsService;
import com.carplus.subscribe.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.Objects;

@Slf4j
@Component
public class CarEventListener {

    @Autowired
    private CrsService crsService;

    @Async
    @TransactionalEventListener
    public void handleCarDepartEvent(CarDepartEvent event) {

        String orderNo = event.getCarUpdateKmDto().getOrderNo();
        log.info("出車 - 長放車註冊事件, orderNo: {} 開始處理", orderNo);

        // crsCarInfo 為 null 或 crsCarNo 為 null 時，不發動事件處理並記錄
        CarBaseInfoSearchResponse crsCarInfo = event.getCrsCarInfo();
        if (crsCarInfo == null || crsCarInfo.getCarNo() == null) {
            log.warn("出車 - 長放車註冊事件不處理, crsCarInfo: {}, crsCarNo: null", crsCarInfo);
            return;
        }

        try {
            crsService.handleLongTermCarRegistration(crsCarInfo, orderNo, event.getMemberId());
        } catch (Exception e) {
            log.error("出車 - 長放車註冊事件失敗", e);
        }

        // 向 CRS 更新車輛最新里程數 (dealerOrder 暫無登打出車里程，先不執行)
        if (event.getSource() instanceof OrderService) {
            crsService.updateKm(event.getCarUpdateKmDto(), crsCarInfo.getCarNo());
        }
    }

    @Async
    @TransactionalEventListener
    public void handleCarReturnEvent(CarReturnEvent event) {

        String orderNo = event.getCarUpdateKmDto().getOrderNo();
        log.info("還車 - 長放車註銷事件, orderNo: {} 開始處理", orderNo);

        // crsCarInfo 為 null 或 crsCarNo 為 null 時，不發動事件處理並記錄
        CarBaseInfoSearchResponse crsCarInfo = event.getCarCrsInfo();
        if (crsCarInfo == null || crsCarInfo.getCarNo() == null) {
            log.warn("還車 - 長放車註銷事件不處理, carCrsInfo: {}, carCrsNo: null", crsCarInfo);
            return;
        }

        try {
            crsService.handleLongTermCarCancellation(crsCarInfo, orderNo, event.getMemberId());
        } catch (Exception e) {
            log.error("還車 - 長放車註銷事件失敗", e);
        }

        // 向 CRS 更新車輛最新里程數 (dealerOrder 暫無登打還車里程，先不執行)
        if (event.getSource() instanceof OrderService) {
            crsService.updateKm(event.getCarUpdateKmDto(), crsCarInfo.getCarNo());
        }
    }

    @Async
    @TransactionalEventListener
    public void handleCarReplaceEvent(CarReplaceEvent event) {

        String orderNo = event.getInCarUpdateKmDto().getOrderNo();
        log.info("出車中換車 - 長放車註冊 & 註銷事件, orderNo: {} 開始處理", orderNo);

        CarBaseInfoSearchResponse outCrsCarInfo = event.getOutCarCrsInfo();
        CarBaseInfoSearchResponse inCrsCarInfo = event.getInCarCrsInfo();

        // 檢查車輛是否符合 CRS 處理條件
        boolean canHandleOutCar = isValidForCrsProcessing(outCrsCarInfo);
        boolean canHandleInCar = isValidForCrsProcessing(inCrsCarInfo);

        // 長放車註冊 & 註銷事件處理 (需要兩台車都符合條件)
        if (canHandleOutCar && canHandleInCar) {
            try {
                crsService.handleCarReplacement(outCrsCarInfo, inCrsCarInfo, orderNo, event.getMemberId());
                log.info("出車中換車 - 長放車註冊 & 註銷事件處理完成, orderNo: {}", orderNo);
            } catch (Exception e) {
                log.error("出車中換車 - 長放車註冊 & 註銷事件失敗, orderNo: {}", orderNo, e);
            }
        } else {
            log.warn("出車中換車 - 長放車註冊 & 註銷事件不處理, orderNo: {}, 原因: 車輛不符合條件(buId=4 && 車牌為購入), "
                + "outCar[{}], inCar[{}]", orderNo, getCrsCarInfoForLog(outCrsCarInfo), getCrsCarInfoForLog(inCrsCarInfo));
        }

        // 向 CRS 更新汰換車輛最新里程數
        if (canHandleOutCar) {
            updateCarKm(event.getOutCarUpdateKmDto(), outCrsCarInfo, orderNo, "汰換車輛");
        } else {
            log.info("出車中換車 - 汰換車輛里程數更新跳過, orderNo: {}, 原因: 車輛不符合條件(buId=4 && 車牌為購入), [{}]",
                orderNo, getCrsCarInfoForLog(outCrsCarInfo));
        }

        // 向 CRS 更新替代車輛最新里程數
        if (canHandleInCar) {
            updateCarKm(event.getInCarUpdateKmDto(), inCrsCarInfo, orderNo, "替代車輛");
        } else {
            log.info("出車中換車 - 替代車輛里程數更新跳過, orderNo: {}, 原因: 車輛不符合條件(buId=4 && 車牌為購入), [{}]",
                orderNo, getCrsCarInfoForLog(inCrsCarInfo));
        }
    }

    /**
     * 檢查車輛是否符合 CRS 處理條件 (buId=4 && 車牌為購入)
     */
    private boolean isValidForCrsProcessing(CarBaseInfoSearchResponse carInfo) {
        return carInfo != null
            && carInfo.getCarNo() != null
            && Objects.equals(BuIdEnum.subscribe.getCode(), carInfo.getBuId())
            && carInfo.getCarLicense() != null
            && CRS.LicenseStatus.BUY.getCode().equals(carInfo.getCarLicense().getLicenseStatus());
    }

    /**
     * 更新車輛里程數
     */
    private void updateCarKm(CarUpdateKmDto updateKmDto, CarBaseInfoSearchResponse carInfo, String orderNo, String carType) {
        try {
            crsService.updateKm(updateKmDto, carInfo.getCarNo());
            log.info("出車中換車 - {}里程數更新完成, orderNo: {}, carNo: {}", carType, orderNo, carInfo.getCarNo());
        } catch (Exception e) {
            log.error("出車中換車 - {}里程數更新失敗, orderNo: {}, carNo: {}", carType, orderNo, carInfo.getCarNo(), e);
        }
    }

    /**
     * 格式化車輛資訊用於 log 輸出
     */
    private String getCrsCarInfoForLog(CarBaseInfoSearchResponse carInfo) {
        if (carInfo == null) {
            return "CRS carNo: null, buId: null, licenseStatus: null";
        }
        return String.format("CRS carNo: %d, buId: %d, licenseStatus: %s",
            carInfo.getCarNo(),
            carInfo.getBuId(),
            carInfo.getCarLicense() != null ? carInfo.getCarLicense().getLicenseStatus() : null);
    }

    @Async
    @TransactionalEventListener
    public void handleCarCalculateMileageEvent(CarCalculateMileageEvent event) {

        String orderNo = event.getCarUpdateKmDto().getOrderNo();
        log.info("車輛中途結算里程 - 更新 CRS 車輛里程數, orderNo: {}", orderNo);

        crsService.updateKm(event.getCarUpdateKmDto(), event.getCrsCarNo());
    }
}
