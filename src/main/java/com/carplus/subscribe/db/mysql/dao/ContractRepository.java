package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Repository
public class ContractRepository extends SimpleJpaRepository<Contract, String> {

    private final EntityManager em;

    public ContractRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Contract.class, em);
        this.em = em;
    }

    /**
     * 拿取主合約下最大期數
     */
    public Integer getContractMaxStage(String mainContractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Integer> cq = cb.createQuery(Integer.class);
        Root<Contract> root = cq.from(Contract.class);
        cq.select(cb.max(root.get("stage"))).where(cb.equal(root.get("mainContractNo"), mainContractNo));
        return em.createQuery(cq).getSingleResult();
    }

    /**
     * 拿取主合約下最大期數合約
     */
    public Contract getMaxStageContract(String mainContractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Contract> cq = cb.createQuery(Contract.class);
        Root<Contract> root = cq.from(Contract.class);
        cq.select(root).where(cb.equal(root.get("mainContractNo"), mainContractNo)).orderBy(cb.desc(root.get("stage")));
        return em.createQuery(cq).setMaxResults(1).getSingleResult();
    }

    /**
     * 取得主合約下所有合約
     */
    public List<Contract> getContractsByMainContractNo(String mainContractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Contract> cq = cb.createQuery(Contract.class);
        Root<Contract> root = cq.from(Contract.class);
        cq.select(root).where(cb.equal(root.get("mainContractNo"), mainContractNo)).orderBy(cb.desc(root.get("stage")));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 取得最後的合約編號
     */
    public String getLastContractNo() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<Contract> root = cq.from(Contract.class);
        cq.select(root.get("contractNo")).orderBy(cb.desc(root.get("contractNo")));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 拿取合約並同步拿取合約之下的訂單
     */
    public Contract getContractOrders(String contractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Contract> cq = cb.createQuery(Contract.class);
        Root<Contract> root = cq.from(Contract.class);
        cq.select(root).where(cb.equal(root.get("contractNo"), contractNo));
        try {
            em.clear();
            Contract contract = em.createQuery(cq).setMaxResults(1).getSingleResult();
            Hibernate.initialize(contract.getOrders());
            return contract;
        } catch (NoResultException e) {
            return null;
        }
    }
}
