package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.model.request.CarModelQueryRequest;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.Tuple;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

@Repository
public class CarModelRepository extends SimpleJpaRepository<CarModel, String> {

    private final EntityManager em;

    public CarModelRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarModel.class, em);
        this.em = em;
    }

    public void updateIsDeleted(String carModelCode, boolean isDeleted) {
        em.createQuery("update CarModel cm set cm.isDeleted = :isDeleted where cm.carModelCode = :carModelCode")
            .setParameter("carModelCode", carModelCode)
            .setParameter("isDeleted", isDeleted)
            .executeUpdate();
    }

    public CarModel findByCarModelCode(String carModelCode) {
        try {
            return em.createQuery("select cm from CarModel cm where cm.carModelCode = :carModelCode", CarModel.class)
                .setParameter("carModelCode", carModelCode)
                .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public CarModel findByBrandCode(String brandCode) {
        try {
            return em.createQuery("select cm from CarModel cm where cm.brandCode = :brandCode", CarModel.class)
                    .setParameter("brandCode", brandCode)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 透過一組carModelCode找尋CarModel清單
     * 會透過Partition一次500筆
     */
    public List<CarModel> findByCarModelCodes(List<String> carModelCods) {
        try {
            List<CarModel> result = new ArrayList<>();
            for (List<String> partition : Lists.partition(carModelCods, 500)) {
                result.addAll(this.findAll((Specification<CarModel>) ((root, query, builder) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(root.get(CarModel.Fields.carModelCode).in(partition));
                    return builder.and(predicates.toArray(new Predicate[0]));
                })));
            }
            return result;
        } catch (NoResultException e) {
            return null;
        }
    }

    public CarModel findByOriginalCarModelCode(String originalCarModelCode) {
        try {
            return em.createQuery("select cm from CarModel cm where cm.originalCarModelCode = :originalCarModelCode", CarModel.class)
                .setParameter("originalCarModelCode", originalCarModelCode)
                .getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 系統台車型列表查詢
     */
    @NonNull
    @Transactional(readOnly = true, transactionManager = "mysqlTransactionManager")
    public List<Tuple> findBySearch(@NonNull CarModelQueryRequest queryRequest,
                                    @Nullable Integer limit, @Nullable Integer offset) {
        String sql =
            "select "
                + "cm.brandCode, cb.brandName,cb.brandNameEn, cm.carModelName, cm.carModelCode, cm.carKind, cm.isDeleted, cm.originalCarModelCode, "
                + "IF(img.carModelCode IS NULL, NULL, JSON_ARRAYAGG(JSON_OBJECT('paths', img.paths, 'year', img.year))) AS images "
                + "from car_model cm "
                + "left join car_brand cb on cm.brandCode = cb.brandCode "
                + "left join car_model_image img on cm.carModelCode = img.carModelCode ";

        // 查詢條件
        sql += criteriaSearchSql(queryRequest);
        sql += " group by cm.carModelCode  ";
        // 分頁
        if (limit != null) {
            sql += "limit " + limit;
            if (offset != null) {
                sql += " offset " + offset + " ";
            }
        }

        Query q = em.createNativeQuery(sql, Tuple.class);
        criteriaSearchParameter(queryRequest, q);
        //noinspection unchecked
        return ((List<Tuple>) q.getResultList());
    }

    /**
     * 系統台車型列表查詢條件
     */
    private String criteriaSearchSql(@NonNull CarModelQueryRequest queryRequest) {
        List<String> condition = Lists.newArrayList();

        if (queryRequest.getBrandCodes() != null && queryRequest.getBrandCodes().length > 0) {
            condition.add("cm.brandCode in :brandCodes");
        }
        if (queryRequest.getCarModelCodes() != null && queryRequest.getCarModelCodes().length > 0) {
            condition.add("cm.carModelCode in :carModelCodes");
        }

        if (condition.isEmpty()) {
            return "";
        }

        return "where " + String.join(" and ", condition) + " ";
    }

    /**
     * 系統台車型列表查詢條件參數
     */
    private void criteriaSearchParameter(@NonNull CarModelQueryRequest queryRequest, @NonNull Query query) {
        if (queryRequest.getBrandCodes() != null && queryRequest.getBrandCodes().length > 0) {
            query.setParameter("brandCodes", Lists.newArrayList(queryRequest.getBrandCodes()));
        }
        if (queryRequest.getCarModelCodes() != null && queryRequest.getCarModelCodes().length > 0) {
            query.setParameter("carModelCodes", Lists.newArrayList(queryRequest.getCarModelCodes()));
        }
    }

    /**
     * 名稱找尋車型
     */
    public List<CarModel> getCarModelByName(String name) {
        return this.findAll((Specification<CarModel>) (root, query, builder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(builder.equal(root.get(CarModel.Fields.carModelName), name));
            return builder.and(predicates.toArray(new Predicate[0]));
        });
    }

    /**
     * 車型代碼序列
     */
    @NonNull
    public String nextSeq() {
        String max = getMax();

        return nextSeq(max);
    }

    /**
     * 車型代碼編碼
     */
    @NonNull
    private String nextSeq(@Nullable String current) {
        if (StringUtils.isBlank(current) || !current.startsWith("S")) {
            return "S0000";
        }

        return Long.toString(Integer.parseInt(current, 36) + 1, 36).toUpperCase();
    }

    private String getMax() {
        String max = null;
        try {
            max = em.createQuery("select MAX(carModelCode) from CarModel", String.class).getSingleResult();
        } catch (NoResultException ignore) {
            return null;
        }
        return max;
    }


}
