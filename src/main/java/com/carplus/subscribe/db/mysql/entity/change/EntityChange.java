package com.carplus.subscribe.db.mysql.entity.change;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@FieldNameConstants
public class EntityChange extends EntityBase {
    private List<? extends FieldChange> fieldChanges;

    public EntityChange(String entityName, String entityId, List<FieldChange> fieldChanges) {
        super(entityName, entityId);
        this.fieldChanges = fieldChanges;
    }
}
