package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.cars.CarModelImage;
import com.carplus.subscribe.db.mysql.entity.cars.CarModelImagePK;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

@Repository
public class CarModelImageRepository extends SimpleJpaRepository<CarModelImage, CarModelImagePK> {

    private final EntityManager em;

    public CarModelImageRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarModelImage.class, em);
        this.em = em;
    }

    @NonNull
    public List<CarModelImage> findByCarModelCode(@NonNull String carModelCode) {
        return findAll((root, query, builder) ->
            builder.and(
                builder.equal(root.get(CarModelImage.Fields.carModelCode), carModelCode)));
    }

    /**
     * 透過一組carModelCode找尋CarModel清單
     * 會透過Partition一次500筆
     */
    public List<CarModelImage> findByCarModelImages(List<String> carModelCods) {
        try {
            List<CarModelImage> result = new ArrayList<>();
            for (List<String> partition : Lists.partition(carModelCods, 500)) {
                result.addAll(this.findAll((Specification<CarModelImage>) ((root, query, builder) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(root.get(CarModelImage.Fields.carModelCode).in(partition));
                    return builder.and(predicates.toArray(new Predicate[0]));
                })));
            }
            return result;
        } catch (NoResultException e) {
            return null;
        }
    }
}
