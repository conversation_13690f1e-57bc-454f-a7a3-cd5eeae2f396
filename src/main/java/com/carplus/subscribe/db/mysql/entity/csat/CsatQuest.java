package com.carplus.subscribe.db.mysql.entity.csat;


import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "csat_quest")
@Data
public class CsatQuest extends GeneralEntity {

    @Id
    private Integer csatId;

    private Integer q0;

    private Integer q1;

    private Integer q2;

    private Integer q3;

    private Integer q4;

    private Integer q5;

    private Integer q6;

    private String q7;

    private Integer version;


}
