package com.carplus.subscribe.db.mysql.entity;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.aspects.ChineseName;
import com.carplus.subscribe.db.mysql.CustomizedJsonStringType;
import com.carplus.subscribe.enums.StationDefine;
import com.carplus.subscribe.model.Landmark;
import com.carplus.subscribe.model.Photo;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.springframework.lang.NonNull;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 站所表
 */
@Data
@Entity
@Table(name = "stations")
@FieldNameConstants
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@TypeDef(name = "json", typeClass = CustomizedJsonStringType.class)
public class Stations {

    /**
     * 站所代碼
     */
    @ChineseName("站所代碼")
    @Id
    @Column(name = "stationCode", nullable = false)
    private String stationCode;

    /**
     * 上級站所代碼
     */
    @Column(name = "parentStationCode")
    private String parentStationCode;

    /**
     * 站所中文名
     */
    @Column(name = "stationName", nullable = false)
    private String stationName;

    /**
     * 站所英文名
     */
    @Column(name = "stationNameEn")
    private String stationNameEn;

    /**
     * 站所短名
     */
    @Column(name = "shortName", nullable = false)
    private String shortName;

    /**
     * 部門代碼
     */
    @Column(name = "orgLevelCode", nullable = false)
    private String orgLevelCode;

    /**
     * 新部門代碼(權限相關)
     */
    @Column(name = "newLevelCode")
    private String newLevelCode;

    /**
     * 站所性質 GENERAL:一般門市, COURIER:驛站, VSHOP:V-SHOP, DEALER:經銷商
     */
    @ChineseName("站所性質")
    @Column(name = "stationCategory", nullable = false)
    @Enumerated(EnumType.STRING)
    private StationDefine.StationCategory stationCategory;

    /**
     * 站所類型
     */
    @Column(name = "stationType", nullable = false)
    private Integer stationType;

    /**
     * 分類id 1:客車, 2:貨車, 4:電動車, 8:電動機車
     */
    @Column(name = "stationMode", nullable = false)
    private Integer stationMode;

    /**
     * 站所城市分區
     */
    @Column(name = "cityId", nullable = false)
    private Integer cityId;

    /**
     * 短租所在區域 N:北部, C:中部, S:南部, E:東部
     */
    @Column(name = "geoRegion", nullable = false)
    private String geoRegion;

    /**
     * 所在區域 N:北部, C:中部, S:南部, E:東部
     */
    @Column(name = "locateGeoRegion", nullable = false)
    private String locateGeoRegion;

    /**
     * 站所分區 0:雙北, 1:桃竹苗, 2:中彰投, 3:雲嘉南, 4:高屏, 5:宜蘭, 6:花蓮, 7:玉里, 8:台東
     */
    @Column(name = "stationRegion", nullable = false)
    private Integer stationRegion;

    /**
     * 中文地址
     */
    @Column(name = "addr", nullable = false)
    private String addr;

    /**
     * 英文地址
     */
    @Column(name = "addrEn")
    private String addrEn;

    /**
     * 聯絡電話
     */
    @Column(name = "tel", nullable = false)
    private String tel;

    /**
     * 傳真
     */
    @Column(name = "fax")
    private String fax;

    /**
     * 電子郵件
     */
    @Column(name = "email", nullable = false)
    private String email;

    /**
     * 經度
     */
    @Column(name = "lng", nullable = false)
    private Double lng;

    /**
     * 緯度
     */
    @Column(name = "lat", nullable = false)
    private Double lat;

    /**
     * 中文簡介
     */
    @Column(name = "descr")
    private String descr;

    /**
     * 英文簡介
     */
    @Column(name = "descrEn")
    private String descrEn;

    /**
     * 營業起始時間(HHMM)
     */
    @Column(name = "startHours", nullable = false)
    private String startHours;

    /**
     * 營業結束時間(HHMM)
     */
    @Column(name = "endHours", nullable = false)
    private String endHours;

    /**
     * 附加電子郵件
     */
    @Column(name = "ccEmail")
    private String ccEmail;

    /**
     * 發票統編
     */
    @Column(name = "vatNo", nullable = false)
    private String vatNo;

    /**
     * 靜態地圖
     */
    @Column(name = "staticMap", nullable = false)
    private String staticMap;

    /**
     * 附近地標資訊
     */
    @Column(name = "landmarks")
    @Type(type = "json")
    private List<Landmark> landmarks;

    /**
     * 照片
     */
    @Column(name = "photos")
    @Type(type = "json")
    private List<Photo> photos;

    /**
     * 站所狀態 A:啟用, D:停用
     */
    @ChineseName("站所狀態")
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 站所結束營業時間
     */
    @Column(name = "closeDate")
    private Date closeDate;

    /**
     * 是否為訂閱站點
     */
    @ChineseName(value = "是否為訂閱站點")
    @Column(name = "isSubscribe")
    private Boolean isSubscribe;

    /**
     * 是否刪除 0:無刪除 1:刪除
     */
    @Column(name = "isDeleted")
    private boolean isDeleted;

    /**
     * 業務別 SUB:訂閱, SHORT_RENT:短租, PREOWNED:中古
     */
    @Column(name = "carplusService")
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private StationDefine.CarplusService carplusService = StationDefine.CarplusService.SUB;

    @Transient
    private Double distance;
    @Transient
    private String area;
    @Transient
    private boolean allowRtnFromDiffStation;
    @Transient
    private boolean visible;

    /**
     * 檢查是否為營業時間
     */
    public boolean checkBusinessTime(@NonNull Date datetime) {
        // format 台灣時間時分
        String HHmm = DateUtils.toDateString(datetime, "HHmm");

        return (StringUtils.isBlank(startHours) || HHmm.compareTo(startHours) >= 0)
            && (StringUtils.isBlank(endHours) || HHmm.compareTo(endHours) <= 0);
    }

    @Column(name = "createDate", insertable = false, updatable = false)
    private Date createDate;

    @Column(name = "updateDate", insertable = false, updatable = false)
    private Date updateDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Stations stations = (Stations) o;
        return Objects.equals(stationCode, stations.stationCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), stationCode);
    }
}
