package com.carplus.subscribe.db.mysql.entity.calendar;

import com.carplus.subscribe.aspects.ChineseName;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.List;

@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "subscribe_calendar")
public class SubscribeCalendar extends GeneralEntity {

    /**
     * 日期 yyyy/MM/dd (Taipei時間)
     */
    @Id
    private String date;

    @ChineseName("適用站所")
    @Type(type = "json")
    @Schema(description = "適用站點代碼")
    private List<String> stationCodes;
}
