package com.carplus.subscribe.db.mysql.entity.change;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@FieldNameConstants
public class EntityDeletion extends EntityBase {
    private Object deletedEntity;

    public EntityDeletion(String entityName, String entityId, Object entity) {
        super(entityName, entityId);
        this.deletedEntity = entity;
    }
}
