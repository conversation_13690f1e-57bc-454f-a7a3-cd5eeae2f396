package com.carplus.subscribe.db.mysql.dao;

import carplus.common.redis.cache.DelAll;
import carplus.common.redis.cache.Get;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class SubscribeCalendarRepository extends SimpleJpaRepository<SubscribeCalendar, String> {


    public SubscribeCalendarRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(SubscribeCalendar.class, em);
    }

    /**
     * 創建一個 Specification 用於查詢指定日期之後的記錄
     */
    private Specification<SubscribeCalendar> specAfterTargetDate(String date) {
        return (root, query, cb) ->
            cb.greaterThanOrEqualTo(root.get(SubscribeCalendar.Fields.date), date);
    }

    /**
     * 查指定日期之後時間
     **/
    @Get(group = SubscribeCalendarDto.class, key = "#date")
    public List<SubscribeCalendarDto> findAfterTargetDate(String date) {
        return findAll(specAfterTargetDate(date)).stream()
            .map(SubscribeCalendarDto::new).collect(Collectors.toList());
    }

    /**
     * 查指定日期之後時間 (分頁)
     */
    public List<SubscribeCalendarDto> findAfterTargetDate(String date, int skip, int limit) {
        int page = (limit > 0) ? skip / limit : 0;
        if (limit <= 0) {
            limit = 10;
        }
        // 依 date 由新到舊排序
        Pageable pageable = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, SubscribeCalendar.Fields.date));

        return findAll(specAfterTargetDate(date), pageable).stream()
            .map(SubscribeCalendarDto::new).collect(Collectors.toList());
    }

    public long countAfterTargetDate(String date) {
        return count(specAfterTargetDate(date));
    }

    @DelAll(group = SubscribeCalendarDto.class)
    @Override
    public SubscribeCalendar save(SubscribeCalendar entity) {
        return super.save(entity);
    }


    @DelAll(group = SubscribeCalendarDto.class)
    @Override
    public <S extends SubscribeCalendar> List<S> saveAll(Iterable<S> entities) {
        return super.saveAll(entities);
    }

    public List<SubscribeCalendar> findAllByDateIn(List<String> dateList) {
        return findAll((Specification<SubscribeCalendar>) (root, query, cb) ->
            root.get(SubscribeCalendar.Fields.date).in(dateList));
    }

    @DelAll(group = SubscribeCalendarDto.class)
    @Override
    public void deleteAll(Iterable<? extends SubscribeCalendar> entities) {
        super.deleteAll(entities);
    }
}