package com.carplus.subscribe.db.mysql.entity.econtract;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.Instant;

/**
 * 電子合約範本
 */
@Entity(name = "e_contract_template")
@Data
public class EContractTemplate extends GeneralEntity {

    /**
     * 範本編號
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "templateId", nullable = false)
    private Integer templateId;

    /**
     * 範本代碼
     */
    @Column(name = "templateCode", nullable = false)
    private String templateCode;

    /**
     * 版本號
     */
    @Column(name = "versionId")
    private String versionId;

    /**
     * 範本名稱-營業
     */
    @Column(name = "templateNameSales")
    private String templateNameSales;

    /**
     * 範本名稱-官網
     */
    @Column(name = "templateNameCust")
    private String templateNameCust;

    /**
     * 訂單來源
     */
    @Column(name = "conditionOrderSource")
    private Integer conditionOrderSource;

    /**
     * 訂閱租期
     */
    @NotNull
    @Column(name = "conditionOrderMonth", nullable = false)
    private Integer conditionOrderMonth;

    /**
     * 車輛所屬
     */
    @Column(name = "conditionCarBu")
    private String conditionCarBu;

    /**
     * 車輛廠牌
     */
    @Column(name = "conditionCarBrand")
    private String conditionCarBrand;

    /**
     * 是否有免責費用
     */
    private boolean isDisclaimerFee;

    /**
     * 是否冰宇車
     */
    private boolean isSeaLandCar;

    /**
     * 車型
     */
    private String conditionCarModel;

    /**
     * 訂閱類別
     */
    @Column(name = "conditionCarState")
    private String conditionCarState;

    /**
     * 車輛牌價起始
     */
    @NotNull
    @Column(name = "conditionCarPriceStart", nullable = false)
    private Integer conditionCarPriceStart;

    /**
     * 車輛牌價結束
     */
    @NotNull
    @Column(name = "conditionCarPriceEnd", nullable = false)
    private Integer conditionCarPriceEnd;

    /**
     * 合約範本檔案ID
     */
    @Column(name = "uploadFileId")
    private Integer uploadFileId;

    /**
     * 顯示用合約範本檔案名稱
     */
    @Column(name = "displayFilename")
    private String displayFilename;


    /**
     * 啟用時間
     */
    @Column(name = "enableDate")
    private Instant enableDate;

    /**
     * 修訂說明
     */
    @Column(name = "reviseMemo")
    private String reviseMemo;

    /**
     * 建立人員
     */
    @Column(name = "createUser")
    private String createUser;

    /**
     * 更新人員
     */
    @Column(name = "updateUser")
    private String updateUser;



    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "brandCode", name = "conditionCarBrand", updatable = false, insertable = false)
    private CarBrand carBrand;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "carModelCode", name = "conditionCarModel", updatable = false, insertable = false)
    private CarModel carModel;
}