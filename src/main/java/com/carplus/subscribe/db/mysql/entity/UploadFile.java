package com.carplus.subscribe.db.mysql.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;

@Data
@Schema(description = "檔案上傳記錄")
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Entity(name = "upload_files")
public class UploadFile extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Schema(description = "分類")
    @Column(name = "type")
    private String type;

    @Schema(description = "檔名")
    @Column(name = "filename")
    private String filename;

    @Schema(description = "MIME type")
    @Column(name = "mediaType")
    private String mediaType;

    @Schema(description = "差別費率")
    @Column(name = "path")
    private String path;

    @Schema(description = "會員編號")
    @Column(name = "acctId")
    private Integer acctId;

    @Schema(description = "檔案使用狀態")
    @Column(name = "status")
    private Integer status;

    @Schema(description = "檔案顯示名稱")
    @Column(name = "displayFilename")
    private String displayFilename;

    @Schema(description = "上傳檔案備註")
    @Column(name = "remark")
    private String remark;

    @Schema(description = "建立者")
    @Column(name = "createUser")
    private String createUser;

    @Schema(description = "最後異動者")
    @Column(name = "updateUser")
    private String updateUser;

}
