package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.cars.CarRegistration;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class CarRegistrationRepository extends SimpleJpaRepository<CarRegistration, String> {

    private final EntityManager em;

    public CarRegistrationRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarRegistration.class, em);
        this.em = em;
    }

}
