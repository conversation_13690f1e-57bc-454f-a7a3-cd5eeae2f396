package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class CsatQuestRepository extends SimpleJpaRepository<CsatQuest, Integer> {

    private final EntityManager em;

    public CsatQuestRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CsatQuest.class, em);
        this.em = em;
    }

}
