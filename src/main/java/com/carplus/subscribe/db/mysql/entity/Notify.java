package com.carplus.subscribe.db.mysql.entity;

import com.carplus.subscribe.enums.NotifyCategory;
import com.carplus.subscribe.enums.NotifyStatus;
import com.carplus.subscribe.enums.NotifyType;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 通知紀錄
 */
@Data
@Entity
@Table(name = "notify")
@FieldNameConstants
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
public class Notify implements Serializable {

    /**
     * 附件檔名
     */
    @Column(name = "displayFilename")
    public String displayFilename;
    /**
     * 通知序號
     */
    @Id
    @Column(name = "notifyId", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notifyId;
    /**
     * 訂單編號
     */
    @Column(name = "orderNo", nullable = false)
    private String orderNo;
    /**
     * 通知類型[推播PUSH|簡訊SMS|信箱MAIL...]
     */
    @Column(name = "notifyType", nullable = false)
    @Enumerated(EnumType.STRING)
    private NotifyType notifyType;
    /**
     * 通知內容
     * Json字串,推播PUSH|簡訊SMS|漸強MAAC|信箱MAIL共用
     */
    @Column(name = "notifyContent", nullable = false)
    private String notifyContent;
    /**
     * 預計通知時間
     */
    @Column(name = "defDate", nullable = false)
    private Date defDate;
    /**
     * 實際通知時間
     */
    @Column(name = "exeDate")
    private Date exeDate;
    /**
     * 狀態[待通知pending|取消通知cancel......]
     */
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private NotifyStatus status;
    /**
     * 建立日期時間 , 資料庫已定義更新方式
     */
    @Column(name = "createDate", insertable = false, updatable = false)
    private Date createDate;
    /**
     * 排程訊息
     */
    @Column(name = "msg")
    private String msg;
    /**
     * 共用訊息細項分類 , 簡訊 Email 推播
     */
    @Column(name = "category")
    @Enumerated(EnumType.STRING)
    private NotifyCategory category;
    /**
     * Call Back時間
     */
    @Column(name = "callBackDate")
    private Date callBackDate;
    /**
     * Call Back狀態[待通知pending|取消通知cancel......]
     */
    @Column(name = "callBackStatus")
    @Enumerated(EnumType.STRING)
    private NotifyStatus callBackStatus;
    /**
     * Call Back訊息
     */
    @Column(name = "callBackMsg")
    private String callBackMsg;
    /**
     * 夾帶檔案網址
     */
    @Column(name = "attachmentUrl")
    private String attachmentUrl;


}
