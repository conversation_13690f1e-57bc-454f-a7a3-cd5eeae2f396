package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.sku.SkuCriteria;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Repository
public class SkuRepository extends SimpleJpaRepository<Sku, String> {

    private final EntityManager em;

    public SkuRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Sku.class, em);
        this.em = em;
    }

    /**
     * 根據特定欄位和值查找 SKU
     *
     * @param fieldName 要篩選的欄位名稱
     * @param value 要篩選的值
     * @return 符合條件的 SKU 列表
     */
    public List<Sku> findByField(String fieldName, Object value) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Sku> cq = cb.createQuery(Sku.class);
        Root<Sku> root = cq.from(Sku.class);

        cq.where(cb.equal(root.get(fieldName), value));

        return em.createQuery(cq).getResultList();
    }

    /**
     * 檢查是否存在具有特定欄位值的 SKU
     *
     * @param fieldName 要檢查的欄位名稱
     * @param value 要檢查的值
     * @return 如果存在具有指定欄位值的 SKU，則為 true，否則為 false
     */
    public boolean existsByField(String fieldName, Object value) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<Sku> root = cq.from(Sku.class);

        cq.select(cb.count(root));
        cq.where(cb.equal(root.get(fieldName), value));

        return em.createQuery(cq).getSingleResult() > 0;
    }

    /**
     * 檢查具有給定名稱的 SKU 是否存在
     *
     * @param name 要檢查的 SKU 名稱
     * @return 如果具有該名稱的 SKU 存在，則為 true，否則為 false
     */
    public boolean existsByName(String name) {
        return existsByField(Sku.Fields.name, name);
    }

    /**
     * 根據類型查找 SKU
     *
     * @param type 要搜尋的 SKU 類型
     * @return 具有指定類型的 SKU 列表
     */
    public List<Sku> findByType(String type) {
        return findByField(Sku.Fields.type, type);
    }

    public long count(SkuCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<Sku> root = cq.from(Sku.class);

        List<Predicate> predicates = buildPredicates(criteria, root, cb);

        cq.select(cb.count(root));
        if (!predicates.isEmpty()) {
            cq.where(predicates.toArray(new Predicate[0]));
        }

        return em.createQuery(cq).getSingleResult();
    }

    public List<Sku> search(SkuCriteria criteria, Integer limit, Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Sku> cq = cb.createQuery(Sku.class);
        Root<Sku> root = cq.from(Sku.class);

        List<Predicate> predicates = buildPredicates(criteria, root, cb);

        if (!predicates.isEmpty()) {
            cq.where(predicates.toArray(new Predicate[0]));
        }

        // 預設按照 code 排序
        cq.orderBy(cb.asc(root.get(Sku.Fields.code)));

        TypedQuery<Sku> query = em.createQuery(cq);
        query.setFirstResult(offset);
        query.setMaxResults(limit);

        return query.getResultList();
    }

    private List<Predicate> buildPredicates(SkuCriteria criteria, Root<Sku> root, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();

        if (StringUtils.isNotBlank(criteria.getCode())) {
            predicates.add(cb.equal(root.get(Sku.Fields.code), criteria.getCode()));
        }

        if (StringUtils.isNotBlank(criteria.getType())) {
            predicates.add(cb.equal(root.get(Sku.Fields.type), criteria.getType()));
        }

        if (StringUtils.isNotBlank(criteria.getName())) {
            predicates.add(cb.like(root.get(Sku.Fields.name), "%" + criteria.getName() + "%"));
        }

        if (criteria.getIsOfficial() != null) {
            predicates.add(cb.equal(root.get(Sku.Fields.isOfficial), criteria.getIsOfficial()));
        }

        if (criteria.getIsCashier() != null) {
            predicates.add(cb.equal(root.get(Sku.Fields.isCashier), criteria.getIsCashier()));
        }

        return predicates;
    }

    public void validateAllCodesExist(Set<String> skuCodeSet) {
        List<Sku> skus = findAllById(skuCodeSet);
        if (skus.size() != skuCodeSet.size()) {
            throw new SubscribeException(SubscribeHttpExceptionCode.SKU_NOT_FOUND);
        }
    }
}
