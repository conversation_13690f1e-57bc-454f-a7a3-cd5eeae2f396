package com.carplus.subscribe.db.mysql.dao;

import carplus.common.redis.cache.DelAll;
import carplus.common.redis.cache.Get;
import carplus.common.redis.cache.MultiDelAll;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.model.station.StationResponse;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class StationsRepository extends SimpleJpaRepository<Stations, String> {

    private final EntityManager em;

    public StationsRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Stations.class, em);
        this.em = em;
    }


    @MultiDelAll({
        @DelAll(group = Stations.class),
        @DelAll(group = StationResponse.class)
    })
    public void updateIsDeleted(List<String> stationCodes, boolean isDeleted) {
        em.createQuery("update Stations set isDeleted = :isDeleted where stationCode in (:stationCodes)")
            .setParameter("stationCodes", stationCodes)
            .setParameter("isDeleted", isDeleted)
            .executeUpdate();
    }


    @MultiDelAll({
        @DelAll(group = Stations.class),
        @DelAll(group = StationResponse.class)
    })
    @Override
    public Stations save(Stations entity) {
        return super.save(entity);
    }

    @MultiDelAll({
        @DelAll(group = StationResponse.class),
        @DelAll(group = Stations.class)
    })
    @Override
    public <S extends Stations> List<S> saveAll(Iterable<S> entities) {
        return super.saveAll(entities);
    }

    @Get(group = Stations.class, key = "'list'", ttl = 60 * 24 * 12)
    @Override
    public List<Stations> findAll() {
        return super.findAll();
    }
}
