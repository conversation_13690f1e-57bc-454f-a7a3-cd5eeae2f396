package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.contract.SkuShipment;
import com.carplus.subscribe.model.shipment.SkuShipmentCriteria;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class SkuShipmentRepository extends SimpleJpaRepository<SkuShipment, Integer> {

    private final EntityManager em;

    public SkuShipmentRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(SkuShipment.class, em);
        this.em = em;
    }

    @Override
    public Optional<SkuShipment> findById(Integer id) {
        // 使用 Criteria API 建立查詢，預先 fetch 所有需要的關聯資料
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SkuShipment> cq = cb.createQuery(SkuShipment.class);
        Root<SkuShipment> skuShipmentRoot = cq.from(SkuShipment.class);

        // Fetch SkuShipmentHistory list
        skuShipmentRoot.fetch(SkuShipment.Fields.historyList, JoinType.LEFT);

        // 設定查詢條件 - 根據 id 查詢
        cq.where(cb.equal(skuShipmentRoot.get(SkuShipment.Fields.id), id));

        try {
            return Optional.of(em.createQuery(cq).getSingleResult());
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    public List<SkuShipment> search(SkuShipmentCriteria criteria, Integer skip, Integer limit) {
        if (CollectionUtils.isEmpty(criteria.getPayStatus())) {
            return doQuery(criteria, skip, limit);
        }
        return doQuery(criteria, 0, Integer.MAX_VALUE);
    }

    private List<SkuShipment> doQuery(SkuShipmentCriteria criteria, Integer skip, Integer limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();

        // 第一步：查詢分頁後的 orderNo 列表
        CriteriaQuery<String> orderNoQuery = cb.createQuery(String.class);
        Root<SkuShipment> paginationSkuShipmentRoot = orderNoQuery.from(SkuShipment.class);

        List<Predicate> orderNoPredicates = buildPredicates(criteria, paginationSkuShipmentRoot, cb);
        if (!orderNoPredicates.isEmpty()) {
            orderNoQuery.where(orderNoPredicates.toArray(new Predicate[0]));
        }

        // 直接從 SkuShipment 表獲取 orderNo，而不是通過 Orders 表
        orderNoQuery.select(paginationSkuShipmentRoot.get(SkuShipment.Fields.orderNo)).distinct(true);
        orderNoQuery.orderBy(cb.desc(paginationSkuShipmentRoot.get(SkuShipment.Fields.orderNo)));

        TypedQuery<String> orderNoTypedQuery = em.createQuery(orderNoQuery);
        if (skip != null) {
            orderNoTypedQuery.setFirstResult(skip);
        }
        if (limit != null) {
            orderNoTypedQuery.setMaxResults(limit);
        }

        List<String> orderNos = orderNoTypedQuery.getResultList();
        if (CollectionUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }

        // 第二步：使用 Fetch Join 一次性獲取所有關聯資料
        CriteriaQuery<SkuShipment> dataQuery = cb.createQuery(SkuShipment.class);
        Root<SkuShipment> dataSkuShipmentRoot = dataQuery.from(SkuShipment.class);

        // 根據 orderNo 篩選
        List<Predicate> dataPredicates = buildPredicates(criteria, dataSkuShipmentRoot, cb);
        if (criteria.getOrderNo() == null) {
            dataPredicates.add(dataSkuShipmentRoot.get(SkuShipment.Fields.orderNo).in(orderNos));
        }

        dataQuery.where(dataPredicates.toArray(new Predicate[0]));
        dataQuery.select(dataSkuShipmentRoot);

        // 保持 orderNo desc, id asc 排序
        dataQuery.orderBy(
            cb.desc(dataSkuShipmentRoot.get(SkuShipment.Fields.orderNo)),
            cb.asc(dataSkuShipmentRoot.get(SkuShipment.Fields.id))
        );

        return em.createQuery(dataQuery).getResultList();
    }

    private List<Predicate> buildPredicates(SkuShipmentCriteria criteria, Root<SkuShipment> skuShipmentRoot, CriteriaBuilder cb) {
        List<Predicate> predicates = new ArrayList<>();
        if (criteria.getOrderNo() != null) {
            predicates.add(cb.equal(skuShipmentRoot.get(SkuShipment.Fields.orderNo), criteria.getOrderNo()));
        }
        if (criteria.getSkuCode() != null) {
            predicates.add(cb.equal(skuShipmentRoot.get(SkuShipment.Fields.skuCode), criteria.getSkuCode()));
        }
        if (CollectionUtils.isNotEmpty(criteria.getStatus())) {
            predicates.add(skuShipmentRoot.get(SkuShipment.Fields.status).in(criteria.getStatus()));
        }
        // payStatus 不在這裡處理，因為需要在應用層過濾整個訂單級別的條件
        return predicates;
    }

    public long countDistinctOrdersInDatabase(SkuShipmentCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<SkuShipment> root = countQuery.from(SkuShipment.class);

        // 套用與搜尋方法相同的篩選條件
        List<Predicate> predicates = buildPredicates(criteria, root, cb);
        if (!predicates.isEmpty()) {
            countQuery.where(predicates.toArray(new Predicate[0]));
        }

        // 統計不重複的訂單編號
        countQuery.select(cb.countDistinct(root.get(SkuShipment.Fields.orderNo)));

        return em.createQuery(countQuery).getSingleResult();
    }

    /**
     * Find shipment by OrderPriceInfo ID
     */
    private List<SkuShipment> findByOrderPriceInfoId(Integer orderPriceInfoId) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<SkuShipment> cq = cb.createQuery(SkuShipment.class);
        Root<SkuShipment> root = cq.from(SkuShipment.class);
        
        cq.select(root).where(cb.equal(root.get(SkuShipment.Fields.orderPriceInfoId), orderPriceInfoId));

        return em.createQuery(cq).getResultList();
    }

    public int countByOrderPriceInfoId(Integer orderPriceInfoId) {
        return findByOrderPriceInfoId(orderPriceInfoId).size();
    }
}