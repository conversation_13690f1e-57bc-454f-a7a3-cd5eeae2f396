package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class CarUpdateKmDto {
    private String carNo;
    private String vatNo;
    private String plateNo;
    private int currentMileage;
    private String orderNo;

    public static CarUpdateKmDto from(Cars car, int currentMileage, String orderNo) {
        return new CarUpdateKmDto(car.getCarNo(), car.getVatNo(), car.getPlateNo(), currentMileage, orderNo);
    }
}