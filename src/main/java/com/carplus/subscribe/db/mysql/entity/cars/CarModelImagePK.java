package com.carplus.subscribe.db.mysql.entity.cars;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

@Data
public class CarModelImagePK implements Serializable {

    /**
     * 年份
     */
    @Id
    @Column(name = "year", nullable = false)
    private Integer year;

    /**
     * 車型代碼
     */
    @Id
    @Column(name = "carModelCode", nullable = false)
    private String carModelCode;

}
