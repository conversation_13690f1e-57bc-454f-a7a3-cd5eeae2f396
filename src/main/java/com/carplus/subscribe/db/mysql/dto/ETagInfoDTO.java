package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.enums.CarPlusFleet;
import com.carplus.subscribe.enums.ETagPayment;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.Instant;
import java.util.Date;

@Data
public class ETagInfoDTO {

    private Integer id;
    private Integer eTagAmt;
    private Integer fetcAmt;
    private Integer fetcOffSetAmt;
    private Boolean dealerAndETagAccount;
    private String eTagBankUnitCode;
    private Integer paidETagAmt;
    private Boolean isValid;
    private Boolean isSuccess;
    private ETagPayment eTagPayment;
    private Boolean existETagDetail;
    private String notPayableReason;
    private String remark;
    private Integer creditBankAuto;
    private String edcBatchNo;
    private Boolean payabled;
    private String transactionNumber;
    private Boolean lockETagAmt;
    private Boolean passReturnCar;
    private Boolean uploaded;
    private String orderNo;
    private Integer stage;
    private Integer orderPriceInfoId;
    private String dealerOrderPriceInfoId;
    private CarPlusFleet carPlusFleet;
    private Integer eTagFlow;
    private Integer eTagPayFlow;
    private Instant departDate;
    private Instant returnDate;
    private String srentalContractNo;
    private Date createDate;
    private Date updateDate;
    private Integer departFailCode;
    private Integer returnFailCode;
    private String departFailMsg;
    private String returnFailMsg;

    public static ETagInfoDTO convertToDto(ETagInfo etaginfo) {
        ETagInfoDTO dto = new ETagInfoDTO();
        BeanUtils.copyProperties(etaginfo, dto);
        return dto;
    }

    public ETagInfo revertToEntity() {
        ETagInfo etaginfo = new ETagInfo();
        BeanUtils.copyProperties(this, etaginfo);
        return etaginfo;
    }
}
