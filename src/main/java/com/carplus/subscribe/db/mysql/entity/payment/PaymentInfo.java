package com.carplus.subscribe.db.mysql.entity.payment;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.*;
import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 付退款資訊
 */
@Entity(name = "payment_info")
@Data
public class PaymentInfo extends GeneralEntity {

    /**
     * 明細表唯一識別Id
     */
    @Id
    private Integer paymentId;
    /**
     * 訂單編號
     * 1組訂單編號底下含有多筆tradeId
     */
    private String orderId;
    /**
     * 交易序號
     * 付款時由第三方支付產生.與TapPay查帳與退款皆用此Id
     */
    private String tradeId;
    /**
     * 產品別[GOSMART,SMART2GO,OFFICAL,SUB]
     */
    private String orderFrom;
    /**
     * 授權碼
     */
    private String authCode;
    /**
     * 實際付款或退款金額
     */
    private Integer amount;
    /**
     * 支付方式[CreditCard,LinePay,JKOPAY,ApplePay]
     */
    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;
    /**
     * 付款或退款[PayAuth,Refund]
     */
    @Enumerated(EnumType.STRING)
    private PaymentCategory paymentCategory;
    /**
     * 信用卡號前六後四
     */
    private String cardNumber;
    /**
     * 回覆狀態 0=已授權
     */
    private int status;
    /**
     * 失敗訊息
     */
    private String msg;
    /**
     * 收單行
     */
    @Enumerated(EnumType.STRING)
    private Acquirer acquirer;
    /**
     * 付款項目[Depart,Return,Accident,Other]
     */
    @Enumerated(EnumType.STRING)
    private PayFor payFor;
    /**
     * 退款紀錄識別ID
     */
    private String refundId;
    /**
     * 同訂單編號
     */
    @Transient
    private String key;
    /**
     * 消費者付款方式, Sms:簡訊刷卡|Internet:網路刷卡
     */
    @Enumerated(EnumType.STRING)
    private PayFrom payFrom;
    /**
     * 銀行交易序號
     */
    private String transactionNumber;
    /**
     * 額外資訊
     */
    private String additionalData;
    /**
     * 聯名卡資訊
     */
    @Type(type = "json")
    private List<String> affiliateCodes;
    /**
     * 已請款/退款日期
     */
    private Date paymentDealDate;
    /**
     * 銀行訊息
     */
    private String bankResultMsg;
    /**
     * 銀行回覆代碼
     */
    private String bankResultCode;
    /**
     * 刷卡方式 14: 中信銀Tappay, 15: 台新銀Tappay
     */
    private Integer chargeType;
    /**
     * 支付方式
     */
    @Enumerated(EnumType.STRING)
    private AccountType accountType;
    /**
     *備註內容
     */
    private String remark;
    /**
     * 備註更新者
     */
    private String remarker;
}
