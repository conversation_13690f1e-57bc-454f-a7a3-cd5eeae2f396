package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.BuChangeLog;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.Comparator;
import java.util.List;

@Repository
public class BuChangeLogRepository extends SimpleJpaRepository<BuChangeLog, String> {

    private final EntityManager em;

    public BuChangeLogRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(BuChangeLog.class, em);
        this.em = em;
    }

    public List<BuChangeLog> findByPlateNo(String plateNo) {
        return findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("plateNo"), plateNo));
    }

    public List<BuChangeLog> findByPlateNos(List<String> plateNos) {
        return findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> root.get("plateNo").in(plateNos));
    }

    public List<BuChangeLog> findByOrderNo(String orderNo) {
        return findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("orderNo"), orderNo));
    }

    public List<BuChangeLog> findByOrderNos(List<String> orderNos) {
        return findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> root.get("orderNo").in(orderNos));
    }

    public List<BuChangeLog> findByOrderNoAndPlateNo(String orderNo, String plateNo) {
        return findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> criteriaBuilder.and(
            criteriaBuilder.equal(root.get("orderNo"), orderNo),
            criteriaBuilder.equal(root.get("plateNo"), plateNo)
        ));
    }


    public BuChangeLog findPlateLastLog(String plateNo) {
        List<BuChangeLog> list = findByPlateNo(plateNo);
        if (list.isEmpty()) {
            return null;
        }
        list.sort(Comparator.comparing(BuChangeLog::getId).reversed());
        return list.get(0);
    }

    public BuChangeLog findOrderNoLastLog(String orderNo) {
        List<BuChangeLog> list = findByOrderNo(orderNo);
        if (list.isEmpty()) {
            return null;
        }
        list.sort(Comparator.comparing(BuChangeLog::getId).reversed());
        return list.get(0);
    }

    public BuChangeLog findToBuFirstLog(List<String> orderNos, String plateNo, Integer buId) {
        List<BuChangeLog> list = findAll((Specification<BuChangeLog>) (root, query, criteriaBuilder) -> criteriaBuilder.and(
            root.get("orderNo").in(orderNos),
            criteriaBuilder.equal(root.get("plateNo"), plateNo),
            criteriaBuilder.equal(root.get("toBuId"), buId)
        ));
        if (list.isEmpty()) {
            return null;
        }
        list.sort(Comparator.comparing(BuChangeLog::getId));
        return list.get(0);
    }


    public List<BuChangeLog> findPlatesLogs(List<String> plateNos) {
        List<BuChangeLog> list = findByPlateNos(plateNos);
        list.sort(Comparator.comparing(BuChangeLog::getId).reversed());
        return list;
    }


}
