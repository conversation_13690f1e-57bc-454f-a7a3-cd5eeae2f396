package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dto.MainContractOrderInfoDTO;
import com.carplus.subscribe.db.mysql.entity.contract.Contract;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.model.request.contract.ContractCriteria;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class MainContractRepository extends SimpleJpaRepository<MainContract, String> {

    private final EntityManager em;

    public MainContractRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(MainContract.class, em);
        this.em = em;
    }

    /**
     * 取得最後的主約編號
     */
    public String getLastMainContractNo() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<MainContract> root = cq.from(MainContract.class);
        cq.select(root.get("mainContractNo")).orderBy(cb.desc(root.get("mainContractNo")));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }


    /**
     * 取得特定使用者的訂單編號
     */
    public MainContract getMainContractById(int acctId, @NonNull String contractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<MainContract> cq = cb.createQuery(MainContract.class);
        Root<MainContract> root = cq.from(MainContract.class);
        cq.select(root).where(cb.and(cb.equal(root.get("acctId"), acctId), cb.equal(root.get("mainContractNo"), contractNo)));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 取得特定使用者的所有主合約
     */
    public List<MainContract> getMainContractByAcctId(int acctId) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<MainContract> cq = cb.createQuery(MainContract.class);
        Root<MainContract> root = cq.from(MainContract.class);
        cq.select(root).where(cb.and(cb.equal(root.get("acctId"), acctId)));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 取得特定使用者的主合約
     */
    public MainContract getMainContractByAcctId(int acctId, String mainContractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<MainContract> cq = cb.createQuery(MainContract.class);
        Root<MainContract> root = cq.from(MainContract.class);
        cq.select(root).where(cb.and(cb.equal(root.get("acctId"), acctId), cb.equal(root.get("mainContractNo"), mainContractNo)));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }


    /**
     * 透過編號取得主合約
     */
    public MainContract getMainContractByNo(@NonNull String contractNo, boolean isInit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<MainContract> cq = cb.createQuery(MainContract.class);
        Root<MainContract> root = cq.from(MainContract.class);
        cq.select(root).where(cb.and(cb.equal(root.get("mainContractNo"), contractNo)));
        try {
            MainContract mainContract = em.createQuery(cq).setMaxResults(1).getSingleResult();
            if (isInit) {
                mainContract.getContracts().forEach(c ->
                    Hibernate.initialize(c.getOrders()));
            }
            return mainContract;
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<MainContract> search(@NonNull ContractCriteria criteria,
                                     @Nullable Integer limit, @Nullable Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<MainContract> cq = cb.createQuery(MainContract.class);
        prepareQuery(cb, cq, criteria, false);

        TypedQuery<MainContract> query = em.createQuery(cq);
        if (limit != null) {
            query.setMaxResults(limit);
        }
        if (offset != null) {
            query.setFirstResult(offset);
        }
        List<MainContract> mainContractList = query.getResultList();
        if (criteria.isDetail()) {
            for (MainContract mainContract : mainContractList) {
                for (Contract contract : mainContract.getContracts()) {
                    Hibernate.initialize(contract.getOrders());
                }
            }
        }
        return mainContractList;
    }

    public long count(@NonNull ContractCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getResultList().size();
    }

    @NonNull
    public void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, @NonNull ContractCriteria criteria, boolean isCount) {
        Root<MainContract> root = cq.from(MainContract.class);
        List<Predicate> predicateList = new ArrayList<>();

        if (StringUtils.isNotBlank(criteria.getMainContractNo())) {
            predicateList.add(cb.and(cb.or(root.get("mainContractNo").in(criteria.getMainContractNo()))));
        }
        if (Objects.nonNull(criteria.getContractStatuses()) && !criteria.getContractStatuses().isEmpty()) {
            predicateList.add(cb.and(cb.or(root.get("status").in(criteria.getContractStatuses().stream().map(ContractStatus::getCode).collect(Collectors.toList())))));
        }
        if (Objects.nonNull(criteria.getAcctId())) {
            predicateList.add(cb.and(cb.or(root.get("acctId").in(criteria.getAcctId()))));
        }
        cq.where(predicateList.toArray(new Predicate[0]));
        if (isCount) {
            cq.select(root.get("mainContractNo"));
        } else {
            cq.orderBy(cb.desc(root.get("mainContractNo")));
        }
    }


    public List<String> getOrderNosByMainContractNo(String mainContractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<MainContract> root = cq.from(MainContract.class);
        Join<MainContract, Contract> contractJoin = root.join("contracts");
        Join<Contract, Orders> ordersJoin = contractJoin.join("orders");
        cq.select(ordersJoin.get("orderNo")).where(cb.and(cb.equal(root.get("mainContractNo"), mainContractNo)));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 拿取該車牌過往至今所有主約、Contract、Order部分資訊
     */
    public List<MainContractOrderInfoDTO> getMainContractByPlateNo(String plateNo,
                                                                   List<Integer> mainContractStatus,
                                                                   List<Integer> orderStatus) {
        String path = MainContractOrderInfoDTO.class.getName();
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT NEW ").append(path)
            .append(" (mc.mainContractNo, ct.contractNo, od.orderNo, mc.plateNo, ")
            .append(" od.stage, mc.departStationCode, mc.returnStationCode, ")
            .append(" mc.status, od.status, ")
            .append(" mc.endDate, od.endDate) ")
            .append(" FROM MainContract mc ")
            .append(" JOIN mc.contracts ct ")
            .append(" JOIN ct.orders od ");

        if (Objects.nonNull(plateNo) && !plateNo.isEmpty()) {
            sb.append(" WHERE mc.plateNo = :plateNo ");
        }
        if (Objects.nonNull(mainContractStatus)) {
            sb.append(" AND mc.status IN (:mainStatus) ");
        }
        if (Objects.nonNull(orderStatus)) {
            sb.append(" AND od.status IN (:orderStatus) ");
        }

        Query query = em.createQuery(sb.toString(), MainContractOrderInfoDTO.class);

        if (plateNo != null && !plateNo.isEmpty()) {
            query.setParameter("plateNo", plateNo);
        }
        if (Objects.nonNull(mainContractStatus)) {
            query.setParameter("mainStatus", mainContractStatus);
        }
        if (Objects.nonNull(orderStatus)) {
            query.setParameter("orderStatus", orderStatus);
        }

        return query.getResultList();
    }

}
