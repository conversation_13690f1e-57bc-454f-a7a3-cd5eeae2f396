package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.AccountDetail;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class AccountDetailRepository extends SimpleJpaRepository<AccountDetail, String> {

    private final EntityManager em;

    public AccountDetailRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(AccountDetail.class, em);
        this.em = em;
    }

    private <T> Predicate addNotDeletedAccountCondition(CriteriaBuilder cb, Root<T> root) {
        Subquery<Long> subquery = cb.createQuery().subquery(Long.class);
        Root<Account> accountRoot = subquery.from(Account.class);
        subquery.select(accountRoot.get("id"))
            .where(cb.equal(accountRoot.get("isDeleted"), false));

        return root.get("accountId").in(subquery);
    }

    /**
     * 某訂單編號的所有帳目歷程總合
     */
    public Map<Integer, Integer> findSumByAccountIdIn(@Param("accountIds") Collection<Long> accountIds) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);

        cq.multiselect(root.get("accountId"), cb.sum(root.get("amount")))
            .where(
                root.get("accountId").in(accountIds),
                addNotDeletedAccountCondition(cb, root)
            )
            .groupBy(root.get("accountId"));
        List<Object[]> result = em.createQuery(cq).getResultList();
        return result.stream().collect(Collectors.toMap(o -> Integer.valueOf(o[0].toString()), o -> Integer.valueOf(o[1].toString())));
    }

    public Map<Integer, List<Integer>> findLastDetailByAccount(@Param("accountIds") Collection<Long> accountIds) {
        if (accountIds == null || accountIds.isEmpty()) {
            return Collections.emptyMap();
        }
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);

        cq.multiselect(root.get("accountId"), root.get("orderPriceIds"))
            .where(
                root.get("id").in(findMaxIdByAccountIds(accountIds)),
                addNotDeletedAccountCondition(cb, root)
            )
            .groupBy(root.get("accountId"));
        List<Object[]> result = em.createQuery(cq).getResultList();
        return result.stream().collect(Collectors.toMap(o -> Integer.valueOf(o[0].toString()), o -> Optional.ofNullable(o[1]).map(list -> (List<Integer>) list).orElseGet(ArrayList::new)));
    }

    private List<Long> findMaxIdByAccountIds(@Param("accountIds") Collection<Long> accountIds) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);

        cq.multiselect(cb.max(root.get("id")))
            .where(
                root.get("accountId").in(accountIds),
                addNotDeletedAccountCondition(cb, root)
            );
        return em.createQuery(cq).getResultList();
    }

    public Map<Long, List<AccountDetail>>  getDetailByAccountIds(@Param("accountIds") Collection<Long> accountIds) {
        if (accountIds.isEmpty()) {
            return Collections.emptyMap();
        }

        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<AccountDetail> cq = cb.createQuery(AccountDetail.class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);
        cq.where(
            root.get("accountId").in(accountIds),
            addNotDeletedAccountCondition(cb, root)
        );

        return em.createQuery(cq)
            .getResultStream()
            .collect(Collectors.groupingBy(AccountDetail::getAccountId));
    }


    public Map<Long, List<AccountDetail>> findUncheckOutAmountByAccountIds(@Param("accountIds") Collection<Long> accountIds) {
        if (accountIds.isEmpty()) {
            return Collections.emptyMap();
        }

        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<AccountDetail> cq = cb.createQuery(AccountDetail.class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);
        cq.where(
            root.get("accountId").in(accountIds),
            cb.equal(root.get("isCheckout"), true),
            addNotDeletedAccountCondition(cb, root)
        );

        return em.createQuery(cq)
            .getResultStream()
            .collect(Collectors.groupingBy(AccountDetail::getAccountId));
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public void updateCheckout(@Param("accountIds") Collection<Long> accountIds) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaUpdate<AccountDetail> cu = cb.createCriteriaUpdate(AccountDetail.class);
        Root<AccountDetail> root = cu.from(AccountDetail.class);
        cu.set("isCheckout", false);
        cu.where(
            root.get("accountId").in(accountIds),
            cb.equal(root.get("isCheckout"), true),
            addNotDeletedAccountCondition(cb, root)
        );

        em.createQuery(cu).executeUpdate();
    }

    public List<Long> findAllUnCheckOut() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<AccountDetail> root = cq.from(AccountDetail.class);

        cq.distinct(true).multiselect(root.get("accountId"))
            .where(cb.equal(root.get("isCheckout"), true), addNotDeletedAccountCondition(cb, root));
        return em.createQuery(cq).getResultList();
    }

}
