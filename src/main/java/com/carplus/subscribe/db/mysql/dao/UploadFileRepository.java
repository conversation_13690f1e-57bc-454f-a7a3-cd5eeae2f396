package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.UploadFile;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Repository
public class UploadFileRepository extends SimpleJpaRepository<UploadFile, Integer> {

    private final EntityManager em;

    public UploadFileRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(UploadFile.class, em);
        this.em = em;
    }

    /**
     * 取得上傳檔案
     */
    public Optional<UploadFile> getById(int id, int status) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<UploadFile> cq = cb.createQuery(UploadFile.class);
        Root<UploadFile> root = cq.from(UploadFile.class);
        cq.where(
                cb.and(
                        cb.equal(root.get("id"), id),
                        cb.equal(root.get("status"), status)
                )
        );
        List<UploadFile> results = em.createQuery(cq).getResultList();
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    /**
     * 取得上傳檔案列表
     */
    public List<UploadFile> getByMultiId(List<Integer> ids) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<UploadFile> cq = cb.createQuery(UploadFile.class);
        Root<UploadFile> root = cq.from(UploadFile.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(root.get("id").in(ids));
        predicateList.add(cb.equal(root.get("status"), 1));
        cq.where(predicateList.toArray(predicateList.toArray(new Predicate[0])));
        return em.createQuery(cq).getResultList();
    }

}
