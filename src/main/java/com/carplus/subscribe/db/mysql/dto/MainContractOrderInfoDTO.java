package com.carplus.subscribe.db.mysql.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * 依需要新增欄位
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MainContractOrderInfoDTO {
    private String mainContractNo;
    private String contractNo;
    private String orderNo;
    private String plateNo;
    private Integer orderStage;
    private String departStationCode;
    private String returnStationCode;
    private Integer mainContractStatus;
    private Integer orderStatus;
    private Instant mainContractEndDate;
    private Instant orderEndDate;
}
