package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.response.exception.BadRequestException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.model.subscribelevel.MonthlyFeeProvider;
import com.carplus.subscribe.utils.CarsUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

public interface CarPropertyProvider {

    String getPlateNo();

    String getMfgYear();

    List<Integer> getTagIds();

    @JsonIgnore
    default boolean isVirtualCar() {
        return CarsUtil.isVirtualCar(getPlateNo());
    }

    @JsonIgnore
    default void validateCarMfgYear() {
        if (!isVirtualCar() && StringUtils.isBlank(getMfgYear())) {
            throw new BadRequestException(String.format("[非虛擬車之出廠年份不可為空, 車號: {%s}]", getPlateNo()));
        }
    }

    @JsonIgnore
    default boolean isMonthlyDiscounted() {
        return CollectionUtils.isNotEmpty(getTagIds()) && getTagIds().contains(CarDefine.CarTag.MONTHLY_DISCOUNTED.getId());
    }

    /**
     * 車輛標籤帶有「超激優惠」
     */
    @JsonIgnore
    default boolean hasLevelDiscountedTag() {
        return CollectionUtils.isNotEmpty(getTagIds()) && getTagIds().contains(CarDefine.CarTag.LEVEL_DISCOUNTED.getId());
    }

    @JsonIgnore
    default int getUseMonthlyFee(MonthlyFeeProvider monthlyFeeProvider) {
        if (monthlyFeeProvider == null) {
            throw new IllegalArgumentException("MonthlyFeeProvider cannot be null");
        }
        return isMonthlyDiscounted() ? monthlyFeeProvider.getDiscountMonthlyFee() : monthlyFeeProvider.getMonthlyFee();
    }
}
