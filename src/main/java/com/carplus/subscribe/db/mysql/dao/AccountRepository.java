package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.payment.Account;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Repository
public class AccountRepository extends SimpleJpaRepository<Account, Long> {

    private final EntityManager em;

    public AccountRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Account.class, em);
        this.em = em;
    }

    /**
     * 取得收支登打資料
     */
    public List<Account> getAccountsByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Account> cq = cb.createQuery(Account.class);
        Root<Account> root = cq.from(Account.class);
        cq.select(root).where(cb.equal(root.get("orderNo"), orderNo));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 取得登打帳務總和
     */
    public Integer getAccountAmtByOrderNo(String orderNo) {
        return getAccountsByOrderNo(orderNo).stream().filter(account -> !account.isDeleted()).mapToInt(Account::getAmount).sum();
    }

}
