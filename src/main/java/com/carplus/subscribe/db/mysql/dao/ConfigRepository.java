package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.ConfigEntity;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Repository
public class ConfigRepository extends SimpleJpaRepository<ConfigEntity, String> {
    private EntityManager em;

    public ConfigRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(ConfigEntity.class, em);
        this.em = em;
    }

    public ConfigEntity getConfigByCode(String code) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<ConfigEntity> cq = cb.createQuery(ConfigEntity.class);
        Root<ConfigEntity> root = cq.from(ConfigEntity.class);

        cq.select(root).where(cb.equal(root.get("code"), code));
        List<ConfigEntity> list = em.createQuery(cq).getResultList();
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    @Transactional
    public <S extends ConfigEntity> S save(S entity) {
        return super.save(entity);
    }

}