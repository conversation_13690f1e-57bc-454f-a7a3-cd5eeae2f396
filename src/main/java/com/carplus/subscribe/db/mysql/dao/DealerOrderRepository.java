package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.request.dealer.DealerOrderCriteria;
import com.carplus.subscribe.utils.DateUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceException;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class DealerOrderRepository extends SimpleJpaRepository<DealerOrder, String> {

    private final EntityManager em;

    public DealerOrderRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(DealerOrder.class, em);
        this.em = em;
    }

    @Transactional(transactionManager = "mysqlTransactionManager")
    public <S extends DealerOrder> DealerOrder persist(S entity) {
        try {
            this.em.persist(entity);
            this.em.flush();
            return entity;
        } catch (Exception e) {
            if (e instanceof PersistenceException) {
                Throwable throwable = e.getCause();
                while (throwable != null) {
                    if (throwable.getMessage().toLowerCase().contains("duplicate")) {
                        throw new SubscribeException(SubscribeHttpExceptionCode.DEALER_ORDER_EXIST);
                    }
                    throwable = throwable.getCause();
                }

            }
            throw e;
        }
    }

    /**
     * 查詢同一母約訂單
     */
    public List<DealerOrder> getDealerOrdersByParentOrderNo(String parentOrderNo) {
        return findAll((Specification<DealerOrder>) (root, query, cb) -> cb.equal(root.get(DealerOrder.Fields.parentOrderNo), parentOrderNo));
    }

    /**
     * 資料筆數
     *
     * @param queryRequest 查詢相關參數
     */
    public long count(@NonNull DealerOrderCriteria queryRequest) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, queryRequest, true);
        return em.createQuery(cq).getResultList().size();
    }

    /**
     * 查詢資料
     *
     * @param queryRequest 查詢相關參數
     * @param limit        限制回傳筆數
     * @param offset       略過前N筆資料
     */
    public List<Object[]> findBySearch(@NonNull DealerOrderCriteria queryRequest,
                                       @Nullable Integer limit, @Nullable Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        prepareQuery(cb, cq, queryRequest, false);
        TypedQuery<Object[]> query = em.createQuery(cq);
        if (limit != null) {
            query.setMaxResults(limit);
        }
        if (offset != null) {
            query.setFirstResult(offset);
        }

        List<Object[]> resultList = query.getResultList();

        return resultList;
    }

    /**
     * 組SQL語法
     *
     * @param cb           CriteriaBuilder
     * @param cq           CriteriaQuery
     * @param queryRequest 查詢相關參數
     * @param isCount      是否為查詢資料筆數
     */
    @NonNull
    public void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, @NonNull DealerOrderCriteria queryRequest, boolean isCount) {
        Root<DealerOrder> dealerOrdersRoot = cq.from(DealerOrder.class);

        Join<DealerOrder, Cars> carsJoin = dealerOrdersRoot.join("cars", JoinType.LEFT);
        Join<Cars, CarModel> carModelJoin = carsJoin.join("carModel", JoinType.LEFT);
        Join<CarModel, CarBrand> carBrandJoin = carModelJoin.join("carBrand", JoinType.LEFT);

        List<Predicate> predicateList = new ArrayList<>();
        List<String> stationCodeList = new ArrayList<>();

        // 查看單位
        if (Objects.nonNull(queryRequest.getStationCode()) && !queryRequest.getStationCode().isEmpty()) {
            stationCodeList.addAll(queryRequest.getStationCode());
        }
        // 經銷商訂單編號
        if (Objects.nonNull(queryRequest.getOrderNo()) && !queryRequest.getOrderNo().isEmpty()) {
            predicateList.add(cb.equal(dealerOrdersRoot.get("orderNo"), queryRequest.getOrderNo()));
        }
        // 訂單狀態
        if (Objects.nonNull(queryRequest.getOrderStatus()) && !queryRequest.getOrderStatus().isEmpty()) {
            predicateList.add(cb.and(
                dealerOrdersRoot.get("orderStatus").in(new ArrayList<>(queryRequest.getOrderStatus()))
            ));
        }
        // 車牌號碼
        if ((Objects.nonNull(queryRequest.getPlateNo())) && !queryRequest.getPlateNo().isEmpty()) {
            predicateList.add(cb.and(
                dealerOrdersRoot.get("plateNo").in(new ArrayList<>(queryRequest.getPlateNo()))
            ));
        }
        // 訂單成立時間
        if (Objects.nonNull(queryRequest.getSecurityDepositDateFrom()) && Objects.nonNull(queryRequest.getSecurityDepositDateTo())) {
            predicateList.add(cb.between(
                dealerOrdersRoot.get("securityDepositDate"), queryRequest.getSecurityDepositDateFrom().toInstant(), queryRequest.getSecurityDepositDateTo().toInstant())
            );
        }
        // 出車站點
        if (!stationCodeList.isEmpty()) {
            // [預定]出車站點
            Predicate expectDepartStationCondition = dealerOrdersRoot.get("expectDepartStation").in(stationCodeList);
            // [預定]還車站點
            Predicate expectReturnStationCondition = dealerOrdersRoot.get("expectReturnStation").in(stationCodeList);
            // [實際]出車站點
            Predicate departStationCondition = dealerOrdersRoot.get("departStation").in(stationCodeList);
            // [實際]還車站點
            Predicate returnStationCondition = dealerOrdersRoot.get("returnStation").in(stationCodeList);
            // 組合四個站點條件
            Predicate stationConditions = cb.or(expectDepartStationCondition, expectReturnStationCondition,
                departStationCondition, returnStationCondition);

            predicateList.add(stationConditions);
        }
        // [預定]還車
        if (Objects.nonNull(queryRequest.getExpectReturnDateFrom()) && Objects.nonNull(queryRequest.getExpectReturnDateTo())) {
            predicateList.add(cb.between(dealerOrdersRoot.get("expectReturnDate"), queryRequest.getExpectReturnDateFrom().toInstant(), queryRequest.getExpectReturnDateTo().toInstant()));
        }
        // [預定]出車
        if (Objects.nonNull(queryRequest.getExpectDepartDateFrom()) && Objects.nonNull(queryRequest.getExpectDepartDateTo())) {
            predicateList.add(cb.between(dealerOrdersRoot.get("expectDepartDate"), queryRequest.getExpectDepartDateFrom().toInstant(), queryRequest.getExpectDepartDateTo().toInstant()));
        }
        // [實際]還車
        if (Objects.nonNull(queryRequest.getReturnDateFrom()) && Objects.nonNull(queryRequest.getReturnDateTo())) {
            predicateList.add(cb.between(dealerOrdersRoot.get("returnDate"), queryRequest.getReturnDateFrom().toInstant(), queryRequest.getReturnDateTo().toInstant()));
        }
        // [實際]出車
        if (Objects.nonNull(queryRequest.getDepartDateFrom()) && Objects.nonNull(queryRequest.getDepartDateTo())) {
            predicateList.add(cb.between(dealerOrdersRoot.get("departDate"), queryRequest.getDepartDateFrom().toInstant(), queryRequest.getDepartDateTo().toInstant()));
        }
        if (queryRequest.getAcctId() != null && !queryRequest.getAcctId().isEmpty()) {
            predicateList.add(dealerOrdersRoot.get(DealerOrder.Fields.dealerUserId).in(queryRequest.getAcctId()));
        }

        cq.where(predicateList.toArray(new Predicate[0]));

        if (isCount) {
            cq.select(dealerOrdersRoot.get("orderNo"));
        } else {
            cq.multiselect(dealerOrdersRoot, carModelJoin, carBrandJoin);
            cq.orderBy(cb.desc(dealerOrdersRoot.get("createDate")));
        }
    }

    /**
     * 符合以下提醒投保條件，orderStatus = 0(create) & expectDepartDate = 3天後 & lrentalContractNo is null
     */
    public List<DealerOrder> getDealerOrderForRemindInsurance() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<DealerOrder> cq = cb.createQuery(DealerOrder.class);
        Root<DealerOrder> dealerOrdersRoot = cq.from(DealerOrder.class);

        cq.where(
            cb.and(
                cb.equal(dealerOrdersRoot.get("orderStatus"), ContractStatus.CREATE.getCode()),
                cb.lessThan(dealerOrdersRoot.get("expectDepartDate"), DateUtil.convertToStartOfInstant(Instant.now().plus(4, ChronoUnit.DAYS))),
                cb.isNull(dealerOrdersRoot.get("lrentalContractNo"))
            )
        );
        return em.createQuery(cq).getResultList();
    }

    /**
     * 符合以下出車異常條件 orderStatus = 1(going) & lrentalContractNo is null
     */
    public List<DealerOrder> getDealerOrderNoForDepartAbnormal() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<DealerOrder> cq = cb.createQuery(DealerOrder.class);
        Root<DealerOrder> dealerOrdersRoot = cq.from(DealerOrder.class);

        cq.where(
            cb.and(
                cb.equal(dealerOrdersRoot.get("orderStatus"), ContractStatus.GOING.getCode()),
                cb.isNull(dealerOrdersRoot.get("lrentalContractNo"))
            )
        );
        return em.createQuery(cq).getResultList();
    }


    /**
     * 拿取前一筆訂單
     */
    public DealerOrder getPreviousDealerOrder(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<DealerOrder> cq = cb.createQuery(DealerOrder.class);
        Root<DealerOrder> dealerOrdersRoot = cq.from(DealerOrder.class);

        cq.where(cb.equal(dealerOrdersRoot.get(DealerOrder.Fields.nextStageOrderNo), orderNo));
        try {
            return em.createQuery(cq).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 取得擁有相同 parentOrderNo 的訂單列表
     */
    public List<DealerOrder> getRelatedDealerOrdersByParentOrderNo(String orderNo, String parentOrderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<DealerOrder> cq = cb.createQuery(DealerOrder.class);
        Root<DealerOrder> dealerOrderRoot = cq.from(DealerOrder.class);

        Predicate parentOrderNoPredicate = cb.equal(dealerOrderRoot.get("parentOrderNo"), parentOrderNo);
        Predicate orderNoPredicate = cb.notEqual(dealerOrderRoot.get("orderNo"), orderNo);
        cq.where(cb.and(parentOrderNoPredicate, orderNoPredicate));

        return em.createQuery(cq).getResultList();
    }

    /**
     * 取得所有經銷商訂單客戶編號 (Distinct)
     */
    public List<Integer> getAllDealerUserIds() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<DealerOrder> dealerOrderRoot = cq.from(DealerOrder.class);

        cq.select(dealerOrderRoot.get("dealerUserId"));
        cq.distinct(true);

        return em.createQuery(cq).getResultList().stream().map(Long::intValue).collect(Collectors.toList());
    }
}
