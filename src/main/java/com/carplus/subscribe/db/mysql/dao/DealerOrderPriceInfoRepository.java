package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.dealer.DealerOrderPriceInfo;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Repository
public class DealerOrderPriceInfoRepository extends SimpleJpaRepository<DealerOrderPriceInfo, String> {

    private final EntityManager em;

    public DealerOrderPriceInfoRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(DealerOrderPriceInfo.class, em);
        this.em = em;
    }

    public List<DealerOrderPriceInfo> findByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<DealerOrderPriceInfo> cq = cb.createQuery(DealerOrderPriceInfo.class);
        Root<DealerOrderPriceInfo> root = cq.from(DealerOrderPriceInfo.class);
        cq.where(cb.equal(root.get("orderNo"), orderNo));
        return em.createQuery(cq).getResultList();
    }
}
