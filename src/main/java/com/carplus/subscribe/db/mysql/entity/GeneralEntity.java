package com.carplus.subscribe.db.mysql.entity;

import com.carplus.subscribe.db.mysql.CustomizedJsonStringType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.time.Instant;
import java.util.Date;
import java.util.Optional;

@Data
@MappedSuperclass
@TypeDef(name = "json", typeClass = CustomizedJsonStringType.class)
public abstract class GeneralEntity {

    @Column(name = "createDate", insertable = false, updatable = false)
    private Date createDate;

    @Column(name = "updateDate", insertable = false, updatable = false)
    private Date updateDate;

    @JsonIgnore
    public Instant getInstantCreateDate() {
        return Optional.ofNullable(createDate).map(Date::toInstant).orElse(null);
    }

    @JsonIgnore
    public Instant getInstantUpdateDate() {
        return Optional.ofNullable(updateDate).map(Date::toInstant).orElse(null);
    }

    @JsonIgnore
    public void setInstantCreateDate(Instant createDate) {
        this.createDate = Date.from(createDate);
    }

    @JsonIgnore
    public void setInstantUpdateDate(Instant updateDate) {
        this.updateDate = Date.from(updateDate);
    }
}
