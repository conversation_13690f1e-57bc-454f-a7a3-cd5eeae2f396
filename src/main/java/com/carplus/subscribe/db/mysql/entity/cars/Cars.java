package com.carplus.subscribe.db.mysql.entity.cars;

import com.carplus.subscribe.aspects.ChineseName;
import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;


@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Entity
@Table(name = "cars")
public class Cars extends GeneralEntity implements CarPropertyProvider, Orderable {
    /**
     * 車號
     **/
    @ChineseName("車號")
    @Id
    @Column(name = "plateNo", nullable = false)
    private String plateNo;


    /**
     * 車輛狀態
     **/
    @ChineseName("車輛狀態")
    @Column(name = "carStatus", nullable = false)
    private String carStatus;

    /**
     * 車型代碼
     **/
    @ChineseName("車型代碼")
    @Column(name = "carModelCode")
    private String carModelCode;

    /**
     * 實際排氣量
     * 車型代號有排氣量分級
     **/
    @ChineseName("排氣量")
    @Column(name = "displacement")
    private BigDecimal displacement;

    /**
     * 車型類別
     **/
    @ChineseName("車型類別")
    @Column(name = "carType")
    @Enumerated(EnumType.STRING)
    private CarDefine.CarType carType;

    /**
     * 燃料類別
     **/
    @ChineseName("燃料類別")
    @Column(name = "fuelType")
    @Enumerated(EnumType.STRING)
    private CarDefine.FuelType fuelType;

    /**
     * 能源類別
     **/
    @ChineseName("能源類別")
    @Enumerated(EnumType.ORDINAL)
    private CarDefine.EnergyType energyType;

    /**
     * 排檔類別
     **/
    @ChineseName("排檔類別")
    @Column(name = "gearType")
    @Enumerated(EnumType.STRING)
    private CarDefine.GearType gearType;

    /**
     * 車籍統編
     **/
    @ChineseName("車籍統編")
    @Column(name = "taxId")
    private String taxId;

    /**
     * 出廠年份
     **/
    @ChineseName("出廠年份")
    @Column(name = "mfgYear")
    private String mfgYear;

    /**
     * 出廠月份
     **/
    @ChineseName("出廠月份")
    @Column(name = "mfgMonth")
    private String mfgMonth;

    /**
     * 車籍 所屬 站所代號
     **/
    @ChineseName("車籍所屬站所代號")
    @Column(name = "regisStationCode")
    private String regisStationCode;

    /**
     * 車輛 所有權 站所代號
     **/
    @ChineseName("車輛所有權站所代號")
    @Column(name = "ownerStationCode")
    private String ownerStationCode;

    /**
     * 車輛所有權站所代號
     **/
    @ChineseName("所在站所")
    @Column(name = "locationStationCode")
    private String locationStationCode;

    /**
     * 當前里程數
     **/
    @ChineseName("當前里程數")
    @Column(name = "currentMileage")
    private Integer currentMileage;

    /**
     * 訂閱車方案
     **/
    @ChineseName("訂閱車方案")
    @Column(name = "subscribeLevel")
    private Integer subscribeLevel;

    /**
     * 訂閱類別
     **/
    @ChineseName("訂閱類別")
    @Column(name = "carState")
    @Enumerated(EnumType.STRING)
    private CarDefine.CarState carState;

    /**
     * 啟用狀態
     **/
    @ChineseName("是否在訂閱官網上架")
    @Column(name = "launched")
    @Enumerated(EnumType.STRING)
    private CarDefine.Launched launched;

    /**
     * 設備id
     **/
    @ChineseName("設備id")
    @Column(name = "equipIds")
    @Type(type = "json")
    private List<Integer> equipIds;

    /**
     * 標籤id
     **/
    @ChineseName("標籤id")
    @Column(name = "tagIds")
    @Type(type = "json")
    private List<Integer> tagIds;

    /**
     * etag型式
     */
    @ChineseName("ETag型式")
    @Column(name = "etagModel")
    @Enumerated(EnumType.STRING)
    private ETagModelEnum etagModel;

    /**
     * etag序號 (etag 照片 url)
     */
    @ChineseName("ETag照片Url")
    @Column(name = "etagNo")
    private String etagNo;

    /**
     * 顏色說明
     **/
    @ChineseName("車色")
    @Column(name = "colorDesc")
    private String colorDesc;

    /**
     * 中文說明
     **/
    @ChineseName("車體介紹")
    @Column(name = "cnDesc")
    private String cnDesc;

    /**
     * 車輛編號
     **/
    @ChineseName("車輛編號")
    @Column(name = "carNo")
    private String carNo;

    /**
     * CRS車輛編號
     **/
    @ChineseName("CRS車輛編號")
    @Column(name = "crsCarNo")
    private Integer crsCarNo;

    /**
     * 中古車定價
     */
    @ChineseName("中古車定價")
    @Column(name = "usedCarPrice")
    private int usedCarPrice;

    /**
     * 新車定價
     */
    @ChineseName("新車定價")
    @Column(name = "newCarPrice")
    private int newCarPrice;

    /**
     * 座位數量
     */
    @ChineseName("座位數")
    @Column(name = "seat")
    private int seat;

    /**
     * 是否刪除
     */
    @ChineseName("是否刪除")
    @Column(name = "isDeleted")
    private Boolean isDeleted;

    /**
     * 鎖車訂單編號
     */
    @ChineseName("鎖車訂單編號")
    private String bookingOrderNo;

    /**
     * 牌價
     */
    @ChineseName("牌價")
    private Integer stdPrice;

    /**
     * 車型
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "carModelCode", name = "carModelCode", insertable = false, updatable = false)
    private CarModel carModel;

    /**
     * 站點
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "stationCode", name = "locationStationCode", insertable = false, updatable = false)
    private Stations stations;

    /**
     * 撥車申請單單號
     */
    @ChineseName("撥車申請單單號")
    @Column(name = "buchangemasterid")
    private Integer buChangeMasterId;

    /**
     * 是否在SL上架
     */
    @ChineseName("是否開放經銷商上架")
    private boolean isSealandLaunched;

    /**
     * 是否專案車
     */
    @ChineseName("是否專案車")
    private boolean isProjectCar;

    /**
     * 所屬公司統編
     */
    @ChineseName("所屬公司統編")
    private String vatNo;

    /**
     * 準備工作天數
     */
    @ChineseName("準備工作天數")
    private Integer prepWorkdays;

    /**
     * 資料庫新增資料寫入前處理
     */
    @PrePersist
    private void prePersist() {
        isDeleted = false;
    }

}
