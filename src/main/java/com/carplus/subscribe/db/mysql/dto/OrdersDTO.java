package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.RenewType;
import com.carplus.subscribe.model.AccidentInfo;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.order.Remark;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.time.Instant;
import java.util.List;

@Data
public class OrdersDTO {

    private String orderNo;
    private Integer status;
    private String contractNo;
    private Integer stage;
    private Instant expectStartDate;
    private Instant startDate;
    private Instant expectEndDate;
    private Instant endDate;
    private Integer departMileage;
    private Integer returnMileage;
    private Integer reportMileage;
    private String departRemark;
    private String returnRemark;
    private AccidentInfo accidentInfo;
    private Invoice invoice;
    private String remark;
    private List<Remark> remarks;
    private CreditInfo creditInfo;
    private String nextStageOrderNo;
    private String nonRenewRemark;
    @Enumerated(EnumType.STRING)
    private RenewType renewType = RenewType.PENDING;
    private Boolean isUnpaid;
    private Instant securityDepositDate;
    private Boolean isNewOrder;
    private Integer month;
    private String cancelMemo;
    private String cancelUser;
    private Instant cancelDate;
    private String closeUser;
    private Instant closeDate;
    private Boolean isChangeCarRenew;
    private String srentalParentOrderNo;
    private String departMemberId;
    private String returnMemberId;
    private String lrentalContractNo;
    private String memberId;
    private Integer acctId;

    public static OrdersDTO convertToDto(Orders order) {
        OrdersDTO dto = new OrdersDTO();
        BeanUtils.copyProperties(order, dto);
        return dto;
    }

    public Orders revertToEntity() {
        Orders orders = new Orders();
        BeanUtils.copyProperties(this, orders);
        return orders;
    }
}
