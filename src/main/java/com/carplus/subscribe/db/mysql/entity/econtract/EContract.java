package com.carplus.subscribe.db.mysql.entity.econtract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

/**
 * 電子合約主檔
 */
@Entity
@Table(name = "e_contracts")
@Data
public class EContract extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer eContractId;

    /**
     * 合約檔案類型
     */
    @Column(name = "eContractType")
    private String eContractType;

    /**
     * 合約檔案ID
     */
    @Column(name = "uploadFileId")
    @Type(type = "json")
    private List<Integer> uploadFileId;

    /**
     * 範本代碼
     */
    @Column(name = "templateId")
    private Integer templateId;

    /**
     * 範本版本號
     */
    @Column(name = "versionId")
    private String versionId;

    /**
     * 合約編號
     */
    @Column(name = "contractNo")
    private String contractNo;

    /**
     * 電子合約客戶簽署日期
     */
    @Column(name = "eContractSignDate")
    private Instant eContractSignDate;

    /**
     * 電子合約客戶簽署資訊
     */
    @Column(name = "eContractSignInfo")
    @Type(type = "json")
    private List<String> eContractSignInfo;

    /**
     * 電子出租單客戶簽署日期
     */
    @Column(name = "eRentalSignDate")
    private Instant eRentalSignDate;

    /**
     * 電子出租單客戶簽署資訊
     */
    @Column(name = "eRentalSignInfo")
    @Type(type = "json")
    private List<String> eRentalSignInfo;

    /**
     * 出車任務編號
     */
    @Column(name = "departTaskId")
    private String departTaskId;

    /**
     * 還車任務編號
     */
    @Column(name = "returnTaskId")
    private String returnTaskId;

    /**
     * 會員編號
     */
    @Column(name = "acctId")
    private Integer acctId;

    /**
     * 建立人員
     */
    @Column(name = "createUser")
    private String createUser;

    /**
     * 更新人員
     */
    @Column(name = "updateUser")
    private String updateUser;
}