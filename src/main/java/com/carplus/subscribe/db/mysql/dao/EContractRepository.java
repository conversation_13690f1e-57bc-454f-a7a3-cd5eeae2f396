package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.enums.EContractType;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Repository
public class EContractRepository extends SimpleJpaRepository<EContract, Integer> {

    private final EntityManager em;

    public EContractRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(EContract.class, em);
        this.em = em;
    }

    public List<EContract> getEContractByRefEntityNoAndTypes(String econtractRefEntityNo, List<String> types) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContract> cq = cb.createQuery(EContract.class);
        Root<EContract> root = cq.from(EContract.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(cb.equal(root.get("contractNo"), econtractRefEntityNo));
        predicateList.add(root.get("eContractType").in(types));
        cq.where(predicateList.toArray(new Predicate[0]));
        try {
            return em.createQuery(cq).getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }

    public EContract getEContractByContractNo(String contractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContract> cq = cb.createQuery(EContract.class);
        Root<EContract> root = cq.from(EContract.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(cb.equal(root.get("contractNo"), contractNo));
        predicateList.add(root.get("eContractType").in(new String[] {EContractType.E_CONTRACT.name(), EContractType.OTHER.name()}));
        cq.where(predicateList.toArray(new Predicate[0]));
        try {
            return em.createQuery(cq).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<EContract> getRentalTaskEContractByRefEntityNo(String refEntityNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContract> cq = cb.createQuery(EContract.class);
        Root<EContract> root = cq.from(EContract.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(cb.equal(root.get("contractNo"), refEntityNo));
        predicateList.add(cb.equal(root.get("eContractType"), EContractType.E_RENTAL.name()));
        cq.where(predicateList.toArray(predicateList.toArray(new Predicate[0])));
        try {
            return em.createQuery(cq).getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<EContract> getEContractsByEContractRefEntityNo(String econtractRefEntityNo) {
        String query = "SELECT * FROM e_contracts AS ec WHERE ec.contractNo = :contractNo";
        Query nativeQuery = em.createNativeQuery(query, EContract.class)
            .setParameter("contractNo", econtractRefEntityNo);
        return nativeQuery.getResultList();
    }
}
