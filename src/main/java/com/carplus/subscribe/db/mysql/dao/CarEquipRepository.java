package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.cars.CarEquip;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class CarEquipRepository extends SimpleJpaRepository<CarEquip, Integer> {

    private final EntityManager em;

    public CarEquipRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarEquip.class, em);
        this.em = em;
    }
}
