package com.carplus.subscribe.db.mysql.entity.contract;

import java.time.Instant;

public interface EContractReferencable {

    String getEntityNo();

    String getPlateNo();

    String getDepartStationCode();

    String getReturnStationCode();

    Instant getExpectStartDate();

    Instant getExpectEndDate();

    Instant getStartDate();

    Instant getEndDate();

    Integer getAcctId();

    String getDepartTaskId();

    String getReturnTaskId();

    Integer getStatus();

    String getEContractId();

    int getSecurityDeposit();

    int getMonthlyFee();

    double getMileageFee();
}
