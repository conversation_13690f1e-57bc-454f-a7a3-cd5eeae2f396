package com.carplus.subscribe.db.mysql.entity.cars;

import com.carplus.subscribe.enums.CarDefine;

public interface Orderable {

    Boolean getIsDeleted();

    String getCarStatus();

    CarDefine.Launched getLaunched();

    default boolean isOrderableOfficially() {
        return !Boolean.TRUE.equals(getIsDeleted())
            && CarDefine.CarStatus.Free.getCode().equals(getCarStatus())
            && CarDefine.Launched.open == getLaunched();
    }
}
