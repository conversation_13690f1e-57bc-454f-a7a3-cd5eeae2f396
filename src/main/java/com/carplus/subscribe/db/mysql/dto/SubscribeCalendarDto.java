package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class SubscribeCalendarDto {

    @Schema(description = "日期")
    private String date;

    @Schema(description = "站所代碼")
    private List<String> stationCodes;

    public SubscribeCalendarDto(SubscribeCalendar subscribeCalendar) {
        this.date = subscribeCalendar.getDate();
        this.stationCodes = subscribeCalendar.getStationCodes();
    }
}
