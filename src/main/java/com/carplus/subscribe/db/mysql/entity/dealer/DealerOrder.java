package com.carplus.subscribe.db.mysql.entity.dealer;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.EContractReferencable;
import com.carplus.subscribe.db.mysql.entity.contract.IOrder;
import com.carplus.subscribe.enums.ContractStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.Instant;
import java.util.Optional;

@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Entity(name = "dealer_order")
@DynamicInsert
@DynamicUpdate
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealerOrder extends GeneralEntity implements IOrder, EContractReferencable {

    /**
     * 經銷商訂單編號
     */
    @Id
    private String orderNo;

    /**
     * 經銷商名稱
     */
    private String dealerName;

    /**
     * 訂單狀態
     */
    private Integer orderStatus;

    /**
     * 是否新訂單
     */
    private Boolean isNewOrder;

    /**
     * 是否通過授信
     */
    private Boolean isAudit;

    /**
     * 是否支付保證金
     */
    private Boolean isPaySecurityDeposit;

    /**
     * 保證金支付時間
     */
    private Instant securityDepositDate;

    /**
     * 母約編號
     */
    private String parentOrderNo;

    /**
     * 續約編號
     */
    private String nextStageOrderNo;

    /**
     * 期數
     */
    private String stage;

    /**
     * 車牌號碼
     */
    private String plateNo;

    /**
     * 保險ID
     */
    private String insuranceId;

    /**
     * 經銷商客戶編號
     */
    private Long dealerUserId;

    /**
     * 預定出車站點
     */
    private String expectDepartStation;

    /**
     * 預定還車站點
     */
    private String expectReturnStation;

    /**
     * 實際出車站點
     */
    private String departStation;

    /**
     * 實際還車站點
     */
    private String returnStation;

    /**
     * 預定出車時間
     */
    private Instant expectDepartDate;

    /**
     * 預定還車時間
     */
    private Instant expectReturnDate;

    /**
     * 實際出車時間
     */
    private Instant departDate;

    /**
     * 實際還車時間
     */
    private Instant returnDate;

    /**
     * 保證金
     */
    private Integer securityDeposit;

    /**
     * 月費租金
     */
    private Integer monthlyFee;

    /**
     * 里程費率(實際)
     */
    private Double actualMileageRate;

    /**
     * 里程費率(原價)
     */
    private Double originalMileageRate;

    /**
     * 訂閱租期
     */
    private Integer subscribeMonth;

    /**
     * 款項明細 (待確認)
     */
    private String infoDetail;

    /**
     * 訂單應收總金額
     */
    private Integer totalAmt;

    /**
     * 訂單實收總金額
     */
    private Integer paidAmt;

    /**
     * 是否實際還車
     */
    private Boolean isReturned;

    /**
     * 是否取消
     */
    private Boolean isCancel;

    /**
     * 取消時間
     */
    private Instant cancelDate;

    /**
     * 取消備註
     */
    private String cancelRemark;

    /**
     * 是否同意建立格上會員
     */
    private Boolean isCreateAccount;

    /**
     * 月租金折抵
     */
    private Integer monthlyFeeDiscount;

    /**
     * 里程費率折數
     */
    private Double mileageRateDiscount;

    /**
     * 預收月數
     */
    private Integer prepaidMonths;

    /**
     * 每月預收里程
     */
    private Integer prepaidMileage;

    /**
     * 預收里程折抵
     */
    private Integer prepaidMileageDiscount;

    /**
     * 彈性里程
     */
    private Integer flexibleMileage;

    /**
     * 實際使用里程
     */
    private Integer actualMileageUsed;

    /**
     * 折抵里程1
     */
    private Integer offsetMileage1;

    /**
     * 折抵里程2
     */
    private Integer offsetMileage2;

    /**
     * 預收里程
     */
    private Integer prepaidMileageFee;

    /**
     * 起租金額
     */
    private Integer beginAmt;

    /**
     * 迄租金額
     */
    private Integer closeAmt;

    /**
     * 長租契約編號
     */
    private String lrentalContractNo;

    /**
     * 是否投保免責險
     */
    private boolean disclaimer;

    /**
     * 出車任務編號
     */
    private Integer departTaskId;

    /**
     * 還車任務編號
     */
    private Integer returnTaskId;

    /**
     * 電子合約範本 ID
     */
    private Integer eContractTemplateId;

    /**
     * 電子合約編號
     */
    private Integer eContractId;

    /**
     * 車相關資訊
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "plateNo", name = "plateNo", updatable = false, insertable = false)
    private Cars cars;


    public String getOrderStatusName() {
        return Optional.ofNullable(ContractStatus.codeOfValue(orderStatus)).map(ContractStatus::getName).orElse("");
    }

    @Override
    public Instant getStartDate() {
        return getDepartDate();
    }

    @Override
    public Instant getEndDate() {
        return getReturnDate();
    }

    @Override
    public Instant getExpectStartDate() {
        return getExpectDepartDate();
    }

    @Override
    public Instant getExpectEndDate() {
        return getExpectReturnDate();
    }

    @Override
    public String getTypeDescription() {
        return "經銷商訂單";
    }

    // --- EContractReferencable Implementation ---

    @Override
    public String getEntityNo() {
        return this.orderNo;
    }

    @Override
    public String getDepartStationCode() {
        return this.expectDepartStation;
    }

    @Override
    public String getReturnStationCode() {
        return this.expectReturnStation;
    }

    @Override
    public Integer getAcctId() {
        // DealerOrder is not directly linked to an acctId
        return null;
    }

    @Override
    public String getDepartTaskId() {
        return (this.departTaskId != null) ? String.valueOf(this.departTaskId) : null;
    }

    @Override
    public String getReturnTaskId() {
        return (this.returnTaskId != null) ? String.valueOf(this.returnTaskId) : null;
    }

    @Override
    public Integer getStatus() {
        return this.orderStatus;
    }

    @Override
    public String getEContractId() {
        return (this.eContractId != null) ? String.valueOf(this.eContractId) : null;
    }

    @Override
    public int getSecurityDeposit() {
        return this.securityDeposit;
    }

    @Override
    public int getMonthlyFee() {
        return this.monthlyFee;
    }

    @Override
    public double getMileageFee() {
        return this.actualMileageRate;
    }
}
