package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import java.util.List;

@Data
@Entity(name = "car_model_image")
@Version
@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@IdClass(CarModelImagePK.class)
public class CarModelImage extends GeneralEntity {

    /**
     * 年份
     */
    @Id
    @Column(name = "year", nullable = false)
    private Integer year;

    /**
     * 車型代碼
     */
    @Id
    @Column(name = "carModelCode", nullable = false)
    private String carModelCode;

    /**
     * 路徑
     */
    @Column(name = "paths")
    @Type(type = "json")
    private List<String> paths;

}
