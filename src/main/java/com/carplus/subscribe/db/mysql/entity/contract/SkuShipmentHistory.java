package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.ShipmentStatus;
import lombok.*;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity(name = "sku_shipment_history")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
public class SkuShipmentHistory extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 出貨記錄編號
     */
    private Integer shipmentId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shipmentId", referencedColumnName = "id", insertable = false, updatable = false)
    private SkuShipment skuShipment;

    /**
     * 出貨狀態
     */
    private ShipmentStatus status;

    /**
     * 操作者
     */
    private String operator;
}