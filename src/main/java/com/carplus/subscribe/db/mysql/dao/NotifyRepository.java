package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.Notify;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class NotifyRepository extends SimpleJpaRepository<Notify, Long> {

    private final EntityManager em;

    public NotifyRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Notify.class, em);
        this.em = em;
    }
}
