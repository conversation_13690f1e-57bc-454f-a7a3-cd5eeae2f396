package com.carplus.subscribe.db.mysql.entity.contract;

import java.time.Instant;

public interface IOrder {

    String getOrderNo();

    String getOrderStatusName();

    String getLrentalContractNo();

    void setLrentalContractNo(String contractNo);

    String getPlateNo();

    Boolean getIsNewOrder();

    Instant getStartDate();

    Instant getEndDate();

    Instant getExpectStartDate();

    Instant getExpectEndDate();

    String getTypeDescription();

    String getDepartStationCode();

    String getDepartTaskId();
}
