package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;

@Repository
public class EntityChangeLogRepository extends SimpleJpaRepository<EntityChangeLog, Long> {

    private final EntityManager em;

    public EntityChangeLogRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(EntityChangeLog.class, em);
        this.em = em;
    }

    public List<EntityChangeLog> getByMainEntityNameAndMainEntityId(String mainEntityName, String mainEntityId) {
        return getByMainEntityNameAndMainEntityId(mainEntityName, mainEntityId, 0, Integer.MAX_VALUE);
    }

    public List<EntityChangeLog> getByMainEntityNameAndMainEntityId(String mainEntityName, String mainEntityId, int skip, int limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EntityChangeLog> cq = cb.createQuery(EntityChangeLog.class);
        Root<EntityChangeLog> root = cq.from(EntityChangeLog.class);

        // 使用共用方法創建查詢條件
        Predicate[] predicates = buildMainEntityPredicates(cb, root, mainEntityName, mainEntityId);

        cq.select(root).where(predicates).orderBy(cb.desc(root.get("createDate")));

        return em.createQuery(cq)
            .setFirstResult(skip)
            .setMaxResults(limit)
            .getResultList();
    }

    public long countByMainEntityNameAndMainEntityId(String mainEntityName, String mainEntityId) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<EntityChangeLog> root = cq.from(EntityChangeLog.class);

        // 使用共用方法創建查詢條件
        Predicate[] predicates = buildMainEntityPredicates(cb, root, mainEntityName, mainEntityId);

        cq.select(cb.count(root)).where(predicates);
        return em.createQuery(cq).getSingleResult();
    }

    private Predicate[] buildMainEntityPredicates(CriteriaBuilder cb, Root<EntityChangeLog> root,
                                                  String mainEntityName, String mainEntityId) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.equal(root.get(EntityChangeLog.Fields.mainEntityName), mainEntityName));
        if (mainEntityId != null) {
            predicates.add(cb.equal(root.get(EntityChangeLog.Fields.mainEntityId), mainEntityId));
        }
        return predicates.toArray(new Predicate[0]);
    }

}
