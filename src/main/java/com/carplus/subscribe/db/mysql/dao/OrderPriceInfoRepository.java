package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.OrderPriceInfoCriteria;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class OrderPriceInfoRepository extends SimpleJpaRepository<OrderPriceInfo, Integer> {

    private final EntityManager em;

    public OrderPriceInfoRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(OrderPriceInfo.class, em);
        this.em = em;
    }

    public long countNonZeroAmountPriceInfos(OrderPriceInfoCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<OrderPriceInfo> root = cq.from(OrderPriceInfo.class);

        // 先執行原有的查詢條件
        preparedQuery(criteria, cb, cq, root);

        // 獲取現有的 where 條件
        Predicate existingPredicate = cq.getRestriction();

        // 添加 amount != 0 的條件
        Predicate amountNotZero = cb.notEqual(root.get("amount"), 0);

        // 組合所有條件
        cq.where(cb.and(existingPredicate, amountNotZero));

        cq.select(cb.count(root));
        return em.createQuery(cq).getSingleResult();
    }

    public List<OrderPriceInfo> getPriceInfos(OrderPriceInfoCriteria criteria, Integer skip, Integer limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<OrderPriceInfo> cq = cb.createQuery(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cq.from(OrderPriceInfo.class);
        preparedQuery(criteria, cb, cq, root);
        cq.orderBy(cb.asc(root.get("id")));
        TypedQuery<OrderPriceInfo> typedQuery = em.createQuery(cq);
        if (skip != null) {
            typedQuery.setFirstResult(skip);
        }
        if (limit != null) {
            typedQuery.setMaxResults(limit);
        }
        return typedQuery.getResultList();
    }

    public List<OrderPriceInfo> getPriceInfos(OrderPriceInfoCriteria criteria) {
        return getPriceInfos(criteria, null, null);
    }

    private void preparedQuery(OrderPriceInfoCriteria criteria, CriteriaBuilder cb, CriteriaQuery<?> cq, Root<OrderPriceInfo> root) {

        List<Predicate> predicates = new ArrayList<>();
        if (criteria.getId() != null && criteria.getId() > 0) {
            predicates.add(cb.equal(root.get("id"), criteria.getId()));
        }
        if (criteria.getStage() != null && criteria.getStage() > 0) {
            predicates.add(cb.equal(root.get("stage"), criteria.getStage()));
        }
        if (criteria.getOrderNo() != null && !criteria.getOrderNo().isEmpty()) {
            predicates.add(root.get("orderNo").in(criteria.getOrderNo()));
        }
        if (criteria.getCategory() != null && !criteria.getCategory().isEmpty()) {
            predicates.add(root.get("category").in(criteria.getCategory()));
        }
        if (criteria.getType() != null) {
            predicates.add(cb.equal(root.get("type"), criteria.getType()));
        }
        if (StringUtils.isNotBlank(criteria.getRecTradeId())) {
            predicates.add(cb.equal(root.get("recTradeId"), criteria.getRecTradeId()));
        }
        if (criteria.getPaymentId() != null) {
            predicates.add(cb.equal(root.get("paymentId"), criteria.getPaymentId()));
        }
        if (criteria.getRefPriceInfoNo() != null) {
            predicates.add(cb.equal(root.get("refPriceInfoNo"), criteria.getRefPriceInfoNo()));
        }
        if (StringUtils.isNotBlank(criteria.getUid())) {
            predicates.add(cb.equal(root.get("uid"), criteria.getUid()));
        }
        if (criteria.getDateFrom() != null && criteria.getDateTo() != null) {
            predicates.add(cb.between(root.get("updateDate"), Date.from(criteria.getDateFrom()), Date.from(criteria.getDateTo())));
        }
        cq.where(predicates.toArray(new Predicate[0]));
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer updateRecTradeId(String recTradeId, List<Integer> ids) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaUpdate<OrderPriceInfo> cu = cb.createCriteriaUpdate(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cu.from(OrderPriceInfo.class);
        cu.set("recTradeId", recTradeId).where(root.get("id").in(ids));
        return em.createQuery(cu).executeUpdate();
    }


    /**
     * 刪除訂單所有未付款的額外費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer deleteNotPaidYetExtraFee(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaDelete<OrderPriceInfo> cd = cb.createCriteriaDelete(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cd.from(OrderPriceInfo.class);
        cd.where(
            cb.equal(root.get("category"), (PriceInfoDefinition.PriceInfoCategory.Others)),
            cb.equal(root.get("orderNo"), orderNo),
            cb.isNull(root.get("paymentId")),
            cb.equal(root.get("receivedAmount"), 0)
        );
        return em.createQuery(cd).executeUpdate();
    }

    /**
     * 刪除訂單所有未退款/折扣的門市折扣費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer deleteNotPaidEmpDiscardFee(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaDelete<OrderPriceInfo> cd = cb.createCriteriaDelete(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cd.from(OrderPriceInfo.class);
        cd.where(
            cb.equal(root.get("category"), (PriceInfoDefinition.PriceInfoCategory.EmpDiscount)),
            cb.equal(root.get("orderNo"), orderNo),
            cb.isNull(root.get("paymentId")),
            cb.equal(root.get("receivedAmount"), 0)
        );
        return em.createQuery(cd).executeUpdate();
    }

    /**
     * 刪除訂單所有未退款/折扣的門市折扣費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer deleteNotPaidReturnFee(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaDelete<OrderPriceInfo> cd = cb.createCriteriaDelete(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cd.from(OrderPriceInfo.class);
        cd.where(
            cb.or(cb.equal(root.get("category"), (PriceInfoDefinition.PriceInfoCategory.ReturnEarly)),
                cb.equal(root.get("category"), (PriceInfoDefinition.PriceInfoCategory.ReturnLate))),
            cb.equal(root.get("orderNo"), orderNo),
            cb.isNull(root.get("paymentId")),
            cb.equal(root.get("receivedAmount"), 0)
        );
        return em.createQuery(cd).executeUpdate();
    }

    /**
     * 刪除訂單所有未退款/折扣的費用
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer deleteNotPaidReturnNegativeFee(String orderNo, PriceInfoDefinition.PriceInfoCategory category) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaDelete<OrderPriceInfo> cd = cb.createCriteriaDelete(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cd.from(OrderPriceInfo.class);
        cd.where(
            cb.equal(root.get("category"), category),
            cb.equal(root.get("orderNo"), orderNo),
            cb.isNull(root.get("paymentId")),
            cb.equal(root.get("receivedAmount"), 0),
            cb.or(cb.equal(root.get(OrderPriceInfo.Fields.type), PriceInfoDefinition.PriceInfoType.Discount.getCode()),
                cb.equal(root.get(OrderPriceInfo.Fields.type), PriceInfoDefinition.PriceInfoType.Refund.getCode())
            )
        );
        return em.createQuery(cd).executeUpdate();
    }


    @Transactional(transactionManager = "mysqlTransactionManager")
    public Integer deleteAllByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaDelete<OrderPriceInfo> cd = cb.createCriteriaDelete(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cd.from(OrderPriceInfo.class);
        cd.where(
            cb.equal(root.get("orderNo"), orderNo)
        );
        return em.createQuery(cd).executeUpdate();
    }

    public List<OrderPriceInfo> findAllGeneralCategoryByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<OrderPriceInfo> cq = cb.createQuery(OrderPriceInfo.class);
        Root<OrderPriceInfo> root = cq.from(OrderPriceInfo.class);

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(cb.equal(root.get(OrderPriceInfo.Fields.orderNo), orderNo));

        ArrayList<PriceInfoDefinition.PriceInfoCategory> categories = new ArrayList<>(PriceInfoDefinition.PriceInfoCategory.getCategoriesAddableAsExtraFee());
        categories.add(PriceInfoDefinition.PriceInfoCategory.CarAccident);
        predicates.add(root.get(OrderPriceInfo.Fields.category).in(categories));

        cq.where(predicates.toArray(new Predicate[0]));

        TypedQuery<OrderPriceInfo> typedQuery = em.createQuery(cq);
        return typedQuery.getResultList();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateOrderPriceInfos(List<OrderPriceInfo> list) {
        saveAll(list);
        em.flush();
        em.clear();
    }

    public List<OrderPriceInfo> findAllByRefPriceInfoNoIn(List<Integer> refPriceInfoNos) {
        return findAll((root, query, builder) ->
            root.get("refPriceInfoNo").in(refPriceInfoNos));
    }
}
