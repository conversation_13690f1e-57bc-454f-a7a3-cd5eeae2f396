package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.CarDefine;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;

@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Version(2)
@Entity
@Table(name = "car_model")
public class CarModel extends GeneralEntity {

    /**
     * 車型代碼
     */
    @Id
    @Column(name = "carModelCode", nullable = false)
    private String carModelCode;

    /**
     * 廠牌代碼
     */
    @Column(name = "brandCode", nullable = false)
    private String brandCode;

    /**
     * 車型名稱
     */
    @Column(name = "carModelName", nullable = false)
    private String carModelName;

    /**
     * 車型種類 0:轎車, 1:休旅車, 2:貨車, 8:電動機車
     */
    @Column(name = "carKind", nullable = false)
    private CarDefine.CarKind carKind;

    /**
     * 是否刪除
     */
    @Column(name = "isDeleted", nullable = false)
    private boolean isDeleted;

    /**
     * 短租車型代碼
     */
    @Column(name = "originalCarModelCode")
    private String originalCarModelCode;

    /**
     * 車型
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    @JoinColumn(referencedColumnName = "brandCode", name = "brandCode", insertable = false, updatable = false)
    private CarBrand carBrand;
}
