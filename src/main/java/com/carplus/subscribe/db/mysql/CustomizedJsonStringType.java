package com.carplus.subscribe.db.mysql;

import com.carplus.subscribe.config.mapper.ObjectMapperConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vladmihalcea.hibernate.type.AbstractHibernateType;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import com.vladmihalcea.hibernate.type.json.internal.JsonStringSqlTypeDescriptor;
import com.vladmihalcea.hibernate.type.json.internal.JsonTypeDescriptor;
import com.vladmihalcea.hibernate.type.util.Configuration;
import com.vladmihalcea.hibernate.type.util.ObjectMapperWrapper;
import org.hibernate.usertype.DynamicParameterizedType;

import java.lang.reflect.Type;
import java.util.Properties;

public class CustomizedJsonStringType extends AbstractHibernateType<Object> implements DynamicParameterizedType {
    public static final JsonStringType INSTANCE = new JsonStringType();

    public CustomizedJsonStringType() {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(new ObjectMapperWrapper(new ObjectMapperConfig().objectMapper())));
    }

    public CustomizedJsonStringType(Type javaType) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(new ObjectMapperWrapper(new ObjectMapperConfig().objectMapper()), javaType));
    }

    public CustomizedJsonStringType(Configuration configuration) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(new ObjectMapperWrapper(new ObjectMapperConfig().objectMapper())), configuration);
    }

    public CustomizedJsonStringType(ObjectMapper objectMapper) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(new ObjectMapperWrapper(objectMapper)));
    }

    public CustomizedJsonStringType(ObjectMapperWrapper objectMapperWrapper) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(objectMapperWrapper));
    }

    public CustomizedJsonStringType(ObjectMapper objectMapper, Type javaType) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(new ObjectMapperWrapper(objectMapper), javaType));
    }

    public CustomizedJsonStringType(ObjectMapperWrapper objectMapperWrapper, Type javaType) {
        super(JsonStringSqlTypeDescriptor.INSTANCE, new JsonTypeDescriptor(objectMapperWrapper, javaType));
    }

    public ObjectMapperWrapper getWrapper() {
        return new ObjectMapperWrapper(new ObjectMapperConfig().objectMapper());
    }

    public String getName() {
        return "json";
    }

    protected boolean registerUnderJavaType() {
        return true;
    }

    public void setParameterValues(Properties parameters) {
        ((JsonTypeDescriptor) this.getJavaTypeDescriptor()).setParameterValues(parameters);
    }
}
