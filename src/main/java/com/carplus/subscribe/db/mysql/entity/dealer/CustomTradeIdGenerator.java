package com.carplus.subscribe.db.mysql.entity.dealer;

import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import javax.persistence.Query;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomTradeIdGenerator implements IdentifierGenerator {

    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        String prefix = "T";
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());

        // Query to get the latest tradeId
        String queryStr = "SELECT tradeId FROM dealer_order_price_info WHERE tradeId LIKE :prefix ORDER BY tradeId DESC LIMIT 1";
        Query query = session.createNativeQuery(queryStr);
        query.setParameter("prefix", prefix + date + "%");

        String lastTradeId = (String) query.getResultList().stream().findFirst().orElse(null);

        int nextNumber = 1;

        if (lastTradeId != null) {
            Pattern pattern = Pattern.compile("(\\d{3})$");
            Matcher matcher = pattern.matcher(lastTradeId);

            if (matcher.find()) {
                nextNumber = Integer.parseInt(matcher.group(1)) + 1;
            }
        }

        return String.format("%s%s%03d", prefix, date, nextNumber);
    }
}
