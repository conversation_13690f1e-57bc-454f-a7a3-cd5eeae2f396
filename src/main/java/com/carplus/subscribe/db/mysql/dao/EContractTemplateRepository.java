package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.econtract.EContractTemplate;
import com.carplus.subscribe.enums.ConditionOrderSource;
import com.carplus.subscribe.model.request.econtract.EContractTemplateCriteria;
import com.carplus.subscribe.model.request.econtract.TemplateValidateReq;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static carplus.common.utils.DateUtils.ZONE_TPE;

@Repository
public class EContractTemplateRepository extends SimpleJpaRepository<EContractTemplate, Integer> {

    private final EntityManager em;

    public EContractTemplateRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(EContractTemplate.class, em);
        this.em = em;
    }

    /**
     * 資料筆數
     *
     * @param queryRequest 查詢相關參數
     */
    public long count(@NonNull EContractTemplateCriteria queryRequest) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, queryRequest, true);
        return em.createQuery(cq).getSingleResult();
    }

    /**
     * 查詢資料
     *
     * @param queryRequest 查詢相關參數
     * @param limit        限制回傳筆數
     * @param offset       略過前N筆資料
     */
    public List<Object[]> findBySearch(@NonNull EContractTemplateCriteria queryRequest,
                                       @Nullable Integer limit, @Nullable Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        prepareQuery(cb, cq, queryRequest, false);
        TypedQuery<Object[]> query = em.createQuery(cq);
        if (limit != null) {
            query.setMaxResults(limit);
        }
        if (offset != null) {
            query.setFirstResult(offset);
        }
        return query.getResultList();
    }

    /**
     * 查詢版本歷程資料
     */
    public List<EContractTemplate> findByTemplateCode(@NonNull String templateCode, @Nullable Integer limit, @Nullable Integer offset) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContractTemplate> cq = cb.createQuery(EContractTemplate.class);
        Root<EContractTemplate> root = cq.from(EContractTemplate.class);
        cq.where(cb.equal(root.get("templateCode"), templateCode))
            .orderBy(cb.desc(root.get("createDate")));
        return em.createQuery(cq).setMaxResults(limit).setFirstResult(offset).getResultList();
    }

    /**
     * 查詢版本歷程資料Count
     */
    public Integer findByTemplateCodeCount(@NonNull String templateCode, @Nullable Integer limit, @Nullable Integer offset) {
        return ((Number) em.createNativeQuery("select count(*) from e_contract_template where templateCode = :templateCode order by createDate desc limit :skip, :limit")
            .setParameter("templateCode", templateCode)
            .setParameter("skip", offset)
            .setParameter("limit", limit)
            .getSingleResult()).intValue();
    }

    /**
     * 組SQL語法
     *
     * @param cb           CriteriaBuilder
     * @param cq           CriteriaQuery
     * @param queryRequest 查詢相關參數
     * @param isCount      是否為查詢資料筆數
     */
    @NonNull
    public void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, @NonNull EContractTemplateCriteria queryRequest, boolean isCount) {
        Root<EContractTemplate> ectRoot = cq.from(EContractTemplate.class);
        Join<EContractTemplate, CarBrand> carBrandJoin = ectRoot.join("carBrand", JoinType.LEFT);
        Join<EContractTemplate, CarModel> carModelJoin = ectRoot.join("carModel", JoinType.LEFT);
        List<Predicate> predicateList = new ArrayList<>();

        addExtraPredicates(cb, queryRequest, predicateList, ectRoot);

        if (queryRequest.getIsEnabled() != null) {
            Instant endOfToday = DateUtils.toDate(LocalDate.now(ZONE_TPE).plusDays(1)).toInstant();
            if (queryRequest.getIsEnabled()) {
                findLatestEnableTemplate(cb, cq, predicateList, ectRoot, endOfToday);
            } else {
                findExcludeLatestEnableTemplate(cb, cq, predicateList, ectRoot, endOfToday);
            }
        }
        cq.where(predicateList.toArray(new Predicate[0]));

        if (isCount) {
            @SuppressWarnings("unchecked")
            CriteriaQuery<Long> countQuery = (CriteriaQuery<Long>) cq;
            countQuery.select(cb.count(ectRoot));
        } else {
            cq.multiselect(ectRoot, carBrandJoin, carModelJoin);
            cq.orderBy(cb.desc(ectRoot.get("createDate")));
        }
    }

    /**
     * 生效日(最新日期至多為當天)
     */
    private static void findLatestEnableTemplate(CriteriaBuilder cb, CriteriaQuery<?> cq, List<Predicate> predicateList,
                                                 Root<EContractTemplate> ectRoot, Instant endOfToday) {
        Subquery<Integer> subquery = getLatestEnableTemplates(cb, cq, ectRoot, endOfToday);
        predicateList.add(cb.not(cb.exists(subquery)));
        predicateList.add(cb.lessThan(ectRoot.get("enableDate"), endOfToday));
    }

    private static Subquery<Integer> getLatestEnableTemplates(CriteriaBuilder cb, CriteriaQuery<?> cq, Root<EContractTemplate> ectRoot, Instant endOfToday) {
        Subquery<Integer> subquery = cq.subquery(Integer.class);
        Root<EContractTemplate> ectSub = subquery.from(EContractTemplate.class);

        subquery.select(cb.literal(1))
                .where(cb.and(
                        cb.equal(ectSub.get("templateCode"), ectRoot.get("templateCode")),
                        cb.or(
                                cb.greaterThan(ectSub.get("enableDate"), ectRoot.get("enableDate")),
                                cb.and(
                                        cb.equal(ectSub.get("enableDate"), ectRoot.get("enableDate")),
                                        cb.greaterThan(ectSub.get("createDate"), ectRoot.get("createDate"))
                                )
                        ),
                        cb.lessThan(ectSub.get("enableDate"), endOfToday)
                ));
        return subquery;
    }

    /**
     * isEnable=false，呈現排除LatestEnableTemplate剩下的合約範本
     */
    private static void findExcludeLatestEnableTemplate(CriteriaBuilder cb, CriteriaQuery<?> cq, List<Predicate> predicateList,
                                                        Root<EContractTemplate> ectRoot, Instant endOfToday) {
        Subquery<Integer> excludeSubquery = cq.subquery(Integer.class);
        Root<EContractTemplate> excludeRoot = excludeSubquery.from(EContractTemplate.class);

        Subquery<Integer> innerSubquery = getLatestEnableTemplates(cb, cq, excludeRoot, endOfToday);

        excludeSubquery.select(excludeRoot.get("templateId"))
                .where(cb.and(
                        cb.not(cb.exists(innerSubquery)),
                        cb.lessThanOrEqualTo(excludeRoot.get("enableDate"), endOfToday)
                ));

        predicateList.add(cb.not(cb.in(ectRoot.get("templateId")).value(excludeSubquery)));
    }

    /**
     * 未來(不包含當天)
     */
    private static void findFutureNotEnableTemplate(CriteriaBuilder cb, CriteriaQuery<?> cq,
                                                    List<Predicate> predicateList, Root<EContractTemplate> ectRoot) {
        predicateList.add(cb.greaterThan(ectRoot.get("enableDate"), cb.currentDate()));
        predicateList.add(cb.isNotNull(ectRoot.get("enableDate")));

        Subquery<Integer> subquery = cq.subquery(Integer.class);
        Root<EContractTemplate> ectSub = subquery.from(EContractTemplate.class);

        subquery.select(cb.literal(1))
                .where(cb.and(
                        cb.equal(ectSub.get("templateCode"), ectRoot.get("templateCode")),
                        cb.greaterThan(ectSub.get("enableDate"), cb.currentDate()),
                        cb.isNotNull(ectSub.get("enableDate")),
                        cb.or(
                                cb.lessThan(ectSub.get("enableDate"), ectRoot.get("enableDate")),
                                cb.and(
                                        cb.equal(ectSub.get("enableDate"), ectRoot.get("enableDate")),
                                        cb.greaterThan(ectSub.get("createDate"), ectRoot.get("createDate"))
                                )
                        )
            ));

        predicateList.add(cb.not(cb.exists(subquery)));
    }

    private static void addExtraPredicates(CriteriaBuilder cb, EContractTemplateCriteria queryRequest,
                                           List<Predicate> predicateList, Root<EContractTemplate> ectRoot) {
        // 範本代碼/名稱(營業)
        if (Objects.nonNull(queryRequest.getTemplateIdNameSales()) && !queryRequest.getTemplateIdNameSales().isEmpty()) {
            predicateList.add(cb.like(ectRoot.get("templateNameSales"), "%" + queryRequest.getTemplateIdNameSales() + "%"));
        }
        // 訂單來源
        if (Objects.nonNull(queryRequest.getConditionOrderSource())) {
            predicateList.add(cb.equal(ectRoot.get("conditionOrderSource"), queryRequest.getConditionOrderSource()));
        }
        // 車輛所屬
        if (Objects.nonNull(queryRequest.getConditionCarBu()) && !queryRequest.getConditionCarBu().isEmpty()) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarBu"), queryRequest.getConditionCarBu()));
        }
        // 車輛廠牌
        if (Objects.nonNull(queryRequest.getConditionCarBrand()) && !queryRequest.getConditionCarBrand().isEmpty()) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarBrand"), queryRequest.getConditionCarBrand()));
        }
        // 訂閱類別
        if (Objects.nonNull(queryRequest.getConditionCarState()) && !queryRequest.getConditionCarState().isEmpty()) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarState"), queryRequest.getConditionCarState()));
        }
        // 車輛牌價起始
        if (Objects.nonNull(queryRequest.getConditionCarPriceStart())) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarPriceStart"), queryRequest.getConditionCarPriceStart()));
        }
        // 車輛牌價結束
        if (Objects.nonNull(queryRequest.getConditionCarPriceEnd())) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarPriceEnd"), queryRequest.getConditionCarPriceEnd()));
        }
        // 訂閱租期
        if (Objects.nonNull(queryRequest.getConditionOrderMonth())) {
            predicateList.add(cb.equal(ectRoot.get("conditionOrderMonth"), queryRequest.getConditionOrderMonth()));
        }
        // 額外駕駛保障
        if (Objects.nonNull(queryRequest.getIsDisclaimerFee())) {
            predicateList.add(cb.equal(ectRoot.get("isDisclaimerFee"), queryRequest.getIsDisclaimerFee()));
        }
        // 是否冰宇車
        if (Objects.nonNull(queryRequest.getIsSeaLandCar())) {
            predicateList.add(cb.equal(ectRoot.get("isSeaLandCar"), queryRequest.getIsSeaLandCar()));
        }
        // 車型
        if (Objects.nonNull(queryRequest.getConditionCarModel()) && !queryRequest.getConditionCarModel().isEmpty()) {
            predicateList.add(cb.equal(ectRoot.get("conditionCarModel"), queryRequest.getConditionCarModel()));
        }
    }

    /**
     * 取得最後的範本代碼
     */
    public String getLastTemplateCode() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<EContractTemplate> root = cq.from(EContractTemplate.class);
        cq.select(root.get("templateCode")).orderBy(cb.desc(root.get("templateCode")));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public String getLastVersionId(String templateCode) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<EContractTemplate> root = cq.from(EContractTemplate.class);
        cq.select(root.get("versionId"))
            .where(cb.equal(root.get("templateCode"), templateCode))
            .orderBy(cb.desc(root.get("versionId")));
        try {
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 範本是否已存在
     */
    public boolean isTemplateIsExists(TemplateValidateReq req) {
        List<EContractTemplate> results = getTemplateList(req);
        return !results.isEmpty();
    }

    /**
     * 透過特定條件拿取範本清單
     */
    public List<EContractTemplate> getTemplateList(TemplateValidateReq req) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContractTemplate> cq = cb.createQuery(EContractTemplate.class);
        Root<EContractTemplate> root = cq.from(EContractTemplate.class);
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(root.get("conditionOrderSource"), Objects.requireNonNull(ConditionOrderSource.codeOfValue(req.getConditionOrderSource())).getCode()));
        predicates.add(cb.equal(root.get("conditionOrderMonth"), req.getConditionOrderMonth()));
        predicates.add(cb.equal(root.get("conditionCarBu"), req.getConditionCarBu()));
        predicates.add(cb.equal(root.get("conditionCarState"), req.getConditionCarState()));
        predicates.add(cb.equal(root.get("isDisclaimerFee"), req.isDisclaimerFee()));
        predicates.add(cb.equal(root.get("isSeaLandCar"), req.isSeaLandCar()));
        if (StringUtils.isNotBlank(req.getConditionCarBrand())) {
            predicates.add(cb.equal(root.get("conditionCarBrand"), req.getConditionCarBrand()));
        } else {
            predicates.add(cb.or(
                cb.isNull(root.get("conditionCarBrand")),
                cb.equal(root.get("conditionCarBrand"), "")
            ));
        }
        if (StringUtils.isNotBlank(req.getConditionCarModel())) {
            predicates.add(cb.equal(root.get("conditionCarModel"), req.getConditionCarModel()));
        } else {
            predicates.add(cb.or(
                cb.isNull(root.get("conditionCarModel")),
                cb.equal(root.get("conditionCarModel"), "")
            ));
        }
        predicates.add(cb.or(
            cb.and(
                cb.lessThanOrEqualTo(root.get("conditionCarPriceStart"), req.getConditionCarPriceStart()),
                cb.greaterThanOrEqualTo(root.get("conditionCarPriceEnd"), req.getConditionCarPriceStart())),
            cb.and(
                cb.lessThanOrEqualTo(root.get("conditionCarPriceStart"), req.getConditionCarPriceEnd()),
                cb.greaterThanOrEqualTo(root.get("conditionCarPriceEnd"), req.getConditionCarPriceEnd()))
        ));

        cq.where(predicates.toArray(new Predicate[0]));
        return em.createQuery(cq).getResultList();
    }
}
