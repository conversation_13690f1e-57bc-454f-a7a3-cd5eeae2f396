package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

import javax.persistence.Entity;
import javax.persistence.Id;

@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Data
@Entity(name = "sku")
public class Sku extends GeneralEntity {

    /**
     * 商品代碼
     */
    @Id
    private String code;

    /**
     * 商品類型
     */
    private String type;

    /**
     * 商品名稱
     */
    private String name;

    /**
     * 商品單價
     */
    private Integer unitPrice;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品圖片路徑
     */
    private String imgPath;

    /**
     * 是否顯示於官網
     */
    private boolean isOfficial;

    /**
     * 是否顯示於收銀台
     */
    private boolean isCashier;
}
