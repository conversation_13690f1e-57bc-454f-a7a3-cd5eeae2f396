package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.CarReady;
import com.carplus.subscribe.model.*;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.*;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "mainContractNo")
@Entity
@Table(name = "main_contract")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MainContract extends GeneralEntity {
    /**
     * 合約編號
     */
    @Id
    private String mainContractNo;

    /**
     * 合約狀態
     */
    private Integer status;

    /**
     * 訂車客戶編號
     */
    private Integer acctId;

    /**
     * 訂車身分證號
     */
    @Column(name = "idNo")
    private String idNo;

    /**
     * 車型代碼
     */
    @Column(name = "carModelCode")
    private String carModelCode;

    /**
     * 實際排車方案
     */
    @Column(name = "givenCarLevel")
    private Integer givenCarLevel;

    /**
     * 實際排車的車型代碼
     */
    @Column(name = "givenCarModelCode")
    private String givenCarModelCode;

    /**
     * 駕駛人
     */
    @Column(name = "driver")
    @Type(type = "json")
    private Driver driver;


    /**
     * 車牌號碼
     */
    @Column(name = "plateNo")
    private String plateNo;

    /**
     * 下單時車牌號碼
     */
    @Column(name = "orderPlateNo")
    private String orderPlateNo;

    /**
     * 最後一台被汰換的車牌號碼
     */
    @Column(name = "replacedPlateNo")
    private String replacedPlateNo;

    /**
     * 車籍資料
     */
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(referencedColumnName = "plateNo", name = "plateNo", insertable = false, updatable = false)
    private Cars cars;

    /**
     * 預計開始時間
     */
    @Column(name = "expectStartDate")
    private Instant expectStartDate;

    /**
     * 實際開始時間
     */
    @Column(name = "startDate")
    private Instant startDate;

    /**
     * 預計結束時間
     */
    @Column(name = "expectEndDate")
    private Instant expectEndDate;

    /**
     * 出車後預計結束時間
     */
    @Column(name = "newExpectEndDate")
    private Instant newExpectEndDate;

    /**
     * 實際結束時間
     */
    @Column(name = "endDate")
    private Instant endDate;

    /**
     * 出發站所
     */
    @Column(name = "departStationCode")
    private String departStationCode;

    /**
     * 還車站所
     */
    @Column(name = "returnStationCode")
    private String returnStationCode;

    /**
     * 租車備註
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 客戶備註
     */
    @Column(name = "custRemark")
    private String custRemark;

    /**
     * 對客戶留言
     */
    @Type(type = "json")
    @Column(name = "msgForCust")
    private List<MsgForCust> msgForCust;

    /**
     * 線上訂單初始金額明細
     */
    @Type(type = "json")
    @Column(name = "originalPriceInfo")
    private PriceInfo originalPriceInfo;

    /**
     * 事故類別 1:車損 2:失竊
     */
    @Column(name = "accidentType")
    private Integer accidentType;

    /**
     * 車損資訊
     */
    @Type(type = "json")
    @Column(name = "accidentInfo")
    private AccidentInfo accidentInfo;


    /**
     * UTM活動
     */
    @Column(name = "utmCampaign")
    private String utmCampaign;
    /**
     * UTM來源
     */
    @Column(name = "utmSource")
    private String utmSource;
    /**
     * UTM裝置
     */
    @Column(name = "utmMedium")
    private String utmMedium;

    /**
     * 介紹人資訊
     */
    @Column(name = "referInfo")
    @Type(type = "json")
    private ReferInfo referInfo;

    /**
     * 訂單來源
     */
    private Integer custSource;

    /**
     * 法人承租人
     */
    @Type(type = "json")
    private CompanyDriver companyDriver;

    /**
     * 備車註記
     */
    @Enumerated(EnumType.STRING)
    private CarReady carReady;

    @OneToMany(mappedBy = "mainContract")
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<Contract> contracts;

    /**
     * Srental母單號馬
     */
    @Column(name = "parentNo")
    private String parentNo;
}