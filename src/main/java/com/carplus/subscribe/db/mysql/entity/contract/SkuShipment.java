package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.ShipmentStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity(name = "sku_shipment")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@FieldNameConstants
public class SkuShipment extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 訂單編號
     */
    private String orderNo;

    /**
     * 訂單費用資訊編號
     */
    private Integer orderPriceInfoId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "orderPriceInfoId", referencedColumnName = "id", insertable = false, updatable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private OrderPriceInfo orderPriceInfo;

    /**
     * 汽車用品代碼
     */
    private String skuCode;

    /**
     * 出貨狀態
     */
    private ShipmentStatus status;

    @OneToMany(mappedBy = "skuShipment", cascade = CascadeType.REMOVE)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<SkuShipmentHistory> historyList = new ArrayList<>();

    /**
     * 最後異動人員
     */
    private String updater;
}