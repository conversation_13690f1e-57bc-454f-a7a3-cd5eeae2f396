package com.carplus.subscribe.db.mysql.entity.econtract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.model.notify.MailAttachment;
import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

/**
 * 電子合約主檔
 */
@Entity(name = "e_contract_mail_history")
@Data
public class EContractMailHistory extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 合約編號
     */
    @Column(name = "contractNo")
    private String contractNo;

    /**
     * 附件
     */
    @Column(name = "attachments")
    @Type(type = "json")
    private List<MailAttachment> attachments;

    /**
     * 收件者會員編號
     */
    @Column(name = "receiveAcctId")
    private Integer receiveAcctId;

    /**
     * 收件者信箱
     */
    @Column(name = "receiver")
    private String receiver;

    /**
     * 寄送人ID
     */
    @Column(name = "targetMemberId")
    private String targetMemberId;

    /**
     * 寄送人Name
     */
    @Column(name = "targetMember")
    private String targetMember;

}