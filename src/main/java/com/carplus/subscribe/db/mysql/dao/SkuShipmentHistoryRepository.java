package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.contract.SkuShipmentHistory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;

@Repository
public class SkuShipmentHistoryRepository extends SimpleJpaRepository<SkuShipmentHistory, Integer> {

    private final EntityManager em;

    public SkuShipmentHistoryRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(SkuShipmentHistory.class, em);
        this.em = em;
    }
}