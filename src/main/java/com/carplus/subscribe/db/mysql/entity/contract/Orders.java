package com.carplus.subscribe.db.mysql.entity.contract;

import com.carplus.subscribe.db.mysql.LazyFieldsFilter;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.enums.RenewType;
import com.carplus.subscribe.model.AccidentInfo;
import com.carplus.subscribe.model.credit.CreditInfo;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import static java.time.temporal.ChronoUnit.DAYS;

@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "orderNo")
@FieldNameConstants
@Entity(name = "orders")
@Data
public class Orders extends GeneralEntity implements IOrder {

    @Id
    private String orderNo;

    /**
     * 訂單狀態
     */
    private Integer status;

    /**
     * 合約編號
     */
    private String contractNo;

    /**
     * 期數
     */
    private Integer stage;

    /**
     * 預期出車時間
     */
    private Instant expectStartDate;

    /**
     * 實際出車時間
     */
    private Instant startDate;

    /**
     * 預期還車時間
     */
    private Instant expectEndDate;

    /**
     * 實際還車時間
     */
    private Instant endDate;

    /**
     * 出車里程
     */
    private Integer departMileage;

    /**
     * 還車里程
     */
    private Integer returnMileage;

    /**
     * 出車備註
     */
    private String departRemark;

    /**
     * 還車備註
     */
    private String returnRemark;

    /**
     * 途中最後回報里程
     */
    private Integer reportMileage;

    /**
     * 備註
     */
    private String remark;

    /**
     * 訂單備註
     */
    @Type(type = "json")
    private List<Remark> remarks;

    /**
     * 執行法務作業事由 (預設 0:未執行, 1-5:對應 LegalOperationReason enum)
     */
    @JsonIgnore
    @Column(name = "legalOperationReason", nullable = false)
    private int legalOperationReason;

    /**
     * 授信內容
     */
    @Type(type = "json")
    private CreditInfo creditInfo;

    /**
     * 發票資訊
     */
    @Type(type = "json")
    private Invoice invoice;

    /**
     * 訂單有無未繳款狀態
     */
    @Column(name = "isUnpaid")
    private Boolean isUnpaid;

    /**
     * 是否新訂單
     */
    private Boolean isNewOrder = false;

    /**
     * 保證金付款時間、訂單成立時間
     */
    @Column(name = "securityDepositDate")
    private Instant securityDepositDate;

    /**
     * 訂閱月份
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 結案人員
     */
    @Column(name = "closeUser")
    private String closeUser;
    /**
     * 結案日期
     */
    @Column(name = "closeDate")
    private Instant closeDate;

    /**
     * 取消人員
     */
    private String cancelUser;
    /**
     * 取消時間
     */
    private Instant cancelDate;
    /**
     * 取消備註
     */
    private String cancelMemo;

    /**
     * etag 相關資訊
     */
    @OneToMany(mappedBy = "order")
    @Column(name = "eTagInfos")
    @JsonIgnore
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<ETagInfo> eTagInfos;

    /**
     * 價格資訊
     */
    @OneToMany(mappedBy = "order")
    @Column(name = "priceInfo")
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<OrderPriceInfo> priceInfos;

    /**
     * 價格資訊
     */
    @Column(name = "invoices")
    @OneToMany
    @JoinColumn(referencedColumnName = "orderNo", name = "orderNo", updatable = false, insertable = false)
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = LazyFieldsFilter.class)
    private List<Invoices> invoices;

    @ManyToOne(cascade = CascadeType.REFRESH)
    @JoinColumn(referencedColumnName = "contractNo", name = "contractNo", updatable = false, insertable = false)
    private Contract contract;

    /**
     * 續約訂單號
     */
    private String nextStageOrderNo;

    /**
     * 不續約原因
     */
    private String nonRenewRemark;

    /**
     * 續約狀態
     */
    @Enumerated(EnumType.STRING)
    private RenewType renewType = RenewType.PENDING;

    /**
     * 訂閱V1是否有換車續約
     */
    private Boolean isChangeCarRenew = false;

    /**
     * 訂閱V1母約編號
     */
    private String srentalParentOrderNo;

    /**
     * 出車人員編號
     */
    @Column(name = "departMemberId")
    private String departMemberId;

    /**
     * 還車人員編號
     */
    @Column(name = "returnMemberId")
    private String returnMemberId;

    /**
     * 長租契約編號
     */
    private String lrentalContractNo;

    /**
     * 車損資訊
     */
    @Type(type = "json")
    @Column(name = "accidentPriceInfo")
    private AccidentInfo accidentInfo;

    /**
     * 員工編號
     */
    @Column(name = "memberId")
    private String memberId;

    /**
     * 使用者編號
     */
    @Column(name = "acctId")
    private Integer acctId;

    /**
     * 訂單平台來源
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "orderPlatform")
    private OrderPlatform orderPlatform;

    public String getOrderStatusName() {
        return OrderStatus.of(status).getName();
    }

    public CalculateStage getCurrentStage() {
        return DateUtil.calculateStageAndDateByTargetDate(this, Instant.now());
    }

    public CalculateStage getActualCurrentStage() {
        return DateUtil.calculateStageAndDateByTargetDate(this, Instant.now(), true);
    }

    public void setExpectEndDate(Instant expectEndDate) {
        // 檢查訂單預計結束日是否接近合約結束日10天內，若是，則為合約預計結束
        if (!isNewOrder && contract != null && expectEndDate.isBefore(contract.getExpectEndDate()) && DateUtil.calculateDiffDate(expectEndDate, contract.getExpectEndDate(), DAYS) < 10) {
            this.expectEndDate = contract.getExpectEndDate();
        } else {
            this.expectEndDate = expectEndDate;
        }
    }

    @Override
    @JsonIgnore
    public String getPlateNo() {
        return Optional.ofNullable(getContract())
            .map(Contract::getMainContract)
            .map(MainContract::getPlateNo)
            .orElse(null);
    }

    @Override
    public String getTypeDescription() {
        return "格上訂單";
    }

    @Override
    @JsonIgnore
    public String getDepartStationCode() {
        return Optional.ofNullable(getContract())
            .map(Contract::getMainContract)
            .map(MainContract::getDepartStationCode)
            .orElse(null);
    }

    @Override
    @JsonIgnore
    public String getDepartTaskId() {
        return Optional.ofNullable(this.getContract()).map(Contract::getDepartTaskId).orElse(null);
    }
}
