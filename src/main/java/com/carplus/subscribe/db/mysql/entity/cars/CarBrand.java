package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.*;
import lombok.experimental.FieldNameConstants;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Version(2)
@Entity
@Table(name = "car_brand")
public class CarBrand extends GeneralEntity {

    /**
     * 廠牌代碼
     */
    @Id
    @Column(name = "brandCode", nullable = false)
    private String brandCode;

    /**
     * 廠牌名稱
     */
    @Column(name = "brandName", nullable = false)
    private String brandName;

    /**
     * 廠牌英文名稱
     */
    @Column(name = "brandNameEn", nullable = false)
    private String brandNameEn;

    /**
     * 車廠牌是否呈現官網介紹
     */
    @Column(name = "isAppearOnOfficial")
    private boolean isAppearOnOfficial;

    /**
     * 排序號碼
     */
    @Column(name = "seqNo")
    private Integer seqNo;

    /**
     * 是否顯示品牌專頁
     */
    @Column(name = "isShowPage")
    private boolean isShowPage;

    /**
     * LOGO圖卡
     */
    @Column(name = "logoImgUrl")
    private String logoImgUrl;

    /**
     * 車輛圖卡
     */
    @Column(name = "carImgUrl")
    private String carImgUrl;
}
