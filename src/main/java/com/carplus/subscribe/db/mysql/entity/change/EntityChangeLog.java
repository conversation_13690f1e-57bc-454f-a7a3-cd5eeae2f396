package com.carplus.subscribe.db.mysql.entity.change;

import com.carplus.subscribe.config.mapper.EntityBaseDeserializer;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@FieldNameConstants
@Entity(name = "entity_change_log")
public class EntityChangeLog extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    protected Integer id;

    @Column(name = "requestUrl")
    protected String requestUrl;

    @Column(name = "requestMethod")
    protected String requestMethod;

    @Column(name = "requestBody")
    protected String requestBody;

    /**
     * 異動主實體名稱
     */
    @Column(name = "mainEntityName")
    protected String mainEntityName;

    /**
     * 異動主實體主鍵
     */
    @Column(name = "mainEntityId")
    protected String mainEntityId;

    /**
     * 異動者
     */
    @Column(name = "changedBy")
    protected String changedBy;

    /**
     * 異動者中文名稱
     */
    @Transient
    protected String operatorName;

    /**
     * 異動內容
     */
    @Type(type = "json")
    @Column(name = "changes")
    @JsonDeserialize(contentUsing = EntityBaseDeserializer.class)
    private List<? extends EntityBase> changes;
}
