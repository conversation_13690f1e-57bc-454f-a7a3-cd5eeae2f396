package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.model.request.campaign.CampaignCriteria;
import com.carplus.subscribe.model.request.campaign.CommonCampaignCriteria;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Repository
public class CampaignRepository extends SimpleJpaRepository<Campaign, Integer> {

    private final EntityManager em;

    public CampaignRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Campaign.class, em);
        this.em = em;
    }

    public <T extends CommonCampaignCriteria> long count(@NonNull T criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getResultList().size();
    }

    @NonNull
    public <T extends CommonCampaignCriteria> void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, @NonNull T criteria, boolean isCount) {
        Root<Campaign> campaignRoot = cq.from(Campaign.class);

        List<Predicate> predicateList = new ArrayList<>();

        // 處理 isDeleted 條件
        if (!(criteria instanceof CampaignCriteria) || ((CampaignCriteria) criteria).isExcludeDeleted()) {
            predicateList.add(cb.equal(campaignRoot.get(Campaign.Fields.isDeleted), false));
        }
        // 處理活動時間條件
        if (!(criteria instanceof CampaignCriteria) || ((CampaignCriteria) criteria).isOnlyActive()) {
            // 活動開始時間 ≤ 現在 ≤ 活動結束時間
            Instant now = Instant.now();
            predicateList.add(cb.lessThanOrEqualTo(campaignRoot.get(Campaign.Fields.startDate), now));
            predicateList.add(cb.greaterThanOrEqualTo(campaignRoot.get(Campaign.Fields.endDate), now));
        }

        if (isCount) {
            cq.select(campaignRoot.get(Campaign.Fields.id));
        }
        cq.where(predicateList.toArray(new Predicate[0]));
    }

    public <T extends CommonCampaignCriteria> List<Campaign> findBySearch(@NonNull T criteria, @Nullable Integer offset, @Nullable Integer limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Campaign> cq = cb.createQuery(Campaign.class);
        prepareQuery(cb, cq, criteria, false);
        TypedQuery<Campaign> query = em.createQuery(cq);
        if (offset != null) {
            query.setFirstResult(offset);
        }
        if (limit != null) {
            query.setMaxResults(limit);
        }
        return query.getResultList();
    }
}
