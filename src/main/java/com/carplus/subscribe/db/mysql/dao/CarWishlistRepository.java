package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.CarWishlist;
import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistCriteria;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistReportRequest;
import com.carplus.subscribe.model.request.carwishlist.CommonCarWishlistCriteria;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class CarWishlistRepository extends SimpleJpaRepository<CarWishlist, Integer> {

    private final EntityManager em;

    public CarWishlistRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarWishlist.class, em);
        this.em = em;
    }

    @SuppressWarnings("unchecked")
    private <T extends CommonCarWishlistCriteria> void prepareQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, T criteria, boolean isCount) {
        Root<CarWishlist> carWishlistRoot = cq.from(CarWishlist.class);
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(carWishlistRoot.get(CarWishlist.Fields.acctId), criteria.getAcctId()));
        if (CollectionUtils.isNotEmpty(criteria.getPlateNos())) {
            predicates.add(carWishlistRoot.get(CarWishlist.Fields.plateNo).in(criteria.getPlateNos()));
        }

        if (!criteria.isIncludeDeleted()) {
            predicates.add(cb.isNull(carWishlistRoot.get(CarWishlist.Fields.deletedAt)));
        }

        if (criteria instanceof CarWishlistCriteria) {
            CarWishlistCriteria carWishlistCriteria = (CarWishlistCriteria) criteria;
            if (CollectionUtils.isNotEmpty(carWishlistCriteria.getBrandCodes()) || CollectionUtils.isNotEmpty(carWishlistCriteria.getCarModelCodes())) {
                Join<CarWishlist, Cars> carsJoin = carWishlistRoot.join(CarWishlist.Fields.car, JoinType.LEFT);
                Join<Cars, CarModel> carModelJoin = carsJoin.join(Cars.Fields.carModel, JoinType.LEFT);

                if (CollectionUtils.isNotEmpty(carWishlistCriteria.getBrandCodes())) {
                    Join<CarModel, CarBrand> carBrandJoin = carModelJoin.join(CarModel.Fields.carBrand, JoinType.LEFT);
                    predicates.add(carBrandJoin.get(CarBrand.Fields.brandCode).in(carWishlistCriteria.getBrandCodes()));
                }
                if (CollectionUtils.isNotEmpty(carWishlistCriteria.getCarModelCodes())) {
                    predicates.add(carModelJoin.get(CarModel.Fields.carModelCode).in(carWishlistCriteria.getCarModelCodes()));
                }
            }
        }

        if (isCount) {
            ((CriteriaQuery<Long>) cq).select(cb.count(carWishlistRoot));
        } else {
            ((CriteriaQuery<CarWishlist>) cq).select(carWishlistRoot).orderBy(cb.desc(carWishlistRoot.get("updateDate")));
        }
        cq.where(predicates.toArray(new Predicate[0]));
    }

    private void prepareReportQuery(CriteriaBuilder cb, CriteriaQuery<CarWishlist> cq,
                                    CarWishlistReportRequest request, List<Integer> acctIds) {
        Root<CarWishlist> carWishlistRoot = cq.from(CarWishlist.class);
        List<Predicate> predicates = new ArrayList<>();

        // 排除已刪除的項目
        predicates.add(cb.isNull(carWishlistRoot.get(CarWishlist.Fields.deletedAt)));

        // acctId 過濾
        if (CollectionUtils.isNotEmpty(acctIds)) {
            predicates.add(carWishlistRoot.get(CarWishlist.Fields.acctId).in(acctIds));
        }

        // 加入收藏時間範圍過濾
        predicates.add(cb.between(carWishlistRoot.get("updateDate"), Date.from(request.getStartTime()), Date.from(request.getEndTime())));

        // 車型代碼過濾
        if (CollectionUtils.isNotEmpty(request.getCarModelCodes())) {
            Join<CarWishlist, Cars> carsJoin = carWishlistRoot.join(CarWishlist.Fields.car, JoinType.LEFT);
            Join<Cars, CarModel> carModelJoin = carsJoin.join(Cars.Fields.carModel, JoinType.LEFT);
            predicates.add(carModelJoin.get(CarModel.Fields.carModelCode).in(request.getCarModelCodes()));
        }

        cq.select(carWishlistRoot)
            .where(predicates.toArray(new Predicate[0]))
            .orderBy(cb.desc(carWishlistRoot.get("updateDate")));
    }

    public long count(@NonNull CommonCarWishlistCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        prepareQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getSingleResult();
    }

    public <T extends CommonCarWishlistCriteria> List<CarWishlist> findBySearch(T criteria, int offset, int limit) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<CarWishlist> cq = cb.createQuery(CarWishlist.class);
        prepareQuery(cb, cq, criteria, false);
        return em.createQuery(cq)
            .setFirstResult(offset)
            .setMaxResults(limit)
            .getResultList();
    }

    public boolean existsByAcctIdAndPlateNo(Integer acctId, String plateNo) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        criteria.setPlateNos(Collections.singletonList(plateNo));
        return count(criteria) > 0;
    }

    public long countByAcctIdAndDeletedAtIsNull(Integer acctId) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        return count(criteria);
    }

    public Map<Integer, Long> countNonDeletedByAcctIds(Set<Integer> acctIds) {
        if (acctIds.isEmpty()) {
            return Collections.emptyMap();
        }

        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Object[]> cq = cb.createQuery(Object[].class);
        Root<CarWishlist> root = cq.from(CarWishlist.class);

        cq.multiselect(root.get(CarWishlist.Fields.acctId), cb.count(root))
            .where(cb.and(root.get(CarWishlist.Fields.acctId).in(acctIds), cb.isNull(root.get(CarWishlist.Fields.deletedAt))))
            .groupBy(root.get(CarWishlist.Fields.acctId));

        return em.createQuery(cq).getResultList().stream()
            .collect(Collectors.toMap(row -> (Integer) row[0], row -> (Long) row[1]));
    }

    public Optional<CarWishlist> findByAcctIdAndPlateNo(Integer acctId, String plateNo, boolean includeDeleted) {
        CommonCarWishlistCriteria criteria = new CommonCarWishlistCriteria();
        criteria.setAcctId(acctId);
        criteria.setPlateNos(Collections.singletonList(plateNo));
        criteria.setIncludeDeleted(includeDeleted);

        List<CarWishlist> results = findBySearch(criteria, 0, 1);
        return results.isEmpty() ? Optional.empty() : Optional.ofNullable(results.get(0));
    }

    public List<CarWishlist> findByReportCriteria(@NonNull CarWishlistReportRequest request,
                                                  @Nullable List<Integer> acctIds) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<CarWishlist> cq = cb.createQuery(CarWishlist.class);
        prepareReportQuery(cb, cq, request, acctIds);
        return em.createQuery(cq).getResultList();
    }

    // 保持向後兼容的方法
    public List<CarWishlist> findByReportCriteria(@NonNull CarWishlistReportRequest request) {
        return findByReportCriteria(request, null);
    }
}