package com.carplus.subscribe.db.mysql.entity.change;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@FieldNameConstants
public class EntityInsertion extends EntityBase {
    private String memo;

    public EntityInsertion(String entityName, String entityId, String memo) {
        super(entityName, entityId);
        this.memo = memo;
    }
}
