package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public class CarBrandRepository extends SimpleJpaRepository<CarBrand, String> {

    private final EntityManager em;

    public CarBrandRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarBrand.class, em);
        this.em = em;
    }

    public Optional<CarBrand> findByBrandCode(String brandCode) {
        try {
            CarBrand carBrand = em.createQuery("select cb from CarBrand cb where cb.brandCode = :brandCode", CarBrand.class)
                    .setParameter("brandCode", brandCode)
                    .getSingleResult();
            return Optional.of(carBrand);
        } catch (NoResultException e) {
            return Optional.empty();
        }
    }

    public long count(List<String> brands) {
        try {
            String sql;
            if (brands == null || brands.isEmpty()) {
                sql = "select count(brandCode) from car_brand";
            } else {
                sql = "select count(brandCode) from car_brand where brandCode in (:brands)";
            }

            Query query = em.createNativeQuery(sql);

            if (brands != null && !brands.isEmpty()) {
                query.setParameter("brands", brands);
            }

            return ((Number) query.getSingleResult()).longValue();
        } catch (NoResultException e) {
            return 0;
        }
    }

    public List<CarBrand> findByPage(Integer limit, Integer offset, List<String> brands) {
        String sql = " select cb.* from car_brand cb ";

        if (brands != null && !brands.isEmpty()) {
            sql += " where cb.brandCode in (:brands) ";
        }

        // 分頁
        if (null != limit && null != offset) {
            sql += "limit " + limit + " offset " + offset + " ";
        }

        Query query = em.createNativeQuery(sql, CarBrand.class);

        if (brands != null && !brands.isEmpty()) {
            query.setParameter("brands", brands);
        }

        return query.getResultList();
    }

    public List<CarBrand> findByBrandCodeIn(Set<String> brandCodes) {
        return em.createQuery("SELECT cb FROM CarBrand cb WHERE cb.brandCode IN :brandCodes", CarBrand.class)
            .setParameter("brandCodes", brandCodes)
            .getResultList();
    }
}
