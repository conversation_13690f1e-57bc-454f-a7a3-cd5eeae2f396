package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.econtract.EContractMailHistory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;

@Repository
public class EContractMailHistoryRepository extends SimpleJpaRepository<EContractMailHistory, String> {

    private final EntityManager em;

    public EContractMailHistoryRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(EContractMailHistory.class, em);
        this.em = em;
    }

    public List<EContractMailHistory> getByContractId(String contractNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<EContractMailHistory> cq = cb.createQuery(EContractMailHistory.class);
        Root<EContractMailHistory> root = cq.from(EContractMailHistory.class);
        cq.select(root).where(cb.equal(root.get("contractNo"), contractNo)).orderBy(cb.desc(root.get("createDate")));
        return em.createQuery(cq).getResultList();
    }


}
