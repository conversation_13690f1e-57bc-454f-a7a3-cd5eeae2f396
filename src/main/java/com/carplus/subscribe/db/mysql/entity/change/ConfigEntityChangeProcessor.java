package com.carplus.subscribe.db.mysql.entity.change;

import com.carplus.subscribe.db.mysql.entity.ConfigEntity;
import com.carplus.subscribe.model.station.StationResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Component
public class ConfigEntityChangeProcessor implements EntityChangeProcessor {

    @Override
    public boolean supports(Class<?> entityClass, String propertyName) {
        return ConfigEntity.class.isAssignableFrom(entityClass) && ConfigEntity.Fields.value.equals(propertyName);
    }

    @Override
    public List<FieldChange> process(String propertyName, Object oldValue, Object newValue) {
        List<FieldChange> changes = new ArrayList<>();
        if (oldValue instanceof String && newValue instanceof String) {
            List<String> oldList = parseJsonArray((String) oldValue);
            List<String> newList = parseJsonArray((String) newValue);

            Set<String> removedItems = new HashSet<>(oldList);
            newList.forEach(removedItems::remove);

            Set<String> addedItems = new HashSet<>(newList);
            oldList.forEach(addedItems::remove);

            if (!removedItems.isEmpty()) {
                changes.add(new FieldChange(StationResponse.Fields.forceOnlineRForm, false, true));
            } else if (!addedItems.isEmpty()) {
                changes.add(new FieldChange(StationResponse.Fields.forceOnlineRForm, true, false));
            }
        }
        return changes;
    }

    private List<String> parseJsonArray(String jsonArray) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonArray, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON array", e);
        }
    }
}
