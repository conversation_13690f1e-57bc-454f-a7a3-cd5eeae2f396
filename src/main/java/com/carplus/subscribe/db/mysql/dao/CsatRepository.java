package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.enums.csat.CsatStatus;
import com.carplus.subscribe.model.csat.CsatCriteria;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class CsatRepository extends SimpleJpaRepository<Csat, Integer> {

    private final EntityManager em;

    public CsatRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(Csat.class, em);
        this.em = em;
    }


    public List<Csat> query(CsatCriteria criteria, Integer limit, Integer skip) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Csat> cq = cb.createQuery(Csat.class);
        preparedQuery(cb, cq, criteria, false);
        if (limit == null) {
            limit = Integer.MAX_VALUE;
        }
        if (skip == null) {
            skip = 0;
        }
        return em.createQuery(cq).setMaxResults(limit).setFirstResult(skip).getResultList();
    }

    public long count(CsatCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Integer> cq = cb.createQuery(Integer.class);
        preparedQuery(cb, cq, criteria, true);
        return em.createQuery(cq).getResultList().size();
    }

    private void preparedQuery(CriteriaBuilder cb, CriteriaQuery<?> cq, CsatCriteria criteria, boolean isCount) {
        String dateFormat = "yyyyMM";
        Root<Csat> root = cq.from(Csat.class);
        List<Predicate> predicateList = new ArrayList<>();
        if (StringUtils.isNotBlank(criteria.getMemberId())) {
            predicateList.add(cb.equal(root.get(Csat.Fields.memberId), criteria.getMemberId()));
        }

        if (CollectionUtils.isNotEmpty(criteria.getStatus())) {
            predicateList.add(root.get(Csat.Fields.status).in(criteria.getStatus().stream().map(CsatStatus::getCode).collect(Collectors.toList())));
        }

        if (StringUtils.isNotBlank(criteria.getPlateNo())) {
            predicateList.add(cb.equal(root.get(Csat.Fields.plateNo), criteria.getPlateNo()));
        }

        if (criteria.getFrom() != null && criteria.getTo() != null) {
            predicateList.add(cb.between(root.get(Csat.Fields.assignYearMonth), Integer.parseInt(DateUtils.toDateString(criteria.getFrom(), dateFormat)), Integer.parseInt(DateUtils.toDateString(criteria.getTo(), dateFormat))));
        }

        if (CollectionUtils.isNotEmpty(criteria.getSource())) {
            predicateList.add(root.get(Csat.Fields.source).in(criteria.getSource()));
        }

        if (isCount) {
            cq.select(root.get(Csat.Fields.id));
        }
        if (!predicateList.isEmpty()) {
            cq.where(predicateList.toArray(new Predicate[0]));
        }
    }


    public List<Csat> findByOrders(List<String> orderNos) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Csat> cq = cb.createQuery(Csat.class);
        Root<Csat> root = cq.from(Csat.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(root.get(Csat.Fields.orderNo).in(orderNos));
        cq.where(predicateList.toArray(new Predicate[0]));
        return em.createQuery(cq).getResultList();
    }


    public List<String> getMemberIds() {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<String> cq = cb.createQuery(String.class);
        Root<Csat> root = cq.from(Csat.class);
        List<Predicate> predicateList = new ArrayList<>();
        predicateList.add(cb.isNotNull(root.get(Csat.Fields.memberId)));
        cq.distinct(true).select(root.get(Csat.Fields.memberId));
        cq.where(predicateList.toArray(new Predicate[0]));
        return em.createQuery(cq).getResultList();
    }
}
