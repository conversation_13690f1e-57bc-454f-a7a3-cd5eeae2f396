package com.carplus.subscribe.db.mysql.entity.econtract;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.model.notify.MailAttachment;
import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import java.util.List;

/**
 * 電子合約主檔
 */
@Data
public class EContractMailAttchment extends GeneralEntity {


    private Integer fileId;

    /**
     * 合約檔案類型
     */
    @Column(name = "eContractType")
    private String eContractType;

    /**
     * 附件
     */
    @Column(name = "attachments")
    @Type(type = "json")
    private List<MailAttachment> attachments;

    /**
     * 收件者會員編號
     */
    @Column(name = "receiveAcctId")
    private Integer receiveAcctId;

    /**
     * 收件者信箱
     */
    @Column(name = "receiver")
    private String receiver;

    /**
     * 寄送檔案ID
     */
    @Column(name = "attachmentFileId")
    private String attachmentFileId;

    /**
     * 寄送人
     */
    @Column(name = "tragerMember")
    private String tragerMember;

}