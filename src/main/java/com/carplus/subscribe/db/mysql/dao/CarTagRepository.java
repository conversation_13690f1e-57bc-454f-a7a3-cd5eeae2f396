package com.carplus.subscribe.db.mysql.dao;

import carplus.common.redis.cache.DelAll;
import com.carplus.subscribe.db.mysql.entity.cars.CarTag;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.List;

@Repository
public class CarTagRepository extends SimpleJpaRepository<CarTag, Integer> {

    private final EntityManager em;

    public CarTagRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CarTag.class, em);
        this.em = em;
    }

    /**
     * 多筆查
     **/
    @NonNull
    public List<CarTag> findAll() {
        return super.findAll();
    }


    @Transactional
    @Override
    @DelAll(group = CarTag.class)
    public <S extends CarTag> S save(S entity) {
        return super.save(entity);
    }

    @Transactional
    @Override
    @DelAll(group = CarTag.class)
    public <S extends CarTag> List<S> saveAll(Iterable<S> entities) {
        return super.saveAll(entities);
    }


    /**
     * 名稱是否重複
     **/
    public boolean isDuplicateName(String cnName, Integer id) {
        final Integer finalId = (id == null) ? -1 : id;
        return super.findAll().stream().anyMatch(carTag -> carTag.getCnName().equals(cnName) && !carTag.getTagId().equals(finalId));
    }

    public Integer getMaxSeqNo() {
        return super.findAll((Specification<CarTag>) (root, query, criteriaBuilder) -> {
            query.orderBy(criteriaBuilder.desc(root.get(CarTag.Fields.seqNo)));
            return null;
        }).stream().findFirst().map(CarTag::getSeqNo).orElse(0);
    }


}
