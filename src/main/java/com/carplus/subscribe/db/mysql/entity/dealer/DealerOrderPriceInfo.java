package com.carplus.subscribe.db.mysql.entity.dealer;

import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import java.time.Instant;

/**
 * 經銷商訂單費用資訊
 */
@FieldNameConstants
@Entity(name = "dealer_order_price_info")
@Data
public class DealerOrderPriceInfo {

    /**
     * 經銷商名稱
     */
    private String dealerName;

    /**
     * 經銷商訂單編號
     */
    private String orderNo;

    /**
     * 交易ID
     */
    @Id
    @GeneratedValue(generator = "custom-generator")
    @GenericGenerator(name = "custom-generator",
            strategy = "com.carplus.subscribe.db.mysql.entity.dealer.CustomTradeIdGenerator")
    private String tradeId;

    /**
     * 付款日期
     */
    private Instant paymentDate;

    /**
     * 費用目的
     */
    private String payFor;

    /**
     * 交易項目
     */
    private Integer transactionItem;

    /**
     * 交易金額
     */
    private Integer transactionAmt = 0;

    /**
     * 計算公式
     */
    private String formula;

    /**
     * 是否分潤
     */
    private Boolean isProfitSharing;

    /**
     * 分潤比例
     */
    private Double profitSharingRatio;

}