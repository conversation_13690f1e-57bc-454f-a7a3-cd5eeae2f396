package com.carplus.subscribe.db.mysql.entity;

import com.carplus.subscribe.enums.BuChangeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.*;
import java.util.Optional;

@Data
@Entity
@Table(name = "bu_change_log")
public class BuChangeLog extends GeneralEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 撥車單號
     */
    private Integer buChangeMasterId;


    /**
     * 訂單編號
     */
    private String orderNo;

    /**
     * 車牌號碼
     */
    private String plateNo;

    /**
     * 來源
     */
    private Integer fromBuId;

    /**
     * 目的
     */
    private Integer toBuId;

    /**
     * 事由
     */
    @Enumerated(EnumType.STRING)
    private BuChangeEnum.ChangeTypeOfAssign changeType;

    /**
     * 備註
     */
    private String memo;

    @JsonProperty("changeTypeName")
    private String getChangeTypeName() {
        return Optional.ofNullable(changeType).map(BuChangeEnum.ChangeTypeOfAssign::getName).orElse(null);
    }

    @Transient
    private Integer firstToBuId;

}
