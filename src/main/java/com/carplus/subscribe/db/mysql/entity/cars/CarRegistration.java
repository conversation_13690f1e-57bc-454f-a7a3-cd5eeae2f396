package com.carplus.subscribe.db.mysql.entity.cars;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "car_registration")
@Data
public class CarRegistration extends GeneralEntity {

    /**
     * 統一編號
     */
    @Id
    private String vatNo;

    /**
     * 公司名稱
     */
    private String name;

    /**
     * 簡稱
     */
    private String shortName;
}
