package com.carplus.subscribe.db.mysql.dao;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.OrderPaymentStatus;
import com.carplus.subscribe.model.request.paymentinfo.PaymentInfoQueryRequest;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Objects;

@Repository
public class PaymentInfoRepository extends SimpleJpaRepository<PaymentInfo, Integer> {

    private final EntityManager em;

    public PaymentInfoRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(PaymentInfo.class, em);
        this.em = em;
    }

    /**
     * 取得訂單付款資訊
     */
    public List<PaymentInfo> getPaymentInfosByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<PaymentInfo> cq = cb.createQuery(PaymentInfo.class);
        Root<PaymentInfo> root = cq.from(PaymentInfo.class);
        cq.select(root).where(cb.equal(root.get("orderId"), orderNo), cb.gt(root.get("amount"), 0), root.get("status").in(OrderPaymentStatus.getSuccessCode()));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 取得過期的付款資訊(status=18)
     */
    public List<PaymentInfo> getExpiredPaymentInfosByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<PaymentInfo> cq = cb.createQuery(PaymentInfo.class);
        Root<PaymentInfo> root = cq.from(PaymentInfo.class);
        cq.select(root).where(cb.equal(root.get("orderId"), orderNo), cb.gt(root.get("amount"), 0), root.get("status").in(OrderPaymentStatus.EXPIRED_REFUND.getCode()));
        return em.createQuery(cq).getResultList();
    }

    public List<PaymentInfo> getPaymentInfosByTradeId(String tradId) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<PaymentInfo> cq = cb.createQuery(PaymentInfo.class);
        Root<PaymentInfo> root = cq.from(PaymentInfo.class);
        cq.select(root).where(cb.equal(root.get("tradeId"), tradId), cb.gt(root.get("amount"), 0));
        return em.createQuery(cq).getResultList();
    }

    public long count(PaymentInfoQueryRequest queryRequest) {
        String sql =
            "select count(*) from payment_info p join orders o on p.orderId = o.orderNo "
                +
                "left join contract c on o.contractNo = c.contractNo "
                +
                "left join main_contract m on c.mainContractNo = m.mainContractNo";

        sql += criteriaSearchSql(queryRequest);

        TypedQuery<Long> query = em.createQuery(sql, Long.class);

        criteriaSearchParameter(queryRequest, query);
        return query.getSingleResult();
    }

    /**
     * 系統台帳務作業查詢
     */
    @NonNull
    public List<Tuple> findBySearch(@NonNull PaymentInfoQueryRequest queryRequest,
                                    @Nullable Integer limit, @Nullable Integer offset) {
        String sql =
            "select "
                +
                "m.mainContractNo, m.departStationCode, m.returnStationCode, m.acctId, m.createDate as mainCreateDate, "
                +
                "(JSON_EXTRACT(m.originalPriceInfo,'$.\"securityDepositInfo\".\"refundSecurityDepositDate\"')) as refundSecurityDepositDate, "
                +
                "(JSON_EXTRACT(m.originalPriceInfo,'$.\"securityDepositInfo\".\"isManualRefundSecurityDeposit\"')) as isManualRefundSecurityDeposit, "
                +
                "(JSON_EXTRACT(m.originalPriceInfo,'$.\"securityDepositInfo\".\"manualRefundStatus\"')) as manualRefundStatus, "
                +
                "(JSON_EXTRACT(m.originalPriceInfo,'$.\"securityDepositInfo\".\"refundMethod\"')) as refundMethod, "
                +
                "o.orderNo, o.startDate as orderStartDate, o.status as orderStatus, "
                +
                "p.status as paymentStatus, p.amount, p.createDate as payDate, p.paymentId, p.transactionNumber, p.remark, p.remarker, p.acquirer "
                +
                "from payment_info p join orders o on p.orderId = o.orderNo "
                +
                "left join contract c on o.contractNo = c.contractNo "
                +
                "left join main_contract m on c.mainContractNo = m.mainContractNo";

        // 查詢條件
        sql += criteriaSearchSql(queryRequest);

        Query q = em.createNativeQuery(sql, Tuple.class);
        criteriaSearchParameter(queryRequest, q);
        //noinspection unchecked
        return ((List<Tuple>) q.getResultList());
    }

    /**
     * 系統台帳務作業查詢條件
     */
    private String criteriaSearchSql(@NonNull PaymentInfoQueryRequest queryRequest) {
        List<String> condition = Lists.newArrayList();

        if (Objects.nonNull(queryRequest.getPayDateStart())) {
            condition.add("p.createDate >= :payDateStart");
        }
        if (Objects.nonNull(queryRequest.getPayDateEnd())) {
            condition.add("p.createDate < :payDateEnd");
        }
        if (Objects.nonNull(queryRequest.getMainDateStart())) {
            condition.add("m.createDate >= :mainDateStart");
        }
        if (Objects.nonNull(queryRequest.getMainDateEnd())) {
            condition.add("m.createDate < :mainDateEnd");
        }
        if (StringUtils.isNotEmpty(queryRequest.getStationCode())) {
            condition.add("(m.departStationCode = :stationCode or m.returnStationCode = :stationCode)");
        }
        if (StringUtils.isNotEmpty(queryRequest.getMainContractNo())) {
            condition.add("m.mainContractNo = :mainContractNo");
        }
        if (StringUtils.isNotEmpty(queryRequest.getOrderNo())) {
            condition.add("o.orderNo = :orderNo");
        }
        if (queryRequest.getPaymentId() != null) {
            condition.add("p.paymentId = :paymentId");
        }
        if (queryRequest.getPaymentStatus() != null) {
            condition.add("p.status = :paymentStatus");
        }
        if (StringUtils.isNotBlank(queryRequest.getTransactionNumber())) {
            condition.add("p.transactionNumber = :transactionNumber");
        }
        if (StringUtils.isNotBlank(queryRequest.getAcquirer())) {
            condition.add("p.acquirer = :acquirer");
        }
        if (condition.isEmpty()) {
            return " ";
        }

        return " where " + String.join(" and ", condition) + " ";
    }

    /**
     * 系統台帳務作業查詢條件參數
     */
    private void criteriaSearchParameter(@NonNull PaymentInfoQueryRequest queryRequest, @NonNull Query query) {
        if (Objects.nonNull(queryRequest.getPayDateStart())) {
            query.setParameter("payDateStart", queryRequest.getPayDateStart());
        }
        if (Objects.nonNull(queryRequest.getPayDateEnd())) {
            query.setParameter("payDateEnd", queryRequest.getPayDateEnd());
        }
        if (Objects.nonNull(queryRequest.getMainDateStart())) {
            query.setParameter("mainDateStart", queryRequest.getMainDateStart());
        }
        if (Objects.nonNull(queryRequest.getMainDateEnd())) {
            query.setParameter("mainDateEnd", queryRequest.getMainDateEnd());
        }
        if (StringUtils.isNotEmpty(queryRequest.getStationCode())) {
            query.setParameter("stationCode", queryRequest.getStationCode());
        }
        if (StringUtils.isNotEmpty(queryRequest.getMainContractNo())) {
            query.setParameter("mainContractNo", queryRequest.getMainContractNo());
        }
        if (StringUtils.isNotEmpty(queryRequest.getOrderNo())) {
            query.setParameter("orderNo", queryRequest.getOrderNo());
        }
        if (queryRequest.getPaymentId() != null) {
            query.setParameter("paymentId", queryRequest.getPaymentId());
        }
        if (queryRequest.getPaymentStatus() != null) {
            query.setParameter("paymentStatus", queryRequest.getPaymentStatus());
        }
        if (StringUtils.isNotBlank(queryRequest.getTransactionNumber())) {
            query.setParameter("transactionNumber", queryRequest.getTransactionNumber());
        }
        if (StringUtils.isNotBlank(queryRequest.getAcquirer())) {
            query.setParameter("acquirer", queryRequest.getAcquirer());
        }

    }
}
