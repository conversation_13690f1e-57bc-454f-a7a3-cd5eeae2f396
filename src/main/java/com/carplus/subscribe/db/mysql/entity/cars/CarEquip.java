package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;


@Data
@FieldNameConstants
@Entity(name = "car_equip")
@Version(1)
@Schema(description = "車輛配件")
public class CarEquip extends GeneralEntity {
    @Id
    @Column(name = "equipId", nullable = false)
    @Schema(description = "ID")
    private Integer equipId;

    @Column(name = "cnName", nullable = false)
    @Schema(description = "名稱")
    private String cnName;

    @Column(name = "isShow", nullable = false)
    @Schema(description = "是否在官網顯示")
    private Integer isShow;

    @Column(name = "seqNo", nullable = false)
    @Schema(description = "排序")
    private Integer seqNo;
}
