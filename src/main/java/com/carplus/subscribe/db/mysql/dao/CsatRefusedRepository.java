package com.carplus.subscribe.db.mysql.dao;

import com.carplus.subscribe.db.mysql.entity.csat.CsatRefused;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;
import java.util.Optional;

@Repository
public class CsatRefusedRepository extends SimpleJpaRepository<CsatRefused, Integer> {

    private final EntityManager em;

    public CsatRefusedRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(CsatRefused.class, em);
        this.em = em;
    }

    public CsatRefused findByAcctId(Integer acctId) {
        Optional<CsatRefused> optional = findOne((Specification<CsatRefused>) (root, cq, cb) -> {
            return cb.equal(root.get(CsatRefused.Fields.acctId), acctId);
        });
        return optional.orElse(null);
    }

    public List<CsatRefused> findByAcctIds(List<Integer> acctIds) {
        return findAll((Specification<CsatRefused>) (root, cq, cb) -> {
            return root.get(CsatRefused.Fields.acctId).in(acctIds);
        });
    }

    public CsatRefused findBySeaLandAcctId(Integer seaLandAcctId) {
        Optional<CsatRefused> optional = findOne((Specification<CsatRefused>) (root, cq, cb) -> {
            return cb.equal(root.get(CsatRefused.Fields.seaLandAcctId), seaLandAcctId);
        });
        return optional.orElse(null);
    }

    public List<CsatRefused> findBySeaLandAcctIds(List<Integer> seaLandAcctIds) {
        return findAll((Specification<CsatRefused>) (root, cq, cb) -> {
            return root.get(CsatRefused.Fields.seaLandAcctId).in(seaLandAcctIds);
        });
    }

}
