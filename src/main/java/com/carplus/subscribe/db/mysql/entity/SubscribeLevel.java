package com.carplus.subscribe.db.mysql.entity;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.enums.SubscribeType;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.subscribelevel.MileageDiscount;
import com.carplus.subscribe.model.subscribelevel.MonthlyFeeProvider;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

import static com.carplus.subscribe.enums.SubscribeType.SEASON;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.MISSING_CORRESPONDING_DISCOUNT_LEVEL;


@Data
@Builder
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@FieldNameConstants
@Version(2)
@Entity(name = "subscribe_level")
public class SubscribeLevel extends GeneralEntity implements MonthlyFeeProvider {

    @Id
    @Column(name = "id", nullable = false)
    private Integer id;
    /**
     * 訂閱車方案
     */
    @Column(name = "level", nullable = false)
    private int level;
    /**
     * 訂閱車方案名稱
     */
    @Column(name = "name")
    private String name;
    /**
     * 對照超激優惠方案
     */
    @Column(name = "discountLevel")
    private Integer discountLevel;
    /**
     * 保證金
     */
    @Column(name = "securityDeposit", nullable = false)
    private int securityDeposit;
    /**
     * 基本月費
     */
    @Column(name = "monthlyFee", nullable = false)
    private int monthlyFee;
    /**
     * 里程費
     */
    @Column(name = "mileageFee", nullable = false)
    private double mileageFee;
    /**
     * 是否自動授信
     */
    @Column(name = "autoCredit", nullable = false)
    private boolean autoCredit;
    /**
     * 里程數優惠
     */
    @Type(type = "json")
    private List<MileageDiscount> mileageDiscount = Lists.newArrayList();
    /**
     * 優惠價
     */
    @Column(name = "discountMonthlyFee", nullable = false)
    private int discountMonthlyFee;
    /**
     * 方案類型
     */
    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private SubscribeType type = SEASON;
    /**
     * 是否刪除 0:無刪除 1:刪除
     */
    @Column(name = "isDeleted")
    private boolean isDeleted;
    /**
     * 建立人員工號
     */
    @Column(name = "createdBy")
    private String createdBy;
    /**
     * 最後異動人員工號
     */
    @Column(name = "updatedBy")
    private String updatedBy;
    /**
     * 是否為超激優惠方案
     */
    @JsonIgnore
    @Transient
    private boolean isDiscountLevelEnabled;

    /**
     * 驗證訂閱方案應設置對應的優惠方案
     */
    @JsonIgnore
    public void checkMissingCorrespondingDiscountLevel() {
        if (getDiscountLevel() == null) {
            throw new SubscribeException(MISSING_CORRESPONDING_DISCOUNT_LEVEL);
        }
    }
}
