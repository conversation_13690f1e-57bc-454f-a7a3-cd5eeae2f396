package com.carplus.subscribe.db.mysql.entity.invoice;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.CustomizedJsonStringType;
import com.carplus.subscribe.enums.InvoiceDefine;
import com.carplus.subscribe.model.invoice.Invoice;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.springframework.lang.NonNull;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.Month;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 發票檔
 */
@Data
@Entity
@Table(name = "invoices")
@TypeDef(name = "json", typeClass = CustomizedJsonStringType.class)
public class Invoices implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 發票號碼
     */
    @Id
    @Column(name = "invNo", nullable = false)
    private String invNo;

    /**
     * 訂單編號
     */
    @Column(name = "orderNo", nullable = false)
    private String orderNo;

    /**
     * 開票狀態 CREATE:開立, INVALIDATE:作廢, ALLOWANCE:折讓
     */
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 支付目的
     */
    @Column(name = "payFor", nullable = false)
    private String payFor;

    /**
     * 發票備註
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 站所代碼
     */
    @Column(name = "stationCode", nullable = false)
    private String stationCode;

    /**
     * 站所統編
     */
    @Column(name = "stationVATNo", nullable = false)
    private String stationVATNo;

    /**
     * 發票內容
     */
    @Column(name = "invoice", nullable = false)
    @Type(type = "json")
    private Invoice invoice;

    /**
     * 未稅金額
     */
    @Column(name = "unTax", nullable = false)
    private Integer unTax;

    /**
     * 稅金
     */
    @Column(name = "tax", nullable = false)
    private Integer tax;

    /**
     * 發票金額
     */
    @Column(name = "amount", nullable = false)
    private Integer amount;

    /**
     * 作廢、折讓原因
     */
    @Column(name = "reason")
    private String reason;

    /**
     * 開立日期
     */
    @Column(name = "createdAt", nullable = false)
    private Date createdAt;

    /**
     * 作廢、折讓日期
     */
    @Column(name = "deletedAt")
    private Date deletedAt;

    /**
     * 折讓單號
     */
    private String allowanceNo;

    /**
     * 是否財務中台開立
     *
     * @deprecated 2024-11-04 用不到可移除
     */
    @Column
    @Deprecated
    private Boolean isFinBus;


    /**
     * 是否立賬,0:不立帳,1:需立帳,2:開立作廢都要立帳
     */
    @Column
    private Integer isCheckout;

    /**
     * 員工編號
     */
    @Column
    private String memberId;

    /**
     * 關聯費用ID編號
     */
    @Type(type = "json")
    private List<Integer> refPriceInfoIds;

    public void update(@NonNull Invoices invoices) {
        this.setStatus(invoices.getStatus());
        this.setReason(invoices.getReason());
        this.setDeletedAt(invoices.getDeletedAt());
    }

    public void action(InvoiceDefine.InvStatus status, String reason) {
        if (status != null) {
            this.status = status.name();
            this.reason = reason;
            this.deletedAt = new Date();
        }
    }

    public InvoiceDefine.InvStatus getAction() {
        if (!this.status.equals(InvoiceDefine.InvStatus.CREATE.name())) {
            return null;
        }
        LocalDate now = DateUtils.toLocalDate(new Date());
        Month nowMonth = now.getMonth();
        int nowYear = now.getYear();

        LocalDate invDate = DateUtils.toLocalDate(createdAt);
        Month invMonth = invDate.getMonth();
        int invYear = invDate.getYear();

        if (nowYear != invYear) {
            return InvoiceDefine.InvStatus.ALLOWANCE;
        }
        if (nowMonth != invMonth) {
            int diff = nowMonth.getValue() - invMonth.getValue();
            if (diff > 1 || invMonth.getValue() % 2 == 0) {
                return InvoiceDefine.InvStatus.ALLOWANCE;
            }
        }
        return InvoiceDefine.InvStatus.INVALIDATE;
    }

    /**
     * 作廢折讓時，設定是否要立賬
     */
    public void setCancelCheckOut() {
        if (status.equals(InvoiceDefine.InvStatus.INVALIDATE.name()) && isCheckout == InvoiceDefine.InvCheckoutStatus.CHECKOUT.getStatus()) {
            isCheckout = InvoiceDefine.InvCheckoutStatus.MULTIPLE_CHECKOUT.getStatus();
        } else {
            isCheckout = InvoiceDefine.InvCheckoutStatus.CHECKOUT.getStatus();
        }
    }

    public boolean isMultipleCheckout() {
        return Objects.equals(getIsCheckout(), InvoiceDefine.InvCheckoutStatus.MULTIPLE_CHECKOUT.getStatus());
    }
}
