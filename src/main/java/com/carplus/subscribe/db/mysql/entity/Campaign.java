package com.carplus.subscribe.db.mysql.entity;

import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.Instant;

@Schema(description = "活動資訊")
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
@Data
@Entity(name = "campaign")
public class Campaign extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "活動編號")
    private Integer id;

    @Schema(description = "活動標題")
    private String title;

    @Lob
    @Column(columnDefinition = "TEXT")
    @Schema(description = "活動說明")
    private String description;

    @Type(type = "json")
    @Schema(description = "車輛顯示條件")
    private CarsCondition carsCondition;

    @Schema(description = "橫幅類別編號")
    private Integer bannerCategoryId;

    @Schema(description = "活動開始時間")
    private Instant startDate;

    @Schema(description = "活動結束時間")
    private Instant endDate;

    @JsonProperty("isDeleted")
    @Schema(description = "是否已刪除")
    private boolean isDeleted = false;

    @Schema(description = "最後異動人員")
    private String updater;

    @PrePersist
    @PreUpdate
    private void validate() {
        if (startDate != null && endDate != null && endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("結束時間不可早於開始時間");
        }

        // 檢查月費條件：如果兩者均提供，檢查月費結束不得小於月費起始
        if (carsCondition.getMonthFeeStart() != null && carsCondition.getMonthFeeEnd() != null
            && carsCondition.getMonthFeeEnd() < carsCondition.getMonthFeeStart()) {
            throw new IllegalArgumentException("月費範圍錯誤：月費結束不得小於月費起始");
        }

        // 檢查出廠年份條件：如果兩者均提供，檢查出廠年份迄不得小於出廠年份起
        if (carsCondition.getMfgYearFrom() != null && carsCondition.getMfgYearTo() != null
            && carsCondition.getMfgYearTo() < carsCondition.getMfgYearFrom()) {
            throw new IllegalArgumentException("出廠年份錯誤：出廠年份迄不得小於出廠年份起");
        }
    }

    public boolean isValid() {
        if (isDeleted()) {
            return false;
        }
        // 檢查活動時間是否有效
        Instant now = Instant.now();
        return !getStartDate().isAfter(now) && !getEndDate().isBefore(now);
    }
}
