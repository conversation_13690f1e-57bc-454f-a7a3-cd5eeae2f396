package com.carplus.subscribe.db.mysql.dao;

import carplus.common.enums.etag.ETagFlow;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.CriteriaUpdate;
import javax.persistence.criteria.Root;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Repository
public class EtagInfoRepository extends SimpleJpaRepository<ETagInfo, Integer> {

    private final EntityManager em;

    public EtagInfoRepository(@Qualifier("mysqlEntityManager") EntityManager em) {
        super(ETagInfo.class, em);
        this.em = em;
    }


    public List<ETagInfo> getETagInfosByOrderNo(String orderNo) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<ETagInfo> cq = cb.createQuery(ETagInfo.class);
        Root<ETagInfo> root = cq.from(ETagInfo.class);
        cq.where(cb.equal(root.get("orderNo"), orderNo));
        return em.createQuery(cq).getResultList();
    }

    /**
     * 透過訂單拿取Etag第一期出車失敗的資料
     */
    public List<ETagInfo> getETagInfosDepartFailByOrderNos(List<String> orderNos) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<ETagInfo> cq = cb.createQuery(ETagInfo.class);
        Root<ETagInfo> root = cq.from(ETagInfo.class);
        cq.where(cb.and(
            root.get("orderNo").in(orderNos),
            cb.equal(root.get("isSuccess"), false),
            cb.equal(root.get("stage"), 1)
        ));
        return em.createQuery(cq).getResultList();
    }

    public List<ETagInfo> getAlertETagInfosDepartFailByOrderNos(List<String> orderNos) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<ETagInfo> cq = cb.createQuery(ETagInfo.class);
        Root<ETagInfo> root = cq.from(ETagInfo.class);
        cq.where(cb.and(
            root.get("orderNo").in(orderNos),
            root.get("departFailCode").in(Arrays.asList(49, 2105)),
            cb.equal(root.get("stage"), 1),
            cb.or(
                cb.equal(root.get("isSuccess"), false),
                cb.notEqual(root.get("eTagFlow"), ETagFlow.DEPART_SUCCESS.getCode()))
        ));
        return em.createQuery(cq).getResultList();
    }

    public ETagInfo getEtagInfoByOrderPriceId(int orderPriceInfoId) {
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<ETagInfo> cq = cb.createQuery(ETagInfo.class);
            Root<ETagInfo> root = cq.from(ETagInfo.class);
            cq.where(cb.equal(root.get("orderPriceInfoId"), orderPriceInfoId));
            return em.createQuery(cq).setMaxResults(1).getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    public List<ETagInfo> getByOrderPriceInfoIdIn(List<Integer> orderPriceInfoIds) {
        try {
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<ETagInfo> cq = cb.createQuery(ETagInfo.class);
            Root<ETagInfo> root = cq.from(ETagInfo.class);
            cq.where(root.get("orderPriceInfoId").in(orderPriceInfoIds));
            return em.createQuery(cq).getResultList();
        } catch (NoResultException e) {
            return null;
        }
    }

    /**
     * 設定Etag已立帳
     */
    @Transactional(transactionManager = "mysqlTransactionManager")
    public int updateCheckout(@Param("accountIds") Collection<Integer> etagIds) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaUpdate<ETagInfo> cu = cb.createCriteriaUpdate(ETagInfo.class);
        Root<ETagInfo> root = cu.from(ETagInfo.class);
        cu.set("uploaded", true);
        cu.where(root.get("id").in(etagIds));
        return em.createQuery(cu).executeUpdate();
    }

}
