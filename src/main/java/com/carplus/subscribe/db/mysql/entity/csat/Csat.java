package com.carplus.subscribe.db.mysql.entity.csat;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import com.carplus.subscribe.enums.CsatOrderSource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.time.Instant;
import java.util.Objects;

@Entity
@Table(name = "csat")
@FieldNameConstants
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Csat extends GeneralEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 訂單編號
     */
    private String orderNo;

    /**
     * 電訪狀態
     */
    private Integer status;

    /**
     * 訂單來源
     */
    private Integer source;

    /**
     * 電訪者
     */
    private String memberId;

    /**
     * 電訪時間
     */
    private Instant contractDate;

    /**
     * 派工月份
     */
    private Integer assignYearMonth;

    /**
     * 滿意度調查狀態<br><br>
     * 0 未調查 (status < 2)<br>
     * 1 已調查 (status = 2)<br>
     * 2 拒調查 (status = 2 且 q0 = false)
     */
    private Integer questStatus;

    /**
     * 電訪備註
     */
    private String csatMemo;

    /**
     * 車牌
     */
    private String plateNo;

    /**
     * 是否經銷商
     */
    public boolean isDealer() {
        return !Objects.equals(CsatOrderSource.SUBSCRIBE.getCode(), source);
    }

}
