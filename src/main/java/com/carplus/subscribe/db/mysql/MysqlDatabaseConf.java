package com.carplus.subscribe.db.mysql;

import com.zaxxer.hikari.HikariDataSource;
import org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl;
import org.hibernate.dialect.MySQLDialect;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static org.hibernate.cfg.AvailableSettings.*;

@Configuration
@EnableTransactionManagement//(proxyTargetClass = true)
@EnableJpaRepositories(
    entityManagerFactoryRef = "mysqlEntityManagerFactory",
    transactionManagerRef = "mysqlTransactionManager",
    basePackages = {"com.carplus.subscribe.db.mysql.dao"})
public class MysqlDatabaseConf {

    @Primary
    @Bean(name = "mysqlDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.mysql")
    public DataSource mysqlDataSource() {
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Primary
    @Bean(name = "mysqlEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean mysqlEntityManagerFactory(
        EntityManagerFactoryBuilder builder, @Qualifier("mysqlDataSource") DataSource mysqlDataSource) {

        Map<String, String> mysqlJpaProperties = new HashMap<>();

        mysqlJpaProperties.put(DIALECT, MySQLDialect.class.getName());
        mysqlJpaProperties.put(PHYSICAL_NAMING_STRATEGY, PhysicalNamingStrategyStandardImpl.class.getName());
//        mysqlJpaProperties.put(ENABLE_LAZY_LOAD_NO_TRANS,"true");
        mysqlJpaProperties.put(PROCEDURE_NULL_PARAM_PASSING, "true");
        return builder
            .dataSource(mysqlDataSource)
            .packages("com.carplus.subscribe.db.mysql.entity")
            .persistenceUnit("mysql")
            .properties(mysqlJpaProperties)
            .build();
    }

    @Primary
    @Bean(name = "mysqlEntityManager")
    public EntityManager mysqlEntityManager(@Qualifier("mysqlEntityManagerFactory") LocalContainerEntityManagerFactoryBean factory) {
        return SharedEntityManagerCreator.createSharedEntityManager(Objects.requireNonNull(factory.getObject()));
    }

    @Primary
    @Bean(name = "mysqlTransactionManager")
    public PlatformTransactionManager mysqlTransactionManager(
        @Qualifier("mysqlEntityManagerFactory") EntityManagerFactory mysqlEntityManagerFactory) {

        return new JpaTransactionManager(mysqlEntityManagerFactory);
    }
}
