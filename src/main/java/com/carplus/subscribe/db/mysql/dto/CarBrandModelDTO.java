package com.carplus.subscribe.db.mysql.dto;

import com.carplus.subscribe.db.mysql.entity.cars.CarBrand;
import com.carplus.subscribe.db.mysql.entity.cars.CarModel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import lombok.Data;

@Data
public class CarBrandModelDTO {
    private Cars car;
    private CarBrand brand;
    private CarModel model;

    public CarBrandModelDTO(Cars car, CarModel model, CarBrand brand) {
        this.car = car;
        this.model = model;
        this.brand = brand;
    }

}
