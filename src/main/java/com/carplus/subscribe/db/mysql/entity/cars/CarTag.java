package com.carplus.subscribe.db.mysql.entity.cars;

import carplus.common.redis.serializer.Version;
import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;


@Data
@FieldNameConstants
@Entity(name = "car_tag")
@Version(1)
@Schema(description = "車輛標籤")
public class CarTag extends GeneralEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "tagId", nullable = false)
    @Schema(description = "ID")
    private Integer tagId;

    @Column(name = "cnName", nullable = false)
    @Schema(description = "名稱")
    private String cnName;

    @Column(name = "isShow", nullable = false)
    @Schema(description = "是否在官網顯示")
    private Boolean isShow;

    @Column(name = "seqNo", nullable = false)
    @Schema(description = "排序")
    private Integer seqNo;

    private String memberId;
}
