package com.carplus.subscribe.db.mysql.entity.csat;

import com.carplus.subscribe.db.mysql.entity.GeneralEntity;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "csat_refused")
@FieldNameConstants
public class CsatRefused extends GeneralEntity {

    /**
     * 身分證字號
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 會員編號(SL不會有)
     */
    private Integer acctId;

    /**
     * SeaLand會員編號
     */
    private Integer seaLandAcctId;

    /**
     * 是否拒訪
     */
    private boolean isRefused;

    /**
     * 拒訪備註
     */
    private String refuseMemo;

    /**
     * 拒訪時間
     */
    private Date refusalDate;

}
