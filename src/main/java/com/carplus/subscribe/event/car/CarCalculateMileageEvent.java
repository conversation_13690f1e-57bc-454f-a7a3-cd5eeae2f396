package com.carplus.subscribe.event.car;

import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 車輛中途結算里程事件
 */
@Getter
public class CarCalculateMileageEvent extends ApplicationEvent {

    private final CarUpdateKmDto carUpdateKmDto;

    private final Integer crsCarNo;

    public CarCalculateMileageEvent(Object source, CarUpdateKmDto carUpdateKmDto, Integer crsCarNo) {
        super(source);
        this.carUpdateKmDto = carUpdateKmDto;
        this.crsCarNo = crsCarNo;
    }
}