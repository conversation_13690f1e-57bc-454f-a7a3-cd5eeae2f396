package com.carplus.subscribe.event.car;

import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 出車中換車事件，處理
 * 1. 汰換車註銷
 * 2. 替代車註冊
 * 3. 更新 CRS 車籍里程數
 */
@Getter
public class CarReplaceEvent extends ApplicationEvent {

    private final CarUpdateKmDto outCarUpdateKmDto;

    private final CarUpdateKmDto inCarUpdateKmDto;

    private final CarBaseInfoSearchResponse outCarCrsInfo;

    private final CarBaseInfoSearchResponse inCarCrsInfo;

    private final String memberId;

    public CarReplaceEvent(Object source, CarUpdateKmDto outCarUpdateKmDto, CarUpdateKmDto inCarUpdateKmDto,
                           CarBaseInfoSearchResponse outCarCrsInfo, CarBaseInfoSearchResponse inCarCrsInfo, String memberId) {
        super(source);
        this.outCarUpdateKmDto = outCarUpdateKmDto;
        this.inCarUpdateKmDto = inCarUpdateKmDto;
        this.outCarCrsInfo = outCarCrsInfo;
        this.inCarCrsInfo = inCarCrsInfo;
        this.memberId = memberId;
    }
}
