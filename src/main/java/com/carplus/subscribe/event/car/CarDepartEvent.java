package com.carplus.subscribe.event.car;

import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 車輛出車事件，處理
 * 1. 長放車註冊
 * 2. 更新 CRS 車籍里程數
 */
@Getter
public class CarDepartEvent extends ApplicationEvent {

    private final CarUpdateKmDto carUpdateKmDto;

    private final CarBaseInfoSearchResponse crsCarInfo;

    private final String memberId;

    public CarDepartEvent(Object source, CarUpdateKmDto carUpdateKmDto, CarBaseInfoSearchResponse crsCarInfo, String memberId) {
        super(source);
        this.carUpdateKmDto = carUpdateKmDto;
        this.crsCarInfo = crsCarInfo;
        this.memberId = memberId;
    }
}
