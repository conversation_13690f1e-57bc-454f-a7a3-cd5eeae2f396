package com.carplus.subscribe.event.car;

import com.carplus.subscribe.db.mysql.dto.CarUpdateKmDto;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 車輛還車事件，處理
 * 1. 長放車註銷
 * 2. 更新 CRS 車籍里程數
 */
@Getter
public class CarReturnEvent extends ApplicationEvent {

    private final CarUpdateKmDto carUpdateKmDto;

    private final CarBaseInfoSearchResponse carCrsInfo;

    private final String memberId;

    public CarReturnEvent(Object source, CarUpdateKmDto carUpdateKmDto, CarBaseInfoSearchResponse carCrsInfo, String memberId) {
        super(source);
        this.carUpdateKmDto = carUpdateKmDto;
        this.carCrsInfo = carCrsInfo;
        this.memberId = memberId;
    }
}
