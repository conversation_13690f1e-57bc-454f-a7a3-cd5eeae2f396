package com.carplus.subscribe.event.order;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderBookingEvent extends ApplicationEvent {

    private final Orders order;
    private final String operator;

    public OrderBookingEvent(Object source, Orders order, String operator) {
        super(source);
        this.order = order;
        this.operator = operator;
    }
}
