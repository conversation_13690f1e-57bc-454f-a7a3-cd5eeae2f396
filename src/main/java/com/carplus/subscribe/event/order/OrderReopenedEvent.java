package com.carplus.subscribe.event.order;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class OrderReopenedEvent extends ApplicationEvent {

    private final Orders order;
    private final String memberId;

    public OrderReopenedEvent(Object source, Orders order, String memberId) {
        super(source);
        this.order = order;
        this.memberId = memberId;
    }
}
