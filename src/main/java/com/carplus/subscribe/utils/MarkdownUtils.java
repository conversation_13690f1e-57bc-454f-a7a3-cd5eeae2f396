package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Markdown Builder
 */
public final class MarkdownUtils {

    /**
     * <pre>
     * ``` ${type}
     *
     * ${content}
     *
     * ```
     * </pre>
     */
    public static String code(@Nullable String type, @NonNull String content) {
        StringBuilder code = new StringBuilder("``` ");

        if (StringUtils.isNotBlank(type)) {
            code.append(type);
        }
        code.append("\n");

        return code.append(content).append("\n")
            .append("```").append("\n")
            .toString();
    }

    /**
     * <pre>
     * ``` ${type}
     *
     * ${content}
     *
     * ```
     * </pre>
     */
    public static String code(String content) {
        return code(null, content);
    }

    /**
     * <pre>
     * ### ${text}
     * </pre>
     *
     * @param level 1 ~ 6
     * @param text  heading
     */
    public static String header(int level, @NonNull String text) {
        if (level > 6 || level < 1) {
            throw new IllegalArgumentException("Markdown Header 等級須介於 1~6");
        }

        StringBuilder heading = new StringBuilder();
        for (int i = 0; i < level; i++) {
            heading.append("#");
        }

        return heading.append(" ").append(text).append("\n").toString();
    }
}
