package com.carplus.subscribe.utils;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.pdf.PdfWriter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;

public final class PdfUtil {

    public static <T> void createPdf(byte[] content, OutputStream output) throws DocumentException, IOException {
        Document document = new Document(PageSize.A4);
        PdfWriter.getInstance(document, output);
        document.open();
        Image image = Image.getInstance(content);
        document.add(image);
        document.close();
    }

    public static class ByteArrayOutputStream2ByteBuffer extends ByteArrayOutputStream {

        public ByteBuffer toByteBuffer() {
            return ByteBuffer.wrap(buf, 0, count);
        }
    }
}