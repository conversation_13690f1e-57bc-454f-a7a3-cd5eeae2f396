package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import org.springframework.lang.NonNull;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * IP 工具類
 */
public final class IpUtils {

    /**
     * 與 Ip 相關的 Http Header
     */
    private static final String[] IP_HEADER_CANDIDATES = new String[] {
        "X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED", "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP", "HTTP_FORWARDED_FOR", "HTTP_FORWARDED", "HTTP_VIA", "X-Real-IP", "REMOTE_ADDR"};

    private static final String IP_UNKNOWN = "unknown ip";

    /**
     * Client IP
     *
     * @param request ServerHttpRequest
     */
    public static String getClientIp(@NonNull HttpServletRequest request) {
        for (String headerName : IP_HEADER_CANDIDATES) {
            String ip = request.getHeader(headerName);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                return ip.split(",")[0];
            }
        }

        return Optional.ofNullable(request.getRemoteAddr()).orElse(IP_UNKNOWN);
    }
}
