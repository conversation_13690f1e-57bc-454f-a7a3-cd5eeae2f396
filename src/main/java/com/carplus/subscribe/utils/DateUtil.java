package com.carplus.subscribe.utils;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.priceinfo.CalculateStage;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.chrono.MinguoDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.MONTHS;

public class DateUtil {

    public static final DateTimeFormatter SLASH_FORMATTER_WITHOUT_TIME = DateTimeFormatter.ofPattern("yyyy/MM/dd").withZone(DateUtils.ZONE_TPE);
    public static final DateTimeFormatter SLASH_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm").withZone(DateUtils.ZONE_TPE);
    public static final DateTimeFormatter DASH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").withZone(DateUtils.ZONE_TPE);

    /**
     * 取得d1與d2的差異單位數
     */
    public static long calculateDiffDate(Instant d1, Instant d2, ChronoUnit unit) {
        return DateUtil.convertToStartOfInstant(d1).atZone(DateUtils.ZONE_TPE).until(DateUtil.convertToStartOfInstant(d2).atZone(DateUtils.ZONE_TPE), unit);
    }

    public static Instant convertToEndOfInstant(Instant instant) {
        ZoneId zoneId = ZoneId.of("Asia/Taipei");
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        ZonedDateTime endOfDay = zonedDateTime.with(LocalTime.MAX);
        return endOfDay.toInstant();
    }

    public static Instant convertToStartOfInstant(Instant instant) {
        ZoneId zoneId = ZoneId.of("Asia/Taipei");
        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
        ZonedDateTime startOfDay = zonedDateTime.with(LocalTime.MIN);
        return startOfDay.toInstant();
    }

    public static Date convertToStartOfInstant(Date date) {
        ZoneId zoneId = ZoneId.of("Asia/Taipei");
        ZonedDateTime zonedDateTime = date.toInstant().atZone(zoneId);
        ZonedDateTime startOfDay = zonedDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.toInstant());
    }

    public static String getFormatString(Instant instant, DateTimeFormatter formatter) {
        return null != instant ? formatter.format(instant) : null;
    }

    public static String getFormatString(DateTimeFormatter formatter) {
        return formatter.format(Instant.now());
    }

    /**
     * 取得訂單每期開始結束時間
     */
    public static List<CalculateStage> calculateStageAndDate(Orders order) {
        List<CalculateStage> result = new ArrayList<>();
        Instant startDate = DateUtil.convertToStartOfInstant(Instant.ofEpochMilli(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()).toEpochMilli()));
        int stage = 0;
        // 以三個月為一期
        int seasonMonths = Math.min(3, order.getMonth());
        Instant expectEndDate = order.getExpectEndDate();
        if (order.getStartDate() != null) {
            expectEndDate = DateUtil.convertToStartOfInstant(startDate.atZone(DateUtils.ZONE_TPE).plus(order.getMonth(), MONTHS).minus(1, DAYS).toInstant());
        }
        // 減少天數是因為，有部分資料由短租帶過來，一年的訂單最後一期會是95天，避免因這樣資料導致多出第二期
        while (startDate.toEpochMilli() < expectEndDate.minus(6, DAYS).toEpochMilli() && stage < 4) {
            stage++;
            CalculateStage cs = new CalculateStage();
            cs.setStage(stage);
            cs.setStartDate(startDate);
            Instant endDate = Instant.ofEpochMilli(startDate.toEpochMilli()).atZone(DateUtils.ZONE_TPE).plus(seasonMonths, MONTHS).minus(1, DAYS).toInstant();
            cs.setEndDate(DateUtil.convertToEndOfInstant(endDate));
            result.add(cs);
            startDate = Instant.ofEpochMilli(startDate.toEpochMilli()).atZone(DateUtils.ZONE_TPE).plus(seasonMonths, MONTHS).toInstant();
        }
        // 最後一期固定為預計還車時間
        result.get(result.size() - 1).setEndDate(DateUtil.convertToEndOfInstant(order.getExpectEndDate()));
        return result;
    }

    public static CalculateStage calculateStageAndDateByTargetDate(Orders order, Instant date) {
        return calculateStageAndDateByTargetDate(order, date, false);
    }

    public static CalculateStage calculateStageAndDateByTargetDate(Orders order, Instant date, boolean ignoreEndDate) {
        List<CalculateStage> result = calculateStageAndDate(order);
        if (result.size() == 1 || date.isBefore(Optional.ofNullable(order.getStartDate()).orElse(order.getExpectStartDate()))) {
            return result.get(0);
        } else if (Optional.ofNullable(ignoreEndDate ? order.getExpectEndDate() : order.getEndDate()).orElse(order.getExpectEndDate()).isBefore(date)) {
            return result.get(result.size() - 1);
        } else if (result.size() == 0) {
            return null;
        }
        return result.stream().filter(calculateStage -> calculateStage.getStartDate().isBefore(date) && calculateStage.getEndDate().isAfter(date)).findFirst().orElseGet(() -> result.get(result.size() - 1));
    }

    /**
     * 計算新還車時間
     */
    @Deprecated
    public static Instant calculateNewEndDate(Instant originalStartDate, Instant newStartDate, Instant originalEndDate) {
        return calculateNewEndDate(newStartDate, calculateDiffMonth(originalStartDate, originalEndDate)).toInstant();
    }

    public static ZonedDateTime calculateNewEndDate(Instant newStartDate, int month) {
        return newStartDate.atZone(DateUtils.ZONE_TPE).plusMonths(month).minusDays(1);
    }


    public static int calculateDiffMonth(Instant d1, Instant d2) {
        long diffDay = calculateDiffDate(d1, d2, DAYS);
        return Math.abs(Math.round(diffDay / 30.0f));
    }


    /**
     * Transfer AD date to minguo date.
     * 西元年 yyyyMMdd 轉 民國年 yyyMMdd
     *
     * @param date the date
     * @return the string
     */
    public static String transferADDateToMinguoDate(Instant date) {
        return transferADDateToMinguoDate(date, "yyyMMdd");
    }

    public static String transferADDateToMinguoDate(Instant date, String pattern) {
        return MinguoDate.from(date.atZone(DateUtils.ZONE_TPE).toLocalDate()).format(DateTimeFormatter.ofPattern(pattern));
    }

    public static Date stringToDate(String date, String format) throws ParseException {

        return stringToDate(date, format, ZoneId.of("Asia/Taipei"));
    }

    public static Date stringToDate(String date, String format, ZoneId zoneId) throws ParseException {
        SimpleDateFormat sf = new SimpleDateFormat(format);
        sf.setTimeZone(TimeZone.getTimeZone(zoneId));
        return sf.parse(date);
    }
}
