package com.carplus.subscribe.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class HttpRequestUtils {

    public static String get(String url, Map<String, String> parameter) {

        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resEntity = null;
        URI uri = null;

        try {
            HttpGet httpGet = new HttpGet();
            URIBuilder urib = new URIBuilder(url);
            parameter.forEach((k, v) -> urib.addParameter(k, v));
            uri = urib.build();

            httpGet.setURI(uri);
            httpGet.setHeader("Accept", "application/json; charset=UTF-8");
            httpGet.setHeader("Content-Type", "application/json; charset=UTF-8");

            response = httpclient.execute(httpGet);
            StatusLine status = response.getStatusLine();
            if (status.getStatusCode() == HttpStatus.SC_OK) {
                resEntity = EntityUtils.toString(response.getEntity(), "UTF-8");
            } else {
                log.error("uri:{}, req:{}, res:{}", uri, status.getStatusCode(), status.getReasonPhrase());
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return resEntity;
    }

    public static void upload(String url, byte[] bytes, String mimeType) {
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpPut httpMethod = new HttpPut(url);
            httpMethod.addHeader("Content-Type", mimeType);
            httpMethod.setEntity(new ByteArrayEntity(bytes));

            try (CloseableHttpResponse response = httpclient.execute(httpMethod)) {
                StatusLine status = response.getStatusLine();
                if (status.getStatusCode() != HttpStatus.SC_OK) {
                    String errorMessage = String.format("upload fail, statusCode=%s, reasonPhrase=%s", status.getStatusCode(), status.getReasonPhrase());
                    log.error(errorMessage);
                    throw new RuntimeException(errorMessage);
                }
            }
        } catch (IOException e) {
            log.error("upload fail", e);
            throw new RuntimeException(e);
        }
    }

    public static Optional<HttpServletRequest> getHttpServletRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return Optional.ofNullable(attributes).map(ServletRequestAttributes::getRequest);
    }
}









