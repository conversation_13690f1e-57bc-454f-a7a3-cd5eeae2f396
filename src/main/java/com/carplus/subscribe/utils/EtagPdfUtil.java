package com.carplus.subscribe.utils;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.model.etag.ETagResult;
import com.carplus.subscribe.model.etag.QueryDetail;
import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;

import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER;

@Slf4j
public final class EtagPdfUtil {

    private static final String FONT_PATH = "fonts/微軟正黑體/微軟正黑體.ttf";
    private static final DecimalFormat MONEY_FORMAT = new DecimalFormat("#,##0.0");
    private static final NumberFormat INTEGER_FORMAT = NumberFormat.getNumberInstance(Locale.TAIWAN);

    static {
        INTEGER_FORMAT.setGroupingUsed(true);
        INTEGER_FORMAT.setMaximumFractionDigits(0);
        INTEGER_FORMAT.setMinimumFractionDigits(0);
    }

    /**
     * 生成 ETag PDF 報表
     */
    public static byte[] generateEtagPdf(ETagResult etagResult, String plateNo, String contractNo, ETagInfo etagInfo) throws DocumentException {

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 28, 28, 28, 28);
        PdfWriter.getInstance(document, outputStream);

        document.open();

        try {
            // 設定中文字體
            BaseFont chineseFont;
            try {
                chineseFont = BaseFont.createFont(FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            } catch (Exception e) {
                log.warn("無法載入中文字體，使用預設字體: {}", e.getMessage());
                chineseFont = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }

            Font titleFont = new Font(chineseFont, 9, Font.NORMAL);
            Font subtitleFont = new Font(chineseFont, 12, Font.BOLD);
            Font normalFont = new Font(chineseFont, 8, Font.NORMAL);
            Font detailHeaderFont = new Font(chineseFont, 8, Font.BOLD);

            // 添加標題區域
            addTitleSection(document, titleFont, subtitleFont, normalFont);

            // 添加基本資訊
            addBasicInfo(document, plateNo, contractNo, etagResult, etagInfo, normalFont);

            // 添加明細表格
            addDetailTable(document, etagResult.getDetails(), detailHeaderFont, normalFont);

        } catch (Exception e) {
            log.error("生成 ETag PDF 時發生錯誤", e);
            throw new DocumentException("PDF 生成失敗: " + e.getMessage());
        } finally {
            document.close();
        }

        return outputStream.toByteArray();
    }

    /**
     * 添加標題區域
     */
    private static void addTitleSection(Document document, Font titleFont, Font subtitleFont, Font dateFont) throws DocumentException, IOException {

        // 創建包含 Logo,標題,製表日期的表格
        PdfPTable headerTable = new PdfPTable(3);
        headerTable.setWidthPercentage(100);
        headerTable.setWidths(new float[]{1.5f, 4f, 1.5f});

        // Logo 區域
        PdfPCell logoCell = new PdfPCell();
        logoCell.setBorder(Rectangle.NO_BORDER);
        logoCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        logoCell.setVerticalAlignment(Element.ALIGN_BOTTOM);
        ClassPathResource resource = new ClassPathResource("jrxml/img/logo.png");
        InputStream inputStream = resource.getInputStream();
        byte[] imageBytes = IOUtils.toByteArray(inputStream);
        Image logo = Image.getInstance(imageBytes);
        inputStream.close();
        logo.scaleToFit(80, 80);
        logoCell.addElement(logo);

        headerTable.addCell(logoCell);

        // 標題區域
        PdfPCell titleCell = new PdfPCell();
        titleCell.setBorder(Rectangle.NO_BORDER);
        titleCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        titleCell.setPadding(10);
        // 公司名稱
        Paragraph companyParagraph = new Paragraph("格上汽車租賃股份有限公司", titleFont);
        companyParagraph.setAlignment(Element.ALIGN_CENTER);
        titleCell.addElement(companyParagraph);
        // 報表標題
        Paragraph titleParagraph = new Paragraph("遠通電收計價明細表", subtitleFont);
        titleParagraph.setAlignment(Element.ALIGN_CENTER);
        titleCell.addElement(titleParagraph);

        headerTable.addCell(titleCell);

        // 製表日期
        String reportDate = "製表日期：" + DateUtils.toDateString("yyyy/MM/dd HH:mm");
        PdfPCell dateCell = new PdfPCell(new Phrase(reportDate, dateFont));
        dateCell.setBorder(Rectangle.NO_BORDER);
        dateCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        dateCell.setVerticalAlignment(Element.ALIGN_BOTTOM);

        headerTable.addCell(dateCell);

        document.add(headerTable);

        // 添加空行
        document.add(new Paragraph(" ", titleFont));
    }

    /**
     * 添加基本資訊
     */
    private static void addBasicInfo(Document document, String plateNo, String contractNo,
                                     ETagResult etagResult, ETagInfo etagInfo, Font normalFont) throws DocumentException {

        // 創建基本資訊區域
        PdfPTable infoTable = new PdfPTable(2);
        infoTable.setWidthPercentage(100);
        infoTable.setWidths(new float[]{1f, 7f});
        infoTable.setSpacingAfter(15);

        addInfoRow(infoTable, "合約編號", contractNo, normalFont);
        addInfoRow(infoTable, "車牌號碼", plateNo, normalFont);
        addInfoRow(infoTable, "計價區間", formatDateRange(etagInfo), normalFont);
        addInfoRow(infoTable, "門架牌價總金額", "$ " + formatMoney(etagResult.getTotalAmount()) + " 元", normalFont);
        BigDecimal roundedAmount = BigDecimal.valueOf(etagResult.getTotalAmount()).setScale(0, RoundingMode.HALF_UP);
        addInfoRow(infoTable, "合計金額", "$ " + INTEGER_FORMAT.format(roundedAmount) + " 元", normalFont);

        document.add(infoTable);
    }

    private static void addInfoRow(PdfPTable table, String label, String value, Font font) {
        PdfPCell labelCell = new PdfPCell(new Phrase(label, font));
        labelCell.setBorder(Rectangle.NO_BORDER);
        labelCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        labelCell.setVerticalAlignment(Element.ALIGN_MIDDLE);

        PdfPCell valueCell = new PdfPCell(new Phrase("： " + value, font));
        valueCell.setBorder(Rectangle.NO_BORDER);
        valueCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        valueCell.setVerticalAlignment(Element.ALIGN_MIDDLE);

        table.addCell(labelCell);
        table.addCell(valueCell);
    }

    /**
     * 添加明細表格
     */
    private static void addDetailTable(Document document, List<QueryDetail> details,
                                       Font headerFont, Font normalFont) throws DocumentException {

        if (CollectionUtils.isEmpty(details)) {
            Paragraph noData = new Paragraph("無通行明細資料", normalFont);
            document.add(noData);
            return;
        }

        PdfPTable table = new PdfPTable(4);
        table.setWidthPercentage(100);

        // 設定欄位寬度：過站時間、門架、車行方向、通行費
        float[] columnWidths = {2f, 6f, 1.5f, 1.5f};
        table.setWidths(columnWidths);

        // 添加表頭
        addTableHeader(table, headerFont);

        // 添加明細資料
        for (QueryDetail detail : details) {
            addDetailRow(table, detail, normalFont);
        }

        document.add(table);
    }

    /**
     * 添加表頭
     */
    private static void addTableHeader(PdfPTable table, Font headerFont) {
        String[] headers = {"過站時間", "門架", "車行方向", "通行費"};

        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            cell.setBackgroundColor(Color.LIGHT_GRAY);
            cell.setPadding(8);
            cell.setBorder(Rectangle.BOX);
            table.addCell(cell);
        }
    }

    /**
     * 添加明細行
     */
    private static void addDetailRow(PdfPTable table, QueryDetail detail, Font smallFont) {
        // 過站時間
        addTableCell(table, formatDetailDateTime(detail.getTollDate()), smallFont, Element.ALIGN_CENTER);

        // 門架
        String gantryInfo = String.format("%s - %s",
            detail.getGantry() != null ? detail.getGantry() : "未知通行門架",
            detail.getDirection() != null ? detail.getDirection() : "未知通行路段");
        addTableCell(table, gantryInfo, smallFont, Element.ALIGN_CENTER);

        // 車行方向
        addTableCell(table, detail.getDirection(), smallFont, Element.ALIGN_CENTER);

        // 通行費
        addTableCell(table, "$ " + formatMoney(detail.getFee()), smallFont, Element.ALIGN_CENTER);
    }

    /**
     * 添加表格欄位
     */
    private static void addTableCell(PdfPTable table, String content, Font font, int alignment) {
        PdfPCell cell = new PdfPCell(new Phrase(content, font));
        cell.setHorizontalAlignment(alignment);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setPadding(5);
        cell.setBorder(Rectangle.BOX);
        table.addCell(cell);
    }

    /**
     * 格式化計價日期區間
     */
    private static String formatDateRange(ETagInfo etagInfo) {
        if (etagInfo.getDepartDate() != null && etagInfo.getReturnDate() != null) {
            return String.format("%s ~ %s",
                DateUtil.getFormatString(etagInfo.getDepartDate(), SLASH_FORMATTER),
                DateUtil.getFormatString(etagInfo.getReturnDate(), SLASH_FORMATTER));
        }
        return "未知";
    }

    /**
     * 格式化明細日期時間 (只顯示日期和時分)
     */
    private static String formatDetailDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.length() != 14) {
            return dateTimeStr;
        }

        try {
            String year = dateTimeStr.substring(0, 4);
            String month = dateTimeStr.substring(4, 6);
            String day = dateTimeStr.substring(6, 8);
            String hour = dateTimeStr.substring(8, 10);
            String minute = dateTimeStr.substring(10, 12);

            return String.format("%s/%s/%s %s:%s", year, month, day, hour, minute);
        } catch (Exception e) {
            return dateTimeStr;
        }
    }

    /**
     * 格式化金額
     */
    private static String formatMoney(Object amount) {
        if (amount == null) {
            return "0.0";
        }

        try {
            if (amount instanceof String) {
                return MONEY_FORMAT.format(Double.parseDouble((String) amount));
            } else if (amount instanceof Number) {
                return MONEY_FORMAT.format(((Number) amount).doubleValue());
            }
        } catch (Exception e) {
            log.warn("格式化金額失敗: {}", amount, e);
        }

        return String.valueOf(amount);
    }
}
