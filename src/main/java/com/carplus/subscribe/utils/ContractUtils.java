package com.carplus.subscribe.utils;

import com.carplus.subscribe.db.mysql.entity.econtract.EContract;
import com.carplus.subscribe.enums.ContractStatus;
import com.carplus.subscribe.enums.EContractType;
import com.carplus.subscribe.enums.TaskType;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
public class ContractUtils {
    private ContractUtils() {
    }

    public static boolean isStatusEqual(Integer contractStatusCode, ContractStatus status) {
        return Objects.equals(contractStatusCode, status.getCode());
    }

    /**
     * Email 寄送邏輯檢核 Map <br/>
     * {@link #isECFilesMatchCompleteCond} <br/>
     * {@link #isECFilesMatchGoingCond}
     */
    public static final Map<ContractStatus, Predicate<EContract>> CONTRACT_STATUS_PREDICATE_MAP =
            ImmutableMap.<ContractStatus, Predicate<EContract>>builder()
                    .put(ContractStatus.GOING, ContractUtils::isECFilesMatchGoingCond)
                    .put(ContractStatus.COMPLETE, ContractUtils::isECFilesMatchCompleteCond)
                    //.put(其他狀態, 其他處理邏輯)
                    .build();

    /**
     * 合約檔案寄送(起租) - 檔案檢核
     */
    private static boolean isECFilesMatchGoingCond(EContract ec) {
        EContractType t = EContractType.valueOf(ec.getEContractType());
        switch (t) {
            case E_CONTRACT:
                return t.name().equals(ec.getEContractType()) && ec.getEContractSignDate() != null;
            case E_RENTAL:
                return t.name().equals(ec.getEContractType()) && ec.getERentalSignDate() != null && ec.getDepartTaskId() != null;
            default:
//                log.info("起租 notify - no match e_contract type and signed");
                return false;
        }
    }

    /**
     * 合約檔案寄送(迄租) - 檔案檢核
     */
    private static boolean isECFilesMatchCompleteCond(EContract ec) {
        EContractType t = EContractType.valueOf(ec.getEContractType());
        if (t.equals(EContractType.E_RENTAL) && ec.getERentalSignDate() != null && ec.getReturnTaskId() != null) {
            return true;
        }
//        log.info("迄租 notify - no match e_contract type and signed");
        return false;
    }

    public static TaskType ecToTaskType(EContract ec) {
        if (ec.getDepartTaskId() != null) {
            return TaskType.DEPART;
        }

        if (ec.getReturnTaskId() != null) {
            return TaskType.RETURN;
        }
        return null;
    }

    public static Map<EContractType, List<EContract>> toECMap(List<EContract> contracts) {
        return contracts.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ec -> EContractType.valueOf(ec.getEContractType())));
    }

    public static Map<Integer, EContract> getECFileMap(List<EContract> contracts) {
        return contracts.stream().collect(Collectors.toMap(ec -> ec.getUploadFileId().get(0), Function.identity()));
    }

    public static List<Integer> getECFiles(List<EContract> contracts) {
        return contracts.stream()
                .map(EContract::getUploadFileId)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public static boolean isContract(String econtractRefEntityNo) {
        return econtractRefEntityNo.startsWith("C");
    }
}
