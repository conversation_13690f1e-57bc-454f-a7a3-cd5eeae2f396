package com.carplus.subscribe.utils;

import com.univocity.parsers.annotations.Parsed;
import com.univocity.parsers.common.processor.BeanListProcessor;
import com.univocity.parsers.common.processor.BeanWriterProcessor;
import com.univocity.parsers.csv.CsvParser;
import com.univocity.parsers.csv.CsvParserSettings;
import com.univocity.parsers.csv.CsvWriter;
import com.univocity.parsers.csv.CsvWriterSettings;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.List;

public final class CsvUtil {

    public static <T> void createCsv(
        List<T> records,
        String[] headers,
        boolean isWriteHeaders,
        char delimiter,
        OutputStream output,
        Charset encoding,
        Class<T> type
    ) {
        CsvWriterSettings settings = new CsvWriterSettings();
        settings.setRowWriterProcessor(new BeanWriterProcessor<>(type));
        settings.setHeaders(headers);
        settings.getFormat().setDelimiter(delimiter);

        CsvWriter writer = new CsvWriter(output, encoding, settings);
        if (isWriteHeaders) {
            writer.writeHeaders(headers);
        }

        writer.processRecordsAndClose(records);
    }

    /**
     * 讀入CSV並轉為物件
     *
     * @Notice 若field使用中文，可能會出現轉碼問題，在  {@link Parsed}裡使用 index設定
     */
    public static <T> List<T> readCsv(Reader reader, Class<T> type) {
        BeanListProcessor<T> rowProcessor = new BeanListProcessor<>(type);
        CsvParserSettings settings = new CsvParserSettings();
        settings.setHeaderExtractionEnabled(true);
        settings.setProcessor(rowProcessor);
        CsvParser parser = new CsvParser(settings);
        parser.parse(reader);
        return rowProcessor.getBeans();
    }

    public static class ByteArrayOutputStream2ByteBuffer extends ByteArrayOutputStream {

        public ByteBuffer toByteBuffer() {
            return ByteBuffer.wrap(buf, 0, count);
        }
    }
}
