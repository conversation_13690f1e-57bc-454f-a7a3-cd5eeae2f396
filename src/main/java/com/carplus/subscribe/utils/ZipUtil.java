package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import com.google.common.collect.Maps;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.io.outputstream.ZipOutputStream;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.CompressionMethod;
import net.lingala.zip4j.model.enums.EncryptionMethod;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Map;

public class ZipUtil {

    public static ZipFile encryptZip(File sourceFile, String fileName, String password, String encode) {

        ZipParameters zipParameters = new ZipParameters();

        // Below line is optional. AES 256 is used by default. You can override it to use AES 128. AES 192 is supported only for extracting.
        if (StringUtils.isNotBlank(password)) {
            zipParameters.setEncryptFiles(true);
            zipParameters.setEncryptionMethod(EncryptionMethod.ZIP_STANDARD);
            zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
        }

        ZipFile zipFile = StringUtils.isNotBlank(password) ? new ZipFile(fileName, password.toCharArray()) : new ZipFile(fileName);
        zipFile.setCharset((encode == null || encode.isEmpty())
            ? Charset.defaultCharset() : Charset.forName(encode));
        try {
            zipFile.addFile(sourceFile, zipParameters);
            return zipFile;
        } catch (ZipException e) {
            throw new RuntimeException(e);
        }
    }

    public static byte[] zipAndEncrypt(String innerFileName, String password, byte[] arrSource) throws IOException {
        Map<String, byte[]> map = Maps.newHashMap();
        map.put(innerFileName, arrSource);

        return zipAndEncrypt(map, password);
    }


    public static byte[] zipAndEncrypt(Map<String, byte[]> fileByteMap, String password) throws IOException {
        CompressionMethod compressionMethod = CompressionMethod.DEFLATE;

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);
        if (StringUtils.isNotBlank(password)) {
            zos = new ZipOutputStream(baos, password.toCharArray());
        }

        for (Map.Entry<String, byte[]> entry : fileByteMap.entrySet()) {
            ZipParameters zipParameters = new ZipParameters();
            zipParameters.setCompressionMethod(compressionMethod);
            if (StringUtils.isNotBlank(password)) {
                EncryptionMethod encryptionMethod = EncryptionMethod.AES;
                AesKeyStrength aesKeyStrength = AesKeyStrength.KEY_STRENGTH_256;
                zipParameters.setEncryptionMethod(encryptionMethod);
                zipParameters.setAesKeyStrength(aesKeyStrength);
                zipParameters.setEncryptFiles(true);
            }
            zipParameters.setFileNameInZip(entry.getKey());
            zos.putNextEntry(zipParameters);
            zos.write(entry.getValue(), 0, entry.getValue().length);
            zos.closeEntry();
        }
        zos.close();
        return baos.toByteArray();
    }
}
