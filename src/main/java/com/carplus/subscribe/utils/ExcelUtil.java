package com.carplus.subscribe.utils;

import carplus.common.response.exception.ServerException;
import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted;

@Slf4j
public class ExcelUtil {

    private Workbook workbook;

    @Getter
    private String fileName;

    public Builder builder() {
        return new Builder();
    }

    public boolean sheetIsExist(String sheetName) {
        return Optional.ofNullable(workbook).map(w -> w.getSheet(sheetName)).map(s -> s != null).orElse(false);
    }

    public void export(OutputStream outputStream) {
        try {
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
        } catch (IOException e) {
            throw new ServerException("匯出Excel失敗", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    throw new ServerException("匯出Excel失敗" + e.getMessage(), e);
                }
            }
        }
    }

    public byte[] toByte() {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            workbook.write(out);
            workbook.close();
            return out.toByteArray();
        } catch (IOException e) {
            throw new ServerException("Excel轉Byte失敗", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    throw new ServerException("Excel轉Byte失敗" + e.getMessage(), e);
                }
            }
        }
    }

    public File toFile() {
        File file = new File(fileName);
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file, false);
            outputStream.write(toByte());
            return file;
        } catch (IOException e) {
            throw new ServerException("Excel轉File失敗", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    throw new ServerException("Excel轉File失敗" + e.getMessage(), e);
                }
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SheetData {
        private String sheetName;
        private List<String> heads;
        private List<Map<String, Object>> data;

    }

    public class Builder {
        ExcelUtil parent = new ExcelUtil();

        public Builder() {
            parent.workbook = new XSSFWorkbook();
        }

        public Builder setfileName(String name) {
            parent.fileName = name;
            return this;
        }

        public Builder addSheetData(String sheetName, List<String> heads, List<Map<String, Object>> data) {
            if (!parent.sheetIsExist(sheetName)) {
                parent.workbook.createSheet(sheetName);
            }
            Sheet sheet = parent.workbook.getSheet(sheetName);
            addHeadRow(sheet, heads);
            addRows(sheet, heads, data, 1);
            return this;
        }

        public Builder addSheetData(SheetData sheetData) {
            return addSheetData(sheetData.getSheetName(), sheetData.getHeads(), sheetData.getData());
        }

        public Builder addHeadRow(Sheet sheet, List<String> heads) {
            Row headRow = sheet.createRow(0);
            for (int i = 0; i < heads.size(); i++) {
                headRow.createCell(i).setCellValue(heads.get(i));
            }
            return this;
        }

        public Builder addRows(Sheet sheet, List<String> heads, List<Map<String, Object>> data, int startIndex) {

            for (Map<String, Object> map : data) {
                addRow(sheet, heads, map, startIndex);
                startIndex++;
            }
            return this;
        }

        public Builder addRow(Sheet sheet, List<String> row, int index) {
            Row headRow = sheet.createRow(index);
            for (int i = 0; i < row.size(); i++) {
                createCell(headRow, i, row.get(i));
            }
            return this;
        }

        public Builder addRow(Sheet sheet, List<String> heads, Map<String, Object> data, int index) {
            Row row = sheet.createRow(index);
            for (int i = 0; i < heads.size(); i++) {
                createCell(row, i, Optional.ofNullable(data.get(heads.get(i))).orElse(""));
            }
            return this;
        }

        public Builder setForegroundColor(int sheetIndex, int[][] cellCoordinates, IndexedColors color) {
            Sheet sheet = parent.workbook.getSheetAt(sheetIndex);
            CellStyle style = parent.workbook.createCellStyle();
            style.setFillForegroundColor(color.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            for (int[] coordinate : cellCoordinates) {
                Cell cell = getCell(coordinate, sheet);
                cell.setCellStyle(style);
            }

            return this;
        }

        public Builder setBorders(int sheetIndex, int[][] cellCoordinates, BorderStyle borderStyle, IndexedColors borderColor) {
            Sheet sheet = parent.workbook.getSheetAt(sheetIndex);

            for (int[] coordinate : cellCoordinates) {
                Cell cell = getCell(coordinate, sheet);
                CellStyle existingStyle = cell.getCellStyle();
                CellStyle newStyle = parent.workbook.createCellStyle();
                newStyle.cloneStyleFrom(existingStyle);
                newStyle.setBorderTop(borderStyle);
                newStyle.setBorderRight(borderStyle);
                newStyle.setBorderBottom(borderStyle);
                newStyle.setBorderLeft(borderStyle);
                newStyle.setTopBorderColor(borderColor.getIndex());
                newStyle.setRightBorderColor(borderColor.getIndex());
                newStyle.setBottomBorderColor(borderColor.getIndex());
                newStyle.setLeftBorderColor(borderColor.getIndex());
                cell.setCellStyle(newStyle);
            }

            return this;
        }

        private Cell getCell(int[] coordinate, Sheet sheet) {
            int rowIndex = coordinate[0];
            int columnIndex = coordinate[1];

            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }
            Cell cell = row.getCell(columnIndex);
            if (cell == null) {
                cell = row.createCell(columnIndex);
            }
            return cell;
        }

        public Builder addComment(int sheetIndex, int[] cellCoordinate, String comment) {
            XSSFSheet sheet = (XSSFSheet) parent.workbook.getSheetAt(sheetIndex);

            Cell cell = getCell(cellCoordinate, sheet);
            XSSFDrawing drawing = sheet.createDrawingPatriarch();
            CreationHelper creationHelper = parent.workbook.getCreationHelper();
            ClientAnchor anchor = creationHelper.createClientAnchor();
            anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
            anchor.setCol1(cellCoordinate[1] + 1);
            anchor.setRow1(cellCoordinate[0] + 8);
            anchor.setCol2(cellCoordinate[1] + 4);
            anchor.setRow2(cellCoordinate[0] + 12);
            RichTextString richTextString = creationHelper.createRichTextString(comment);

            XSSFComment commentCell = drawing.createCellComment(anchor);
            commentCell.setString(richTextString);
            commentCell.setAuthor("chiachen.tu");
            commentCell.setVisible(true);
            cell.setCellComment(commentCell);

            return this;
        }

        public ExcelUtil build() {
            return parent;
        }

        private void createCell(Row row, int index, Object valueOfCell) {
            Cell cell = row.createCell(index);
            if (valueOfCell instanceof Integer) {
                cell.setCellValue((Integer) valueOfCell);
            } else if (valueOfCell instanceof Long) {
                cell.setCellValue((Long) valueOfCell);
            } else if (valueOfCell instanceof String) {
                cell.setCellValue((String) valueOfCell);
            } else if (valueOfCell instanceof Double) {
                cell.setCellValue((Double) valueOfCell);
            } else {
                cell.setCellValue((Boolean) valueOfCell);
            }
        }
    }

    public static boolean isCellNullOrBlank(Cell cell) {
        return cell == null || cell.getCellType() == CellType.BLANK
            || (cell.getCellType() == CellType.STRING && StringUtils.isBlank(cell.getStringCellValue()));
    }

    public static <T> T getCellValue(Cell cell, Class<T> targetType) throws ParseException {
        if (isCellNullOrBlank(cell)) {
            if (Boolean.class == targetType) {
                return targetType.cast(false);
            }
            return null;
        }

        switch (cell.getCellType()) {
            case NUMERIC:
                if (isCellDateFormatted(cell)) {
                    if (targetType == Date.class) {
                        return targetType.cast(cell.getDateCellValue());
                    } else if (targetType == String.class) {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(DateUtils.ZONE_TPE));
                        return targetType.cast(simpleDateFormat.format(cell.getDateCellValue()));
                    }
                }
                double numericValue = cell.getNumericCellValue();
                if (targetType == Integer.class) {
                    return targetType.cast((int) numericValue);
                } else if (targetType == Double.class) {
                    return targetType.cast(numericValue);
                } else if (targetType == Long.class) {
                    return targetType.cast((long) numericValue);
                } else if (targetType == String.class) {
                    return targetType.cast(String.valueOf((long) numericValue));
                }
                break;

            case STRING:
                String stringValue = cell.getStringCellValue().trim();
                if (targetType == String.class) {
                    return targetType.cast(stringValue);
                } else if (targetType == Integer.class) {
                    return targetType.cast(Integer.valueOf(stringValue));
                } else if (targetType == Double.class) {
                    return targetType.cast(Double.valueOf(stringValue));
                } else if (targetType == Long.class) {
                    return targetType.cast(Long.valueOf(stringValue));
                } else if (targetType == Date.class) {
                    return targetType.cast(DateUtil.stringToDate(stringValue, "yyyy-MM-dd HH:mm:ss"));
                } else if (targetType == Boolean.class) {
                    return targetType.cast("Y".equalsIgnoreCase(stringValue));
                }
                break;

            case BOOLEAN:
                boolean boolValue = cell.getBooleanCellValue();
                if (targetType == Boolean.class) {
                    return targetType.cast(boolValue);
                } else if (targetType == String.class) {
                    return targetType.cast(boolValue ? "Y" : "N");
                }
                break;

            default:
                return null;
        }

        return null;
    }

    public static int calculateCellOffset(Sheet sheet) {
        if (sheet == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.DEALER_ORDER_SHEET_EMPTY);
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            throw new SubscribeException(DEALER_ORDER_SHEET_FIRST_ROW_EMPTY);
        }
        Cell firstCellOfFirstRow = firstRow.getCell(0);
        String cellValue;
        try {
            cellValue = Optional.ofNullable(getCellValue(firstCellOfFirstRow, String.class)).orElseThrow(() -> new SubscribeException(DEALER_ORDER_SHEET_FIRST_CELL_EMPTY));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return DealerOrderExcelColumn.ORDER_NO.getDescription().equals(cellValue) ? 0 : 1;
    }

    public static boolean isEntireRowEmpty(Row row, int startIndex) {
        if (row == null) {
            return true;
        }
        int endIndex = Math.min(DealerOrderExcelColumn.values().length + startIndex, row.getLastCellNum());
        for (int cellIndex = startIndex; cellIndex < endIndex; cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (!isCellNullOrBlank(cell)) {
                return false; // Found a non-empty cell
            }
        }
        return true; // All cells in the range are empty
    }
}
