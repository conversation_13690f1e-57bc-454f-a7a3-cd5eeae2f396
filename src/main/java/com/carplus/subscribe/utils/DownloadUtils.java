package com.carplus.subscribe.utils;

import carplus.common.response.exception.ServerException;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.server.GoSmartServer;
import com.univocity.parsers.csv.CsvParserSettings;
import com.univocity.parsers.csv.CsvRoutines;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URI;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.List;

@Slf4j
@Component
public class DownloadUtils {

    private static String ossURL;
    private GoSmartServer goSmartServer;

    @Deprecated
    public static InputStream getInputStreamByURL(String url) {
        return getInputStream(url);
    }

    public static InputStream getInputStreamByURN(String urn) {
        return getInputStream(ossURL + urn);
    }

    public static Workbook getWorkBookFileByURL(String url) {
        return getWorkBookFile(url);
    }

    public static Workbook getWorkBookFileByURN(String urn) {
        return getWorkBookFile(ossURL + urn);
    }

    public static <T> List<T> getCSVListByURL(String url, Class<T> beanType, String delimiter, Charset charset) {
        return getCSVList(url, beanType, delimiter, charset);
    }

    public static <T> List<T> getCSVListByURN(String urn, Class<T> beanType, String delimiter, Charset charset) {
        return getCSVList(ossURL + urn, beanType, delimiter, charset);
    }

    /**
     * excel 檔案下載 & 轉換成Workbook類別
     * 帶URN (網址後綴)
     * ex: https://int-carplus-uploader.oss-cn-hongkong.aliyuncs.com/suploader/rental_coupon/de63c08e-22dc-4872-8bf9-617c657e7d41.xls
     * fileURN = suploader/rental_coupon/de63c08e-22dc-4872-8bf9-617c657e7d41.xls
     */
    private static Workbook getWorkBookFile(String fileURL) {
        try {
            return HttpUtils.get(fileURL)
                .then(res -> {
                    Header contentType = res.getEntity().getContentType();
                    if (contentType != null
                            && !StringUtils.equals(contentType.getValue(), "application/vnd.ms-excel")
                            && !StringUtils.equals(contentType.getValue(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")) {
                        throw new ServerException("檔案格式不是xls或xlsx");
                    }
                    try (InputStream content = res.getEntity().getContent()) {
                        if (contentType.getValue().equals("application/vnd.ms-excel")) {
                            return new HSSFWorkbook(content);
                        } else {
                            return new XSSFWorkbook(content);
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                })
                .fetch();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * csv 檔案下載 & 轉換成代入的類別集合
     * fileURN -> 網址後綴 (URN)
     * beanType -> class型別
     * delimiter -> 分隔符號 通常為 ','
     * charset -> csv編碼
     */
    private static <T> List<T> getCSVList(String fileURN, Class<T> beanType, String delimiter, Charset charset) {
        try {
            return HttpUtils.get(fileURN)
                .then(res -> {
                    Header contentType = res.getEntity().getContentType();
                    if (contentType != null && !StringUtils.equals(contentType.getValue(), "text/csv")) {
                        throw new ServerException("檔案格式不是CSV");
                    }
                    try (InputStreamReader inputStream = new InputStreamReader(res.getEntity().getContent(), charset);
                         Reader reader = new BufferedReader(inputStream)) {
                        CsvRoutines routines = getCsvRoutines(delimiter);
                        return routines.parseAll(beanType, reader);
                    } catch (IOException e) {
                        throw new ServerException(e.getMessage());
                    }
                })
                .fetch();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 檔案下載
     * 記得關閉資料流
     */
    private static InputStream getInputStream(String url) {
        try {
            CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet();
            httpGet.setURI(URI.create(url));
            CloseableHttpResponse execute = closeableHttpClient.execute(httpGet);
            return execute.getEntity().getContent();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @NonNull
    private static CsvRoutines getCsvRoutines(String delimiter) {
        CsvParserSettings parserSettings = new CsvParserSettings();
        parserSettings.getFormat().setLineSeparator("\n");
        parserSettings.getFormat().setDelimiter(delimiter);
        return new CsvRoutines(parserSettings);
    }

    public static byte[] downloadFileAsByteArray(String fileUrl) throws Exception {
        URL url = new URL(fileUrl);

        try (InputStream in = url.openStream(); ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int n;
            while ((n = in.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }
            return out.toByteArray();
        }
    }
}
