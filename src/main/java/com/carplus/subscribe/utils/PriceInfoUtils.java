package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import org.hibernate.persister.entity.EntityPersister;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class PriceInfoUtils {

    public static boolean hasPriceInfoChanged(Object[] oldState, Object[] newState, EntityPersister persister) {
        if (oldState == null || newState == null || persister == null) {
            return false;
        }

        Map<String, Integer> fieldIndexMap = getFieldIndexMap(persister);

        if (!Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.amount)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.amount)])
            || !Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.receivedAmount)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.receivedAmount)])
            || !Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.lastPayDate)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.lastPayDate)])
            || !Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.receivableDate)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.receivableDate)])
            || !Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.refPriceInfoNo)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.refPriceInfoNo)])
            || !Objects.equals(oldState[fieldIndexMap.get(OrderPriceInfo.Fields.uid)], newState[fieldIndexMap.get(OrderPriceInfo.Fields.uid)])) {
            return true;
        }

        PriceInfoDetail oldInfoDetail = (PriceInfoDetail) oldState[fieldIndexMap.get(OrderPriceInfo.Fields.infoDetail)];
        PriceInfoDetail newInfoDetail = (PriceInfoDetail) newState[fieldIndexMap.get(OrderPriceInfo.Fields.infoDetail)];

        if (oldInfoDetail == null && newInfoDetail == null) {
            return false;
        }
        if (oldInfoDetail == null || newInfoDetail == null) {
            return true;
        }

        return !Objects.equals(oldInfoDetail.getStartMileage(), newInfoDetail.getStartMileage())
            || !Objects.equals(oldInfoDetail.getEndMileage(), newInfoDetail.getEndMileage())
            || !Objects.equals(oldInfoDetail.getDiscountMileage(), newInfoDetail.getDiscountMileage())
            || !Objects.equals(oldInfoDetail.getOriginalDiscountMileage(), newInfoDetail.getOriginalDiscountMileage())
            || !Objects.equals(oldInfoDetail.getRenewDiscountMileage(), newInfoDetail.getRenewDiscountMileage())
            || !Objects.equals(oldInfoDetail.getRentalDiscountMileage(), newInfoDetail.getRentalDiscountMileage())
            || !Objects.equals(oldInfoDetail.getTotalMileage(), newInfoDetail.getTotalMileage())
            || !Objects.equals(oldInfoDetail.getMileageFee(), newInfoDetail.getMileageFee())
            || !Objects.equals(oldInfoDetail.getMonthlyFee(), newInfoDetail.getMonthlyFee())
            || !Objects.equals(oldInfoDetail.getMonth(), newInfoDetail.getMonth())
            || !Objects.equals(oldInfoDetail.getDay(), newInfoDetail.getDay())
            || !Objects.equals(oldInfoDetail.getInsurance(), newInfoDetail.getInsurance())
            || !Objects.equals(oldInfoDetail.getReplacementCarFee(), newInfoDetail.getReplacementCarFee())
            || !Objects.equals(oldInfoDetail.getARCarLossAmt(), newInfoDetail.getARCarLossAmt())
            || !Objects.equals(oldInfoDetail.getCarLossAmt(), newInfoDetail.getCarLossAmt())
            || !Objects.equals(oldInfoDetail.getDelayDays(), newInfoDetail.getDelayDays())
            || !Objects.equals(oldInfoDetail.getFineFrom(), newInfoDetail.getFineFrom())
            || !Objects.equals(oldInfoDetail.getOriginAmount(), newInfoDetail.getOriginAmount())
            || !Objects.equals(oldInfoDetail.getDiscount(), newInfoDetail.getDiscount())
            || !Objects.equals(oldInfoDetail.getReason(), newInfoDetail.getReason())
            || !Objects.equals(oldInfoDetail.getIsAgree(), newInfoDetail.getIsAgree())
            || !Objects.equals(oldInfoDetail.getAdminId(), newInfoDetail.getAdminId())
            || !Objects.equals(oldInfoDetail.getManagerId(), newInfoDetail.getManagerId())
            || !Objects.equals(oldInfoDetail.getDecideRemark(), newInfoDetail.getDecideRemark())
            || !Objects.equals(oldInfoDetail.getOriginalDiscount(), newInfoDetail.getOriginalDiscount())
            || !Objects.equals(oldInfoDetail.getChargingPoint(), newInfoDetail.getChargingPoint())
            || !Objects.equals(oldInfoDetail.getAddMileage(), newInfoDetail.getAddMileage())
            || !Objects.equals(oldInfoDetail.getUnitPrice(), newInfoDetail.getUnitPrice())
            || !Objects.equals(oldInfoDetail.getActualUnitPrice(), newInfoDetail.getActualUnitPrice())
            || !Objects.equals(oldInfoDetail.getQuantity(), newInfoDetail.getQuantity());
    }

    private static Map<String, Integer> getFieldIndexMap(EntityPersister persister) {
        Map<String, Integer> map = new HashMap<>();
        String[] propertyNames = persister.getPropertyNames();
        for (int i = 0; i < propertyNames.length; i++) {
            map.put(propertyNames[i], i);
        }
        return map;
    }

    public static void setUpdater(OrderPriceInfo priceInfo, String memberId) {
        if (priceInfo != null && StringUtils.isNotBlank(memberId)) {
            priceInfo.setUpdater(memberId);
        }
    }
}
