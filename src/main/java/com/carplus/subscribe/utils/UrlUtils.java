package com.carplus.subscribe.utils;

import com.carplus.subscribe.config.AppProperties;

public class UrlUtils {

    private UrlUtils() {
    }

    public static String normalizeGcsUrl(String rawUrlOrPathString) {
        if (rawUrlOrPathString == null) {
            return null;
        }

        String gcsBaseUrlFromProperties = AppProperties.GCS.getUrl();

        // 1. 標準化 GCS 基礎 URL：移除末尾的斜線（如果存在）
        String cleanGcsBaseUrl = gcsBaseUrlFromProperties.endsWith("/")
            ? gcsBaseUrlFromProperties.substring(0, gcsBaseUrlFromProperties.length() - 1)
            : gcsBaseUrlFromProperties;

        // 2. 提取路徑部分
        //    使用原始的 gcsBaseUrlFromProperties（可能帶有末尾斜線）進行搜索，
        //    因為 rawUrlOrPathString 可能是用該確切字符串形成的。
        String pathSegment;
        int lastBaseUrlIndex = rawUrlOrPathString.lastIndexOf(gcsBaseUrlFromProperties);

        if (lastBaseUrlIndex != -1) {
            // 路徑是 GCS 基礎 URL 最後一次出現位置之後的部分
            pathSegment = rawUrlOrPathString.substring(lastBaseUrlIndex + gcsBaseUrlFromProperties.length());
        } else {
            // 在輸入字串中未找到 GCS 基礎 URL，將整個輸入視為路徑
            pathSegment = rawUrlOrPathString;
        }

        // 3. 標準化提取出的路徑部分
        //    - 如果路徑非空且不以 "/" 開頭，則添加前導 "/"
        //    - 如果路徑僅為 "/"，則轉換為空字符串
        if (!pathSegment.isEmpty() && !pathSegment.startsWith("/")) {
            pathSegment = "/" + pathSegment;
        }

        if (pathSegment.equals("/")) {
            pathSegment = ""; // 避免形成 "https://host//" 或 "https://host/" (如果期望是 "https://host")
        }

        // 4. 組合標準化的基礎 URL 和標準化的路徑部分
        return cleanGcsBaseUrl + pathSegment;
    }
}