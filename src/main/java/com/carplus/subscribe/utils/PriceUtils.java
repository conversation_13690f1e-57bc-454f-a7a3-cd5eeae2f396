package com.carplus.subscribe.utils;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CancellationPolicy;
import com.carplus.subscribe.enums.CustSource;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.SecurityDepositInfo;
import com.carplus.subscribe.model.priceinfo.resp.CancelOrderCalculateResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.Merchandise;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.ORDER_PRICE_INFO_CATEGORY_MIXED;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT;
import static java.time.temporal.ChronoUnit.DAYS;

/**
 * 計算金額 utils
 */
public final class PriceUtils {

    /**
     * 取得折扣後的整數金額
     *
     * @param price    原金額
     * @param disCount 折扣值 ex: 5代表 價格打95折
     */
    public static int priceOff(int price, double disCount) {
        return (int) Math.round(price * ((100d - disCount) / 100)); // 金額 *（扣除折扣後的百分比）並四捨五入
    }

    /**
     * 取得折扣後的整數金額
     *
     * @param number 要格式化的整數
     * @return 格式化後的字符串，每三位數字用逗號分隔
     */
    public static String formatWithThousandsSeparator(int number) {
        return String.format("%,d", number);
    }

    /**
     * 是否為線上訂單
     */
    public static boolean isOnline(@Nullable CustSource custSource) {
        return CustSource.GOSMART == custSource || CustSource.WEB == custSource;
    }

    public static CancelOrderCalculateResponse calculateCancelOrder(Orders order, boolean isNewCar) {
        Map<String, CancellationPolicy> map = calculateCancelOrderLevelsAmt(isNewCar);
        map.forEach((key, value) -> value.calculateCancelPolicy(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit()));
        CancellationPolicy level = null;
        if (order.getStatus() < OrderStatus.BOOKING.getStatus() || !order.getIsNewOrder()) {
            level = CancellationPolicy.of(CancellationPolicy.SubscribeNewCar.ofDays(0));
        } else {
            level = getCancelOrderLevel(
                Optional.ofNullable(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo())
                    .map(SecurityDepositInfo::getSecurityDepositDate).map(Date::toInstant).orElseThrow(() -> new SubscribeException(
                        ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT)), isNewCar);
        }
        level.calculateCancelPolicy(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit());
        CancelOrderCalculateResponse response = new CancelOrderCalculateResponse();
        response.setLevel(level);
        response.setLevelRefundAmtMap(map);
        response.setSecurityDepositAmt(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo().getSecurityDeposit());
        response.setPaidSecurityDeposit(Optional.ofNullable(order.getContract().getMainContract().getOriginalPriceInfo().getSecurityDepositInfo()).map(SecurityDepositInfo::getPaidSecurityDeposit).orElse(0));
        return response;
    }

    /**
     * 取得級距Map
     */
    private static Map<String, CancellationPolicy> calculateCancelOrderLevelsAmt(boolean isNewCar) {
        if (isNewCar) {
            return CancellationPolicy.SubscribeNewCar.policyMap;
        }
        return CancellationPolicy.SubscribeOldCar.policyMap;
    }

    /**
     * 訂單取消級距
     */
    public static CancellationPolicy getCancelOrderLevel(Instant date, boolean isNewCar) {
        long diff = DAYS.between(Instant.now().atZone(DateUtils.ZONE_TPE).toLocalDate(), date.atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay());
        CancellationPolicy policy;
        if (isNewCar) {
            diff *= -1;
            policy = CancellationPolicy.of(CancellationPolicy.SubscribeNewCar.ofDays(diff));
        } else {
            policy = CancellationPolicy.of(CancellationPolicy.SubscribeOldCar.ofDays(diff));
        }
        return policy;
    }

    /**
     * 檢查所有 OrderPriceInfo 是否皆為汽車用品類別或關聯費用編號為汽車用品費用 id
     */
    public static boolean areAllPriceInfosMerchandiseRelated(List<OrderPriceInfo> orderPriceInfoList) {
        return areAllPriceInfosMerchandiseRelated(orderPriceInfoList, null);
    }

    /**
     * 檢查所有 OrderPriceInfo 是否皆為汽車用品類別或關聯費用編號為汽車用品費用 id
     *
     * @param orderPriceInfoList 訂單價格資訊列表
     * @param filterIds          需要過濾的價格資訊ID列表，如果為null則不過濾
     */
    public static boolean areAllPriceInfosMerchandiseRelated(List<OrderPriceInfo> orderPriceInfoList, List<Integer> filterIds) {
        if (CollectionUtils.isEmpty(orderPriceInfoList)) {
            return false;
        }

        // 取得所有汽車用品相關的 ID 集合
        Set<Integer> merchandiseRelatedIds = orderPriceInfoList.stream()
            .filter(opi -> opi.getCategory() == Merchandise)
            .map(OrderPriceInfo::getId)
            .collect(Collectors.toSet());

        // 根據是否有過濾條件來處理不同的邏輯
        Stream<OrderPriceInfo> targetStream = orderPriceInfoList.stream();
        if (filterIds != null && !filterIds.isEmpty()) {
            targetStream = targetStream.filter(opi -> filterIds.contains(opi.getId()));
        }

        // 如果同時存在汽車用品和非汽車用品類別，拋出異常，例外：關聯費用編號為汽車用品費用 id
        Set<Boolean> isMerchandiseRelated = targetStream
            .map(opi -> isMerchandiseRelatedItem(opi, merchandiseRelatedIds))
            .collect(Collectors.toSet());

        if (isMerchandiseRelated.size() > 1) {
            throw new SubscribeException(ORDER_PRICE_INFO_CATEGORY_MIXED);
        }

        return isMerchandiseRelated.iterator().next();
    }

    private static boolean isMerchandiseRelatedItem(OrderPriceInfo opi, Set<Integer> merchandiseRelatedIds) {
        // 直接是汽車用品類別
        if (Merchandise == opi.getCategory()) {
            return true;
        }

        // 關聯費用編號為汽車用品費用 id
        if (opi.getRefPriceInfoNo() != null && merchandiseRelatedIds.contains(opi.getRefPriceInfoNo())) {
            return true;
        }

        // 如果有 RefPriceInfo 物件，也檢查其類別
        return opi.getRefPriceInfo() != null && opi.getRefPriceInfo().getCategory() == Merchandise;
    }
}
