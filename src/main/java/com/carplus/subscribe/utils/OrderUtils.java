package com.carplus.subscribe.utils;

import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.region.City;

import java.util.*;
import java.util.stream.Collectors;

public class OrderUtils {

    private OrderUtils() {}

    public static String addChars(String text, String c, int length) {
        if (StringUtils.isNotBlank(text) && text.length() < length) {
            StringBuilder textBuilder = new StringBuilder(text);
            while (textBuilder.length() < length) {
                textBuilder.insert(0, c);
            }
            text = textBuilder.toString();
        }
        return text;
    }


    public static String getFullHHAddress(AuthUser authUser, List<City> cities) {
        StringBuilder address = new StringBuilder();
        if (authUser.getHhrCityId() != null && authUser.getHhrAreaId() != null) {
            City city = cities.stream().filter(c -> c.getCityId() == authUser.getHhrCityId()).findAny().orElse(null);
            if (city != null) {
                if (city.getArea() != null) {
                    Optional.ofNullable(city.getArea()).orElse(Collections.emptyList()).stream()
                        .filter(a -> a.getAreaId() == authUser.getHhrAreaId())
                        .findAny().ifPresent(a -> {
                            address.append(city.getCityName()).append(a.getAreaName()).append(authUser.getHhrAddr());
                        });
                }
            }
        }
        return address.toString();
    }

    /**
     * 判斷訂單是否有車損
     */
    public static boolean hasAccident(Orders order) {
        return order.getAccidentInfo() != null && order.getAccidentInfo().isCarDamaged();
    }

    public static void sortRemarks(Orders order) {
        if (order == null || order.getRemarks() == null) {
            return;
        }

        List<Remark> sortedRemarks = order.getRemarks().stream()
            .sorted(Comparator.comparing(
                    Remark::getCreateTime, Comparator.nullsLast(Comparator.reverseOrder()) // null 在最後，其他按 createTime 降序
                )
            ).collect(Collectors.toList());

        order.setRemarks(sortedRemarks);
    }
}
