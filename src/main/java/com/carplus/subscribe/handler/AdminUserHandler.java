package com.carplus.subscribe.handler;

import carplus.common.response.exception.AuthException;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.authority.AdminUser;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.server.AuthorityServer;
import org.springframework.context.ApplicationContext;
import org.springframework.core.MethodParameter;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.List;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_CODE;

public class AdminUserHand<PERSON> implements HandlerMethodArgumentResolver {

    private static final Class<AdminUser> HANDLED = AdminUser.class;
    private static final Class<Nullable> NULLABLE = Nullable.class;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return HANDLED.equals(parameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        AdminUser admin = null;
        String memberId = webRequest.getHeader(CarPlusConstant.AUTH_HEADER_MEMBER);
        String companyCode = webRequest.getHeader(CarPlusConstant.HEADER_COMPANY_CODE);
        companyCode = StringUtils.isNotBlank(companyCode) ? companyCode : CARPLUS_COMPANY_CODE;
        if (StringUtils.isNotBlank(memberId) && StringUtils.isNotBlank(companyCode)) {
            ApplicationContext ctx = App.getContext();
            AuthorityServer authorityServer = ctx.getBean(AuthorityServer.class);
            List<MemberInfo> memberInfos = authorityServer.getMemberInfosNotRetired(memberId);
            if (!memberInfos.isEmpty()) {
                admin = new AdminUser();
                admin.setMemberId(memberId);
                admin.setCompanyCode(companyCode);
                admin.setMemberInfos(memberInfos);
                admin.setRoles(authorityServer.getAdminRoles(companyCode, memberId));
            }
        }

        if (admin == null && parameter.getParameterAnnotation(NULLABLE) == null) {
            throw new AuthException();
        }

        return admin;
    }
}
