package com.carplus.subscribe.constant;

import lombok.Getter;

@Getter
public enum MaacConstant {

    SECURITY_DEPOSIT_PAID(null, 97354),
    OPEN_FOR_PAY_STAGE_FEE(1866, 97356),
    CANCEL_ORDER(1867, 97358),
    RENEW_CALL_ORDER(1868, 97359),
    MODIFY_ORDER(1869, 97362),
    TAPPAY_REFUND_SUCCESS(1870, 97365)
    ;

    private final Integer pnpSettingId;
    private final Integer linePushTemplateId;

    MaacConstant(Integer pnpSettingId, Integer linePushTemplateId) {
        this.pnpSettingId = pnpSettingId;
        this.linePushTemplateId = linePushTemplateId;
    }
}
