package com.carplus.subscribe.constant;

import lombok.Getter;

@Getter
public class SMSConstant {

    /**
     * 繳完保證金，訂單成立
     */
    public static final String SECURITY_PAID = "訂單(%s)保證金繳納成功，詳見 %s";

    /**
     * 授信通過，通知繳費
     */
    public static final String CREDIT_SUCCESS = "您的訂閱車訂單已審核通過！請盡速點擊下方連結並完成保證金支付，訂單才會生效，避免被他人訂走哦！逾時24小時後將自動取消訂單。%s";

    /**
     * 授信未通過
     */
    public static final String CREDIT_FAIL = "格上租車關心您！%s君您好，您的預約：#%s 未通過審核！系統已自動取消訂單。如有未繳交通罰單，可能影響審核結果，建議可提前確認！其他疑問請透過格上訂閱車官方Line詢問，將由專人為您服務。https://s.car-plus.com.tw/s/ZU2KN";

    /**
     * 訂單取消
     */
    public static final String CANCEL_ORDER = "%s%s 您好，訂單 %s 已取消";

    /**
     * 續約提醒
     */
    public static final String RENEW_ORDER = "%s%s 您好，您的訂閱合約將於 %s 到期，請於到期前致電取車門市進行續約，未完成續約就必須準時還車囉，避免產生逾期租金。請點擊連結前往續約：%s";

    /**
     * 續約成立
     */
    public static final String RENEW_CONFIRM = "格上租車關心您！[訂單編號] #%s 您已完成續約，可點擊下方連結掌握訂單資訊。%s";

    /**
     * 還車提醒
     */
    public static final String RETURN_ORDER = "格上租車關心您！[訂單編號] #%s 您的訂閱合約將於 %s 到期，記得準時還車，避免產生逾期租金喔。";

    /**
     * 變更訂單提醒
     */
    public static final String MODIFY_ORDER = "%s%s 您好，訂單(%s)內容變更成功，詳見信箱或格上租車官網：%s";

    /**
     * 退款成功
     */
    public static final String REFUND_SUCCESS = "%s訂單(%s)已申請退款$%s元，實際入帳需待發卡行完成作業。如有疑問請洽發卡行詢問";

    /**
     * 繳費提醒
     */
    public static final String OPEN_FOR_PAY_STAGE_FEE = "%s%s 您好，本期訂單 (%s) 租賃費用已開放繳款。請於 %s 前點擊連結確認行駛里程數並繳納費用，否則將產生逾期付款罰金，感謝您！%s";

}
