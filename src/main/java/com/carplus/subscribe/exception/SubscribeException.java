package com.carplus.subscribe.exception;

import carplus.common.response.exception.CarPlusException;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

public class SubscribeException extends CarPlusException {

    public SubscribeException(@NonNull SubscribeHttpExceptionCode code) {
        super(code.getHttpStatus(), code, code.getMsg());
    }

    public SubscribeException(HttpStatus httpStatus, SubscribeHttpExceptionCode code, String reason) {
        super(httpStatus, code, reason);
    }

    public SubscribeException(SubscribeHttpExceptionCode code, String messageArg) {
        super(code.getHttpStatus(), code, String.format(code.getMsg(), messageArg));
    }

    public SubscribeException(@NonNull SubscribeHttpExceptionCode code, Object data) {
        super(code.getHttpStatus(), code, code.getMsg(), data);
    }
}
