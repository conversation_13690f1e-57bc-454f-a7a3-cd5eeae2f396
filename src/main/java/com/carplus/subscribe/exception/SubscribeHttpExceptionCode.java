package com.carplus.subscribe.exception;

import carplus.common.response.CarPlusCode;
import lombok.Getter;
import org.springframework.http.HttpStatus;

public class SubscribeHttpExceptionCode extends CarPlusCode {

    public static final SubscribeHttpExceptionCode MAIN_CONTRACT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10001, "查無主約編號，請洽資訊人員");

    public static final SubscribeHttpExceptionCode MAIN_CONTRACT_IS_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 10002, "主約編號找不到");
    public static final SubscribeHttpExceptionCode MAIN_CONTRACT_CAN_NOT_RENEW = new SubscribeHttpExceptionCode(HttpStatus.OK, 10003, "該主約不可續約，請檢查訂單為出車中，進行中訂單租期不可小於3個月，訂單到期日前30天，且沒有續約訂單方可續約");

    public static final SubscribeHttpExceptionCode CONTRACT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10501, "查無合約 %s");
    public static final SubscribeHttpExceptionCode CONTRACT_IS_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 10502, "合約為空");
    public static final SubscribeHttpExceptionCode GOING_CONTRACT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10503, "找不到進行中的合約");
    public static final SubscribeHttpExceptionCode CONTRACT_COMPLETE_CAN_NOT_RENEW = new SubscribeHttpExceptionCode(HttpStatus.OK, 10504, "合約已結束不可續約");
    public static final SubscribeHttpExceptionCode SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10901, "保證金付款資訊找不到");
    public static final SubscribeHttpExceptionCode SECURITY_DEPOSIT_PAY_TIMEOUT = new SubscribeHttpExceptionCode(HttpStatus.OK, 10902, "保證金付款已過期");
    public static final SubscribeHttpExceptionCode MANUAL_REFUND_SECURITY_DEPOSIT_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10903, "找不到保證金需人工退款資訊");
    public static final SubscribeHttpExceptionCode MANUAL_REFUND_SECURITY_DEPOSIT_TIME_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10904, "找不到保證金人工退日期款資訊");
    public static final SubscribeHttpExceptionCode NO_HOLIDAY_LIST_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 10905, "找不到假日列表資訊");
    public static final SubscribeHttpExceptionCode MANUAL_REFUND_SECURITY_DEPOSIT_FOR_CHANGE_CAR = new SubscribeHttpExceptionCode(HttpStatus.OK, 10906, "因有換車，保證金付款資訊不只一筆，需通知IT人員手動處理");

    public static final SubscribeHttpExceptionCode ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 13001, "找不到該訂單");
    public static final SubscribeHttpExceptionCode ORDER_CANCEL_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 13002, "訂單不可取消，請檢查訂當是否為已出車前狀態，或尚未登打帳務與開立發票");
    public static final SubscribeHttpExceptionCode DEPART_ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 13003, "合約 %s 查無出車中訂單");
    public static final SubscribeHttpExceptionCode RENEW_DEPART_ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 13003, "不可續約：訂單狀態應為出車中\n(訂單出車中，且尚未續約、到期30天內、非短續單、車輛上架中)");
    public static final SubscribeHttpExceptionCode ORDER_BOOKING_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 13004, "訂單下訂失敗");
    public static final SubscribeHttpExceptionCode ORDER_RENEW_START_DATE_BEFORE_MAIN_CONTRACT_EXPECT_END_DAY = new SubscribeHttpExceptionCode(HttpStatus.OK, 13005, "續約開始時間早於目前預計還車時間");
    public static final SubscribeHttpExceptionCode ORDER_RENEW_START_DATE_NOT_THE_TIME_YET = new SubscribeHttpExceptionCode(HttpStatus.OK, 13006, "不可續約：訂單到期日30天內方開放續約\n(訂單出車中，且尚未續約、到期30天內、非短續單、車輛上架中)");
    public static final SubscribeHttpExceptionCode INVOICE_ALREADY_CREATED_CANCEL_ORDER_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 13007, "已有收支紀錄，不可取消，請完成出、還車作業!");
    public static final SubscribeHttpExceptionCode ORDERING_ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 13008, "合約 %s 查無合約中訂單");
    public static final SubscribeHttpExceptionCode MODIFY_ORDER_DEPART_DATE_INVALIDATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 13009, "不為新訂單不可異動出車時間");
    public static final SubscribeHttpExceptionCode RENEW_ORDER_ALREADY_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 13010, "不可續約：已經產生其他續約單\n(訂單出車中，且尚未續約、到期30天內、非短續單、車輛上架中)");
    public static final SubscribeHttpExceptionCode RENEW_INVALIDATE_SHORT_MONTH = new SubscribeHttpExceptionCode(HttpStatus.OK, 13011, "不可續約：短續單不可再續約\n(訂單出車中，且尚未續約、到期30天內、非短續單、車輛上架中)");
    public static final SubscribeHttpExceptionCode LEGAL_OPERATION_ORDER_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 13012, "訂單狀態非出車中或已產生續約訂單，不可執行法務作業");
    public static final SubscribeHttpExceptionCode INVALID_LEGAL_OPERATION_REASON = new SubscribeHttpExceptionCode(HttpStatus.OK, 13013, "無效的法務事由");
    public static final SubscribeHttpExceptionCode RETURNED_ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 13014, "找不到相同主約下第一筆已還車訂單");
    public static final SubscribeHttpExceptionCode NOT_NEW_ORDER_CANNOT_UPDATE_DEPART_MILEAGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 13015, "不可更改出車里程 - 續約單應接續前單迄租里程");
    public static final SubscribeHttpExceptionCode LEGAL_OPERATION_SECURITY_DEPOSIT_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 13016, "保證金應收金額等於 0 或已經請求保證金退款，不可執行法務作業");
    public static final SubscribeHttpExceptionCode INVALID_LEGAL_OPERATION_REASON_FOR_ARRIVE_NO_CLOSE = new SubscribeHttpExceptionCode(HttpStatus.OK, 13017, "已還車未結案不可用逾期不還車作為法務事由");
    public static final SubscribeHttpExceptionCode ORDER_CAN_NOT_CANCEL_CAUSE_REMIT = new SubscribeHttpExceptionCode(HttpStatus.OK, 13018, "訂單不可取消因有匯款立帳，欲取消請洽資訊相關人員");
    public static final SubscribeHttpExceptionCode ORDER_CAN_NOT_DEPART_FOR_TASK = new SubscribeHttpExceptionCode(HttpStatus.OK, 13019, "訂單透過任務出車失敗");
    public static final SubscribeHttpExceptionCode MODIFY_RENEWAL_ORDER_RENTAL_PERIOD_BEFORE_DEPART_NOT_APPLY_TO_NEW_ORDER = new SubscribeHttpExceptionCode(HttpStatus.OK, 13020, "續約訂單出車前異動租期不適用於新訂單");
    public static final SubscribeHttpExceptionCode MODIFY_RENEWAL_ORDER_RENTAL_PERIOD_BEFORE_DEPART_ORDER_STATUS_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 13021, "續約訂單出車前異動租期，訂單狀態必須為審核中或已訂車");
    public static final SubscribeHttpExceptionCode MODIFY_RENEWAL_ORDER_RENTAL_PERIOD_BEFORE_DEPART_ORDER_ALREADY_PAID = new SubscribeHttpExceptionCode(HttpStatus.OK, 13022, "續約訂單出車前異動租期，訂單已有付款記錄，無法異動租期");
    public static final SubscribeHttpExceptionCode MODIFY_RENEWAL_ORDER_RENTAL_PERIOD_BEFORE_DEPART_TASK_ALREADY_DONE = new SubscribeHttpExceptionCode(HttpStatus.OK, 13023, "續約訂單出車前異動租期，出車任務已完成，無法異動租期");
    public static final SubscribeHttpExceptionCode MODIFY_RENEWAL_ORDER_RENTAL_PERIOD_BEFORE_DEPART_TASK_STATUS_UNKNOWN = new SubscribeHttpExceptionCode(HttpStatus.OK, 13024, "續約訂單出車前異動租期，無法確認出車任務狀態，無法異動租期");
    public static final SubscribeHttpExceptionCode ORDER_NOT_FOUND_BY_CONTRACT = new SubscribeHttpExceptionCode(HttpStatus.OK, 13025, "合約 %s 查無訂單");
    public static final SubscribeHttpExceptionCode MULTIPLE_ERRORMESSAGE_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 14001, "存在多筆錯誤訊息");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 19001, "經銷商訂單已存在");

    /**********************************
     ******** Request Validate ********
     **********************************/
    public static final SubscribeHttpExceptionCode EXPECT_DEPART_DATE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21001, "出車時間不可為空");
    public static final SubscribeHttpExceptionCode NEW_CAR_DEPART_TIME_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21002, "新車交車日必須為訂車日後8個工作天");
    public static final SubscribeHttpExceptionCode STATION_DEPART_TIME_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21003, "交車時間必須為該站點營業時間");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_LEVEL_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21004, "訂閱方案找不到");
    public static final SubscribeHttpExceptionCode MEMBER_INFO_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21005, "查詢不到員工編號");
    public static final SubscribeHttpExceptionCode STATION_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21006, "找不到該站點 %s");
    public static final SubscribeHttpExceptionCode COMPANY_DRIVER_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 21007, "請確定法人資訊是否正確，且'統一編號'、'公司名稱'、'公司地址'皆有提供");
    public static final SubscribeHttpExceptionCode LESS_THAN_MUST_RENT_DAYS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21008, "低於最低可租天數");
    public static final SubscribeHttpExceptionCode MUST_GIVE_ORDER_PRICE_INFO_IDS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21009, "必須提供款項明細編號");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_LEVEL_CHANGED = new SubscribeHttpExceptionCode(HttpStatus.OK, 21010, "方案已異動，請重新計價後再建立訂單");
    public static final SubscribeHttpExceptionCode ORDER_REMARK_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21011, "訂單備註找不到");
    public static final SubscribeHttpExceptionCode DEPART_TIME_CONSTRAINT_INTERNAL = new SubscribeHttpExceptionCode(HttpStatus.OK, 21012, "交車日最早為訂車日隔天");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_MONTH_NOT_VALID = new SubscribeHttpExceptionCode(HttpStatus.OK, 21013, "訂閱租期無效");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_RENEW_MONTH_NOT_VALID = new SubscribeHttpExceptionCode(HttpStatus.OK, 21014, "訂閱續約租期無效");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_LEVEL_CAN_NOT_CHANGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21015, "有進行中訂單不可異動訂閱方案");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_QUERY_DATE_NOT_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 21016, "查詢時間區間不可為空");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_QUERY_DATE_TOO_LONG = new SubscribeHttpExceptionCode(HttpStatus.OK, 21017, "查詢時間區間不可為超過三個月");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_UPDATE_DATE_NOT_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 21018, "異動時間區間不可為空");
    public static final SubscribeHttpExceptionCode SUBSCRIBE_RECEIVE_DATE_INVALIDATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21019, "異動應收時間不可晚於最後付款時間");
    public static final SubscribeHttpExceptionCode OLD_CAR_DEPART_TIME_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21020, "中古車交車日必須為訂車日後5個工作天");
    public static final SubscribeHttpExceptionCode TAG_IDS_CONTAIN_MONTHLY_DISCOUNTED_AND_LEVEL_DISCOUNTED = new SubscribeHttpExceptionCode(HttpStatus.OK, 21021, "車籍標籤不可同時有優惠月費和超激優惠");
    public static final SubscribeHttpExceptionCode MISSING_CORRESPONDING_DISCOUNT_LEVEL = new SubscribeHttpExceptionCode(HttpStatus.OK, 21022, "此訂閱方案沒有設置對應的超激優惠訂閱方案");
    public static final SubscribeHttpExceptionCode DISCOUNT_LEVEL_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21023, "超激優惠訂閱方案找不到");
    public static final SubscribeHttpExceptionCode DISCOUNT_LEVEL_MONTHLY_FEE_INVALID = new SubscribeHttpExceptionCode(HttpStatus.OK, 21024, "超激優惠方案月費不可大於基本訂閱方案月費");
    public static final SubscribeHttpExceptionCode CANNOT_UPDATE_SUBSCRIBED_OR_BIZOUT_CAR_TAGS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21025, "已訂閱或已出車車籍不可修改標籤");
    public static final SubscribeHttpExceptionCode SPECIFIED_PREPARE_DAYS_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21026, "此車輛已指定備車工作天數%d個工作天");

    /****************************
     ******** 出/還車相關 *********
     ****************************/
    public static final SubscribeHttpExceptionCode DEPART_TIME_SHOULD_LESS_THAN_NOW = new SubscribeHttpExceptionCode(HttpStatus.OK, 21101, "實際出車時間不可在未來");
    public static final SubscribeHttpExceptionCode DEPART_ORDER_STATUS_CONSTRAINT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21102, "狀態非 出車前/還車前 不可編輯出車資訊");
    public static final SubscribeHttpExceptionCode DEPART_MILEAGE_SHOULD_MORE_THAN_CURRENT_MILEAGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21103, "實際出車里程不可小於車輛目前里程");
    public static final SubscribeHttpExceptionCode CAR_LAUNCHED_DEPRECATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21104, "請檢查車號是否正確 - 該車被設定不可使用");
    public static final SubscribeHttpExceptionCode CAR_LAUNCHED_ACCIDENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21105, "請檢查車號是否正確 - 該車有車損註記不可出車");
    public static final SubscribeHttpExceptionCode ORDER_STAGE_NOT_INITIAL_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21106, "訂單期數非首期訂單");
    public static final SubscribeHttpExceptionCode DEPART_ORDER_STATUS_NOT_BOOKING = new SubscribeHttpExceptionCode(HttpStatus.OK, 21107, "請檢查訂單狀態 - 訂單已出車無法重複出車");
    public static final SubscribeHttpExceptionCode DEPART_ORDER_DEPARTMILEAGE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 21108, "取得車輛目前里程失敗");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 21109, "訂單狀態非已出車");
    public static final SubscribeHttpExceptionCode RETURN_TIME_SHOULD_MORE_THAN_START_TIME = new SubscribeHttpExceptionCode(HttpStatus.OK, 21110, "實際還車時間應晚於實際出車時間");
    public static final SubscribeHttpExceptionCode DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_RETURN_MILEAGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21111, "實際還車里程應大於實際出車里程");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_ARRIVE_NO_CLOSE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21112, "訂單狀態非已還車未結案");
    public static final SubscribeHttpExceptionCode ACCIDENT_CANNOT_CLOSE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21113, "車損議價中不能結案");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_BOOKING_NOT_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 21114, "訂單狀態非已訂車或已出車未還車");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_CLOSE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21115, "訂單狀態非已還車");
    public static final SubscribeHttpExceptionCode ORDER_ALREADY_PAID_OR_REFUND_RETURN_CAR_FEE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21116, "已完成提前/延後還車費用收退款，不可異動還車時間至其他日期");
    public static final SubscribeHttpExceptionCode CAR_LAUNCHED_NOT_OPEN = new SubscribeHttpExceptionCode(HttpStatus.OK, 21116, "車籍狀態不為上架");
    public static final SubscribeHttpExceptionCode PRE_ORDER_NOT_RETURN = new SubscribeHttpExceptionCode(HttpStatus.OK, 21117, "前一筆訂單尚未迄租，無法編輯當前訂單");
    public static final SubscribeHttpExceptionCode ETAG_PAID_CAN_NOT_MODIFY_RETURN_TIME = new SubscribeHttpExceptionCode(HttpStatus.OK, 21118, "etag已收款，不可異動實際還車時間");
    public static final SubscribeHttpExceptionCode CAR_NOT_FREE_OR_HAS_PROCESSING_ORDERS_FROM_OTHER_USER = new SubscribeHttpExceptionCode(HttpStatus.OK, 21119, "車輛有其他訂單使用中，請洽訂閱營業人員");
    public static final SubscribeHttpExceptionCode MULTIPLE_BOOKING_SAME_PLATE_NO_ORDERS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21120, "同車牌有多筆下定訂單");
    public static final SubscribeHttpExceptionCode DEPART_TASK_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21121, "出車任務已存在");
    public static final SubscribeHttpExceptionCode RETURN_TASK_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 21122, "還車任務已存在");
    public static final SubscribeHttpExceptionCode UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21123, "起租里程不可大於迄租里程");
    public static final SubscribeHttpExceptionCode NOT_YET_RETURN_EARLY = new SubscribeHttpExceptionCode(HttpStatus.OK, 21124, "尚未執行提前還車");
    public static final SubscribeHttpExceptionCode CAR_NOT_FREE = new SubscribeHttpExceptionCode(HttpStatus.OK, 21125, "車籍非空車");
    public static final SubscribeHttpExceptionCode LATEST_ETAG_PAID_RETURN_TIME_CAN_NOT_EARLIER_THAN_ETAG_END_TIME = new SubscribeHttpExceptionCode(HttpStatus.OK, 21126, "最後一期etag已收款，實際還車時間不可早於etag迄租時間");
    public static final SubscribeHttpExceptionCode CAR_IS_MONTHLY_DISCOUNTED_NOT_MATCH_WITH_MAIN_CONTRACT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21127, "主約與車籍的優惠月費設定不一致，請檢查車籍標籤");
    public static final SubscribeHttpExceptionCode CAR_DISCOUNT_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21128, "主約與車籍的超激優惠設定不一致，請檢查車籍標籤");
    public static final SubscribeHttpExceptionCode CAR_SUBSCRIBE_LEVEL_NOT_MATCH_WITH_MAIN_CONTRACT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21129, "方案等級不同不可替換");
    public static final SubscribeHttpExceptionCode RETURN_TIME_CAN_NOT_BE_NULL = new SubscribeHttpExceptionCode(HttpStatus.OK, 21130, "實際還車時間不可為空");
    public static final SubscribeHttpExceptionCode RETURN_MILEAGE_CAN_NOT_BE_NULL = new SubscribeHttpExceptionCode(HttpStatus.OK, 21131, "實際還車里程不可為空");
    public static final SubscribeHttpExceptionCode CAR_LAUNCHED_TBC = new SubscribeHttpExceptionCode(HttpStatus.OK, 21132, "車輛資料尚未設定完成，請洽訂閱營業人員");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_REJECT = new SubscribeHttpExceptionCode(HttpStatus.OK, 21133, "訂單狀態非審核失敗");
    public static final SubscribeHttpExceptionCode ALREADY_CREDIT_APPROVE_ORDER = new SubscribeHttpExceptionCode(HttpStatus.OK, 21134, "已有審核通過的訂單");
    public static final SubscribeHttpExceptionCode ADD_TASK_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 21135, "產生神馬維運任務失敗，請洽訂閱資訊人員");
    /*************************
     ******** 計價相關 ********
     *************************/
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30001, "更新失敗：費用不存在");
    public static final SubscribeHttpExceptionCode REFUND_AMOUNT_OVER_PAY_AMOUNT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30002, "退款金額超過付費金額");
    public static final SubscribeHttpExceptionCode DISCOUNT_REF_ORDER_PRICE_INFO_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30003, "找不到訂單款項明細");
    public static final SubscribeHttpExceptionCode ACTUAL_PAYMENT_COUNT_NOT_MATCH_ORDER_PRICE_INFO = new SubscribeHttpExceptionCode(HttpStatus.OK, 30004, "Payment Queue對應的Order Price Info數量不對");
    public static final SubscribeHttpExceptionCode ALREADY_REFUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30005, "已退款成功，不可重複退款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_DISPATCH_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30006, "找不到調度費");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_DISPATCH_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30007, "調度費未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_SECURITY_DEPOSIT_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30008, "保證金未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MONTHLY_FEE_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30009, "月租費未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_INSURANCE_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30010, "保險費未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_FEE_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30011, "里程費未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ETAG_NON_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30012, "通行費ETAG未付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_MORE_THAN_ONE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30013, "該訂單當期里程費資訊超過一筆");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_FEE_HAS_PAYED = new SubscribeHttpExceptionCode(HttpStatus.OK, 30014, "該訂單當期里程費已付款不可異動");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_LAST_END_MILEAGE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30015, "無法取得該訂單上期結束里程或出車里程");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_LAST_END_MILEAGE_OVER_CURRENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30016, "目前里程小於起起始里程");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30017, "收支不平衡");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_DISCOUNT_PAID_CANNOT_EDIT_DELETE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30018, "折扣已付費無法編輯和刪除,請使用退款");
    public static final SubscribeHttpExceptionCode INVOICE_TOTAL_AMOUNT_GREATER_THAN_PAID_AMOUNT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30019, "發票開立總金額大於實際已收金額");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_NOT_MILEAGE_CATEGORY = new SubscribeHttpExceptionCode(HttpStatus.OK, 30020, "款項明細不是里程費");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_FEE_HAVE_BEEN_PAYMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30021, "里程費已付款，不可設定里程優惠");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_HAVE_PAID = new SubscribeHttpExceptionCode(HttpStatus.OK, 30022, "更新失敗：費用已付款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_UPDATE_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 30023, "款項明細更新失敗");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_RETURN_LATE_HAVE_PAID = new SubscribeHttpExceptionCode(HttpStatus.OK, 30024, "已付過延後還車款項，訂單時間不可異動");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_UNPAID_NOT_EQUALS_PAY = new SubscribeHttpExceptionCode(HttpStatus.OK, 30025, "請求付款金額與應繳金額不同");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_UNPAID_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30026, "找不到可付款里程費");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30027, "找不到第一期里程費款項");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_NOT_REPLY_YET = new SubscribeHttpExceptionCode(HttpStatus.OK, 30028, "尚有款項結算中，若有問題請洽門市服務人員或訂閱車LINE客服小編");
    public static final SubscribeHttpExceptionCode RETURN_MILEAGE_HAS_SETTING = new SubscribeHttpExceptionCode(HttpStatus.OK, 30029, "已設定還車里程，無法再設定里程數");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_CAN_NOT_SETTING = new SubscribeHttpExceptionCode(HttpStatus.OK, 30030, "不能設定最後期里程費，只可由還車操作設定");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ALREADY_AGREE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30031, "裁決請求已[同意]");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ALREADY_DISAGREE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30032, "裁決請求已[取消]");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_IS_CHANGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30033, "已重新請求送審或取消請求");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ALREADY_DISCOUNTED_OR_REFUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30034, "已退款或以折扣，不可修改/刪除折扣請求");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_MILEAGE_FEE_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 30035, "找不到每公里里程費");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_EXTRA_FEE_NOT_BEFORE_THAN_CURRENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30036, "額外費用期數不可早於當前期數");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_AMOUNT_NOT_MATCH_INVOICE_AMOUNT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30037, "發票開立金額與對應費用總額不平");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_REF_NEED_COMBINED_TOGETHER = new SubscribeHttpExceptionCode(HttpStatus.OK, 30038, "請確認尚有 \"待作廢/待折讓\" 發票需重新開立");
    public static final SubscribeHttpExceptionCode SECURITY_DEPOSIT_SEIZURE_AMOUNT_NOT_MATCH = new SubscribeHttpExceptionCode(HttpStatus.OK, 30039, "保證金沒收比例與系統計算值不一致");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_ALREADY_PAID = new SubscribeHttpExceptionCode(HttpStatus.OK, 30040, "不可更改出車里程 - 第一筆里程已完成收款");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_CATEGORY_MIXED = new SubscribeHttpExceptionCode(HttpStatus.OK, 30041, "發票款項明細類別不可同時包含汽車用品與其他類別");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_UNRECEIVABLE_NOT_ALLOW_RETURN = new SubscribeHttpExceptionCode(HttpStatus.OK, 30042, "尚有費用未開放繳款導致無法還車，請通知客戶立即繳款。");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ALREADY_PAID_BY_REMIT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30043, "匯款款項不可退款");
    public static final SubscribeHttpExceptionCode CAN_NOT_FIND_BY_REMIT_NO = new SubscribeHttpExceptionCode(HttpStatus.OK, 30044, "查詢匯款資訊失敗");
    public static final SubscribeHttpExceptionCode ORDER_AMOUNT_NOT_EQUAL_INVOICE_AMOUNT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30046, "登打金額與發票金額不同");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_REFERENCE_NEED_SELECT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30047, "付費款項預付款時，需與該款項的折扣一同選擇，不可分開處理，請重新選擇");
    public static final SubscribeHttpExceptionCode PREVIOUS_MILEAGE_INVALID = new SubscribeHttpExceptionCode(HttpStatus.OK, 30048, "前次里程必須為正整數");
    public static final SubscribeHttpExceptionCode PREVIOUS_MILEAGE_SMALLER_THAN_PREVIOUS_STAGE_END_MILEAGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 30049, "前次里程不可小於前一期結束里程");
    public static final SubscribeHttpExceptionCode STAGE_ONE_PREVIOUS_MILEAGE_CONFLICT = new SubscribeHttpExceptionCode(HttpStatus.OK, 30050, "第一期前次里程必須與出車里程一致");
    public static final SubscribeHttpExceptionCode COUPON_EXCLUDED_CATEGORIES = new SubscribeHttpExceptionCode(HttpStatus.OK, 30051, "優惠券不適用於費用類別：ETag、汽車用品、保證金");
    public static final SubscribeHttpExceptionCode ORDER_PRICE_INFO_ALREADY_PAID_CANNOT_USE_COUPON = new SubscribeHttpExceptionCode(HttpStatus.OK, 30052, "指定的費用項目中有已付款的項目，不可使用優惠券");

    /************************
     ******** 授信相關 ********
     ************************/
    public static final SubscribeHttpExceptionCode CREDIT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 40001, "授信失敗");
    public static final SubscribeHttpExceptionCode BACK_LIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 40002, "很抱歉，您的會員資料異常，請撥打24小時客服專線，將由專員竭誠為您服務。");
    public static final SubscribeHttpExceptionCode NOT_CREDIT_ORDER = new SubscribeHttpExceptionCode(HttpStatus.OK, 40003, "訂單不為待授信狀態");
    public static final SubscribeHttpExceptionCode NO_CREDIT_EVER = new SubscribeHttpExceptionCode(HttpStatus.OK, 40004, "尚未進行任何自動授信驗證，請先執行任一自動授信");
    public static final SubscribeHttpExceptionCode MANUAL_CREDIT_EVER = new SubscribeHttpExceptionCode(HttpStatus.OK, 40005, "已進行過人工授信");

    /****************************
     ******** 車籍車型相關 ********
     ***************************/
    public static final SubscribeHttpExceptionCode CAR_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 50001, "請檢查車號是否正確 - 該車尚未匯入訂閱車籍系統內");
    public static final SubscribeHttpExceptionCode NOT_EMPTY_CAR = new SubscribeHttpExceptionCode(HttpStatus.OK, 50002, "車輛非空車");
    public static final SubscribeHttpExceptionCode CAR_LOCK_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 50003, "鎖車失敗");
    public static final SubscribeHttpExceptionCode CAR_INFO_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 50004, "車輛資訊異常");
    public static final SubscribeHttpExceptionCode NOT_BUSINESS_CAR = new SubscribeHttpExceptionCode(HttpStatus.OK, 50005, "車輛已退出車隊");
    public static final SubscribeHttpExceptionCode VIRTUAL_CAR_NEED_CAR_NUMBER = new SubscribeHttpExceptionCode(HttpStatus.OK, 50006, "虛擬車請提供車籍編號");

    public static final SubscribeHttpExceptionCode CAR_REGISTRATION_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 52001, "車輛所有權公司找不到");
    public static final SubscribeHttpExceptionCode CAR_REGISTRATION_ALREADY_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 52002, "車輛所有權公司已存在");

    public static final SubscribeHttpExceptionCode LONG_RENTAL_CAR_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 53001, "不屬於長租購入車輛");
    public static final SubscribeHttpExceptionCode SRENTAL_CAR_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 53002, "該車號不在短租車輛中");
    public static final SubscribeHttpExceptionCode NOT_FOR_SUBSCRIBE = new SubscribeHttpExceptionCode(HttpStatus.OK, 53003, "請檢查車號是否正確 - 該車不屬於訂閱單位");
    public static final SubscribeHttpExceptionCode LICENSE_PLATE_NOT_PURCHASED = new SubscribeHttpExceptionCode(HttpStatus.OK, 53004, "請檢查車號是否正確 - 車牌狀態異常(不為購入)");
    public static final SubscribeHttpExceptionCode CAR_NOT_IN_SUBSCRIBE_SYSTEM = new SubscribeHttpExceptionCode(HttpStatus.OK, 53005, "該車號尚未匯入訂閱車籍系統");
    public static final SubscribeHttpExceptionCode CAR_ALREADY_IN_SUBSCRIBE_SYSTEM = new SubscribeHttpExceptionCode(HttpStatus.OK, 53006, "該車號已匯入訂閱車籍系統");
    public static final SubscribeHttpExceptionCode CAR_ALREADY_IN_SUBSCRIBE_SYSTEM_AND_IS_DELETED = new SubscribeHttpExceptionCode(HttpStatus.OK, 53007, "該車輛曾被刪除，若要復原請通知資訊人員");

    public static final SubscribeHttpExceptionCode CAR_MODEL_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 55001, "找不到該車型");
    public static final SubscribeHttpExceptionCode CAR_MODEL_NAME_DUPLICATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 55002, "車型名稱重複");
    public static final SubscribeHttpExceptionCode CAR_MODEL_ORIGINAL_CODE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 55003, "找不到車型對應的舊短租車型");
    public static final SubscribeHttpExceptionCode CAR_MODEL_CAR_KIND_IS_NOT_FIND = new SubscribeHttpExceptionCode(HttpStatus.OK, 55004, "車型的種類找不到");
    public static final SubscribeHttpExceptionCode CAR_TAG_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 56001, "找不到該車標籤");
    public static final SubscribeHttpExceptionCode CAR_TAG_NAME_DUPLICATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 56002, "車標籤名稱已重複");

    public static final SubscribeHttpExceptionCode CAR_BRAND_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 57001, "找不到該車品牌");
    public static final SubscribeHttpExceptionCode CAR_BRAND_CODES_DUPLICATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 57002, "請求中包含重複的廠牌代碼");
    public static final SubscribeHttpExceptionCode CAR_BRAND_ALREADY_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 57003, "該車品牌已存在");

    public static final SubscribeHttpExceptionCode CRS_CAR_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 59001, "CRS查無此車，請確認車牌是否錯誤，或已經繳銷重領產生新車牌");
    public static final SubscribeHttpExceptionCode CRS_CAR_NOT_BELONG_PREOWNED = new SubscribeHttpExceptionCode(HttpStatus.OK, 59002, "CRS庫位不屬於中古車");
    public static final SubscribeHttpExceptionCode CRS_CAR_NOT_BELONG_SUBSCRIBE = new SubscribeHttpExceptionCode(HttpStatus.OK, 59003, "CRS庫位不屬於訂閱車");
    public static final SubscribeHttpExceptionCode CRS_CAR_CAN_NOT_CHANGE_BECAUSE_GOING_ORDER = new SubscribeHttpExceptionCode(HttpStatus.OK, 59004, "不可發動CRS撥車服務，尚有進行中的訂單");
    public static final SubscribeHttpExceptionCode CAR_NOT_HAVE_ASSIGN_NO = new SubscribeHttpExceptionCode(HttpStatus.OK, 59005, "車籍沒有撥車單號，不可自動撥還車");
    public static final SubscribeHttpExceptionCode CRS_CHANGE_CAR_CHANGE_ID_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 59006, "CRS找不到該撥車申請單編號");
    public static final SubscribeHttpExceptionCode CRS_PURCHASE_CAR_INVALIDATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 59007, "為專案車，但契約迄日或殘值無資料，無法建立契約，請洽採購人員");
    public static final SubscribeHttpExceptionCode CRS_PURCHASE_CAR_CONTRACT_END_DATE_INVALIDATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 59007, "此為專案車，「訂單預計還車日」晚於「專案車契約迄日」，不可建立訂單，請洽訂閱車業務部人員");
    public static final SubscribeHttpExceptionCode CAR_NOT_HAVE_STATION_CAN_NOT_RETURN_BU = new SubscribeHttpExceptionCode(HttpStatus.OK, 59008, "CRS撥車單號，沒有中古/共享/短租收車站點，不可撥還中古/共享/短租");
    public static final SubscribeHttpExceptionCode CRS_CHANGE_CAR_CHANGE_DETAIL_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 59009, "CRS找不到該撥車申請單明細");
    public static final SubscribeHttpExceptionCode CRS_CAR_LEVE_CANCEL_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 59010, "CRS取消出車失敗");
    public static final SubscribeHttpExceptionCode CRS_CAR_CHANGE_CANCEL_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 59011, "CRS取消撥車失敗");
    public static final SubscribeHttpExceptionCode CRS_CAR_BELONG_LRENTAL = new SubscribeHttpExceptionCode(HttpStatus.OK, 59012, "CRS庫位屬於長租車，不可撥車");
    public static final SubscribeHttpExceptionCode CAR_NOT_HAVE_STATION_CAN_NOT_CHANGE_BU = new SubscribeHttpExceptionCode(HttpStatus.OK, 59013, "車輛沒有中古/共享/短租出車站點，不可撥入訂閱");
    public static final SubscribeHttpExceptionCode CRS_CAR_NOT_BELONG_LRENTAL_NOR_SUBSCRIBE = new SubscribeHttpExceptionCode(HttpStatus.OK, 59014, "請檢查車號是否正確 - 該車不屬於訂閱或長租單位");
    public static final SubscribeHttpExceptionCode CAR_NOT_ALLOW_CHANGE_BU = new SubscribeHttpExceptionCode(HttpStatus.OK, 59015, "不可發動CRS撥車服務，不屬於格上車");


    /****************************
     ******* ETAG通行費相關 *******
     ***************************/

    public static final SubscribeHttpExceptionCode ETAG_ALREADY_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 60001, "ETAG已還車");
    public static final SubscribeHttpExceptionCode ETAG_AUTO_SETTLEMENT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60002, "ETAG自動結算失敗");
    public static final SubscribeHttpExceptionCode ETAG_INFO_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 60003, "etag明細資訊找不到");
    public static final SubscribeHttpExceptionCode ETAG_CAN_NOT_CHANGE_TO_UNPAYABLE = new SubscribeHttpExceptionCode(HttpStatus.OK, 60004, "etag已付款，不可改為不收款");
    public static final SubscribeHttpExceptionCode ETAG_RENT_CAR_NEXT_ORDER_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60005, "eTag 出車失敗，上一筆Etag尚未結案");
    public static final SubscribeHttpExceptionCode ETAG_RENT_CAR_FAIL_WITHOUT_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 60006, "找不到eTag出車資料，不可還車");
    public static final SubscribeHttpExceptionCode ETAG_RENT_TIME_NOT_GIVEN = new SubscribeHttpExceptionCode(HttpStatus.OK, 60007, "請輸入遠通Etag出還車時間");
    public static final SubscribeHttpExceptionCode ETAG_RENT_AFTER_RETURN = new SubscribeHttpExceptionCode(HttpStatus.OK, 60008, "遠通還車時間不可早於出車時間");

    public static final SubscribeHttpExceptionCode ETAG_RENT_CAR_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 20506, "eTag 出車失敗，請用格上帳密登入遠通官網出車");
    public static final SubscribeHttpExceptionCode ETAG_RENT_CAR_IGNORE = new SubscribeHttpExceptionCode(HttpStatus.OK, 20507, "此車輛未加入格上 etag 車隊");
    public static final SubscribeHttpExceptionCode ETAG_RETURN_CAR_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 20508, "etag 還車失敗");
    public static final SubscribeHttpExceptionCode ETAG_RETURN_CAR_FAIL_NOT_EXPECTED = new SubscribeHttpExceptionCode(HttpStatus.OK, 20509, "etag 還車未知錯誤");
    public static final SubscribeHttpExceptionCode ETAG_RETURN_DEALER_NOT_VALID_CAR = new SubscribeHttpExceptionCode(HttpStatus.OK, 205010, "etag金額無須登打");
    public static final SubscribeHttpExceptionCode ETAG_RETURN_CAR_CUSTOM = new SubscribeHttpExceptionCode(HttpStatus.OK, 20511, "請依車籍登入遠通官網還車，並將金額填入");
    public static final SubscribeHttpExceptionCode ETAG_RETURN_CAR_HAS_NOT_EXECUTED = new SubscribeHttpExceptionCode(HttpStatus.OK, 20512, "尚未完成etag通行費結算，請確認etag費用");
    public static final SubscribeHttpExceptionCode ETAG_INFO_WITHOUT_PLATE_NO = new SubscribeHttpExceptionCode(HttpStatus.OK, 20513, "etag 明細無車牌資料，無法匯出，請至遠通後台查詢下載");

    /****************************
     ********** 立賬相關 **********
     ***************************/
    public static final SubscribeHttpExceptionCode CHECK_OUT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60001, "立賬失敗，請檢查帳務與發票");
    public static final SubscribeHttpExceptionCode CHECK_OUT_SECURITY_DEPOSIT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60002, "保證金立賬失敗，請檢查帳務");
    public static final SubscribeHttpExceptionCode CHECK_OUT_CANCEL_ORDER_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60003, "取消訂單立賬失敗，請檢查帳務");
    public static final SubscribeHttpExceptionCode CHECK_OUT_ETAG_MULTIPLE_REMIT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60003, "一筆Etag不可有兩筆收支");
    public static final SubscribeHttpExceptionCode PAYMENT_INFO_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 60004, "找不到帳務資訊");
    public static final SubscribeHttpExceptionCode ADVANCE_CHECK_OUT_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60005, "應收帳款立賬失敗，請檢查帳務與發票");
    public static final SubscribeHttpExceptionCode PRICE_AND_INVOICE_AMOUNT_NOT_EQUAL = new SubscribeHttpExceptionCode(HttpStatus.OK, 60006, "發票總額與帳務付退款總金額不同");


    /****************************
     ********** 長租相關 **********
     ***************************/
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_ALREADY_CREATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 70001, "已建立長租契約");
    public static final SubscribeHttpExceptionCode LRENTAL_RELATIVE_COMPANY_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 70002, "找不到相關的公司");
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_NOT_FOUND_CAN_NOT_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 70003, "請洽訂閱營業人員 - 尚未建立長租車籍契約，無法出車");
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_CAN_NOT_CREATE_WITH_VIRTUAL_CAR = new SubscribeHttpExceptionCode(HttpStatus.OK, 70004, "不能使用虛擬車建立長租契約");
    public static final SubscribeHttpExceptionCode CAR_INSURANCE_PLAN_ID_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 70005, "車籍找不到匹配保險方案");
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 70006, "找不到指定長租契約編號");
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_NOT_CREATED_YET = new SubscribeHttpExceptionCode(HttpStatus.OK, 70007, "請洽訂閱營業人員 - 尚未建立長租車籍契約");
    public static final SubscribeHttpExceptionCode LRENTAL_CONTRACT_NEED_NOT_CREATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 70008, "非格上車不需建立契約");

    /****************************
     ******** 電子合約相關 ********
     ***************************/
    public static final SubscribeHttpExceptionCode ECONTRACT_TEMPLATE_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80001, "合約範本不可重複");
    public static final SubscribeHttpExceptionCode ECONTRACT_TEMPLATE_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80002, "不存在的合約範本");
    public static final SubscribeHttpExceptionCode ECONTRACT_ACCOUNT_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80003, "不存在的客戶");
    public static final SubscribeHttpExceptionCode ECONTRACT_SIGN_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80004, "簽名檔不存在");
    public static final SubscribeHttpExceptionCode GENERATE_UPLOAD_PRESIGNED_FAILED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80005, "無法取得上傳憑証");
    public static final SubscribeHttpExceptionCode ECONTRACT_SIGN_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 80006, "電子合約簽署失敗");
    public static final SubscribeHttpExceptionCode ECONTRACT_TEMPLATE_FILE_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80007, "產生合約檔案失敗 - 合約範本不存在");
    public static final SubscribeHttpExceptionCode SIGN_FILE_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80008, "簽名檔不存");
    public static final SubscribeHttpExceptionCode GENERATE_DOWNLOAD_PRESIGNED_FAILED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80009, "產生合約檔案失敗 - 查無下載憑證");
    public static final SubscribeHttpExceptionCode PROFILE_INCOMPLETE = new SubscribeHttpExceptionCode(HttpStatus.OK, 80010, "產生合約檔案失敗 - 客戶資料缺乏身分證字號");
    public static final SubscribeHttpExceptionCode FILE_NOT_EXISTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 80011, "指定檔案不存在");
    public static final SubscribeHttpExceptionCode ECONTRACT_UNAUTHORIZED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80012, "合約資料不符，存取被拒");
    public static final SubscribeHttpExceptionCode ACCTID_MISSING = new SubscribeHttpExceptionCode(HttpStatus.OK, 80012, "需提供會員編號");
    public static final SubscribeHttpExceptionCode ECONTRACT_ALREADY_SIGNED_CAN_NOT_CHANGE_TEMPLATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 80013, "已簽署的合約不可變更範本");
    public static final SubscribeHttpExceptionCode CANNOT_MODIFY_ORDER_MONTH_AFTER_ECONTRACT_SIGNED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80014, "電子合約已簽署，不可異動訂閱租期");
    public static final SubscribeHttpExceptionCode CANNOT_CHANGE_PLATE_NO_AFTER_ECONTRACT_SIGNED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80015, "電子合約已簽署，不可換車");
    public static final SubscribeHttpExceptionCode CANNOT_ADD_ECONTRACT_FILE = new SubscribeHttpExceptionCode(HttpStatus.OK, 80016, "電子合約相關檔案新增失敗");
    public static final SubscribeHttpExceptionCode ECONTRACT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 80017, "找不到該電子合約");
    public static final SubscribeHttpExceptionCode REJECT_UPDATE_CONTRACT_TEMPLATE_ID = new SubscribeHttpExceptionCode(HttpStatus.OK, 80018, "電子合約已有簽名，不可更新關聯合約範本");
    public static final SubscribeHttpExceptionCode CANNOT_MODIFY_ORDER_MONTH_AFTER_ECONTRACT_FILE_UPLOADED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80019, "電子合約已有上傳檔案，不可異動訂閱租期");

    /****************************
     ********* 電子出租單 *********
     ***************************/
    public static final SubscribeHttpExceptionCode TASK_ID_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 85001, "沒有電子出租單任務ID");
    public static final SubscribeHttpExceptionCode GET_TASK_DETAIL_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 85002, "取得任務詳細資訊失敗");
    public static final SubscribeHttpExceptionCode ECONTRACT_NOT_SIGNED_TASK_BLOCKED = new SubscribeHttpExceptionCode(HttpStatus.OK, 85003, "尚未簽訂「電子合約」，請簽訂後再完成任務");
    public static final SubscribeHttpExceptionCode UPDATE_TASK_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 85004, "異動出還車任務失敗");
    public static final SubscribeHttpExceptionCode TASK_IS_NOT_DONE = new SubscribeHttpExceptionCode(HttpStatus.OK, 85005, "電子出租單不為完成狀態，不產生出租任務");
    public static final SubscribeHttpExceptionCode TASK_IS_NOT_SIGN = new SubscribeHttpExceptionCode(HttpStatus.OK, 85006, "電子出租單用戶沒簽名，不產生電子出租單");
    public static final SubscribeHttpExceptionCode DELETE_TASK_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 85007, "電子出租單為完成狀態，刪除出還車任務失敗");
    public static final SubscribeHttpExceptionCode TASK_IS_NOT_DONE_CANNOT_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 85008, "電子出租單不為完成狀態，不能出車");

    /****************************
     ******* 經銷商訂單相關 *******
     ***************************/
    public static final SubscribeHttpExceptionCode DEALER_ORDER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 90001, "找不到經銷商訂單");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_NO_REQUIRED = new SubscribeHttpExceptionCode(HttpStatus.OK, 90002, "訂單編號為必填");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_DUPLICATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 90003, "訂單編號重複匯入");
    public static final SubscribeHttpExceptionCode DEALER_USER_QUERY_PARAMETER_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 90004, "經銷商客戶查詢參數錯誤");
    public static final SubscribeHttpExceptionCode DEALER_USER_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 90005, "找不到經銷商客戶");
    public static final SubscribeHttpExceptionCode DEALER_USER_ID_NO_CAN_NOT_CHANGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 90006, "經銷商客戶身分證字號不可異動");
    public static final SubscribeHttpExceptionCode DEALER_USER_ID_NO_LENGTH_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 90007, "身分證號長度應為 10 碼");
    public static final SubscribeHttpExceptionCode DEALER_USER_POSTAL_CODE_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 90008, "找不到對應的縣市區域資訊，請確認戶籍區號是否正確");
    public static final SubscribeHttpExceptionCode DEALER_EXPECT_DEPART_STATION_NOT_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 90009, "預定出車站點不存在");
    public static final SubscribeHttpExceptionCode DEALER_EXPECT_RETURN_STATION_NOT_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 90010, "預定還車站點不存在");
    public static final SubscribeHttpExceptionCode DEALER_DEPART_STATION_NOT_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 90011, "實際出車站點不存在");
    public static final SubscribeHttpExceptionCode DEALER_RETURN_STATION_NOT_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 90012, "實際還車站點不存在");
    public static final SubscribeHttpExceptionCode DEALER_USER_CREATE_FAIL = new SubscribeHttpExceptionCode(HttpStatus.OK, 90013, "經銷商客戶資料建立失敗");
    public static final SubscribeHttpExceptionCode DEALER_USER_CREATE_REQUIRED = new SubscribeHttpExceptionCode(HttpStatus.OK, 90014, "經銷商客戶資料建立必填欄位(姓名、手機國碼、手機號碼、生日)不可為空");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_STATUS_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 90015, "經銷商訂單狀態錯誤，須為「已訂車」或「出車中」");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_CLOSE_AMT_SHOULD_GREATER_THAN_ETAG_AMT = new SubscribeHttpExceptionCode(HttpStatus.OK, 90016, "迄租金額應大於等於etag應收金額");
    public static final SubscribeHttpExceptionCode DEALER_VIRTUAL_PLATE_NO_NOT_ALLOW_DEPART = new SubscribeHttpExceptionCode(HttpStatus.OK, 90017, "SEALAND 虛擬車號不可出車");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_SHEET_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 90018, "匯入經銷商訂單 Excel 工作表不可為空");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_SHEET_FIRST_ROW_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 90019, "匯入經銷商訂單 Excel 工作表第一列不可為空");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_SHEET_FIRST_CELL_EMPTY = new SubscribeHttpExceptionCode(HttpStatus.OK, 90020, "匯入經銷商訂單 Excel 工作表第一列的第一個儲存格不可為空");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_SHEET_DATE_FORMAT_ERROR = new SubscribeHttpExceptionCode(HttpStatus.OK, 90021, "欄位時間格式應符合 yyyy-MM-dd HH:mm:ss");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_STATUS_NOT_ORDERING = new SubscribeHttpExceptionCode(HttpStatus.OK, 90022, "請檢查訂單狀態 - 經銷商訂單已出車無法重複出車");
    public static final SubscribeHttpExceptionCode DEALER_ORDER_STATUS_NOT_GOING = new SubscribeHttpExceptionCode(HttpStatus.OK, 90023, "經銷商訂單狀態非出車中");

    /****************************
     ********* 電訪相關 *********
     ***************************/
    public static final SubscribeHttpExceptionCode CSAT_TASK_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 92001, "找不到電訪任務");
    public static final SubscribeHttpExceptionCode CSAT_STATUS_NOT_COMPLETED = new SubscribeHttpExceptionCode(HttpStatus.OK, 92101, "只能更新狀態為'已電訪'的電訪單");
    public static final SubscribeHttpExceptionCode CSAT_QUEST_NEED_IMPLEMENTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 92102, "需實做toCsatQuest");
    public static final SubscribeHttpExceptionCode VERSION_CSAT_QUEST_NEED_IMPLEMENTS = new SubscribeHttpExceptionCode(HttpStatus.OK, 92103, "需實做toVersionCsatQuest");
    public static final SubscribeHttpExceptionCode USER_NOT_FOUND_BY_LOGIN_ID = new SubscribeHttpExceptionCode(HttpStatus.OK, 92104, "無法透過身分證找到使用者資訊");

    /****************************
     ********* 活動相關 *********
     ***************************/
    public static final SubscribeHttpExceptionCode CAMPAIGN_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 93001, "找不到活動");
    public static final SubscribeHttpExceptionCode INVALID_GEO_REGION = new SubscribeHttpExceptionCode(HttpStatus.OK, 93002, "無效的車輛所在區域");
    public static final SubscribeHttpExceptionCode BANNER_CATEGORY_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 93003, "找不到橫幅類別");

    /****************************
     ********* 收藏相關 *********
     ***************************/
    public static final SubscribeHttpExceptionCode CAR_ALREADY_IN_WISHLIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 94001, "車輛已在收藏清單中");
    public static final SubscribeHttpExceptionCode CAR_WISHLIST_LIMIT_EXCEEDED = new SubscribeHttpExceptionCode(HttpStatus.OK, 94002, "收藏清單已達上限");
    public static final SubscribeHttpExceptionCode CAR_NOT_IN_WISHLIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 94003, "車輛不在收藏清單中");
    public static final SubscribeHttpExceptionCode WISHLIST_RATE_LIMIT_EXCEEDED = new SubscribeHttpExceptionCode(HttpStatus.OK, 94004, "收藏清單操作過於頻繁，請稍後再試");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_INVALID_PHONE_FORMAT = new SubscribeHttpExceptionCode(HttpStatus.OK, 94010, "手機號碼格式錯誤");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_INVALID_DATE_RANGE = new SubscribeHttpExceptionCode(HttpStatus.OK, 94011, "日期範圍錯誤");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_INVALID_CAR_MODEL_CODES = new SubscribeHttpExceptionCode(HttpStatus.OK, 94012, "車型代碼錯誤");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_NO_DATA_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 94013, "查無符合條件的收藏清單資料");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_GENERATION_FAILED = new SubscribeHttpExceptionCode(HttpStatus.OK, 94014, "收藏清單報表產生失敗");
    public static final SubscribeHttpExceptionCode WISHLIST_REPORT_CSV_GENERATION_FAILED = new SubscribeHttpExceptionCode(HttpStatus.OK, 94015, "CSV檔案產生失敗");

    /****************************
     ******** 行事曆相關 ********
     ***************************/
    public static final SubscribeHttpExceptionCode CALENDAR_UPDATABLE_DATE_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 95001, "找不到行事曆可更新日期");
    public static final SubscribeHttpExceptionCode CALENDAR_DATE_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 95002, "行事曆日期已存在");

    /****************************
     ******* 汽車用品相關 *******
     ***************************/
    public static final SubscribeHttpExceptionCode SKU_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 96001, "找不到該汽車用品");
    public static final SubscribeHttpExceptionCode SKU_NAME_DUPLICATE = new SubscribeHttpExceptionCode(HttpStatus.OK, 96002, "汽車用品名稱重複");
    public static final SubscribeHttpExceptionCode SHIPMENT_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 96003, "找不到出貨資料");
    public static final SubscribeHttpExceptionCode ORDER_STATUS_NOT_APPLICABLE_FOR_CREATING_SHIPMENT = new SubscribeHttpExceptionCode(HttpStatus.OK, 96004, "訂單狀態不適用於建立出貨資料");

    /****************************
     ******* CONFIG設定相關 *******
     ***************************/

    public static final SubscribeHttpExceptionCode CONFIG_EXIST = new SubscribeHttpExceptionCode(HttpStatus.OK, 99001, "設定已存在");
    public static final SubscribeHttpExceptionCode CONFIG_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 99002, "設定不存在");
    public static final SubscribeHttpExceptionCode PREOWNED_INV_CONFIG_NOT_FUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 99003, "中古車庫存設定不存在");

    /***************************
     優惠券相關錯誤代碼
     ***************************/

    public static final SubscribeHttpExceptionCode COUPON_NOT_FOUND = new SubscribeHttpExceptionCode(HttpStatus.OK, 80001, "優惠券不存在或已失效");
    public static final SubscribeHttpExceptionCode COUPON_EXPIRED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80002, "優惠券已過期");
    public static final SubscribeHttpExceptionCode COUPON_ALREADY_APPLIED = new SubscribeHttpExceptionCode(HttpStatus.OK, 80003, "訂單已有尚未折抵完成的優惠券");
    public static final SubscribeHttpExceptionCode COUPON_MIN_AMOUNT_NOT_MET = new SubscribeHttpExceptionCode(HttpStatus.OK, 80004, "訂單金額未達優惠券使用門檻");
    public static final SubscribeHttpExceptionCode COUPON_DISCOUNT_EXCEEDS_TOTAL = new SubscribeHttpExceptionCode(HttpStatus.OK, 80005, "優惠券折抵金額不可大於待折扣費用總額");
    public static final SubscribeHttpExceptionCode COUPON_CANNOT_DELETE_PAID_FEES = new SubscribeHttpExceptionCode(HttpStatus.OK, 80006, "無法刪除優惠券，關聯的費用已付款");

    @Getter
    private final String msg;

    @Getter
    private final HttpStatus httpStatus;

    protected SubscribeHttpExceptionCode(HttpStatus status, int code, String msg) {
        super(code);
        this.msg = msg;
        this.httpStatus = status;
    }


}
