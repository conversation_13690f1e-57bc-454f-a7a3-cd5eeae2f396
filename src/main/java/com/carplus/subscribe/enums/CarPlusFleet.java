package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum CarPlusFleet {
    Y("格上車牌"),
    N("非格上車牌"),
    E("不確定"),
    ;

    private static final Map<String, CarPlusFleet> map = Arrays.stream(values()).collect(Collectors.toMap(CarPlusFleet::name, Function.identity()));
    private String desc;

    public static CarPlusFleet of(String carplusFleet) {
        return map.getOrDefault(carplusFleet, E);
    }
}
