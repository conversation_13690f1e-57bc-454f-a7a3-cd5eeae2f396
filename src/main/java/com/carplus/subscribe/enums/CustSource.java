package com.carplus.subscribe.enums;

import carplus.common.enums.HeaderDefine;
import carplus.common.utils.StringUtils;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum CustSource {
    /**
     * 預設值
     */
    DEFAULT(0),
    /**
     * 臨櫃取車
     */
    ONSITE(1),
    /**
     * 預約-官網
     */
    WEB(2, HeaderDefine.SystemKind.OFFICAL),
    /**
     * 預約-App (GOSMART)
     */
    GOSMART(3, HeaderDefine.SystemKind.GOSMART),
    /**
     * 預約-高鐵訂單
     */
    THSR(4),
    /**
     * 預約-電話
     */
    PHONE(5),
    /**
     * 預約-線下
     */
    STORE(6, HeaderDefine.SystemKind.CASHIER),
    /**
     * 預約-外國人
     */
    FOREIGNER(7),
    /**
     * 預約-異業結盟
     */
    ALLIANCE(8),
    /**
     * 代步車
     */
    TEMP_CAR(9),
    /**
     * 線下訂閱(短租建單)
     */
    SUBSCRIBE(10),
    /**
     * LINE OA
     */
    LINE_OA(11),
    /**
     * SEALAND
     */
    SEALAND(12),
    ;

    private static final Map<Integer, CustSource> custSourceMap = Arrays.stream(values()).collect(Collectors.toMap(CustSource::getSource, Function.identity()));
    private static final Map<String, CustSource> custSourceMapBySystemKind = Arrays.stream(values()).filter(custSource -> custSource.getSystemKind() != null).collect(Collectors.toMap(CustSource::getSystemKind, Function.identity()));

    static {
        custSourceMapBySystemKind.put("OFFICIAL", WEB);
    }

    /**
     * Orders.source
     */
    private final int source;
    /**
     * Headers.[X-System-Kind]
     */
    private final String systemKind;

    CustSource(int source, @Nullable String systemKind) {
        this.source = source;
        this.systemKind = systemKind;
    }

    CustSource(int source) {
        this(source, null);
    }

    @NonNull
    public static CustSource systemKindOf(@Nullable String systemKind) {
        try {
            if (StringUtils.isNotBlank(systemKind)) {
                return custSourceMapBySystemKind.getOrDefault(systemKind.toUpperCase(), DEFAULT);
            }
        } catch (Exception ignore) {
            // DO NOTHING
        }

        return DEFAULT;
    }

    @NonNull
    public static CustSource sourceOf(int source) {
        return custSourceMap.getOrDefault(source, DEFAULT);
    }
}
