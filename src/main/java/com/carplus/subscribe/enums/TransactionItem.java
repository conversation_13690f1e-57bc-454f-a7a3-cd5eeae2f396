package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易項目
 */
@Getter
@AllArgsConstructor
public enum TransactionItem {

    PREPAID_BASIC_FEE(1, "預收基本月費"),
    PREPAID_MILEAGE_FEE(2, "預收里程費"),
    EXCESS_MILEAGE_FEE(3, "超額里程費"),
    OVERDUE_RENT_FEE(4, "逾期租金"),
    CURRENT_DELAY_INTEREST(5, "當期起租延遲利息"),
    DAILY_RENTAL_COST(6, "延長還車日租費用"),
    ADDITIONAL_AMT(7, "分攤加項金額"),
    SUBTRACTION_AMT(8, "分攤減項金額"),
    ADVANCE_PAYMENT(9, "車源商應收代墊款"),
    ACCIDENT_COST(10, "車損費"),
    FUEL_COST(11, "油費"),
    SERVICE_FEE(12, "服務費"),
    PREVIOUS_ETAG(13, "換車前Etag"),
    ETAG(14, "Etag"),
    OTHER_ITEM(15, "其他項目"),
    CANCELLATION_FEE(16, "退訂手續費"),
    ;

    private final int code;
    private final String desc;

    TransactionItem(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
