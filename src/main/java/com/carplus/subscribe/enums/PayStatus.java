package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PayStatus {

    PAID("已付款", "已折扣", "已退款"),
    UNPAID("待付款", "待折扣", "待退款"),
    PENDING("未開放", "未開放", "未開放"),
    CREDIT("待審核", "待審核", "待審核"),
    NONE_PASS("審核不通過", "審核不通過", "審核不通過"),
    LATE("已過期", "", "");
    private String name;
    private String discountName;
    private String refundName;

    public String getDisplayName(PriceInfoDefinition.PriceInfoType type) {
        switch (type) {
            case Pay:
                return name;
            case Discount:
                return discountName;
            case Refund:
                return refundName;
            default:
        }
        return null;
    }

    public String getDisplayName(Integer type) {
        if (type == null) {
            return "";
        }
        PriceInfoDefinition.PriceInfoType enmType = PriceInfoDefinition.PriceInfoType.of(type);
        if (enmType == null) {
            return "";
        }
        return getDisplayName(enmType);
    }
}
