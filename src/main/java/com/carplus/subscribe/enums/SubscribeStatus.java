package com.carplus.subscribe.enums;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.order.ContractInfo;
import com.carplus.subscribe.model.order.MainContractResponse;
import com.carplus.subscribe.utils.DateUtil;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;

public enum SubscribeStatus {

    UNDER_REVIEW(1, "審核中", "主約第一筆訂單狀態 = 待審核"),
    WAITING_FOR_DEPOSIT(2, "待付保證金", "主約第一筆訂單狀態 = 待付款"),
    WAITING_FOR_VEHICLE(10, "待取車", "主約第一筆訂單狀態 = 已訂車"),
    IN_CONTRACT(50, "合約中", "主約狀態 = 出車中 AND 當前日期 < 主約.expectEndDate - 10d"),
    APPROACHING_EXPIRY(51, "即將到期", "主約狀態 = 出車中 AND 當前日期 = 主約.expectEndDate - 10d ~ 主約.expectEndDate"),
    OVERDUE(52, "逾期未還", "主約狀態 = 出車中 AND 當前日期 > 主約.expectEndDate"),
    UNSETTLED(80, "未結案", "主約狀態 = 完成 AND 主約內有訂單狀態 in (已還車未結案, 失竊未結案)"),
    EXPIRED(90, "已到期", "主約狀態 = 完成 AND 主約內無訂單狀態 in (已還車未結案, 失竊未結案)"),
    REVIEW_FAILED(98, "審核失敗", "主約第一筆訂單狀態 = 審核失敗"),
    CANCELLED(99, "已取消", "主約狀態 = 已取消");

    private final int code;
    private final String name;
    private final String description;

    SubscribeStatus(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public static SubscribeStatus getByCode(int code) {
        for (SubscribeStatus state : SubscribeStatus.values()) {
            if (state.getCode() == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("No ContractState with code " + code);
    }

    public static SubscribeStatus getByMainContract(MainContractResponse mainContract) {
        int firstOrderStatus = mainContract.getContracts().stream().min(Comparator.comparing(ContractInfo::getContractNo))
            .map(ContractInfo::getOrderResponses)
            .flatMap(orders -> orders.stream().min(Comparator.comparing(Orders::getCreateDate)))
            .map(Orders::getStatus)
            .orElse(-1);
        if (firstOrderStatus == OrderStatus.CREDIT_REJECT.getStatus()) {
            return REVIEW_FAILED;
        } else if (firstOrderStatus == OrderStatus.CANCEL.getStatus()) {
            return CANCELLED;
        } else if (firstOrderStatus == OrderStatus.CREDIT_PENDING.getStatus()) {
            return UNDER_REVIEW;
        }
        if (mainContract.getStatus() == ContractStatus.CREATE.getCode()) {
            if (firstOrderStatus == OrderStatus.CREDITED.getStatus()) {
                return WAITING_FOR_DEPOSIT;
            } else if (firstOrderStatus == OrderStatus.BOOKING.getStatus()) {
                return WAITING_FOR_VEHICLE;
            }
        } else if (mainContract.getStatus() == ContractStatus.CANCEL.getCode()) {
            return CANCELLED;
        } else {
            // 判斷是否有審核通過的續約單
            Orders bookingOrder = mainContract.getContracts().stream()
                .flatMap(contract -> contract.getOrderResponses().stream())
                .filter(order -> order.getStatus() == OrderStatus.BOOKING.getStatus())
                .findFirst()
                .orElse(null);
            if (bookingOrder != null) {
                return IN_CONTRACT;
            }

            Orders departOrder = mainContract.getContracts().stream()
                .flatMap(contract -> contract.getOrderResponses().stream())
                .filter(order -> order.getStatus() == OrderStatus.DEPART.getStatus())
                .findFirst()
                .orElse(null);

            if (departOrder != null) {
                if (Instant.now().isAfter(departOrder.getExpectEndDate())) {
                    return OVERDUE;
                } else if (DateUtil.calculateDiffDate(Instant.now(), departOrder.getExpectEndDate(), ChronoUnit.DAYS) <= 10) {
                    return APPROACHING_EXPIRY;
                }
                return IN_CONTRACT;
            }
            Orders arriveNoClosedOrder = mainContract.getContracts().stream()
                .flatMap(contract -> contract.getOrderResponses().stream())
                .filter(order -> order.getStatus() == OrderStatus.ARRIVE_NO_CLOSE.getStatus() || order.getStatus() == OrderStatus.STOLEN.getStatus())
                .findFirst()
                .orElse(null);
            if (arriveNoClosedOrder != null) {
                return UNSETTLED;
            }
            return EXPIRED;
        }
        return IN_CONTRACT;
    }
}
