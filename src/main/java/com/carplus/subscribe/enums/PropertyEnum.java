package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface PropertyEnum {
    /**
     * 調度車態 Property.status
     * CarItem.type=1250
     */
    @Getter
    @AllArgsConstructor
    enum PropertyStatus {
        /**
         * 0 開放
         */
        open(0, "開放"),
        /**
         * 1 管制
         */
        limit(1, "管制"),
        /**
         * 2 成交
         */
        sold(2, "成交"),
        /**
         * 3 待售
         */
        forSale(3, "待售"),
        /**
         * 4 債管
         */
        claims(4, "債管"),
        /**
         * 5 行將
         */
        sinjang(5, "行將"),
        /**
         * 6 訂閱
         */
        subscribe(6, "訂閱"),
        /**
         * 7 待分配
         */
        noAssign(7, "待分配"),
        ;
        private final Integer code;
        private final String name;
    }

    /**
     * 用途分配 Property.PurposeCode
     * CarItem.type=415
     */
    @Getter
    @AllArgsConstructor
    enum PurposeCode {
        /**
         * 0 待分配
         */
        noAssign(0, "待分配"),
        /**
         * 1 替代車
         */
        loaner(1, "替代車"),
        /**
         * 2 中古車
         */
        secondHand(2, "中古車"),
        /**
         * 3 行將待拍
         */
        sinjang(3, "行將待拍"),
        /**
         * 4 長官公務車
         */
        manager(4, "長官公務車"),
        /**
         * 5 車技公務車
         */
        technicianCar(5, "車技公務車"),
        /**
         * 6 短租
         */
        sRental(6, "短租"),
        /**
         * 7 債管
         */
        claims(7, "債管"),
        /**
         * 8 電動車 (此分配類型未來將不會使用到)
         */
        @Deprecated
        electric(8, "電動車"),
        /**
         * 9 客戶轉租
         */
        carCenter4Change(9, "客戶轉租"),
        /**
         * 10 客戶購回
         */
        carCenter4Buy(10, "客戶購回"),
        /**
         * 11 訂閱式租賃
         */
        subscribeRental(11, "訂閱式租賃"),
        /**
         * 12 訂閱待訂
         */
        subscribe(12, "訂閱待訂"),
        /**
         * 13 長租
         */
        lRental(13, "長租"),
        /**
         * 14 專車
         */
        special(14, "專車"),
        /**
         * 15 調度課
         */
        carCenter(15, "調度課"),
        /**
         * 16 服務部
         */
        carService(16, "服務部"),
        /**
         * 17 共享
         */
        s2g(17, "共享");
        private final Integer purposeCode;
        private final String name;
    }

}
