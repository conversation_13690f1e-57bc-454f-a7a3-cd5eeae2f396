package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 訂閱車相關
 */
public interface SubscribeCarDefine {

    @Getter
    @AllArgsConstructor
    enum Sort {
        ASC("升序"),
        DESC("降序"),
        ;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    enum OrderBy {
        MFG_YEAR("年份"),
        MILEAGE("里程數"),
        MONTH_FEE("月租費"),
        DISCOUNT_MONTH_FEE("優惠月租費"),
        USE_MONTH_FEE("實際月租費"),
        ;
        private String name;
    }
}