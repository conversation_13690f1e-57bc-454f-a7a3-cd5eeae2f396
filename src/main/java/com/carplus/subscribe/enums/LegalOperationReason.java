package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 法務作業請求允許的事由
 */
@Getter
@AllArgsConstructor
public enum LegalOperationReason {
    OVERDUE_NO_RETURN(1, "逾期不還車", OrderStatus.STOLEN, CarDefine.CarStatus.Stolen, false),
    RETURNED_WITH_DAMAGE(2, "已還車全車損", OrderStatus.CLOSE, CarDefine.CarStatus.Scrapped, true),
    RETURNED_WITH_UNPAID(3, "已還車有欠款", OrderStatus.ARRIVE_NO_CLOSE, CarDefine.CarStatus.Free, true),
    RETURNED_WITH_UNPAID_AND_DAMAGE(4, "已還車有欠款且全車損", OrderStatus.ARRIVE_NO_CLOSE, CarDefine.CarStatus.Scrapped, true),
    RETURNED_WITH_PAID(5, "已還車但保證金轉收入", OrderStatus.CLOSE, CarDefine.CarStatus.Free, true);

    private final int code;
    private final String description;
    private final OrderStatus orderStatus;
    private final CarDefine.CarStatus carStatus;
    private final boolean centerContractRequired;

    public static boolean isLegalOperationExecuted(int code) {
        return code > 0;
    }

    public static LegalOperationReason of(int code) {
        return Arrays.stream(values()).filter(reason -> reason.code == code).findFirst().orElse(null);
    }
}
