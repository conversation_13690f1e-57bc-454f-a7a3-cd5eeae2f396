package com.carplus.subscribe.enums;

import lombok.Getter;

@Getter
public enum DealerStation {

    SHILIN_USED_CAR("35", "士林中古車營業所"),
    FENGSHAN_USED_CAR("41", "鳳山中古車營業所"),
    ZHONGLI_USED_CAR("56", "中壢中古車營業所"),
    PINGTUNG_USED_CAR("67", "屏東中古車營業所"),
    XITUN_USED_CAR("71", "西屯中古車營業所"),
    ZHONGHE_USED_CAR("88", "中和中古車營業所"),
    TAIPEI_ZHONGXIAO("202", "台北忠孝站"),
    TAIPEI_JINGMEI("203", "台北景美站"),
    TAIPEI_SHILIN("204", "台北士林站"),
    TAIPEI_NEIHU("205", "台北內湖站"),
    TAIPEI_XINDIAN_XINGBIAN("208-1", "台北新店站_行遍"),
    TAIPEI_TUCHENG("217", "台北土城站"),
    TAIPEI_ZHONGHE("218", "台北中和站"),
    YILAN_LUODONG("231", "宜蘭羅東站"),
    TAOYUAN_FUXING("301", "桃園復興站"),
    TAOYUAN_ZHONGLI("303", "桃園中壢站"),
    HSINCHU_ZHONGHUA("311", "新竹中華站"),
    TAICHUNG_ZHONGQING("405", "台中中清站"),
    CHIAYI("501", "嘉義站"),
    CHIAYI_HSR("503", "嘉義高鐵站"),
    TAINAN_YONGKANG("606", "台南永康站"),
    KAOHSIUNG_SANDUO("702", "高雄三多站"),
    KAOHSIUNG_FENGSHAN("706", "高雄鳳山站"),
    PINGTUNG("801", "屏東站"),
    HUALIEN("821", "花蓮站"),
    TAITUNG("831", "台東站");

    private final String code;
    private final String name;

    DealerStation(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DealerStation getByCode(String code) {
        for (DealerStation station : values()) {
            if (station.getCode().equals(code)) {
                return station;
            }
        }
        return null;
    }
}