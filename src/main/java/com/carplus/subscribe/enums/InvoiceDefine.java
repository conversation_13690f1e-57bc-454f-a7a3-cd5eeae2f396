package com.carplus.subscribe.enums;

import com.carplus.subscribe.enums.inv.InvoiceDataEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface InvoiceDefine {
    @Getter
    @AllArgsConstructor
    enum InvStatus {

        /**
         * 開立
         */
        CREATE("開立"),
        /**
         * 作廢
         */
        INVALIDATE("作廢"),
        /**
         * 折讓
         */
        ALLOWANCE("折讓");

        private final String name;

        public static String getName(String statusStr) {
            return InvStatus.valueOf(statusStr).getName();
        }
    }

    @Getter
    @AllArgsConstructor
    enum InvType {
        Person(2, "二聯式"),
        Biz(3, "三聯式");

        private static final Map<Integer, InvType> invMap = Arrays.stream(values()).collect(Collectors.toMap(InvType::getType, Function.identity()));
        /**
         * 發票種類
         */
        private int type;
        /**
         * 說明
         */
        private String name;

        @Nullable
        @JsonCreator
        public static InvType of(int type) {
            return invMap.get(type);
        }
    }

    @Getter
    @AllArgsConstructor
    enum InvCategory {
        /**
         * 紙本
         */
        PAPER(1, "Paper", "索取證明聯正本"),
        /**
         * 自然人憑證
         */
        CA(2, InvoiceDataEnum.CarrierType.CarrierType_CQ0001.getCode(), "自然人憑證"),
        /**
         * 手機載具
         */
        MOBILE(3, InvoiceDataEnum.CarrierType.CarrierType_3J0002.getCode(), "手機載具"),
        /**
         * 捐贈發票
         */
        DONATE(4, "Donate", "捐贈發票"),
        /**
         * 格上會員載具
         */
        MEMBER(5, InvoiceDataEnum.CarrierType.CarrierType_EG0142.getCode(), "會員載具");

        private static final Map<Integer, InvCategory> invMap = Arrays.stream(values()).collect(Collectors.toMap(InvCategory::getCategory, Function.identity()));
        /**
         * 發票類型
         */
        private int category;
        /**
         * 舊短租SP需要欄位
         */
        @JsonIgnore
        private String carrierType;
        /**
         * 發票類型--中文名稱
         */
        private String title;

        /**
         * InvCategory of by category
         */
        @Nullable
        @JsonCreator
        public static InvCategory of(@Nullable Integer category) {
            return invMap.get(category);
        }


        /**
         * 是否使用載具
         */
        public static boolean isCarrier(int category) {
            return category == InvCategory.CA.getCategory()
                || category == InvCategory.MOBILE.getCategory()
                || category == InvCategory.MEMBER.getCategory();
        }
    }

    @Getter
    enum InvCheckoutStatus {
        /**
         * 不用立帳
         */
        NONE_CHECKOUT(0),
        /**
         * 須立帳
         */
        CHECKOUT(1),
        /**
         * 開立後作廢立帳
         */
        MULTIPLE_CHECKOUT(2);

        private final int status;

        InvCheckoutStatus(int status) {
            this.status = status;
        }
    }
}
