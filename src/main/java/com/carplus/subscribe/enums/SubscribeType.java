package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 訂閱類型
 */
@Getter
@AllArgsConstructor
public enum SubscribeType {
    ONE_MONTH(1, 30),

    TWO_MONTH(2, 60),
    <PERSON><PERSON><PERSON>(3, 90),

    FOUR_MONTH(4, 120),

    FIVE_MONTH(5, 150),

    HALF_YEAR(6, 180),

    SEVEN_MONTH(7, 210),

    EIGHT_MONTH(8, 240),

    NINE_MONTH(9, 270),

    TEN_MONTH(10, 300),

    ELEVEN_MONTH(11, 330),

    YEAR(12, 365),
    ;

    private int months;
    private int days;
}
