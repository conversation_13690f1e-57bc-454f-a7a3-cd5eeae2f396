package com.carplus.subscribe.enums;

import com.carplus.subscribe.constant.EContractConstant;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.google.common.net.MediaType;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UploadFileKindEnum {
    /**
     * 簽名檔(PNG)
     */
    SIGN(MediaType.PNG, EContractConstant.GCS_SING_IMG_PATH, true),
    /**
     * 電子合約
     */
    CUSTOM_CONTRACT(MediaType.PDF, EContractConstant.GCS_CUSTOM_CONTRACT_PATH, true),
    /**
     * 電子合約範本
     */
    CONTRACT_TEMPLATE(MediaType.PDF, EContractConstant.GCS_CONTRACT_TEMPLATE_PATH, false),
    /**
     * 電子出租單
     */
    RENTAL_TASK(MediaType.PDF, EContractConstant.GCS_RENTAL_TASK_PATH, false),
    ;

    private final MediaType mediaType;
    private final String pathTemplate;
    private final boolean requiresAcctId;

    public String buildFilePath(Integer acctId) {
        if (requiresAcctId && acctId == null) {
            throw new SubscribeException(SubscribeHttpExceptionCode.ACCTID_MISSING);
        }
        return requiresAcctId ? String.format(pathTemplate, acctId) : pathTemplate;
    }
}
