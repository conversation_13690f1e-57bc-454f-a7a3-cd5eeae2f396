package com.carplus.subscribe.enums.csat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum CsatStatus {

    NOT_CALLED(0, "未電訪"),
    IN_PROGRESS(1, "電訪中"),
    COMPLETED(2, "已電訪");

    private final int code;
    private final String description;

    private static Map<Integer, CsatStatus> codeMap = null;

    static {
        codeMap = Arrays.stream(CsatStatus.values()).collect(Collectors.toMap(CsatStatus::getCode, Function.identity()));
    }

    public static CsatStatus of(Integer code) {

        return codeMap.getOrDefault(code, NOT_CALLED);
    }
}
