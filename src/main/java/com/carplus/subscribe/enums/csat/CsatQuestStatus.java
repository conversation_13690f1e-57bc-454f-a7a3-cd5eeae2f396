package com.carplus.subscribe.enums.csat;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum CsatQuestStatus {

    NOT_SURVEYED(0, "未調查"),
    SURVEYED(1, "已調查"),
    REFUSED_SURVEY(2, "拒調查");

    private final int code;
    private final String description;

    private static final Map<Integer, CsatQuestStatus> questStatusMap = Arrays.stream(CsatQuestStatus.values()).collect(Collectors.toMap(CsatQuestStatus::getCode, Function.identity()));

    public static CsatQuestStatus of(Integer code) {
        return questStatusMap.getOrDefault(code, NOT_SURVEYED);
    }
}
