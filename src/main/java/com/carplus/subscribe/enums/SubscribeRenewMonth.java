package com.carplus.subscribe.enums;

import com.carplus.subscribe.exception.SubscribeException;
import lombok.Getter;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SUBSCRIBE_RENEW_MONTH_NOT_VALID;

@Getter
public enum SubscribeRenewMonth {

    ONE(1),
    TWO(2),
    THRE<PERSON>(3),
    SIX(6),
    NINE(9),
    TWELVE(12);

    private final int value;

    SubscribeRenewMonth(int value) {
        this.value = value;
    }

    public static SubscribeRenewMonth fromValue(int value) {
        for (SubscribeRenewMonth month : SubscribeRenewMonth.values()) {
            if (month.getValue() == value) {
                return month;
            }
        }
        throw new SubscribeException(SUBSCRIBE_RENEW_MONTH_NOT_VALID);
    }
}
