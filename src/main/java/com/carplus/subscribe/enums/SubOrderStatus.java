package com.carplus.subscribe.enums;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.utils.DateUtil;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

public enum SubOrderStatus {
    UNDER_REVIEW(1, "審核中", "orders.status = 1 待審核"),
    WAITING_FOR_DEPOSIT(2, "待付保證金", "orders.status = 2 待付款"),
    WAITING_FOR_VEHICLE(10, "待取車", "orders.status = 10 已訂車 AND isNewOrder = true"),
    ABOUT_TO_START(11, "即將開始", "orders.status = 10 已訂車 AND isNewOrder = false"),
    IN_CONTRACT(50, "合約中", "orders.status = 50 已出車未還車"),
    APPROACHING_EXPIRY(51, "即將到期", "orders.status = 50 已出車未還車 AND 當前日期 = orders.expectEndDate - 10d ~ orders.expectEndDate"),
    OVERDUE(52, "逾期未結", "orders.status = 50 已出車未還車 AND 當前日期 > orders.expectEndDate"),
    UNSETTLED(80, "未結案", "orders.status in (80 已還車未結案, 81 失竊未結案)"),
    CONTRACT_ENDED(90, "合約到期", "orders.status = 90 已還車"),
    REVIEW_FAILED(98, "審核失敗", "orders.status = 98 審核失敗"),
    CANCELLED(99, "已取消", "orders.status = 99 已取消");

    private final int code;
    private final String name;
    private final String description;

    SubOrderStatus(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 通過狀態碼獲取枚舉值
     *
     * @param code 狀態碼
     * @return 對應的枚舉值
     * @throws IllegalArgumentException 如果沒有找到對應的狀態碼
     */
    public static SubOrderStatus getByCode(int code) {
        for (SubOrderStatus status : SubOrderStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("No SubOrderStatus with code " + code);
    }

    public static SubOrderStatus getByOrder(Orders orders) {
        int orderStatus = orders.getStatus();
        switch (orderStatus) {
            case 1:
                return UNDER_REVIEW;
            case 2:
                return WAITING_FOR_DEPOSIT;
            case 10:
                if (orders.getIsNewOrder()) {
                    return WAITING_FOR_VEHICLE;
                } else {
                    return ABOUT_TO_START;

                }
            case 50:
                Instant now = Instant.now();
                Instant expectEndDate = orders.getExpectEndDate();
                if (expectEndDate != null && expectEndDate.isAfter(now)) {
                    long daysBetween = ChronoUnit.DAYS.between(now, expectEndDate);
                    if (daysBetween <= 10) {
                        return APPROACHING_EXPIRY;
                    }
                } else if (DateUtil.convertToStartOfInstant(now).isAfter(DateUtil.convertToStartOfInstant(expectEndDate))) {
                    return OVERDUE;
                }
                return IN_CONTRACT;
            case 80:
            case 81:
                return UNSETTLED;
            case 90:
                return CONTRACT_ENDED;
            case 98:
                return REVIEW_FAILED;
            case 99:
                return CANCELLED;
            default:
                return null;
        }
    }
}
