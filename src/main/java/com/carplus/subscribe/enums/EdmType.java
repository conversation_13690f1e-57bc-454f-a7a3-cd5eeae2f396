package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EdmType {

    /**
     * 訂閱訂單完成保證金繳款 order.status = 10
     */
    SUB_CREATE_ORDER,
    /**
     * 訂閱訂單授信成功通知用戶繳款 order.status = 2
     */
    SUB_CREDIT_SUCCESS,
    /**
     * 訂閱訂單授信失敗 order.status = 98
     */
    SUB_CREDIT_FAIL,
    /**
     * 訂閱訂單授信需求 order.status = 1
     */
    SUB_CREDIT_DEMAND,
    /**
     * 訂閱訂單取消 order.status = 99
     */
    SUB_CANCEL_ORDER,
    /**
     * 訂閱訂單續約通知 order.status = 50 於合約倒數5/10/30天通知
     */
    SUB_RENEW_CALL,
    /**
     * 訂閱訂單續約成立通知 order.status = 10, order.stage > 1
     */
    SUB_RENEW_CONFIRM,
    /**
     * 短租不可再續約還車通知
     */
    SUB_RETURN,
    /**
     * 訂閱訂單異動提醒通知(出車時間、出車&還車站所)
     */
    SUB_UPDATE_ORDER,
    /**
     * 訂閱訂單已還車通知
     */
    SUB_RETURN_COMPLETE,
    /**
     * 每期繳費通知
     */
    SUB_STAGE_PAY
}
