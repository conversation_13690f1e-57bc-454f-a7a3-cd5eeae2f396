package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderPaymentStatus {

    TRANSACTION_ERROR(-1, "交易發生錯誤", "與Tappay交易發生錯誤"),
    AUTHORIZED(0, "已授權", "付款/退款之待處理交易紀錄"),
    BANK_CAPTURE(1, "已完成請款", "銀行請款結果"),
    PARTIAL_REFUND(2, "已完成部分退款", "退款結果"),
    REFUND(3, "已完成全額退款", "退款結果"),
    ACCOUNTING_OK(4, "已入帳", "會計結帳"),
    PENDING_PAYMENT(5, "等待3D驗證", "3D驗證未完成"),
    AUTHORIZED_UNDO_BANK_CAPTURE(7, "已授權(未請款)", "未請款前已退款完成"),
    PENDING_REFUND_FOR_BANK_CAPTURE(9, "待部份退款", "請款成功後才可部份退款"), // 部份退款需要等請款完成
    PENDING_CANCEL_REFUND(10, "待取消退款", "執行取消退款"),
    CANCEL_REFUND(11, "已取消退款", "取消退款完成"),
    REFUND_FAIL(12, "退款失敗", "排程同步交易紀錄時取得退款失敗"),
    CAPTURE_FAIL(13, "請款失敗", "排程同步交易紀錄時取得請款失敗"),
    CANCEL_REFUND_FAIL(14, "取消退款失敗", "取消退款失敗"),
    FAIL_3D(15, "3D驗證失敗", "3D驗證失敗"),
    CANCEL_3D(16, "取消3D驗證", "取消3D驗證"),
    IS_CAPTURED(17, "已執行過請款", "銀行請款結果"),
    EXPIRED_REFUND(18, "已超過銀行自動退款時間", "已超過銀行自動退款時間，須走紙本流程"),
    ;

    private static final Map<Integer, OrderPaymentStatus> statusMap = Arrays
        .stream(values())
        .collect(Collectors.toMap(OrderPaymentStatus::getCode, Function.identity()));
    private final int code;
    private final String name;
    private final String desc;

    public static OrderPaymentStatus of(@Nullable Integer status) {
        return Optional.ofNullable(statusMap.get(status)).orElse(TRANSACTION_ERROR);
    }

    /**
     * 已申請退款狀態碼
     */
    public static List<Integer> getPendingRefundCode() {
        return Arrays.asList(AUTHORIZED.getCode(), PENDING_REFUND_FOR_BANK_CAPTURE.getCode());
    }

    /**
     * 退款成功狀態碼
     */
    public static List<Integer> getActualRefundCode() {
        return Arrays.asList(PARTIAL_REFUND.getCode(), REFUND.getCode());
    }


    /**
     * 付退款成功狀態碼
     */
    public static List<Integer> getSuccessCode() {
        return Arrays.asList(AUTHORIZED.getCode(),
            BANK_CAPTURE.getCode(),
            PARTIAL_REFUND.getCode(),
            REFUND.getCode(),
            AUTHORIZED_UNDO_BANK_CAPTURE.getCode(),
            PENDING_REFUND_FOR_BANK_CAPTURE.getCode(),
            PENDING_CANCEL_REFUND.getCode());
    }
}
