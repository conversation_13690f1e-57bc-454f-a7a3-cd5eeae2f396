package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 職位
 */
@Getter
@AllArgsConstructor
public enum JobTitle {

    J1001("董事長"),
    J1004("董事"),
    J1005("監察人"),
    J1006("總經理"),
    J1007("副總經理"),
    J1008("特別助理"),
    J1009("資深協理"),
    J1010("協理"),
    J1012("資深經理"),
    J1013("專案經理"),
    J1014("經理"),
    J1016("專案副理"),
    J1017("副理"),
    J1018("專案襄理"),
    J1019("資深襄理"),
    J1020("襄理"),
    J1022("專案課長"),
    J1023("五等課長"),
    J1024("四等課長"),
    J1025("督導"),
    J1027("所長"),
    J1029("站長"),
    J1030("代站長"),
    J1031("副站長"),
    J1032("業務主任"),
    J1033("銷售主任"),
    J1034("電銷主任"),
    J1035("專車業務"),
    J1037("主任"),
    J1038("副主任"),
    J1039("組長"),
    J1040("代組長"),
    J1041("資深專員"),
    J1042("專員"),
    J1043("門市專員"),
    J1044("技師"),
    J1045("駕駛"),
    J1046("工讀生"),
    J1047("計時"),
    J1048("計時(部分工時)"),
    J1049("清潔員"),
    J1050("技術員"),
    J1059("業務主任(企業)"),
    J1060("業務主任(標案)"),
    J1061("副督導"),
    J9999("其他"),
    ;

    private static final Map<String, JobTitle> jobTitleMap = Arrays.stream(values()).collect(Collectors.toMap(JobTitle::name, Function.identity()));
    private String title;

    @Nullable
    public static JobTitle of(@Nullable String code) {
        return jobTitleMap.get(code);
    }
}
