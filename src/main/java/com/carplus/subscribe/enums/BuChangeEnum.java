package com.carplus.subscribe.enums;

import carplus.common.response.exception.BadRequestException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface BuChangeEnum {

    /*
     * 撥/調車申請單-類型
     * serviceCode=bu_change_master-changekind
     * */
    @Getter
    @AllArgsConstructor
    enum ChangeKind {
        //撥車
        Assign("assign", "撥車申請"),
        //調車
        Transfer("transfer", "調車申請");

        private String code;
        private String name;

        private static final Map<String, ChangeKind> codeMap = Arrays.stream(values()).collect(Collectors.toMap(ChangeKind::getCode, Function.identity()));
        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(ChangeKind::getCode, ChangeKind::getName));

        @Nullable
        public static ChangeKind getIdentity(@Nullable String code) {
            return codeMap.get(code);
        }

        @Nullable
        public static String of(@Nullable String code) {
            return map.get(code);
        }
    }

    /*
     * 撥/調車申請單-狀態
     * serviceCode=bu_change_master-buchangemasterstatus
     * */
    @Getter
    @AllArgsConstructor
    enum BUChangeMasterStatus {
        INVALID("-10", "取消申請"),
        CANCELOUT("-20", "取消出車"),
        NOTSIGN("01", "未審核"),
        NONOUT("0", "未出車"),
        OUT("10", "已出車"),
        NONRECEIVE("20", "未收車"),
        RECEIVE("30", "已收車"),
        DONE("40", "已完成"),
        EXPIRED("60", "已過期") // 7天未審核，已過期(由排程異動狀態)
        ;

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(BUChangeMasterStatus::getCode, BUChangeMasterStatus::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }

        public static BUChangeMasterStatus getByValue(String value) {
            for (BUChangeMasterStatus code : values()) {
                if (code.getCode().equals(value)) {
                    return code;
                }
            }
            throw new BadRequestException("查無此狀態代碼");
        }

        // 出收車只顯示0~40狀態資料
        public static List<String> showOnDetailByStatus() {
            return Arrays.asList(BUChangeMasterStatus.NONOUT.getCode(), BUChangeMasterStatus.OUT.getCode(),
                BUChangeMasterStatus.NONRECEIVE.getCode(), BUChangeMasterStatus.RECEIVE.getCode(),
                BUChangeMasterStatus.DONE.getCode()
            );
        }

        // 正在撥車中狀態0 01 10 20 30 60
        public static List<String> showOnGoingByStatus() {
            return Arrays.asList(BUChangeMasterStatus.NONOUT.getCode(), BUChangeMasterStatus.NOTSIGN.getCode(),
                BUChangeMasterStatus.OUT.getCode(), BUChangeMasterStatus.NONRECEIVE.getCode(),
                BUChangeMasterStatus.RECEIVE.getCode(), BUChangeMasterStatus.EXPIRED.getCode()
            );
        }

    }

    /*
     * 申請單明細-出、收車狀態
     * serviceCode=bu_change_detail-buchangedetailstatus
     * */
    @Getter
    @AllArgsConstructor
    enum BuChangeDetailStatus {

        NONOUT("0", "未出車"),
        CANCELOUT("5", "取消出車"),
        OUT("10", "已出車"),
        CANCELRECEIVE("15", "取消收車"), //20220930 停用
        RECEIVE("30", "已收車");

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(BuChangeDetailStatus::getCode, BuChangeDetailStatus::getName));

        private static final Map<String, BuChangeDetailStatus> codeMap = Arrays.stream(values()).collect(Collectors.toMap(BuChangeDetailStatus::getCode, Function.identity()));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }

        @Nullable
        public static BuChangeDetailStatus getCode(@Nullable String code) {
            return codeMap.get(code);
        }

        /*
         * 依申請單明細狀態，以及取消原因代碼，取得相對應的取消原因中文
         * */
        @Nullable
        public static String getCancelReason(String code, String cancelType) {
            BuChangeDetailStatus status = getCode(code);
            switch (status) {
                case CANCELOUT:
                    return LeaveUnchangeType.of(cancelType);
                case CANCELRECEIVE:
                    return ReceiveUnchangeType.of(cancelType);
                default:
            }
            return null;
        }

        // 正在撥車中狀態 0 10
        public static List<String> showOnGoingByStatus() {
            return Arrays.asList(BuChangeDetailStatus.NONOUT.getCode(), BuChangeDetailStatus.OUT.getCode());
        }
    }

    /*
     * 申請單-取消申請原因代碼
     * serviceCode=bu_change_master-canceltype
     * */
    @Getter
    @AllArgsConstructor
    enum BuChangeMasterCancel {

        FAULT("1", "資料填寫錯誤"),
        CANCELOUT("2", "取消出車"),
        OTHER("3", "其他");

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(BuChangeMasterCancel::getCode, BuChangeMasterCancel::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 申請單明細-取消出車原因代碼
     * serviceCode=leave-unchange-type
     * */
    @Getter
    @AllArgsConstructor
    enum LeaveUnchangeType {

        CARERROR("1", "車輛異常"),
        CANCEL("2", "申請取消"),
        OTHER("3", "其他");

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(LeaveUnchangeType::getCode, LeaveUnchangeType::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 申請單明細-取消收車原因代碼
     * serviceCode=receive-unchange-type
     * */
    @Getter
    @AllArgsConstructor
    enum ReceiveUnchangeType {

        CARERROR("1", "車輛異常"),
        CANCEL("2", "申請取消"),
        OTHER("3", "其他");

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(ReceiveUnchangeType::getCode, ReceiveUnchangeType::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 撥、調車申請事由
     * 撥車 serviceCode=bu-change-changetype-assign
     * 調車 serviceCode=bu-change-changetype-transfer
     * */
    @Getter
    @AllArgsConstructor
    enum ChangeType {
        CHANGE("change", "撥車申請"),                           // for 撥車申請
        BATCH_CHANGE("batchChange", "營業用撥車"),              // for 撥車申請
        BATCH_RETURN("batchReturn", "一鍵還車"),                // for 撥車申請
        SELL("sell", "賣車申請"),                              // for 撥車申請
        SHORT_TERM("shortTerm", "短天期撥調"),                 // for 撥車申請
        AUTO_ASSIGN("autoAssign", "自動撥車"),                 // for 撥車申請
        AUTO_BATCH_CHANGE("autoBatchChange", "自動營業用撥車"), // for 撥車申請
        OFFICAL("offical", "一般公務"),                         // for 調車申請
        S2G_OFFICAL("s2g-offical", "共享公務"),                 // for 調車申請
        PUBLICRELATION("publicRelation", "公關用車"),           // for 調車申請
        CONTRACT("contract", "合約用車"),                       // for 調車申請
        OTHER("other", "其他");                                 // for 調車申請

        private String code;
        private String name;

        private static final Map<String, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(ChangeType::getCode, ChangeType::getName));

        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 調車申請事由
     * serviceCode=bu-change-changetype-transfer
     * */
    @Getter
    @AllArgsConstructor
    enum ChangeTypeOfTransfer {
        OFFICAL("offical", "一般公務"),
        S2G_OFFICAL("s2g-offical", "共享公務"),
        PUBLICRELATION("publicRelation", "公關用車"),
        CONTRACT("contract", "合約用車"),
        OTHER("other", "其他");

        private String code;
        private String name;

        private static final Map<String, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(ChangeTypeOfTransfer::getCode, ChangeTypeOfTransfer::getName));

        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 撥車申請事由
     * serviceCode=bu-change-changetype-assign
     * */
    @Getter
    @AllArgsConstructor
    enum ChangeTypeOfAssign {
        /**
         * 撥車申請
         */
        CHANGE("change", "撥車申請", true, true, true, true),
        /**
         * 營業用撥車
         */
        BATCH_CHANGE("batchChange", "營業用撥車", true, true, true, true),
        /**
         * 一鍵還車
         */
        BATCH_RETURN("batchReturn", "一鍵還車", true, false, true, false),
        /**
         * 賣車申請
         */
        SELL("sell", "賣車申請", true, false, true, true),
        /**
         * 短天期撥調
         */
        SHORT_TERM("shortTerm", "短天期撥調", true, false, true, false),
        /**
         * 自動撥車
         */
        AUTO_ASSIGN("autoAssign", "自動撥車", false, false, true, false),
        /**
         * 自動營業用撥車
         */
        AUTO_BATCH_CHANGE("autoBatchChange", "自動營業用撥車", false, true, true, false),
        ;

        private String code;            //代碼
        private String name;            //描述
        private boolean workflow;       //是否需要簽核
        private boolean insurance;      //是否需要處理保險
        private boolean updateProperty; //是否異動舊系統還車分配
        private boolean costEvolution;  //是否計算轉移成本

        private static final Map<String, ChangeTypeOfAssign> map =
            Arrays.stream(values()).collect(Collectors.toMap(ChangeTypeOfAssign::getCode, Function.identity()));

        public static ChangeTypeOfAssign of(@Nullable String code) {
            return map.getOrDefault(code, null);
        }
    }

    /*
     * 合約類型 for 合約用車
     * serviceCode=bu_change_master-contracttype
     * */
    @Getter
    @AllArgsConstructor
    enum ContractType {
        fix("fix", "維修"),
        insure("insure", "出險"),
        maintain("maintain", "保養"),
        newCarLate("newCarlate", "新車未到替代車起租");

        private String code;
        private String name;

        private static final Map<String, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(ContractType::getCode, ContractType::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 出車明細紀錄檔-觸發log的原因
     * serviceCode = bu_change_leave_log-logtype
     * */
    @Getter
    @AllArgsConstructor
    enum LeaveLogType {
        Edit("Edit", "編輯車號"),
        Leave("Leave", "出車"),
        Cancel("Cancel", "取消出車"),
        ;
        private String code;
        private String name;

        private static final Map<String, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(LeaveLogType::getCode, LeaveLogType::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 調車申請-出車庫位
     * */
    @Getter
    @AllArgsConstructor
    enum FromBuOfTransfer {
        lrental(1, "調度"),
        srental(BuIdEnum.sRental.getCode(), "短租"),
        s2g(BuIdEnum.s2g.getCode(), "共享"),
        ;

        private Integer buId;
        private String buName;

        private static final Map<Integer, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(FromBuOfTransfer::getBuId, FromBuOfTransfer::getBuName));

        public static String of(@Nullable Integer code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 撥車申請-出車庫位
     * */
    @Getter
    @AllArgsConstructor
    enum FromBuOfAssign {
        lRental(BuIdEnum.lRental.getCode(), "長租"),
        srental(BuIdEnum.sRental.getCode(), "短租"),
        s2g(BuIdEnum.s2g.getCode(), "共享"),
        secondHand(BuIdEnum.secondHand.getCode(), "中古車"),
        carCenter(BuIdEnum.carCenter.getCode(), "調度課"),
        special(BuIdEnum.special.getCode(), "專車"),
        subscribe(BuIdEnum.subscribe.getCode(), "訂閱車"),
        carService(BuIdEnum.carService.getCode(), "服務部"),
        manager(BuIdEnum.manager.getCode(), "主管用車"),
        ;

        private Integer buId;
        private String buName;

        private static final Map<Integer, String> map =
            Arrays.stream(values()).collect(Collectors.toMap(FromBuOfAssign::getBuId, FromBuOfAssign::getBuName));

        public static String of(@Nullable Integer code) {
            return map.getOrDefault(code, "");
        }
    }

    /*
     * 短租區域對應部門代碼
     * */
    @Getter
    @AllArgsConstructor
    enum DepartmentCodeOfSrentalArea {
        N1("C05100", "北一區"),
        N2("C05200", "北二區"),
        C("C06100", "中區"),
        S("C07100", "南區"),
        E("C07200", "東區"),
        /**
         * 短租目前視 自駕營業管理課 為總部
         */
        headquarters("C04001", "自駕營業管理課");

        private String departmentCode;
        private String areaName;

        public static final DepartmentCodeOfSrentalArea of(String deptCode) {
            return Arrays.stream(values()).collect(Collectors.toMap(DepartmentCodeOfSrentalArea::getDepartmentCode, Function.identity()))
                .get(deptCode);
        }

        /**
         * 短租區域部門代碼列表
         */
        public static final List<String> deptCodeList() {
            return Arrays.stream(values()).map(DepartmentCodeOfSrentalArea::getDepartmentCode).collect(Collectors.toList());
        }
    }

    /**
     * 中古車區域對應部門代碼(已滅亡，不使用)
     */
    @Getter
    @AllArgsConstructor
    @Deprecated
    enum DepartmentCodeOfSecondHandArea {
        N("B10210", "北區"),
        S("B10220", "南區"),
        ;

        private String departmentCode;
        private String areaName;

        public static final DepartmentCodeOfSecondHandArea of(String deptCode) {
            return Arrays.stream(values()).collect(Collectors.toMap(DepartmentCodeOfSecondHandArea::getDepartmentCode, Function.identity()))
                .get(deptCode);
        }

        /**
         * 中古車區域部門代碼列表
         *
         */
        public static final List<String> deptCodeList() {
            return Arrays.stream(values()).map(DepartmentCodeOfSecondHandArea::getDepartmentCode).collect(Collectors.toList());
        }
    }

    /*
     * 查詢出車/收車
     * 無serviceCode，僅用於程式邏輯判斷
     * */
    @Getter
    @AllArgsConstructor
    enum MoveCarType {
        leave("leave", "出車"),
        receive("receive", "收車");

        private String code;
        private String name;

    }

}