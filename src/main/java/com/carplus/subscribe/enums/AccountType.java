package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AccountType {

    /**
     * 信用卡
     */
    Credit("A", "信用卡"),
    /**
     * 現金
     */
    Cash("B", "現金"),
    /**
     * 匯款
     */
    Remit("C", "匯款"),
    /**
     * 支票
     */
    Check("D", "支票"),
    ;

    private static final Map<String, AccountType> accountTypeMap = Arrays.stream(values()).collect(Collectors.toMap(AccountType::getCollectPaymentType, Function.identity()));
    private final String collectPaymentType;
    private final String collectPaymentTypeName;

    @Nullable
    public static AccountType of(@Nullable String paymentType) {
        return accountTypeMap.get(paymentType);
    }
}
