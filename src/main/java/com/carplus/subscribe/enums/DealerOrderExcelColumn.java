package com.carplus.subscribe.enums;

import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import lombok.Getter;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 真實按照順序對應匯入的經銷商訂單 Excel 欄位
 */
@Getter
public enum DealerOrderExcelColumn {

    ORDER_NO("訂單編號"),
    DEALER_NAME("經銷商"),
    SECURITY_DEPOSIT_DATE("保證金支付時間"),
    PARENT_ORDER_NO("母約編號"),
    PREVIOUS_ORDER_NO("前約訂單編號"),
    STAGE("期數"),
    PLATE_NO("車牌號碼"),
    USER_NAME("訂車人姓名"),
    ID_NO("身分證號"),
    NATIONAL_CODE("手機國碼"),
    MAIN_CELL("手機號碼"),
    BIRTHDAY("生日"),
    EMAIL("信箱"),
    POSTAL_CODE("戶籍區號"),
    ADDRESS("戶籍地址"),
    VAT_NUMBER("公司統一編號"),
    COMPANY_NAME("公司抬頭"),
    EXPECT_DEPART_STATION("預定出車站點"),
    EXPECT_RETURN_STATION("預定還車站點"),
    EXPECT_DEPART_DATE("預定出車時間"),
    EXPECT_RETURN_DATE("預定還車時間"),
    SECURITY_DEPOSIT("保證金金額"),
    MONTHLY_FEE("月費租金金額"),
    ACTUAL_MILEAGE_RATE("里程費率(實際)"),
    ORIGINAL_MILEAGE_RATE("里程費率(原價)"),
    SUBSCRIBE_MONTH("訂閱租期"),
    PREPAID_MONTHS("預收月數"),
    DEPART_STATION("實際出車站點"),
    DEPART_DATE("實際出車時間"),
    BEGIN_AMT("起租金額"),
    RETURN_STATION("實際還車站點"),
    RETURN_DATE("實際還車時間"),
    CLOSE_AMT("迄租金額"),
    IS_RETURNED("是否實際還車"),
    IS_CANCEL("是否取消"),
    CANCEL_DATE("取消時間");

    private static final Map<String, Class<?>> dealerOrderExcelColumnsTypeMap = Arrays.stream(DealerOrderExcel.class.getDeclaredFields())
        .collect(Collectors.toMap(field -> field.getName().toLowerCase(), Field::getType));

    private final String description;

    DealerOrderExcelColumn(String description) {
        this.description = description;
    }

    public Class<?> getTargetType() {
        String lowerCaseName = StringUtils.replace(name(), "_", "").toLowerCase();
        // 手動處理例外情況， DealerOrderExcelColumn 的 POSTAL_CODE 對應到 DealerOrderExcel 的 city 和 area
        return this == POSTAL_CODE ? String.class : dealerOrderExcelColumnsTypeMap.get(lowerCaseName);
    }

    public int getIndex(int offset) {
        return ordinal() + offset;
    }

    public int getExampleIndex() {
        return getIndex(1);
    }

    public static List<String> getDescriptions() {
        return Arrays.stream(values())
            .map(DealerOrderExcelColumn::getDescription)
            .collect(Collectors.toList());
    }
}
