package com.carplus.subscribe.enums;

import com.carplus.subscribe.constant.CarPlusConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface GeoDefine {

    /**
     * 業務分區 -- 查詢列表、排車用
     */
    @Getter
    @AllArgsConstructor
    enum GeoRegion {

        N1("北一區", CarPlusConstant.departmentMasterCode, CarPlusConstant.departmentMasterCode),
        N2("北二區", CarPlusConstant.departmentMasterCode, CarPlusConstant.departmentMasterCode),
        C("中區", CarPlusConstant.departmentMasterCode, CarPlusConstant.departmentMasterCode),
        S("南區", CarPlusConstant.departmentMasterCode, CarPlusConstant.departmentMasterCode),
        E("東區", CarPlusConstant.departmentMasterCode, CarPlusConstant.departmentMasterCode),
        ;

        private String name;
        /**
         * 各BU自定義區域代碼 (code)
         */
        private String areaId;
        /**
         * 組織代碼 (codeNew)
         */
        private String areaIdNew;

        public static GeoRegion[] getAvaibleSubscribeGeoRegion() {
            return new GeoRegion[] {N1, N2, C, S};
        }

        public static String enumToCode(@NonNull GeoRegion geo) {
            if (geo == N1 || geo == N2) {
                return "N";
            }
            return geo.name();
        }

        public static String enumToName(@NonNull GeoRegion geo) {
            if (geo == N1 || geo == N2) {
                return "北區";
            }
            return geo.getName();
        }

        public static GeoRegion[] stringToEnum(String str) {
            if (("N").equals(str) || ("N1").equals(str) || ("N2").equals(str)) {
                return new GeoRegion[] {N1, N2};
            }
            for (GeoRegion geo : GeoRegion.values()) {
                if (geo.name().equalsIgnoreCase(str)) {
                    return new GeoRegion[] {geo};
                }
            }
            return null;
        }

        public static List<String> getNamesBy(String geoRegionStr) {
            return Arrays.stream(Objects.requireNonNull(stringToEnum(geoRegionStr))).map(Enum::name).collect(Collectors.toList());
        }
    }

    /**
     * 地理分區
     */
    @Getter
    @AllArgsConstructor
    enum AreaRegion {

        G0("北北基", new int[] {1, 2, 3}),
        G1("桃竹苗", new int[] {5, 6, 7, 8}),
        G2("中彰投", new int[] {9, 11, 12}),
        G3("雲嘉南", new int[] {13, 14, 15, 16}),
        G4("高屏", new int[] {18, 21}),
        G5("宜蘭", new int[] {4}),
        G6("花蓮", new int[] {23}),
        G7("台東", new int[] {22}),
        ;

        private static final Map<String, AreaRegion> geoRegionMap = Arrays.stream(values()).collect(Collectors.toMap(AreaRegion::getName, Function.identity()));
        private final String name;
        private final int[] cities;

        @Nullable
        public static AreaRegion of(int city) {
            for (AreaRegion areaRegion : values()) {
                for (int geoRegionCity : areaRegion.getCities()) {
                    if (geoRegionCity == city) {
                        return areaRegion;
                    }
                }
            }

            return null;
        }

        @Nullable
        public static AreaRegion of(@Nullable String name) {
            return geoRegionMap.get(name);
        }
    }
}