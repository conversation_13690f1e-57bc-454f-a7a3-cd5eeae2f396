package com.carplus.subscribe.enums.finbus;

import carplus.common.utils.StringUtils;

public enum PaymentMethodCodeEnum {
    TAPPAY_TSIB, // TapPay 台新信用卡
    TAPPAY_ADVANCE, // TapPay 預付款
    REMIT, // 匯款
    MARGIN, // 車子保證金
    TAPPAY_NCC; // AE

    public static boolean isValid(String name) {
        try {
            PaymentMethodCodeEnum.valueOf(name);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean isNotValid(String name) {
        return !isValid(name);
    }

    public static boolean isAECard(String cardNumber) {
        return StringUtils.isNotBlank(cardNumber) && (cardNumber.startsWith("34") || cardNumber.startsWith("37"));
    }

    public static PaymentMethodCodeEnum getPaymentMethodCodeEnum(String cardNumber) {
        return isAECard(cardNumber) ? TAPPAY_NCC : TAPPAY_TSIB;
    }
}
