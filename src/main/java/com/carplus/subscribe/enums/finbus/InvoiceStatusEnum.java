package com.carplus.subscribe.enums.finbus;

import com.carplus.subscribe.enums.InvoiceDefine;

public enum InvoiceStatusEnum {
    NEW, // 新開立
    VOID, // 作廢
    ALLOWANCE; // 折讓


    public static InvoiceStatusEnum of(InvoiceDefine.InvStatus status) {
        switch (status) {
            case ALLOWANCE:
                return ALLOWANCE;
            case CREATE:
                return NEW;
            case INVALIDATE:
                return VOID;
            default:
        }
        return null;
    }
}
