package com.carplus.subscribe.enums;

import com.carplus.subscribe.exception.SubscribeException;
import lombok.Getter;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SUBSCRIBE_MONTH_NOT_VALID;

@Getter
public enum SubscribeMonth {

    ONE(1),
    TW<PERSON>(2),
    TH<PERSON><PERSON>(3),
    FOUR(4),
    FIVE(5),
    SIX(6),
    SEVEN(7),
    EIGHT(8),
    NIN<PERSON>(9),
    TEN(10),
    ELEVEN(11),
    TWELVE(12);

    private final int value;

    SubscribeMonth(int value) {
        this.value = value;
    }

    public static SubscribeMonth fromValue(int value) {
        for (SubscribeMonth month : SubscribeMonth.values()) {
            if (month.getValue() == value) {
                return month;
            }
        }
        throw new SubscribeException(SUBSCRIBE_MONTH_NOT_VALID);
    }
}
