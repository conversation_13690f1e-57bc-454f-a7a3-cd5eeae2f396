package com.carplus.subscribe.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Comparator;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
public class CancellationPolicy {

    @Schema(description = "提前取消天數")
    private int overDays;
    @Schema(description = "手續費比例")
    private double commissionRate;
    @Schema(description = "描述")
    private String descr;
    @Schema(description = "等級")
    private int level;
    @Schema(description = "名稱")
    private String name;
    @Schema(description = "退款金額")
    private int refundAmt;
    @Schema(description = "應收金額")
    private int receivableAmt;

    public static CancellationPolicy of(ICancellationPolicy cancellationPolicy) {
        CancellationPolicy policy = new CancellationPolicy();
        policy.overDays = cancellationPolicy.getOverDays();
        policy.commissionRate = cancellationPolicy.getCommissionRate();
        policy.descr = cancellationPolicy.getDescr();
        policy.level = cancellationPolicy.getLevel();
        policy.name = cancellationPolicy.getLevelName();
        return policy;
    }

    public void calculateCancelPolicy(int securityDeposit) {
        this.refundAmt = Double.valueOf(securityDeposit * (100 - commissionRate) / 100).intValue();
        this.receivableAmt = securityDeposit - refundAmt;
    }

    @Getter
    @AllArgsConstructor
    public enum SubscribeOldCar implements ICancellationPolicy {
        L1(10, 0, "於預定取車日 10日之前取消，退還保證金 100% ＝ 手續費 0%", 1),
        L2(7, 30, "於預定取車日 前 7~9日取消，退還保證金 70% ＝ 手續費 30%", 2),
        L3(4, 50, "於預定取車日 前 4~6日取消，退還保證金 50% ＝ 手續費 50%", 3),
        L4(0, 100, "於預定取車日 當日~前 3天取消，退還保證金 0% ＝ 手續費 100%", 4),
        ;

        public static final Map<String, CancellationPolicy> policyMap = Arrays.stream(values()).collect(Collectors.toMap(CancellationPolicy.SubscribeOldCar::name, CancellationPolicy::of));
        private static final Map<String, ICancellationPolicy> cancellationPolicyMap = Arrays.stream(values()).collect(Collectors.toMap(SubscribeOldCar::name, Function.identity()));
        @Schema(description = "提前取消天數")
        int overDays;
        @Schema(description = "手續費比例")
        double commissionRate;
        @Schema(description = "描述")
        String descr;
        @Schema(description = "等級")
        int level;

        @Nullable
        public static CancellationPolicy of(@Nullable String name) {
            return CancellationPolicy.of(cancellationPolicyMap.get(name));
        }

        @NonNull
        public static SubscribeOldCar ofDays(long days) {
            return Arrays.stream(values())
                .sorted(Comparator.comparingDouble(SubscribeOldCar::getCommissionRate))
                .filter(cancellationPolicy -> days >= cancellationPolicy.getOverDays())
                .findFirst()
                .orElse(L4);
        }

        public String getLevelName() {
            return name();
        }
    }

    @Getter
    @AllArgsConstructor
    public enum SubscribeNewCar implements ICancellationPolicy {
        L1(2, 0, "於支付保證金後 2日之內取消，退還保證金 100% ＝ 手續費 0%", 1),
        L2(5, 50, "於支付保證金後 第3日~5日之內取消，退還保證金 50% ＝ 手續費 50%", 2),
        L3(6, 100, "支付保證金後 第6日以上，退還保證金 0% ＝ 手續費 100%", 3),
        ;

        public static final Map<String, CancellationPolicy> policyMap = Arrays.stream(values()).collect(Collectors.toMap(CancellationPolicy.SubscribeNewCar::name, CancellationPolicy::of));
        private static final Map<String, ICancellationPolicy> cancellationPolicyMap = Arrays.stream(values()).collect(Collectors.toMap(CancellationPolicy.SubscribeNewCar::name, Function.identity()));
        @Schema(description = "提前取消天數")
        int overDays;
        @Schema(description = "手續費比例")
        double commissionRate;
        @Schema(description = "描述")
        String descr;
        @Schema(description = "等級")
        int level;

        @Nullable
        public static CancellationPolicy of(@Nullable String name) {
            return CancellationPolicy.of(cancellationPolicyMap.get(name));
        }

        @NonNull
        public static CancellationPolicy.SubscribeNewCar ofDays(long days) {
            return Arrays.stream(values())
                .sorted(Comparator.comparingDouble(CancellationPolicy.SubscribeNewCar::getCommissionRate))
                .filter(cancellationPolicy -> days <= cancellationPolicy.getOverDays())
                .findFirst()
                .orElse(L3);
        }

        public String getLevelName() {
            return name();
        }
    }

    public interface ICancellationPolicy {
        String getLevelName();

        int getOverDays();

        double getCommissionRate();

        String getDescr();

        int getLevel();
    }
}