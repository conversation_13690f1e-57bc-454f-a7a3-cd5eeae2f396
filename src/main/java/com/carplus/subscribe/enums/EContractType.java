package com.carplus.subscribe.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum EContractType {
    /**
     * 電子合約 <br/>
     * 官網簽完名後，儲存 Type <br/>
     * 此Type e_contracts.uploadFileId 只會有一個uploadFileId，對應一個 file
     */
    E_CONTRACT("格上汽車訂閱式租賃契約"),
    /**
     * 出租單 <br/>
     * 維運 APP 出車後，儲存 Type <br/>
     * 此Type  e_contracts.uploadFileId 依據 TaskType <br/>
     * 出車任務-出租單 欄位 [1*uploadFileId, contractNo, departTaskId] <br/>
     * 還車任務-出租單 欄位 [1*uploadFileId, contractNo, returnTaskId]
     */
    E_RENTAL(""),
    /**
     * 其它 <br/>
     * 後台新增附件，儲存 Type <br/>
     * 此Type  uploadFileId 只存1個 File  <br/>
     */
    OTHER("");

    private String description;

    EContractType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static List<String> toNamelist() {
        return Arrays.stream(EContractType.values()).map(EContractType::name).collect(Collectors.toList());
    }
}
