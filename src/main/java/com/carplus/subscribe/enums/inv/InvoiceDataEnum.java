package com.carplus.subscribe.enums.inv;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface InvoiceDataEnum {

    /**
     * 發票狀態 serviceCode=invoice_master-invoicestatus
     */
    @Getter
    @AllArgsConstructor
    enum InvoiceStatus {
        issue("issue", "開立"),
        cancel("cancel", "作廢"),
        ;
        private String code;
        private String name;
    }

    /**
     * 稅別 serviceCode=invoice_master-taxType
     */
    @Getter
    @AllArgsConstructor
    enum TaxType {
        O("O", "免稅"),
        T("T", "應稅"),
        Z("Z", "零稅率"),
        ;
        private static final Map<String, TaxType> TaxTypeMap = Arrays.stream(values()).collect(Collectors.toMap(TaxType::getCode, Function.identity()));
        private String code;
        private String name;

        @Nullable
        public static TaxType ofEnum(@Nullable String code) {
            return TaxTypeMap.get(code);
        }
    }

    /**
     * 發票類型 serviceCode=invoice_master-invoicetype
     */
    @Getter
    @AllArgsConstructor
    enum InvoiceType {
        EC("EC", "B2C電子發票"),
        EB("EB", "B2B電子發票"),
        ;
        private String code;
        private String name;
    }

    /**
     * 發票載具類型，serviceCode=invoice_master-carriertype
     */
    @Getter
    @AllArgsConstructor
    enum CarrierType {
        CarrierType_EG0142("EG0142", "關貿會員載具"),
        CarrierType_3J0002("3J0002", "手機條碼"),
        CarrierType_CQ0001("CQ0001", "自然人憑證"),
        CarrierType_1K0001("1K0001", "悠遊卡"),
        CarrierType_2G0001("G0001", "iCash"),
        ;
        private String code;
        private String name;
    }

    /**
     * 列印註記 serviceCode=invoice_master-printstatus
     */
    @Getter
    @AllArgsConstructor
    enum PrintStatus {
        PrintStatus_0("0", "未列印"),
        PrintStatus_1("1", "已列印"),
        ;
        private String code;
        private String name;
    }

    /**
     * 發票開立通知方式 serviceCode=invoice_master-notifybuyertype
     */
    @Getter
    @AllArgsConstructor
    enum NotifyBuyerType {
        NotifyBuyerType_0("0", "不須通知"),
        NotifyBuyerType_1("1", "前端已通知, ex: POS"),
        NotifyBuyerType_2("2", "EMail 通知"),
        NotifyBuyerType_4("4", "簡訊+email"), // 沒有支付關貿費用，所以不會發出簡訊
        ;
        private String code;
        private String name;
    }

    /**
     * 開立發票憑證類型 serviceCode = invoice_detail-depencedoctype
     */
    @Getter
    @AllArgsConstructor
    enum DepenceDocType {
        orders("orders", "訂單"),
        contract("contract", "契約"),
        manual("manual", "手開發票"), //出售、過戶....
        ;
        private String code;
        private String name;
    }

    /**
     * 開立發票預設收件資訊 serviceCode = invoice_issue_notify_default
     */
    @Getter
    @AllArgsConstructor
    enum InvoiceIssueNotifyDefault {
        ADDRESS,
        EMAIL,
        MOBILE,
        POST,
    }
}
