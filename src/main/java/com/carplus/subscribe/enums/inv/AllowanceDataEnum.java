package com.carplus.subscribe.enums.inv;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AllowanceDataEnum {

    /**
     * 折讓狀態 serviceCode = allowance_master-allowancestatus
     */
    @Getter
    @AllArgsConstructor
    enum AllowanceStatus {
        sellerIssue("sellerIssue", "銷項折讓開立"),
        sellerCancel("sellerCancel", "銷項折讓作廢"),
        ;
        private String code;
        private String name;
    }

    /**
     * 折讓類型 serviceCode=allowance_master-allowancetype
     */
    @Getter
    @AllArgsConstructor
    enum AllowanceType {
        EC("EC", "B2C折讓"),
        EB("EB", "B2B折讓"),
        ;
        private String code;
        private String name;

    }

    /**
     * 開立折讓憑證類型 serviceCode = allowance_detail-depencedoctype
     */
    @Getter
    @AllArgsConstructor
    enum DepenceDocType {
        orders("orders", "訂單"),
        contract("contract", "契約"),
        ;
        private String code;
        private String name;
    }
}
