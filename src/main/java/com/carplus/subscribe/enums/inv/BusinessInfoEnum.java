package com.carplus.subscribe.enums.inv;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface BusinessInfoEnum {

    /**
     * 業務別 serviceCode=business_info-businesstype
     */
    @Getter
    @AllArgsConstructor
    enum BusinessType {
        A1("A1", "長租(中菱)"),
        B1("B1", "司機(協冠)"),
        C1("C1", "出版(宏碩)"),
        E1("E1", "長租(格上)"),
        E2("E2", "共享(格上)"),
        E3("E3", "運務機接(格上)"),
        E4("E4", "備用(格上)"),
        E5("E5", "訂閱(格上)"),
        E6("E6", "短租(格上)"),
        ;
        private static final Map<String, BusinessType> map = Arrays.stream(values()).collect(Collectors.toMap(BusinessType::getCode, Function.identity()));
        private String code;
        private String name;

        public static BusinessType ofEnum(@Nullable String code) {
            return map.getOrDefault(code, null);
        }
    }


    /**
     * 配號方式 serviceCode=business_info-assignkind
     */
    @Getter
    @AllArgsConstructor
    enum AssignKind {
        period("period", "期配"),
        month("month", "月配"),
        ;
        private String code;
        private String name;
    }
}
