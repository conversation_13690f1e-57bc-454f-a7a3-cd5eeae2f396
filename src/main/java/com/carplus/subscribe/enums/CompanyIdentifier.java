package com.carplus.subscribe.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_VAT_NO;

@Getter
public enum CompanyIdentifier {

    CARPLUS(getCarplusTaxIds());

    private final List<String> taxIds;

    CompanyIdentifier(List<String> taxIds) {
        this.taxIds = taxIds;
    }

    private static List<String> getCarplusTaxIds() {
        return Lists.newArrayList(CARPLUS_COMPANY_VAT_NO, "12856727", "53019788", "70817250", "80331925", "80354813");
    }

    public static boolean contains(CompanyIdentifier company, String taxIds) {
        return company.getTaxIds().contains(taxIds);
    }
}
