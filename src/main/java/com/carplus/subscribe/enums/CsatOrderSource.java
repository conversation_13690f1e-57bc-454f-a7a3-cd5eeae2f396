package com.carplus.subscribe.enums;

import carplus.common.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum CsatOrderSource {
    SUBSCRIBE(1, "格上訂閱"),
    SR(2, "格上短租"),
    SEALAND(3, "SEALAND"),
    OTHER(4, "其他");

    private final int code;
    private final String name;


    public static CsatOrderSource of(Integer code) {
        for (CsatOrderSource value : CsatOrderSource.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return OTHER;
    }

    public static CsatOrderSource of(String name) {
        for (CsatOrderSource value : CsatOrderSource.values()) {
            if (value.name().equalsIgnoreCase(StringUtils.trim(name))) {
                return value;
            }
        }
        return OTHER;
    }
}
