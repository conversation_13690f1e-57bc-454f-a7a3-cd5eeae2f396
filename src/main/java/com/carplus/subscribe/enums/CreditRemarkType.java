package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CreditRemarkType {

    HIGH_PRICE("自動授信成功，高價車方案，進人工授信"),
    AUTO_CREDIT_FAIL("自動授信未通過，進人工授信"),
    SYSTEM_CREDIT_FAIL("系統未知原因授信失敗，進人工授信"),
    ID_VALIDATE_FAIL("檢查身分ID格式錯誤，轉人工授信審核"),
    ORDER_COUNT_FAIL("自動授信審查通過，總台數上限轉人工授信"),
    CREDIT_FAIL("自動授信審查未過，不承做"),
    MANUAL_PASS("人工授信審查通過"),
    MANUAL_FAIL("人工授信審查未過"),
    AUTO_CREDIT_SUCCESS("自動授信審查通過"),
    SUCCESS("自動授信審查通過"),
    AUTO_CREDIT_BYPASS("不需要自動授信"),
    ;

    private String description;

    public static boolean isAutoCreditPass(CreditRemarkType type) {
        return type == HIGH_PRICE || type == AUTO_CREDIT_SUCCESS;
    }

    /**
     * 是否需要人工授信
     */
    public static boolean isManualCredit(CreditRemarkType type) {
        switch (type) {
            case HIGH_PRICE:
            case AUTO_CREDIT_FAIL:
            case SYSTEM_CREDIT_FAIL:
            case ID_VALIDATE_FAIL:
            case ORDER_COUNT_FAIL:
                return true;
            default:
                return false;
        }
    }
}
