package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 車輛相關
 */
public interface CarDefine {

    @Getter
    @AllArgsConstructor
    enum CarStatus {

        Free("00", "空車"),
        CarOut("10", "車輛調出"),
        Subscribed("20", "已訂閱"),
        BizOut("30", "營業出車"),
        NonBizOut("31", "非營業出車"),
        NonWorking("60", "停駛"),
        Auction2("65", "出站待拍(待確認)"),
        Auction("70", "出站待拍"),
        Project("75", "專案車購回"),
        Stolen("80", "失竊"),
        <PERSON>aud("81", "詐騙"),
        CarIn("85", "外部調車未生效"),
        Scrapped("90", "報廢"),
        Exit("99", "退出車隊"),
        ;

        private static final Map<String, CarStatus> carStatusMap = Arrays.stream(values()).collect(Collectors.toMap(CarStatus::getCode, Function.identity()));
        private String code;
        private String name;

        @Nullable
        public static CarStatus of(@Nullable String code) {
            return carStatusMap.get(code);
        }
    }


    /**
     * 訂閱車籍類別
     */
    @Getter
    @AllArgsConstructor
    enum CarType {
        sedan("轎車"),
        suv("休旅車"),
        wagon("旅行車"),
        truck("貨車"),
        electric("電動車"),
        sports("跑車"),
        van("客貨車"),
        ;
        private static final Map<String, CarType> carTypeMap = Arrays.stream(values()).collect(Collectors.toMap(CarType::getName, Function.identity()));
        private String name;

        @Nullable
        public static CarType of(@Nullable String name) {
            return carTypeMap.get(name);
        }
    }

    /**
     * 車型類別
     */
    @Getter
    @AllArgsConstructor
    enum CarKind {
        sedan(0, "轎車"),
        suv(1, "休旅車"),
        truck(2, "貨車"),
        moto(8, "機車"),
        ;
        private static final Map<Integer, CarKind> carKindMap = Arrays.stream(values()).collect(Collectors.toMap(CarKind::getCode, Function.identity()));
        private int code;
        private String name;

        @Nullable
        public static CarKind of(@Nullable Integer code) {
            return carKindMap.get(code);
        }
    }

    /**
     * 燃料類別
     */
    @Getter
    @AllArgsConstructor
    enum FuelType {
        petrol92(0, "92汽油"),
        petrol95(1, "95汽油"),
        petrol98(3, "98汽油"),
        hybrid(6, "油電混合"),
        gas(7, "汽油瓦斯"),
        diesel(8, "柴油"),
        electrical(9, "電動"),
        ;

        private static final Map<Integer, FuelType> fuelTypeMap = Arrays.stream(values()).collect(Collectors.toMap(FuelType::getCode, Function.identity()));
        private int code;
        private String name;

        @Nullable
        public static FuelType of(@Nullable String code) {
            if (code != null) {
                try {
                    return of(Integer.parseInt(code));
                } catch (Exception ignore) {
                    return null;
                }
            }

            return null;
        }

        @Nullable
        public static FuelType of(@Nullable Integer code) {
            return fuelTypeMap.get(code);
        }
    }

    @Getter
    @AllArgsConstructor
    enum EnergyType {
        GASOLINE(1, 0, "汽油"),       // 汽油
        DIESEL(2, 1, "柴油"),         // 柴油
        GASOLINE_ELECTRIC(3, 2, "油電"),  // 汽油/電力
        GASOLINE_LPG(4, 3, "汽油/LPG"),    // 汽油/LPG
        ELECTRIC(5, 4, "電動"),        // 電動
        DIESEL_ELECTRIC(6, 5, "柴電"); // 柴油/電力

        private final int code;
        private final int ordinal;
        private final String name;

        private static final Map<Integer, EnergyType> energyTypeMap = Arrays.stream(values()).collect(Collectors.toMap(EnergyType::getCode, Function.identity()));

        @Nullable
        public static EnergyType of(@Nullable Integer code) {
            return energyTypeMap.get(code);
        }
    }

    /**
     * 排檔類別
     */
    @Getter
    @AllArgsConstructor
    enum GearType {
        mt(0, "手排"),
        at(1, "自排"),
        ;

        private static final Map<Integer, GearType> gearTypeMap = Arrays.stream(values()).collect(Collectors.toMap(GearType::getCode, Function.identity()));

        private int code;
        private String name;

        @Nullable
        public static GearType of(@Nullable Integer code) {
            return gearTypeMap.get(code);
        }
    }

    /**
     * 上架狀態
     */
    @Getter
    @AllArgsConstructor
    enum Launched {
        open("上架"),
        close("下架"),
        accident("下架(車損)"),
        deprecate("不使用"),
        tbc("待設定"),
        ;
        private String name;
    }

    /**
     * 車況
     */
    @Getter
    @AllArgsConstructor
    enum CarState {
        NEW("新車", 8),
        OLD("中古車", 5);
        private String name;
        private int workDays;
    }

    /**
     * 車輛狀態
     */
    @Getter
    @AllArgsConstructor
    enum SaleStatus {
        open(Launched.open.name, Launched.open),
        close(Launched.close.name, Launched.close),
        deprecate(Launched.deprecate.name, Launched.deprecate),
        accident(Launched.accident.name, Launched.accident),
        deal("成交", null),
        ;
        private static final Map<Launched, SaleStatus> saleStatusMap = Arrays.stream(values())
            .filter(s -> Objects.nonNull(s.getLaunched()))
            .collect(Collectors.toMap(SaleStatus::getLaunched, Function.identity()));
        private String name;
        private Launched launched;

        public static SaleStatus getSaleStatus(Launched launched) {
            return saleStatusMap.get(launched);
        }
    }

    /**
     * 可出租類別
     */
    @Getter
    @AllArgsConstructor
    enum RentType {
        day("日租"),
        subscribe("訂閱"),
        ;
        private String name;
    }

    /**
     * 車輛標籤
     */
    @Getter
    @AllArgsConstructor
    enum CarTag {
        SELECTED(1, "嚴選車型"),
        HOT_SALE(2, "熱銷車款"),
        MONTHLY_DISCOUNTED(3, "優惠月費"),
        LEVEL_DISCOUNTED(4, "超激優惠");
        private final Integer id;
        private String cnName;
    }
}