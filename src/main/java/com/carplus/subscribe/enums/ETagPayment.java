package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ETagPayment {

    /**
     * 不收款
     */
    Free("0"),
    /**
     * 現金
     */
    Cash("1"),
    /**
     * 信用卡
     */
    Credit("2"),
    /**
     * 簡訊
     */
    Sms("2"),
    /**
     * 無通行費
     */
    None("9"),
    /**
     * 官網付款
     */
    OFFICIAL("2");

    private static final Map<String, ETagPayment> eTagPaymentMap = Arrays.stream(values()).collect(Collectors.toMap(ETagPayment::name, Function.identity()));
    private String typeCode;

    @Nullable
    public static ETagPayment of(@Nullable String name) {
        return eTagPaymentMap.get(name);
    }
}
