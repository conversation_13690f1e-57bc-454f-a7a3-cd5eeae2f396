package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BuIdEnum {

    lRental(1, "長租", 1, 1, 1),
    sRental(2, "日租車", 1, 1, 2),
    s2g(3, "共享車", 1, 1, 3),
    subscribe(4, "訂閱車", 1, 1, 4),
    secondHand(5, "中古車", 1, 1, 5),
    special(6, "專車", 1, 1, 6),
    manager(7, "主管用車", 1, 1, 7),
    carCenter(8, "調度課", 1, 1, 8),
    carService(9, "服務部", 1, 1, 9),
    ;
    Integer code;
    String name;
    Integer isAssign;
    Integer isTransfer;
    Integer seq;

    private static final Map<Integer, BuIdEnum> BuIdMap = Arrays.stream(values()).collect(Collectors.toMap(BuIdEnum::getCode, Function.identity()));

    @Nullable
    public static BuIdEnum ofEnum(@Nullable Integer code) {
        return BuIdMap.get(code);
    }

    public static boolean isNotValidForSubscribe(Integer buId) {
        return !subscribe.getCode().equals(buId) && !lRental.getCode().equals(buId);
    }

    public boolean isCallCrsControl() {
        return this == carCenter || this == subscribe || this == secondHand;
    }

    /**
     * 各BU
     * 撥車區域與站點選單資料取得方式
     * 1 若取得方式為API，需確認 SatelliteAPIs 已設定
     * 2 若取得方式為SERVICECODE，需確認 Eunm:AssignLocationOfBu 已設定
     */
    @Getter
    @AllArgsConstructor
    public enum AssignLocationDropDownSource {
        sRental(2, "日租車", "API"),
        s2g(3, "共享車", "API"),
        subscribe(4, "訂閱車", "API"),
        secondHand(5, "中古車", "API"),
        special(6, "專車", "SERVICECODE"),
        manager(7, "主管用車", "API"),
        carCenter(8, "調度課", "API"),
        carService(9, "服務部", "API"),
        ;
        Integer code;
        String name;
        String sourceType;

        private static final Map<Integer, AssignLocationDropDownSource> BuIdMap = Arrays.stream(values()).collect(Collectors.toMap(
            AssignLocationDropDownSource::getCode, Function.identity()));

        @Nullable
        public static AssignLocationDropDownSource ofEnum(@Nullable Integer code) {
            return BuIdMap.get(code);
        }

    }

    /*
     * 撥/調車申請單-狀態
     * serviceCode=bu_change_master-buchangemasterstatus
     * */
    @Getter
    @AllArgsConstructor
    enum BUChangeMasterStatus {
        INVALID("-10", "取消申請"),
        CANCELOUT("-20", "取消出車"),
        NOTSIGN("01", "未審核"),
        NONOUT("0", "未出車"),
        OUT("10", "已出車"),
        NONRECEIVE("20", "未收車"),
        RECEIVE("30", "已收車"),
        DONE("40", "已完成"),
        EXPIRED("60", "已過期") // 7天未審核，已過期(由排程異動狀態)
        ;

        private String code;
        private String name;

        private static final Map<String, String> map = Arrays.stream(values()).collect(Collectors.toMap(BUChangeMasterStatus::getCode, BUChangeMasterStatus::getName));

        @Nullable
        public static String of(@Nullable String code) {
            return map.getOrDefault(code, "");
        }
    }


}
