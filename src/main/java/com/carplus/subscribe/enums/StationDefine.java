package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface StationDefine {

    @Getter
    enum StationCategory {

        /**
         * 一般門市
         */
        GENERAL,
        /**
         * 驛站
         */
        COURIER,
        /**
         * V-SHOP
         */
        VSHOP,
        /**
         * 經銷商
         */
        DEALER,
        /**
         * 虛擬站
         */
        VIRTUAL,
        /**
         * 倉庫
         */
        WAREHOUSE,
        ;

        /**
         * 取得經銷商以外的category
         */
        public static final List<StationCategory> NON_DEALER_STATION_CATEGORY = Arrays.stream(values())
            .filter(stationCategory -> !DEALER.equals(stationCategory)).collect(Collectors.toList());
    }

    /**
     * 站所分區<br>
     * 異地租還
     */
    @Getter
    @AllArgsConstructor
    enum StationRegion {

        /**
         * 雙北
         */
        S0(0),
        /**
         * 桃竹苗
         */
        S1(1),
        /**
         * 中彰投
         */
        S2(2),
        /**
         * 雲嘉南
         */
        S3(3),
        /**
         * 高屏
         */
        S4(4),
        /**
         * 宜蘭
         */
        S5(5),
        /**
         * 花蓮
         */
        S6(6),
        /**
         * 玉里
         */
        S7(7),
        /**
         * 台東
         */
        S8(8),
        ;

        private static final Map<Integer, StationRegion> stationRegionMap = Arrays.stream(values()).collect(Collectors.toMap(StationRegion::getCode, Function.identity()));
        /**
         * 異地租還費用
         */
        private static final int[][] DIFF_REGION_CHARGE = {
            {0, 500, 1000, 1500, 2000, 1000, 2500, 2500, 2500},
            {500, 0, 500, 1000, 1500, 1500, 2500, 2500, 2500},
            {1000, 500, 0, 500, 1000, 2000, 2500, 2500, 2500},
            {1500, 1000, 500, 0, 500, 2500, 2500, 2500, 2500},
            {2000, 1500, 1000, 500, 0, 2500, 2500, 2500, 2500},
            {1000, 1500, 2000, 2500, 2500, 0, 2500, 2500, 2500},
            {2500, 2500, 2500, 2500, 2500, 2500, 0, 1000, 1500},
            {2500, 2500, 2500, 2500, 2500, 2500, 1000, 0, 1000},
            {2500, 2500, 2500, 2500, 2500, 2500, 1500, 1000, 0}
        };
        private int code;

        /**
         * 取得異地租還費用
         *
         * @param from 租車站所區域
         * @param to   還車站所區域
         */
        public static int diffRegionCharge(@NonNull StationRegion from, @NonNull StationRegion to) {
            return DIFF_REGION_CHARGE[from.code][to.code];
        }

        @NonNull
        public static StationRegion of(int code) {
            return Optional.ofNullable(stationRegionMap.get(code)).orElse(S0);
        }
    }

    @Getter
    @AllArgsConstructor
    enum StationType {

        /**
         * 分公司
         */
        BRANCH_OFFICE(1),
        /**
         * 營業站所
         */
        BUSINESS_STATION(2),
        /**
         * 調度
         */
        DISPATCH(3),
        /**
         * 高鐵 Taiwan High Speed Rail Corporation
         */
        THSR(4),
        /**
         * 分駐所
         */
        BRANCH_STATION(5),
        /**
         * 預約專線
         */
        APPOINTMENT_LINE(6),
        /**
         * 經銷商
         */
        DEALER(7),
        ;

        private static final Map<Integer, StationType> stationTypeMap = Arrays.stream(values()).collect(Collectors.toMap(StationType::getType, Function.identity()));
        private int type;

        /**
         * station of by type
         */
        @Nullable
        public static StationType of(@Nullable Integer type) {
            return stationTypeMap.get(type);
        }
    }

    @Getter
    @AllArgsConstructor
    enum NotAllowRtnDiffStation {

        /**
         * 台北車站
         */
        TAIPET_TRAIN_STATION("210"),
        ;

        private static final Map<String, NotAllowRtnDiffStation> notAllowRtnDiffStationMap =
            Arrays.stream(values()).collect(Collectors.toMap(NotAllowRtnDiffStation::getType, Function.identity()));
        private String type;

        public static boolean isContain(@NonNull String type) {
            return Objects.nonNull(notAllowRtnDiffStationMap.get(type));
        }
    }

    /**
     * 業務別
     */
    enum CarplusService {
        /**
         * 訂閱
         */
        SUB,
        /**
         * 短租
         */
        SHORT_RENT,
        /**
         * 中古
         */
        PREOWNED,
    }
}
