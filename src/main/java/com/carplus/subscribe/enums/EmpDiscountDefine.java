package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface EmpDiscountDefine {
    /**
     * 門市人員折扣類型
     */
    @Getter
    @AllArgsConstructor
    enum DiscountType {

        /**
         * 小時租金折抵
         */
        HOUR("小時租金折抵", "小時租金折抵"),
        /**
         * 指定額度折抵
         */
        AMOUNT("指定額度折抵", "訂閱折扣"),
        /**
         * 提早還車
         */
        EARLY_RETURN("提前還車退款", "提前還車退款"),
        /**
         * 延後還車
         */
        DELAY_RETURN("延後還車折扣", "延後還車折扣"),
        /**
         * 取消訂單
         */
        CANCEL("取消訂單退訂政策調整", "取消訂單退訂政策調整"),
        /**
         * 定價折扣
         */
        BASE_PRICE("定價折扣", "定價折扣"),
        /**
         * 延後付款折扣
         */
        DELAY_PAID("延後付款折扣", "延後付款折扣");

        private String title;
        private String subTitle;

        public String getTitle(boolean isSubscribe) {
            return isSubscribe ? subTitle : title;
        }
    }

    /**
     * 折扣審核人員階級
     */
    @Getter
    enum AuditorLevel {

        /**
         * 門市站長
         */
        MASTER,
        /**
         * 副站長
         */
        SUB_MASTER,
        /**
         * 門市分區督導
         */
        SUPERVISOR,
        /**
         * 門市部(部)主管
         */
        ASSISTANT_MANAGER,
        /**
         * 短租事業群(處)主管
         */
        SENIOR_MANAGER,
    }
}
