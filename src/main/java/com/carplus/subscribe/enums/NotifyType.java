package com.carplus.subscribe.enums;

import lombok.Getter;

public enum NotifyType {
    /**
     * 手機推播
     */
    PUSH,
    /**
     * 簡訊
     */
    SMS,
    /**
     * 信箱
     */
    EMAIL,
    /**
     * 漸強推送
     */
    MAAC;

    /**
     * 收件部門
     */
    @Getter
    public enum NotifyDepartment {
        /**
         * 調度中心 / 車輛調度管理課(全員)
         */
        DISPATCH_CENTER("dispatch-center"),
        SECURITY_DEPOSIT("security-deposit"),
        /**
         * 契約管理課
         */
        CONTRACT_MANAGER("contract-manager"),
        /**
         * 授信管理課
         */
        AUDIT_CENTER("audit-center"),
        /**
         * 訂閱車顧客服務
         */
        SUB("sub"),
        /**
         * 訂閱營業部 / 訂閱車業務部
         */
        SUB_BUSINESS("sub-business"),
        /**
         * 財務
         */
        FINANCE("finance");

        private final String yamlKey;

        NotifyDepartment(String yamlKey) {
            this.yamlKey = yamlKey;
        }

        public static NotifyDepartment fromYamlKey(String yamlKey) {
            for (NotifyDepartment dept : values()) {
                if (dept.yamlKey.equals(yamlKey)) {
                    return dept;
                }
            }
            throw new IllegalArgumentException("Unknown key: " + yamlKey);
        }
    }
}
