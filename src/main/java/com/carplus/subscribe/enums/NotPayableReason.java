package com.carplus.subscribe.enums;

import lombok.Getter;

public enum NotPayableReason {
    /**
     * 無
     */
    None("0"),
    /**
     * 客戶要求優惠
     */
    AcctNeedCoupon("1"),
    /**
     * 使用人不付
     */
    AcctRejectPayIt("2"),
    /**
     * 時間差
     */
    OffSetTime("3"),
    /**
     * 系統異常
     */
    SystemError("4"),
    /**
     * 替代輸送
     */
    Transport("5"),
    /**
     * 客戶試乘
     */
    AcctTestDrive("6"),
    /**
     * 其他(備註)
     */
    Other("99"),
    ;

    @Getter
    private final String typeCode;

    NotPayableReason(String typeCode) {
        this.typeCode = typeCode;
    }
}
