package com.carplus.subscribe.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum MemberLevel {

    /**
     * 一般客戶
     */
    GENERAL("3", "GENERAL", "一般會員"),
    /**
     * 格上員工
     */
    VIP_CARPLUS("4", "VIP_CARPLUS", "格上員工"),
    /**
     * 集團員工
     */
    VIP_GROUP("5", "VIP_SELF", "集團員工"),
    /**
     * VVIP
     */
    VVIP("6", "VIP_SELF", "VVIP"),
    /**
     * 國軍
     */
    VIP_SOLDIER("7", "VIP_SOLDIER", "國軍"),
    /**
     * 長租
     */
    VIP_LRENTAL("8", "VIP_LRENTAL", "長租"),
    /**
     * 特約廠商
     */
    VIP_SPECIAL("9", "VIP_SPECIAL", "特約廠商"),
    ;

    private static final Map<String, MemberLevel> memberLevelMap = Arrays.stream(values()).collect(Collectors.toMap(MemberLevel::getCustRankCode, Function.identity()));
    private static final Map<String, MemberLevel> memberLevelMapByName = Arrays.stream(values()).collect(Collectors.toMap(MemberLevel::getName, Function.identity()));
    private String custRankCode;
    private String priceLevel;
    @JsonValue
    private String name;

    /**
     * 調整序列化 value 邏輯，考慮兼容
     */
    @NonNull
    @JsonCreator
    public static MemberLevel of(@Nullable String name) {
        try {
            return valueOf(name);
        } catch (Exception ignore) {
            // ignore
        }

        return Optional.ofNullable(memberLevelMapByName.get(name))
            .orElseGet(() -> ofRankCode(name));
    }

    @NonNull
    public static MemberLevel ofRankCode(@Nullable String custRankCode) {
        return Optional.ofNullable(memberLevelMap.get(custRankCode)).orElse(GENERAL);
    }
}
