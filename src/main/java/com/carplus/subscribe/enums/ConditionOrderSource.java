package com.carplus.subscribe.enums;

import lombok.Getter;

public enum ConditionOrderSource {

    Carplus(0, "格上"),
    SeaLand(1, "SeaLand"),
    ;

    @Getter
    private final int code;

    @Getter
    private final String desc;

    ConditionOrderSource(int code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public static ConditionOrderSource codeOfValue(int code) {
        for (ConditionOrderSource status : ConditionOrderSource.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return null;
    }

}
