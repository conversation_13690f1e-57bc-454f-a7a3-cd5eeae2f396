package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderStatus {

    PROCESSING(0, "未成立"),
    CREDIT_PENDING(1, "審核中"),
    CREDITED(2, "待付款"),
    RESERVATION(8, "門市預約單 待付款"),
    BOOKING(10, "已訂車"),
    DEPART(50, "已出車未還車"),
    RENEW(70, "續約展期、本單結束"),
    ARRIVE_NO_CLOSE(80, "已還車未結案"),
    STOLEN(81, "失竊未結案"),
    CLOSE_WITH_SUB(89, "已還車(關聯續約單未出車)"),
    CLOSE(90, "已還車"),
    CREDIT_REJECT(98, "審核失敗"),
    CANCEL(99, "已取消"),
    ;

    private static final Map<Integer, OrderStatus> statusMap = Arrays.stream(values()).collect(Collectors.toMap(OrderStatus::getStatus, Function.identity()));
    private final int status;
    private final String name;

    @NonNull
    public static OrderStatus of(@Nullable Integer status) {
        return Optional.ofNullable(statusMap.get(status)).orElse(PROCESSING);
    }

    /**
     * 舊訂單狀態＋契約狀態 migrate MYSQL 新訂單狀態
     *
     * @param rsvStatus      MSSQL 訂單狀態
     * @param contractStatus MSSQL舊契約狀態
     */
    @NonNull
    public static OrderStatus of(@NonNull String rsvStatus, @Nullable String contractStatus) {
        if (contractStatus != null && !"10".equals(contractStatus)) {
            return of(Integer.parseInt(contractStatus));
        }

        return of(Integer.parseInt(rsvStatus));
    }

    public boolean departNeedGeneratePDF() {
        return this == DEPART || this == BOOKING;
    }

    public boolean returnNeedGeneratePDF() {
        return this == ARRIVE_NO_CLOSE || this == CLOSE_WITH_SUB || this == CLOSE || this == STOLEN;
    }

    public boolean isSumOrder() {
        return this == BOOKING || this == DEPART || this == ARRIVE_NO_CLOSE || this == CLOSE_WITH_SUB || this == CLOSE || this == STOLEN;
    }

    public boolean isNeedPay() {
        return !(this == CREDIT_PENDING || this == CANCEL || this == CREDIT_REJECT);
    }

    /**
     * 汽車用品是否可出貨
     */
    public boolean isSkuShipmentAvailable() {
        return Arrays.asList(BOOKING, DEPART, ARRIVE_NO_CLOSE, STOLEN).contains(this);
    }
}
