package com.carplus.subscribe.enums;

public enum RenewableType {
    NONE(0, "無"),
    RENEWABLE(1, "可續約"),
    NON_RENEWABLE(2, "不可續約");

    private final int code;
    private final String description;

    RenewableType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RenewableType fromCode(int code) {
        for (RenewableType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
