package com.carplus.subscribe.enums;

import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public enum ChargeType {

    /**
     * EDC-台新銀
     */
    EDC_TAISHIN(1, "EDC-台新銀", "********", PaymentType.EDC),
    /**
     * EDC-聯信(AE)
     */
    EDC_AE(2, "EDC-聯信(AE)", "********-5", PaymentType.EDC),
    /**
     * EDC-聯信(花旗)
     */
    EDC_CITIBANK(10, "EDC-聯信(花旗)", "********-9", PaymentType.EDC),
    /**
     * 傳刷-台新銀
     */
    FAX_TAISHIN(6, "傳刷-台新銀", "********-2", PaymentType.Fax),
    /**
     * 網路刷卡-中信銀
     */
    INT_CTBC(7, "網路刷卡-中信銀", "********-4", PaymentType.CreditCard),
    /**
     * 網路刷卡-台新銀
     */
    INT_TAISHIN(11, "網路刷卡-台新銀", "********-3", PaymentType.CreditCard),
    /**
     * 簡訊刷卡-台新銀
     */
    SMS_TAISHIN(12, "簡訊刷卡-台新銀", "********-4", PaymentType.SMS),
    /**
     * Tappay-中信銀
     */
    Tappay_CTBC(14, "中信銀Tappay", "********-9", PaymentType.CreditCard),
    /**
     * Tappay-台新銀
     */
    Tappay_TAISHIN(15, "台新銀Tappay", "********-6", PaymentType.CreditCard),
    /**
     * Tappay-聯信
     */
    Tappay_NCCC(16, "聯信-TAPPAY", "********-0", PaymentType.CreditCard),
    /**
     * 手刷-台新銀
     */
    HAND_TAISHIN(3, "手刷-台新銀", "********-1", PaymentType.CreditCard),
    /**
     * 手刷-聯信
     */
    HAND_LANESHIN(4, "手刷-聯信", "********-4", PaymentType.CreditCard),
    /**
     * 手刷-匯豐
     */
    HAND_HSBC(5, "手刷-匯豐", "********-4", PaymentType.CreditCard),
    /**
     * 台新銀行後台
     */
    BE_TAISHIN(13, "台新銀行後台", "********-5", PaymentType.BankBE),
    ;

    private static final Map<Integer, ChargeType> chargeTypeMap = Arrays.stream(values()).collect(Collectors.toMap(ChargeType::getCreditBankAuto, Function.identity()));
    private static final Map<Integer, String> codes = Arrays.stream(values()).collect(Collectors.toMap(ChargeType::getCreditBankAuto, ChargeType::getBankUnitCode));
    @Getter
    private final int creditBankAuto;
    @Getter
    private final String name;
    @Getter
    private final String bankUnitCode;
    @Getter
    private final PaymentType paymentType;

    ChargeType(int creditBankAuto, String name, String bankUnitCode, PaymentType paymentType) {
        this.creditBankAuto = creditBankAuto;
        this.name = name;
        this.bankUnitCode = bankUnitCode;
        this.paymentType = paymentType;
    }

    @Nullable
    public static ChargeType of(@Nullable Integer creditBankAuto) {
        return chargeTypeMap.get(creditBankAuto);
    }

    public static String getBankUnitCodeByCreditBankAuto(int creditBankAuto) {
        return codes.get(creditBankAuto);
    }
}
