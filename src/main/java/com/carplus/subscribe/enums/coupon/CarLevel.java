package com.carplus.subscribe.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;

@Getter
@AllArgsConstructor
public enum CarLevel implements Serializable {
    A("小型車", "A"),
    B("中型車", "B"),
    C("大型車", "C"),
    D("商旅車", "D"),
    E1("高價車1", "E1"),
    E2("高價車2", "E2"),
    E3("高價車3", "E3"),
    E("高價車", "E"),
    F("貨車", ""),
    G("機車", ""),
    H("限時優惠", ""),
    I("高價車I", "I");

    private final String srentalLeveName;
    private final String car2goLevelName;
}
