package com.carplus.subscribe.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface CouponDefine {

    @Getter
    @AllArgsConstructor
    enum DiscountType {
        AMOUNT(1, "折價"),
        PERCENTAGE(2, "折扣");

        private final Integer code;
        private final String description;

        private static final Map<Integer, DiscountType> map = Stream.of(values()).collect(Collectors.toMap(DiscountType::getCode, Function.identity()));

        public static DiscountType of(Integer code) {
            return map.get(code);
        }

        private static final Map<String, DiscountType> descMap = Stream.of(values()).collect(Collectors.toMap(DiscountType::getDescription, Function.identity()));

        public static DiscountType ofDesc(String desc) {
            return descMap.get(desc);
        }
    }

    @Getter
    @AllArgsConstructor
    enum SubDiscountType {
        AMOUNT(1, "折價", DiscountType.AMOUNT),
        PERCENTAGE(2, "折扣", DiscountType.PERCENTAGE),
        FULL_AMOUNT_DISCOUNT(3, "滿額折價", DiscountType.AMOUNT),;

        private final Integer code;
        private final String description;
        private final DiscountType parentType;

        private static final Map<Integer, SubDiscountType> map = Stream.of(values()).collect(Collectors.toMap(SubDiscountType::getCode, Function.identity()));

        public static SubDiscountType of(Integer code) {
            return map.get(code);
        }
    }

    @Getter
    @AllArgsConstructor
    enum CouponSequenceStatus {
        NotUsed(0, "未使用"),
        Used(1, "已使用"),
        Invalid(2, "作廢"),
        ;

        private final Integer status;
        private final String description;

        private static final Map<Integer, CouponSequenceStatus> map = Stream.of(values()).collect(Collectors.toMap(CouponSequenceStatus::getStatus, Function.identity()));

        public static CouponSequenceStatus of(Integer status) {
            return map.get(status);
        }
    }
}