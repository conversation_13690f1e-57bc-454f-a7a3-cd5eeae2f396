package com.carplus.subscribe.enums.coupon;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SourceItem {
    SMART2GO("SMART2GO", "汽車共享"),
    SMARTTAXI("SMARTTAXI", "無碳計程車"),
    DRIVER_T("DRIVER_T", "旅遊專車"),
    DRIVER_B("DRIVER_B", "商務專車"),
    DRIVER_A("DRIVER_A", "機場接送"),
    DRIVER_D("DRIVER_D", "長照專車"),
    OFFICAL("OFFICAL", "短期租車"),
    TWDD("TWDD", "代駕服務"),
    GOSMART("GOSMART", "APP"),
    COUPON("COUPON", "管理後台"),
    RENT("RENT", "舊短期"),
    LEASING("LEASING", "長租"),
    SUBSCRIBE("SUBSCRIBE", "訂閱"),
    YES_CHARGING("YES_CHARGING", "充電"),
    TWTAXI("TWTAXI", "計程車"),
    ;

    private final String code;
    private final String description;

    @Override
    public String toString() {
        return code + ": " + description;
    }
}
