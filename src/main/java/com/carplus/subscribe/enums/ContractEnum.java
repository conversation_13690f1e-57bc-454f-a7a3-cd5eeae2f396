package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface ContractEnum {
    /**
     * 契約狀態
     * das_db.dbo.idcode.cbokind = '契約狀態'
     */
    @Getter
    @AllArgsConstructor
    enum ContractStatus {
        conttract_normal("0", "正常"),
        contract_ChangePlateNo("1", "換牌"),
        contract_Terminate("2", "中途解約"),
        contract_Expired("3", "期滿"),
        contract_EndDispatch("4", "調度結束"),
        change_taxID("6", "統編更換"),
        transfer("8", "三方移轉"),
        renew_sRental("9", "短租續約"),
        contract_Invalid("X", "無效");

        private String statusCode;
        private String memo;
    }

    /**
     * 契約類型
     * for 程式邏輯使用
     */
    @Getter
    @AllArgsConstructor
    enum ContractType {
        add_sRental("add_sRental", "新增-因購車產生的短租約"),
        add_s2g("add_s2g", "新增-因購車產生的S2G約"),
        add_carCenter("add_carCenter", "新增-調度約"),
        renew_sRental("renew_sRental", "續約-短租/S2G約"),
        renew_carCenter("renew_carCenter", "續約-調度約"),
        add_assign_carCenter("add_assign_carCenter", "新增-因撥車產生的調度約"),
        add_assign_sRental("add_assign_sRental", "新增-因撥車產生的短租/S2G約"),
        add_sRental_project("add_sRental_project", "新增-短租專案採購"),
        add_customer_subscribe("add_customer_subscribe", "新增-訂閱客戶約"),
        add_assign_carCenter_project("add_assign_carCenter_project", "新增-調度專案採購"),
        ;
        private String type;
        private String memo;
    }


}
