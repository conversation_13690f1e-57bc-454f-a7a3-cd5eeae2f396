package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式
 */
@Getter
@AllArgsConstructor
public enum PaymentType {

    /*----- 線上支付 -----*/
    CreditCard("CreditCard", "網刷"),
    LinePay("LinePay", "Line Pay"),
    JkoPay("JkoPay", "街口支付"),
    EasyPay("EasyPay", "悠遊付"),
    ApplePay("ApplePay", "Apple Pay"),
    SMS("Sms", "簡訊刷卡"),
    /*----- 線下支付 -----*/
    EDC("EDC", "刷卡機"),
    Cash("Cash", "現金"),
    Fax("Fax", "傳刷"),
    Remit("Remit", "匯款"),
    BankBE("BankBE", "銀行後台"),
    ;


    private String code;
    private String name;
}