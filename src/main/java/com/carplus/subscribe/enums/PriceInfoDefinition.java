package com.carplus.subscribe.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface PriceInfoDefinition {
    @AllArgsConstructor
    @Getter
    enum PriceInfoCategory {
        /**
         * 保證金
         */
        SecurityDeposit("保證金", null),
        /**
         * 月租費
         */
        MonthlyFee("基本月費", null),
        /**
         * 里程費
         */
        MileageFee("里程費", null),
        /**
         * 保險
         */
        Insurance("其他駕駛保障", 660), // 原 PriceInfoConstant.disclaimerFee
        /**
         * 代步車
         */
        Replacement("出險代步車", 966), // 原 PriceInfoConstant.replacementCarFee
        /**
         * 調度費
         */
        Dispatch("跨區調度費", 4000), // 原 PriceInfoConstant.dispatchFee
        /**
         * 充電儲值金
         */
        YesCharging("YES充電點數", null),
        /**
         * 逾期付款
         */
        PayLate("逾期付款", null),
        /**
         * 提早還車
         */
        ReturnEarly("提前解約", null),
        /**
         * 逾期還車
         */
        ReturnLate("逾期還車", null),
        /**
         * 車損
         */
        CarAccident("車損自負額", null),
        /**
         * 取消訂單
         */
        CancelBooking("退訂退款費用", null),
        /**
         * 其他
         */
        Others("其他費用", null),
        /**
         * 門市折扣
         */
        EmpDiscount("門市折扣", null),
        /**
         * ETag費用
         */
        ETag("ETag費用", null),
        /**
         * 汽車用品
         */
        Merchandise("汽車用品", null),
        /**
         * 票券折扣
         */
        CouponDiscount("票券折扣", null),
        ;

        private final String descriptionName;

        private final Integer stdPrice;

        public Integer getSortIndex() {
            return sortMap.get(this);
        }

        private static final Map<PriceInfoCategory, Integer> sortMap;

        static {
            AtomicInteger i = new AtomicInteger();
            sortMap = Arrays.stream(PriceInfoCategory.values()).collect(Collectors.toMap(Function.identity(), k -> i.getAndIncrement()));
        }

        /**
         * 檢查費用類別是否允許作為額外費用新增
         */
        public static boolean canAddAsExtraFee(PriceInfoCategory category) {
            return getCategoriesAddableAsExtraFee().contains(category);
        }

        /**
         * 目前後台可設定新增額外費用的種類
         */
        public static ArrayList<PriceInfoCategory> getCategoriesAddableAsExtraFee() {
            return Lists.newArrayList(Others, Dispatch, Insurance, Replacement);
        }
    }

    @AllArgsConstructor
    @Getter
    enum PriceInfoType {
        /**
         * 付款
         */
        Pay(0, "付款"),
        /**
         * 折扣
         */
        Discount(1, "折扣"),
        /**
         * 退款
         */
        Refund(2, "退款");

        private static final Map<Integer, PriceInfoType> map = Arrays.stream(values()).collect(Collectors.toMap(PriceInfoType::getCode, Function.identity()));
        private int code;
        private String descriptionName;

        /**
         * station of by type
         */
        @Nullable
        public static PriceInfoType of(@Nullable Integer code) {
            return map.get(code);
        }
    }

    @AllArgsConstructor
    @Getter
    enum AmtCategory {
        PAID_AMOUNT("已付款金額"),
        UNPAID_AMOUNT("未付款金額"),
        EMP_DISCOUNT_AMOUNT("門市折扣金額"),
        RECEIVABLE_AMOUNT("應收總金額"),
        ORIGINAL_RECEIVABLE_AMOUNT("原始應收總金額"),
        //        CURRENT_RECEIVABLE_AMOUNT("當下應收金額"),
        RENT_AMOUNT("租金金額"),
        EXT_AMOUNT("額外費用金額"),
        CANCEL_AMOUNT("訂單取消罰金"),
        ACCIDENT_AMOUNT("車損費用"),
        ACCIDENT_RECEIVABLE_AMOUNT("應收車損費用");

        private String name;
    }

    enum SortDirection {
        ASC, DESC
    }
}
