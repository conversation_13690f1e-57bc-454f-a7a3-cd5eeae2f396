package com.carplus.subscribe.enums;

import lombok.Getter;

public enum LandmarksCategory {

    MRT("捷運站", "Metro Rail Transit Station"),
    TRT("火車站", "Train Rail Transit Station"),
    LRT("輕軌站", "Light Rail Transit Station"),
    THSR("高鐵站", "Taiwan High Speed Rail Station"),
    AIRPORT("機場", "Airport"),
    ITH("轉運站", "Integrated Transport Hub"),
    ;

    @Getter
    private final String name;

    @Getter
    private final String desc;

    LandmarksCategory(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

}
