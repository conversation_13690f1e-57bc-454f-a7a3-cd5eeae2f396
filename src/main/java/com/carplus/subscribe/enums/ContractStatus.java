package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ContractStatus {
    /**
     * 建立
     */
    CREATE(0, "建立"),
    /**
     * 進行中
     */
    GOING(1, "進行中"),
    /**
     * 完成
     */
    COMPLETE(10, "完成"),
    /**
     * 取消
     */
    CANCEL(99, "取消"),
    ;
    private int code;

    private String name;

    public static ContractStatus codeOfValue(int code) {
        for (ContractStatus status : ContractStatus.values()) {
            if (code == status.getCode()) {
                return status;
            }
        }
        return null;
    }

}
