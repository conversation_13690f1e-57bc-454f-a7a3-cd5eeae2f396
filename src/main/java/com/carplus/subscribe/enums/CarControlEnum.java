package com.carplus.subscribe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public interface CarControlEnum {

    /**
     * 車輛管制狀態
     * serviceCode=car_control-controlStatus
     */
    @Getter
    @AllArgsConstructor
    enum ControlStatus {
        open("open"),
        close("close"),
        ;

        String code;
    }

    /**
     * 車輛管制事由
     */
    @Getter
    @AllArgsConstructor
    enum ChangeType {
        applyAssign("0001", "撥車申請", false),
        cancelOut("0002", "取消出車", true),
        completeOut("0003", "完成出車", false),
        secondHandReceive("0004", "中古收車作業", true),
        autoAssign("0005", "自動撥車申請", false),
        autoLeaveCar("0006", "自動出車作業", false),
        autoSecondHandReceive("0007", "自動中古收車作業", true),
        setIsOpen_open("0008", "庫存車狀態開啟", true),
        setIsOpen_close("0009", "庫存車狀態關閉", false),
        newCar("0018", "新增車籍", false),
        // 以下狀態不在CRS觸發
//        lRentalContract("0010", "長租契約成立"),
//        lRentalDeal("0011", "長租成交"),
        secondHandDeal("0012", "中古出售申請(成交)", false),
        autoDealOpen("0013", "中古出售駁回(取消成交)", true),
        secondHandSell("0014", "中古車出售核準", false),
        secondHandReturnSell("0015", "中古車退購核準", true),
        subscribeReceive("0016", "訂閱收訂", false),
        subscribeReturn("0017", "訂閱退訂", true),

        ChangeType_0023("0023", "撥車取消申請", true),
        ChangeType_0024("0024", "訂閱完成收訂", true),
        ;

        // 事由代碼
        private String changeType;

        // 事由
        private String changeMemo;

        // 是否開放撥車
        private Boolean isOpen;

        private static final Map<String, ChangeType> map = Arrays.stream(values()).collect(Collectors.toMap(ChangeType::getChangeType, Function.identity()));

        public static ChangeType of(@Nullable String code) {
            return map.getOrDefault(code, null);
        }
    }
}
