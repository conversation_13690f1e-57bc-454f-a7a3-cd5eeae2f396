<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="Blank_A4" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="0" bottomMargin="0" uuid="bfe835cc-05db-4ba2-b259-0d154470283b">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="surround1" class="java.lang.Object"/>
	<parameter name="surround2" class="java.lang.Object"/>
	<parameter name="surround3" class="java.lang.Object"/>
	<parameter name="surround4" class="java.lang.Object"/>
	<parameter name="insight1" class="java.lang.Object"/>
	<parameter name="insight2" class="java.lang.Object"/>
	<parameter name="insight3" class="java.lang.Object"/>
	<parameter name="etag" class="java.lang.Object"/>
	<query language="sql"><![CDATA[]]></query>
	<title height="390" splitType="Stretch">
		<element kind="frame" uuid="db6d5d53-7c8d-4794-92ae-261837b74a31" x="3" y="30" width="550" height="165">
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<element kind="staticText" uuid="c2a6580e-a044-4c59-a8c8-db886609d3db" mode="Opaque" x="0" y="0" width="550" height="20" backcolor="#D9D9D9" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[外觀照片 Exterior Photos]]></text>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<pen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="*************-4627-bc11-173b475346d5" x="0" y="20" width="137" height="145">
				<expression><![CDATA[$P{surround1}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="853b03cd-b72d-4bbe-a715-01dbeb8839b0" x="137" y="20" width="137" height="145">
				<expression><![CDATA[$P{surround2}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="2c4d505e-99e0-42b9-b26e-5c74a3dc9879" x="274" y="20" width="137" height="145">
				<expression><![CDATA[$P{surround3}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="0d9fd852-5428-4d2e-9cf1-3b649195ec35" x="411" y="20" width="139" height="145">
				<expression><![CDATA[$P{surround4}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
		</element>
		<element kind="staticText" uuid="c3b23f5c-cd9d-4b34-82b0-abb3efdd15d4" x="3" y="0" width="550" height="30" fontName="微軟正黑體" fontSize="14.0" bold="true" hTextAlign="Center" vTextAlign="Middle">
			<text><![CDATA[車 況 圖 Vehicle Condition Photos]]></text>
		</element>
		<element kind="frame" uuid="6d18907c-7090-4217-b653-17ee1958c182" x="3" y="195" width="550" height="175">
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<element kind="staticText" uuid="c383d4a2-e7c9-4bd0-9747-cab1ac725c75" mode="Opaque" x="0" y="0" width="411" height="30" backcolor="#D9D9D9" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[內裝照片 Interior Photos]]></text>
				<box>
					<pen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="staticText" uuid="aa8e0c85-6cb3-4495-9e8c-ca43aa480c88" mode="Opaque" x="411" y="0" width="139" height="30" backcolor="#D9D9D9" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[eTag]]></text>
				<box>
					<pen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="85b0f43d-f3c5-4ca5-ae88-200ba282d8ee" x="137" y="30" width="137" height="145">
				<expression><![CDATA[$P{insight2}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="71f13206-10b6-478e-8dcb-ac0ea9561af7" x="0" y="30" width="137" height="145">
				<expression><![CDATA[$P{insight1}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="d286d3fa-ba78-41bc-86a9-dfc758d65a1d" x="274" y="30" width="137" height="145">
				<expression><![CDATA[$P{insight3}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="image" uuid="1b703e7f-9dc8-4ad4-89e2-6fd0273a6de5" x="411" y="30" width="139" height="145">
				<expression><![CDATA[$P{etag}]]></expression>
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
		</element>
	</title>
</jasperReport>
