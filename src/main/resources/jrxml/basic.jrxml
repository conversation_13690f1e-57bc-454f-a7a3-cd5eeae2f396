<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="basic" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape"
              whenNoDataType="AllSectionsNoDetail" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0"
              bottomMargin="0" uuid="3eb19b49-8325-4f1b-9b79-d06491056f40">
    <property name="com.jaspersoft.studio.unit." value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
    <parameter name="custInfoReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="departInfo" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="returnInfo" class="net.sf.jasperreports.engine.JasperReport"/>
    <!--    cust info-->
    <parameter name="acctName" class="java.lang.String"/>
    <parameter name="birthday" class="java.lang.String"/>
    <parameter name="idNo" class="java.lang.String"/>
    <parameter name="mainCell" class="java.lang.String"/>
    <parameter name="email" class="java.lang.String"/>
    <parameter name="address" class="java.lang.String"/>
    <parameter name="plateNo" class="java.lang.String"/>
    <parameter name="engineNo" class="java.lang.String"/>
    <parameter name="carColor" class="java.lang.String"/>
    <parameter name="carModelName" class="java.lang.String"/>
    <parameter name="securityDeposit" class="java.lang.String"/>
    <parameter name="monthlyFee" class="java.lang.String"/>
    <parameter name="mileageFee" class="java.lang.String"/>
    <parameter name="expectDepartDate" class="java.lang.String"/>
    <parameter name="expectReturnDate" class="java.lang.String"/>
    <parameter name="expectDepartStation" class="java.lang.String"/>
    <parameter name="expectReturnStation" class="java.lang.String"/>
    <!--    cust info end-->
    <!--      depart info-->
    <parameter name="startDate" class="java.lang.String"/>
    <parameter name="mileageOut" class="java.lang.String"/>
    <parameter name="fuelOut" class="java.lang.String"/>
    <parameter name="tireDepthDepart" class="java.lang.String"/>
    <parameter name="warningLightDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="odorDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="seatAndTrimDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="carpetAndGloveboxDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="engineDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="acDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="windowAndDoorMirrorDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="audioAndMultimediaDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="vehicleLicenseDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="hasKeyDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="keyCountDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["   "]]></defaultValueExpression>
    </parameter>
    <parameter name="dashCamsDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="trunkLidDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="departRemark" class="java.lang.String"/>
    <parameter name="departMemberSignImage" class="java.lang.Object"/>
    <parameter name="departCustSignImage" class="java.lang.Object"/>
    <parameter name="departMemberSignDate" class="java.lang.String"/>
    <parameter name="departCustSignDate" class="java.lang.String"/>
    <!--    depart info end-->
    <!--	return info-->
    <parameter name="endDate" class="java.lang.String"/>
    <parameter name="mileageIn" class="java.lang.String"/>
    <parameter name="fuelIn" class="java.lang.String"/>
    <parameter name="tireDepth" class="java.lang.String"/>
    <parameter name="warningLight" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="odor" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="seatAndTrim" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="carpetAndGlovebox" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="engine" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="ac" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="windowAndDoorMirror" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="audioAndMultimedia" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="vehicleLicense" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="hasKey" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="keyCount" class="java.lang.String">
        <defaultValueExpression><![CDATA["   "]]></defaultValueExpression>
    </parameter>
    <parameter name="dashCams" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="trunkLid" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="returnRemark" class="java.lang.String"/>
    <parameter name="returnMemberSignImage" class="java.lang.Object"/>
    <parameter name="returnCustSignImage" class="java.lang.Object"/>
    <parameter name="returnMemberSignDate" class="java.lang.String"/>
    <parameter name="returnCustSignDate" class="java.lang.String"/>
    <!--	return info end-->
    <title height="694" splitType="Stretch">
        <element kind="subreport" uuid="742eedda-c9e7-4139-bbc4-22e205401d76" x="0" y="0" width="595" height="190">
            <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
            <expression><![CDATA[$P{custInfoReport}]]></expression>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <parameter name="acctName">
                <expression><![CDATA[$P{acctName}]]></expression>
            </parameter>
            <parameter name="birthday">
                <expression><![CDATA[$P{birthday}]]></expression>
            </parameter>
            <parameter name="idNo">
                <expression><![CDATA[$P{idNo}]]></expression>
            </parameter>
            <parameter name="mainCell">
                <expression><![CDATA[$P{mainCell}]]></expression>
            </parameter>
            <parameter name="email">
                <expression><![CDATA[$P{email}]]></expression>
            </parameter>
            <parameter name="address">
                <expression><![CDATA[$P{address}]]></expression>
            </parameter>
            <parameter name="plateNo">
                <expression><![CDATA[$P{plateNo}]]></expression>
            </parameter>
            <parameter name="engineNo">
                <expression><![CDATA[$P{engineNo}]]></expression>
            </parameter>
            <parameter name="carColor">
                <expression><![CDATA[$P{carColor}]]></expression>
            </parameter>
            <parameter name="carModelName">
                <expression><![CDATA[$P{carModelName}]]></expression>
            </parameter>
            <parameter name="securityDeposit">
                <expression><![CDATA[$P{securityDeposit}]]></expression>
            </parameter>
            <parameter name="monthlyFee">
                <expression><![CDATA[$P{monthlyFee}]]></expression>
            </parameter>
            <parameter name="mileageFee">
                <expression><![CDATA[$P{mileageFee}]]></expression>
            </parameter>
            <parameter name="expectDepartDate">
                <expression><![CDATA[$P{expectDepartDate}]]></expression>
            </parameter>
            <parameter name="expectReturnDate">
                <expression><![CDATA[$P{expectReturnDate}]]></expression>
            </parameter>
            <parameter name="expectDepartStation">
                <expression><![CDATA[$P{expectDepartStation}]]></expression>
            </parameter>
            <parameter name="expectReturnStation">
                <expression><![CDATA[$P{expectReturnStation}]]></expression>
            </parameter>
        </element>
        <element kind="subreport" uuid="1be01eac-c258-44a3-a7b6-9893ce53e161" x="0" y="190" width="595" height="256">
            <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
            <expression><![CDATA[$P{departInfo}]]></expression>
            <!--                depart info-->
            <parameter name="startDate">
                <expression><![CDATA[$P{startDate}]]></expression>
            </parameter>
            <parameter name="mileageOut">
                <expression><![CDATA[$P{mileageOut}]]></expression>
            </parameter>
            <parameter name="fuelOut">
                <expression><![CDATA[$P{fuelOut}]]></expression>
            </parameter>
            <parameter name="tireDepthDepart">
                <expression><![CDATA[$P{tireDepthDepart}]]></expression>
            </parameter>
            <parameter name="warningLightDepart">
                <expression><![CDATA[$P{warningLightDepart}]]></expression>
            </parameter>
            <parameter name="odorDepart">
                <expression><![CDATA[$P{odorDepart}]]></expression>
            </parameter>
            <parameter name="seatAndTrimDepart">
                <expression><![CDATA[$P{seatAndTrimDepart}]]></expression>
            </parameter>
            <parameter name="carpetAndGloveboxDepart">
                <expression><![CDATA[$P{carpetAndGloveboxDepart}]]></expression>
            </parameter>
            <parameter name="engineDepart">
                <expression><![CDATA[$P{engineDepart}]]></expression>
            </parameter>
            <parameter name="acDepart">
                <expression><![CDATA[$P{acDepart}]]></expression>
            </parameter>
            <parameter name="windowAndDoorMirrorDepart">
                <expression><![CDATA[$P{windowAndDoorMirrorDepart}]]></expression>
            </parameter>
            <parameter name="audioAndMultimediaDepart">
                <expression><![CDATA[$P{audioAndMultimediaDepart}]]></expression>
            </parameter>
            <parameter name="vehicleLicenseDepart">
                <expression><![CDATA[$P{vehicleLicenseDepart}]]></expression>
            </parameter>
            <parameter name="hasKeyDepart">
                <expression><![CDATA[$P{hasKeyDepart}]]></expression>
            </parameter>
            <parameter name="keyCountDepart">
                <expression><![CDATA[$P{keyCountDepart}]]></expression>
            </parameter>
            <parameter name="dashCamsDepart">
                <expression><![CDATA[$P{dashCamsDepart}]]></expression>
            </parameter>
            <parameter name="trunkLidDepart">
                <expression><![CDATA[$P{trunkLidDepart}]]></expression>
            </parameter>
            <parameter name="departRemark">
                <expression><![CDATA[$P{departRemark}]]></expression>
            </parameter>
            <parameter name="departMemberSignImage">
                <expression><![CDATA[$P{departMemberSignImage}]]></expression>
            </parameter>
            <parameter name="departCustSignImage">
                <expression><![CDATA[$P{departCustSignImage}]]></expression>
            </parameter>
            <parameter name="departMemberSignDate">
                <expression><![CDATA[$P{departMemberSignDate}]]></expression>
            </parameter>
            <parameter name="departCustSignDate">
                <expression><![CDATA[$P{departCustSignDate}]]></expression>
            </parameter>
            <!--                depart info end-->
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <property name="com.jaspersoft.studio.unit.y" value="px"/>
        </element>
        <element kind="subreport" uuid="addda2b4-4c94-4f21-8ffe-181d9f04b92a" x="0" y="446" width="595" height="242">
            <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
            <expression><![CDATA[$P{returnInfo}]]></expression>
            <!--                return info-->
            <parameter name="endDate">
                <expression><![CDATA[$P{endDate}]]></expression>
            </parameter>
            <parameter name="mileageIn">
                <expression><![CDATA[$P{mileageIn}]]></expression>
            </parameter>
            <parameter name="fuelIn">
                <expression><![CDATA[$P{fuelIn}]]></expression>
            </parameter>
            <parameter name="tireDepth">
                <expression><![CDATA[$P{tireDepth}]]></expression>
            </parameter>
            <parameter name="warningLight">
                <expression><![CDATA[$P{warningLight}]]></expression>
            </parameter>
            <parameter name="odor">
                <expression><![CDATA[$P{odor}]]></expression>
            </parameter>
            <parameter name="seatAndTrim">
                <expression><![CDATA[$P{seatAndTrim}]]></expression>
            </parameter>
            <parameter name="carpetAndGlovebox">
                <expression><![CDATA[$P{carpetAndGlovebox}]]></expression>
            </parameter>
            <parameter name="engine">
                <expression><![CDATA[$P{engine}]]></expression>
            </parameter>
            <parameter name="ac">
                <expression><![CDATA[$P{ac}]]></expression>
            </parameter>
            <parameter name="windowAndDoorMirror">
                <expression><![CDATA[$P{windowAndDoorMirror}]]></expression>
            </parameter>
            <parameter name="audioAndMultimedia">
                <expression><![CDATA[$P{audioAndMultimedia}]]></expression>
            </parameter>
            <parameter name="vehicleLicense">
                <expression><![CDATA[$P{vehicleLicense}]]></expression>
            </parameter>
            <parameter name="hasKey">
                <expression><![CDATA[$P{hasKey}]]></expression>
            </parameter>
            <parameter name="keyCount">
                <expression><![CDATA[$P{keyCount}]]></expression>
            </parameter>
            <parameter name="dashCams">
                <expression><![CDATA[$P{dashCams}]]></expression>
            </parameter>
            <parameter name="trunkLid">
                <expression><![CDATA[$P{trunkLid}]]></expression>
            </parameter>
            <parameter name="returnRemark">
                <expression><![CDATA[$P{returnRemark}]]></expression>
            </parameter>
            <parameter name="returnMemberSignImage">
                <expression><![CDATA[$P{returnMemberSignImage}]]></expression>
            </parameter>
            <parameter name="returnCustSignImage">
                <expression><![CDATA[$P{returnCustSignImage}]]></expression>
            </parameter>
            <parameter name="returnMemberSignDate">
                <expression><![CDATA[$P{returnMemberSignDate}]]></expression>
            </parameter>
            <parameter name="returnCustSignDate">
                <expression><![CDATA[$P{returnCustSignDate}]]></expression>
            </parameter>
            <!--                return info end-->
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <property name="com.jaspersoft.studio.unit.y" value="px"/>
        </element>
        <property name="com.jaspersoft.studio.unit.height" value="px"/>
    </title>
</jasperReport>
