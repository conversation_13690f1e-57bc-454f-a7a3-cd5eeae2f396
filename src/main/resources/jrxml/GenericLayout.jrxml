<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="Blank_A4" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="20" bottomMargin="20" uuid="c346e0c9-e2fe-4c2c-a4f6-1730a6cb993d">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
    <property name="com.jaspersoft.studio.unit." value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
    <style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <dataset name="DepartDataset" uuid="8fa08454-99e7-47ba-830e-d29119f0d245">
        <field name="Field_1" class="java.lang.String"/>
        <field name="Field_2" class="java.lang.String"/>
        <field name="Field_3" class="java.lang.String"/>
        <field name="Field_4" class="java.lang.String"/>
        <field name="Field_5" class="java.lang.Object"/>
        <field name="Field_6" class="java.lang.Object"/>
        <field name="Field_7" class="java.lang.Object"/>
        <field name="Field_8" class="java.lang.Object"/>
    </dataset>
    <dataset name="returnDataSet" uuid="1f57db8f-7163-485c-8e77-8d7bbc97184d">
        <field name="Field_1" class="java.lang.String"/>
        <field name="Field_2" class="java.lang.String"/>
        <field name="Field_3" class="java.lang.String"/>
        <field name="Field_4" class="java.lang.String"/>
        <field name="Field_5" class="java.lang.Object"/>
        <field name="Field_6" class="java.lang.Object"/>
        <field name="Field_7" class="java.lang.Object"/>
        <field name="Field_8" class="java.lang.Object"/>
    </dataset>
    <parameter name="contractNo" class="java.lang.String"/>
    <parameter name="logo" class="java.lang.Object"/>
    <parameter name="tempReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="custInfoReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="departInfo" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="returnInfo" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="departDataSet" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
    <parameter name="returnDataSet" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
    <parameter name="representativeReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="departReturnImageReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="exteriorPhotosReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <parameter name="coordinateReport" class="net.sf.jasperreports.engine.JasperReport"/>
    <!--    cust info-->
    <!--    cust info-->
    <parameter name="acctName" class="java.lang.String"/>
    <parameter name="birthday" class="java.lang.String"/>
    <parameter name="idNo" class="java.lang.String"/>
    <parameter name="mainCell" class="java.lang.String"/>
    <parameter name="email" class="java.lang.String"/>
    <parameter name="address" class="java.lang.String"/>
    <parameter name="plateNo" class="java.lang.String"/>
    <parameter name="engineNo" class="java.lang.String"/>
    <parameter name="carColor" class="java.lang.String"/>
    <parameter name="carModelName" class="java.lang.String"/>
    <parameter name="securityDeposit" class="java.lang.String"/>
    <parameter name="monthlyFee" class="java.lang.String"/>
    <parameter name="mileageFee" class="java.lang.String"/>
    <parameter name="expectDepartDate" class="java.lang.String"/>
    <parameter name="expectReturnDate" class="java.lang.String"/>
    <parameter name="expectDepartStation" class="java.lang.String"/>
    <parameter name="expectReturnStation" class="java.lang.String"/>
    <!--    cust info end-->
    <!--    depart info-->
    <parameter name="startDate" class="java.lang.String"/>
    <parameter name="mileageOut" class="java.lang.String"/>
    <parameter name="fuelOut" class="java.lang.String"/>
    <parameter name="tireDepthDepart" class="java.lang.String"/>
    <parameter name="warningLightDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="odorDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="seatAndTrimDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="carpetAndGloveboxDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="engineDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="acDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="windowAndDoorMirrorDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="audioAndMultimediaDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="vehicleLicenseDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="hasKeyDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="keyCountDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["   "]]></defaultValueExpression>
    </parameter>
    <parameter name="dashCamsDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="trunkLidDepart" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="departRemark" class="java.lang.String"/>
    <parameter name="departMemberSignImage" class="java.lang.Object"/>
    <parameter name="departCustSignImage" class="java.lang.Object"/>
    <parameter name="departMemberSignDate" class="java.lang.String"/>
    <parameter name="departCustSignDate" class="java.lang.String"/>
    <!--    depart info end-->
    <!--    return info-->
    <parameter name="endDate" class="java.lang.String"/>
    <parameter name="mileageIn" class="java.lang.String"/>
    <parameter name="fuelIn" class="java.lang.String"/>
    <parameter name="tireDepth" class="java.lang.String"/>
    <parameter name="warningLight" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="odor" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="seatAndTrim" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="carpetAndGlovebox" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="engine" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="ac" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="windowAndDoorMirror" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="audioAndMultimedia" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="vehicleLicense" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="hasKey" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="keyCount" class="java.lang.String">
        <defaultValueExpression><![CDATA["   "]]></defaultValueExpression>
    </parameter>
    <parameter name="dashCams" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="trunkLid" class="java.lang.String">
        <defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
    </parameter>
    <parameter name="returnRemark" class="java.lang.String"/>
    <parameter name="returnMemberSignImage" class="java.lang.Object"/>
    <parameter name="returnCustSignImage" class="java.lang.Object"/>
    <parameter name="returnMemberSignDate" class="java.lang.String"/>
    <parameter name="returnCustSignDate" class="java.lang.String"/>
    <!--    return info end-->
    <!--    exterior-->
    <parameter name="surround1" class="java.lang.Object"/>
    <parameter name="surround2" class="java.lang.Object"/>
    <parameter name="surround3" class="java.lang.Object"/>
    <parameter name="surround4" class="java.lang.Object"/>
    <parameter name="insight1" class="java.lang.Object"/>
    <parameter name="insight2" class="java.lang.Object"/>
    <parameter name="insight3" class="java.lang.Object"/>
    <parameter name="etag" class="java.lang.Object"/>
    <parameter name="front" class="java.lang.Object"/>
    <parameter name="back" class="java.lang.Object"/>
    <parameter name="right" class="java.lang.Object"/>
    <parameter name="left" class="java.lang.Object"/>
    <parameter name="top" class="java.lang.Object"/>
    <parameter name="insight" class="java.lang.Object"/>
    <!--    exterior end-->
    <!--    depart and return Image-->
    <parameter name="isDepartEmpty" class="java.lang.Boolean">
        <defaultValueExpression><![CDATA[true]]></defaultValueExpression>
    </parameter>
    <parameter name="isReturnEmpty" class="java.lang.Boolean">
        <defaultValueExpression><![CDATA[true]]></defaultValueExpression>
    </parameter>
    <!--    depart and return Image end-->
    <!--    representative-->
    <parameter name="stamp" class="java.lang.Object"/>
    <parameter name="stamp1" class="java.lang.Object"/>
    <parameter name="mingouYear" class="java.lang.String"/>
    <parameter name="mingouMonth" class="java.lang.String"/>
    <parameter name="mingouDate" class="java.lang.String"/>
    <!--    representative end-->
    <pageHeader height="61" splitType="Stretch">
        <element kind="frame" uuid="e599072e-06d7-43da-9409-452c2a17f1be" x="0" y="8" width="595" height="52">
            <property name="com.jaspersoft.studio.unit.x" value="px"/>
            <element kind="staticText" uuid="048d4495-3dad-4386-ba2d-13c37d1010b7" x="0" y="0" width="595" height="16" fontName="微軟正黑體" fontSize="12.0" bold="true" hTextAlign="Center" vTextAlign="Middle">
                <text><![CDATA[汽 車 出 租 單]]></text>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
            </element>
            <element kind="staticText" uuid="71ac1547-bfc5-47ba-a337-b0f1c3f01ecc" x="0" y="16" width="595" height="14" fontName="微軟正黑體" fontSize="10.0" hTextAlign="Center" vTextAlign="Middle">
                <text><![CDATA[訂閱式租賃]]></text>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
            </element>
            <element kind="staticText" uuid="689d1672-3cb9-4af8-8715-bc541f3a8f9d" x="0" y="30" width="595" height="9" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Center" vTextAlign="Middle">
                <paragraph lineSpacing="Single"/>
                <text><![CDATA[( 租賃契約書／Rental Agreement )]]></text>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
            </element>
            <element kind="image" uuid="bd469e0d-078c-4f0a-9ac5-42c27f741d92" x="28" y="22" width="116" height="24">
                <expression><![CDATA[$P{logo}]]></expression>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
            </element>
            <element kind="textField" uuid="9fe3a392-a64d-473a-9f81-ef1b4c284fdb" x="462" y="22" width="105" height="14" fontName="微軟正黑體" fontSize="8.0" linkType="None" linkTarget="Self" hTextAlign="Right" vTextAlign="Middle">
                <expression><![CDATA["合約編號:"+$P{contractNo}]]></expression>
            </element>
        </element>
        <property name="com.jaspersoft.studio.unit.height" value="px"/>
    </pageHeader>
    <detail>
        <band height="50">
            <element kind="subreport" uuid="df80071a-0b14-4b15-b158-c18b84958bbc" stretchType="ContainerHeight" x="0" y="0" width="595" height="50" usingCache="false">
                <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
                <expression><![CDATA[$P{tempReport}]]></expression>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
                <parameter name="custInfoReport">
                    <expression><![CDATA[$P{custInfoReport}]]></expression>
                </parameter>
                <parameter name="departInfo">
                    <expression><![CDATA[$P{departInfo}]]></expression>
                </parameter>
                <parameter name="returnInfo">
                    <expression><![CDATA[$P{returnInfo}]]></expression>
                </parameter>
<!--                cust info-->
                <parameter name="acctName">
                    <expression><![CDATA[$P{acctName}]]></expression>
                </parameter>
                <parameter name="birthday">
                    <expression><![CDATA[$P{birthday}]]></expression>
                </parameter>
                <parameter name="idNo">
                    <expression><![CDATA[$P{idNo}]]></expression>
                </parameter>
                <parameter name="mainCell">
                    <expression><![CDATA[$P{mainCell}]]></expression>
                </parameter>
                <parameter name="email">
                    <expression><![CDATA[$P{email}]]></expression>
                </parameter>
                <parameter name="address">
                    <expression><![CDATA[$P{address}]]></expression>
                </parameter>
                <parameter name="plateNo">
                    <expression><![CDATA[$P{plateNo}]]></expression>
                </parameter>
                <parameter name="engineNo">
                    <expression><![CDATA[$P{engineNo}]]></expression>
                </parameter>
                <parameter name="carColor">
                    <expression><![CDATA[$P{carColor}]]></expression>
                </parameter>
                <parameter name="carModelName">
                    <expression><![CDATA[$P{carModelName}]]></expression>
                </parameter>
                <parameter name="securityDeposit">
                    <expression><![CDATA[$P{securityDeposit}]]></expression>
                </parameter>
                <parameter name="monthlyFee">
                    <expression><![CDATA[$P{monthlyFee}]]></expression>
                </parameter>
                <parameter name="mileageFee">
                    <expression><![CDATA[$P{mileageFee}]]></expression>
                </parameter>
                <parameter name="expectDepartDate">
                    <expression><![CDATA[$P{expectDepartDate}]]></expression>
                </parameter>
                <parameter name="expectReturnDate">
                    <expression><![CDATA[$P{expectReturnDate}]]></expression>
                </parameter>
                <parameter name="expectDepartStation">
                    <expression><![CDATA[$P{expectDepartStation}]]></expression>
                </parameter>
                <parameter name="expectReturnStation">
                    <expression><![CDATA[$P{expectReturnStation}]]></expression>
                </parameter>
<!--                cust info end-->
                <parameter name="startDate">
                    <expression><![CDATA[$P{startDate}]]></expression>
                </parameter>
                <parameter name="mileageOut">
                    <expression><![CDATA[$P{mileageOut}]]></expression>
                </parameter>
                <parameter name="fuelOut">
                    <expression><![CDATA[$P{fuelOut}]]></expression>
                </parameter>
                <parameter name="tireDepthDepart">
                    <expression><![CDATA[$P{tireDepthDepart}]]></expression>
                </parameter>
                <parameter name="warningLightDepart">
                    <expression><![CDATA[$P{warningLightDepart}]]></expression>
                </parameter>
                <parameter name="odorDepart">
                    <expression><![CDATA[$P{odorDepart}]]></expression>
                </parameter>
                <parameter name="seatAndTrimDepart">
                    <expression><![CDATA[$P{seatAndTrimDepart}]]></expression>
                </parameter>
                <parameter name="carpetAndGloveboxDepart">
                    <expression><![CDATA[$P{carpetAndGloveboxDepart}]]></expression>
                </parameter>
                <parameter name="engineDepart">
                    <expression><![CDATA[$P{engineDepart}]]></expression>
                </parameter>
                <parameter name="acDepart">
                    <expression><![CDATA[$P{acDepart}]]></expression>
                </parameter>
                <parameter name="windowAndDoorMirrorDepart">
                    <expression><![CDATA[$P{windowAndDoorMirrorDepart}]]></expression>
                </parameter>
                <parameter name="audioAndMultimediaDepart">
                    <expression><![CDATA[$P{audioAndMultimediaDepart}]]></expression>
                </parameter>
                <parameter name="vehicleLicenseDepart">
                    <expression><![CDATA[$P{vehicleLicenseDepart}]]></expression>
                </parameter>
                <parameter name="hasKeyDepart">
                    <expression><![CDATA[$P{hasKeyDepart}]]></expression>
                </parameter>
                <parameter name="keyCountDepart">
                    <expression><![CDATA[$P{keyCountDepart}]]></expression>
                </parameter>
                <parameter name="dashCamsDepart">
                    <expression><![CDATA[$P{dashCamsDepart}]]></expression>
                </parameter>
                <parameter name="trunkLidDepart">
                    <expression><![CDATA[$P{trunkLidDepart}]]></expression>
                </parameter>
                <parameter name="departRemark">
                    <expression><![CDATA[$P{departRemark}]]></expression>
                </parameter>
                <parameter name="departMemberSignImage">
                    <expression><![CDATA[$P{departMemberSignImage}]]></expression>
                </parameter>
                <parameter name="departCustSignImage">
                    <expression><![CDATA[$P{departCustSignImage}]]></expression>
                </parameter>
                <parameter name="departMemberSignDate">
                    <expression><![CDATA[$P{departMemberSignDate}]]></expression>
                </parameter>
                <parameter name="departCustSignDate">
                    <expression><![CDATA[$P{departCustSignDate}]]></expression>
                </parameter>
                <parameter name="endDate">
                    <expression><![CDATA[$P{endDate}]]></expression>
                </parameter>
                <parameter name="mileageIn">
                    <expression><![CDATA[$P{mileageIn}]]></expression>
                </parameter>
                <parameter name="fuelIn">
                    <expression><![CDATA[$P{fuelIn}]]></expression>
                </parameter>
                <parameter name="tireDepth">
                    <expression><![CDATA[$P{tireDepth}]]></expression>
                </parameter>
                <parameter name="warningLight">
                    <expression><![CDATA[$P{warningLight}]]></expression>
                </parameter>
                <parameter name="odor">
                    <expression><![CDATA[$P{odor}]]></expression>
                </parameter>
                <parameter name="seatAndTrim">
                    <expression><![CDATA[$P{seatAndTrim}]]></expression>
                </parameter>
                <parameter name="carpetAndGlovebox">
                    <expression><![CDATA[$P{carpetAndGlovebox}]]></expression>
                </parameter>
                <parameter name="engine">
                    <expression><![CDATA[$P{engine}]]></expression>
                </parameter>
                <parameter name="ac">
                    <expression><![CDATA[$P{ac}]]></expression>
                </parameter>
                <parameter name="windowAndDoorMirror">
                    <expression><![CDATA[$P{windowAndDoorMirror}]]></expression>
                </parameter>
                <parameter name="audioAndMultimedia">
                    <expression><![CDATA[$P{audioAndMultimedia}]]></expression>
                </parameter>
                <parameter name="vehicleLicense">
                    <expression><![CDATA[$P{vehicleLicense}]]></expression>
                </parameter>
                <parameter name="hasKey">
                    <expression><![CDATA[$P{hasKey}]]></expression>
                </parameter>
                <parameter name="keyCount">
                    <expression><![CDATA[$P{keyCount}]]></expression>
                </parameter>
                <parameter name="dashCams">
                    <expression><![CDATA[$P{dashCams}]]></expression>
                </parameter>
                <parameter name="trunkLid">
                    <expression><![CDATA[$P{trunkLid}]]></expression>
                </parameter>
                <parameter name="returnRemark">
                    <expression><![CDATA[$P{returnRemark}]]></expression>
                </parameter>
                <parameter name="returnMemberSignImage">
                    <expression><![CDATA[$P{returnMemberSignImage}]]></expression>
                </parameter>
                <parameter name="returnCustSignImage">
                    <expression><![CDATA[$P{returnCustSignImage}]]></expression>
                </parameter>
                <parameter name="returnMemberSignDate">
                    <expression><![CDATA[$P{returnMemberSignDate}]]></expression>
                </parameter>
                <parameter name="returnCustSignDate">
                    <expression><![CDATA[$P{returnCustSignDate}]]></expression>
                </parameter>
            </element>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
        <band height="50" splitType="Stretch">
            <element kind="subreport" uuid="0221dd41-7f06-4847-a6a0-fb7996def8ec" x="0" y="0" width="595" height="50">
                <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
                <expression><![CDATA[$P{exteriorPhotosReport}]]></expression>
                <parameter name="surround1">
                    <expression><![CDATA[$P{surround1}]]></expression>
                </parameter>
                <parameter name="surround2">
                    <expression><![CDATA[$P{surround2}]]></expression>
                </parameter>
                <parameter name="surround3">
                    <expression><![CDATA[$P{surround3}]]></expression>
                </parameter>
                <parameter name="surround4">
                    <expression><![CDATA[$P{surround4}]]></expression>
                </parameter>
                <parameter name="insight1">
                    <expression><![CDATA[$P{insight1}]]></expression>
                </parameter>
                <parameter name="insight2">
                    <expression><![CDATA[$P{insight2}]]></expression>
                </parameter>
                <parameter name="insight3">
                    <expression><![CDATA[$P{insight3}]]></expression>
                </parameter>
                <parameter name="etag">
                    <expression><![CDATA[$P{etag}]]></expression>
                </parameter>
            </element>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
        <band height="50" splitType="Stretch">
            <element kind="subreport" uuid="83f3c147-85d2-4f80-8741-5a71201f00fd" x="0" y="0" width="595" height="50">
                <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
                <expression><![CDATA[$P{coordinateReport}]]></expression>
                <parameter name="front">
                    <expression><![CDATA[$P{front}]]></expression>
                </parameter>
                <parameter name="back">
                    <expression><![CDATA[$P{back}]]></expression>
                </parameter>
                <parameter name="right">
                    <expression><![CDATA[$P{right}]]></expression>
                </parameter>
                <parameter name="left">
                    <expression><![CDATA[$P{left}]]></expression>
                </parameter>
                <parameter name="top">
                    <expression><![CDATA[$P{top}]]></expression>
                </parameter>
                <parameter name="insight">
                    <expression><![CDATA[$P{insight}]]></expression>
                </parameter>
            </element>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
        <band height="63" splitType="Stretch">
            <element kind="frame" uuid="2923b41b-a45a-42c9-bf86-82aa37171ae9" positionType="Float" stretchType="ContainerHeight" x="20" y="10" width="555" height="50" printInFirstWholeBand="true">
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
                <element kind="component" uuid="cea3a219-9ac3-4d28-8289-bf150919b6ff" stretchType="NoStretch" x="0" y="30" width="555" height="0" printInFirstWholeBand="true">
                    <component kind="table">
                        <datasetRun uuid="accfc344-0bfc-4132-b556-2d66e3252de1" subDataset="DepartDataset">
                            <dataSourceExpression><![CDATA[$P{departDataSet}]]></dataSourceExpression>
                        </datasetRun>
                        <column kind="group" uuid="abfac14f-18dd-42a3-862b-2a78b4bcce74" width="555">
                            <property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [4]"/>
                            <column kind="single" uuid="cd8e6d7f-57d6-4594-b331-bfb186be0a10" width="140">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="57207f2c-368a-4ed4-b624-e6225cbf8aa7" x="0" y="0" width="140" height="220">
                                        <element kind="textField" uuid="b30e9d9b-3cb5-48d5-8b22-efb4e26bf5da" x="0" y="0" width="140" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_1}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="723b50cd-b7c5-475d-920f-a11f7ad86af0" stretchType="ContainerHeight" x="0" y="20" width="140" height="200" linkType="None" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_5}]]></expression>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
                            </column>
                            <column kind="single" uuid="11ec4fe5-70c8-4320-9423-7b9971a4768e" width="139">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="baa05302-47b5-4465-8f44-a8fba311f072" x="0" y="0" width="139" height="220">
                                        <element kind="textField" uuid="7b4e2337-410f-4192-864a-22aebdb40f6d" x="0" y="0" width="139" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_2}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="4df2198d-7a2a-4630-86af-89a128d0f5b2" stretchType="ContainerHeight" x="0" y="20" width="139" height="200" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_6}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
                            </column>
                            <column kind="single" uuid="16a7f181-1923-47da-bd71-2f0a3887e552" width="138">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="dfd0e6ae-1d71-4a46-a968-************" x="0" y="0" width="138" height="220">
                                        <element kind="textField" uuid="b0a8139e-d37e-49e1-8495-201292eb4268" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_3}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="433ba6cd-49b5-4847-a560-dcd961054ffe" stretchType="ContainerHeight" x="0" y="20" width="138" height="200" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_7}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
                            </column>
                            <column kind="single" uuid="e619c6fb-0bca-44dd-8f4f-df9b54a20c05" width="138">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="45e2e03b-d1f1-4724-99de-ad636d7ba84d" x="0" y="0" width="138" height="220">
                                        <element kind="textField" uuid="7338d67a-bc48-411b-9727-66cbb0e91043" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_4}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="ab95b9ae-4935-4ae5-8495-5146e400485c" stretchType="ContainerHeight" x="0" y="20" width="138" height="200" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_8}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
                            </column>
                        </column>
                    </component>
                    <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
                    <property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
                    <property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
                    <property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
                    <property name="com.jaspersoft.studio.components.autoresize.proportional" value="true"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="net.sf.jasperreports.components.table.repeat.header" value="true"/>
                    <property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
                </element>
                <element kind="staticText" uuid="1980e306-2167-4cd1-8e59-93dbf8f46104" stretchType="NoStretch" x="0" y="0" width="555" height="30" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
                    <text><![CDATA[出 車 Depart]]></text>
                    <box>
                        <pen lineWidth="0.25"/>
                    </box>
                </element>
                <element kind="textField" uuid="df34cbda-5abd-467b-8cba-3739f3119e9c" x="0" y="30" width="555" height="20" hTextAlign="Center" vTextAlign="Middle">
                    <expression><![CDATA[$P{isDepartEmpty} ? "-":""]]></expression>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <box>
                        <pen lineWidth="0.25"/>
                    </box>
                </element>
            </element>
        </band>
        <band height="63" splitType="Stretch">
            <element kind="frame" uuid="8a91cecc-2384-4aac-ae1c-492082b920b9" positionType="Float" stretchType="ContainerHeight" x="20" y="5" width="555" height="50" printInFirstWholeBand="true">
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
                <property name="com.jaspersoft.studio.unit.x" value="px"/>
                <property name="com.jaspersoft.studio.unit.y" value="px"/>
                <element kind="component" uuid="a341599a-f819-4397-81d0-53de03f9fd43" positionType="Float" stretchType="NoStretch" x="0" y="30" width="555" height="0">
                    <component kind="table">
                        <datasetRun uuid="d69b354c-64c7-4737-b781-c8ed0276bd2b" subDataset="returnDataSet">
                            <dataSourceExpression><![CDATA[$P{returnDataSet}]]></dataSourceExpression>
                        </datasetRun>
                        <column kind="group" uuid="2bafda2f-4863-4850-8d51-895dbe9d60ee" width="555">
                            <property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [4]"/>
                            <column kind="single" uuid="ee6a1d0e-9601-4675-a3ee-91502734c0e6" width="140">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="11766d10-2544-48d8-8d3f-af3467712804" x="0" y="0" width="140" height="220">
                                        <element kind="textField" uuid="2d82a68d-e11e-46a7-b534-2f07b37c05a4" x="0" y="0" width="140" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_1}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="0e9fd278-abe1-4a58-bd10-3c332c665f75" x="0" y="20" width="140" height="200" backcolor="#FFFFFF" linkType="None" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_5}]]></expression>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
                            </column>
                            <column kind="single" uuid="*************-4022-a143-73dd0d255571" width="139">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="62bf1420-9fb4-4219-8c09-743c26049e1b" x="0" y="0" width="139" height="220">
                                        <element kind="textField" uuid="b86421bf-9806-492c-aae3-56f03af23161" x="0" y="0" width="139" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_2}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="e8b4ed1a-c4a6-4c55-8728-1374a92d73b6" x="0" y="20" width="139" height="200" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_6}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
                            </column>
                            <column kind="single" uuid="ee16102c-0ce6-4855-b2b2-6b7c916a01bd" width="138">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="409ef64e-556d-46ec-bc9a-54b9a665cf60" x="0" y="0" width="138" height="220">
                                        <element kind="textField" uuid="a8f919f2-b6a4-46e6-9208-0b124e4e6e6e" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_3}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="ca605549-eb2c-4852-aebe-d6d7b19d1c8f" x="0" y="20" width="138" height="200" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_7}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
                            </column>
                            <column kind="single" uuid="07de5f6b-62b9-4b63-9789-a06837ed1d42" width="138">
                                <detailCell height="220" style="Table_TD">
                                    <element kind="frame" uuid="81f613c4-1f2c-4046-b5cd-08153d5d68ca" x="0" y="0" width="138" height="220">
                                        <element kind="textField" uuid="e2331a5a-cf49-4211-8e57-f6b7385c929f" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
                                            <expression><![CDATA[$F{Field_4}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.height" value="px"/>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                            <box>
                                                <pen lineWidth="0.25"/>
                                            </box>
                                        </element>
                                        <element kind="image" uuid="f25c21a9-6777-4bdf-b152-1bd2c115aeb9" x="0" y="20" width="138" height="200" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
                                            <expression><![CDATA[$F{Field_8}]]></expression>
                                            <property name="com.jaspersoft.studio.unit.width" value="px"/>
                                        </element>
                                    </element>
                                </detailCell>
                                <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
                            </column>
                        </column>
                    </component>
                    <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
                    <property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
                    <property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
                    <property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
                    <property name="com.jaspersoft.studio.components.autoresize.proportional" value="true"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="net.sf.jasperreports.components.table.repeat.header" value="true"/>
                    <property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </element>
                <element kind="staticText" uuid="58e260d0-f235-4444-be82-160079618eea" positionType="Float" stretchType="NoStretch" x="0" y="0" width="555" height="30" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
                    <text><![CDATA[還 車 Return]]></text>
                    <box>
                        <pen lineWidth="0.25"/>
                    </box>
                </element>
                <element kind="textField" uuid="377f0bce-5105-4da5-8abf-f7bb415a0d2e" x="0" y="30" width="555" height="20" hTextAlign="Center" vTextAlign="Middle">
                    <expression><![CDATA[$P{isReturnEmpty} ? "-":""]]></expression>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                    <box>
                        <pen lineWidth="0.25"/>
                    </box>
                </element>
            </element>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
        <band height="630" >
            <element kind="subreport" uuid="8bbf8b16-8b26-472e-acd3-da4dfc8902c6" positionType="Float" x="0" y="18" width="595" height="212" usingCache="false">
                <dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.JREmptyDataSource()]]></dataSourceExpression>
                <expression><![CDATA[$P{representativeReport}]]></expression>
                <property name="com.jaspersoft.studio.unit.height" value="px"/>
                <parameter name="stamp">
                    <expression><![CDATA[$P{stamp}]]></expression>
                </parameter>
                <parameter name="stamp1">
                    <expression><![CDATA[$P{stamp1}]]></expression>
                </parameter>
                <parameter name="mingouYear">
                    <expression><![CDATA[$P{mingouYear}]]></expression>
                </parameter>
                <parameter name="mingouMonth">
                    <expression><![CDATA[$P{mingouMonth}]]></expression>
                </parameter>
                <parameter name="mingouDate">
                    <expression><![CDATA[$P{mingouDate}]]></expression>
                </parameter>
            </element>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </band>
    </detail>
    <pageFooter height="40" splitType="Stretch">
        <element kind="staticText" uuid="6e60ef2c-c7ec-4a0c-8cb3-a6306b7db973" x="0" y="9" width="595" height="15" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Center" vTextAlign="Middle">
            <text><![CDATA[本出租單依據「汽車運輸業管理規則」第 101 條制訂。並經臺北市政府交通局同意備查使用，核准文號：北巿監運字第 1110251536 號函。]]></text>
            <property name="com.jaspersoft.studio.unit.y" value="pixel"/>
            <property name="com.jaspersoft.studio.unit.x" value="px"/>
        </element>
        <element kind="textField" uuid="7c8fbbd1-d2c4-447d-9cad-91f70b5fa9e4" x="0" y="24" width="300" height="15" fontSize="8.0" hTextAlign="Right">
            <expression><![CDATA[$V{PAGE_NUMBER}+"/"]]></expression>
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
        </element>
        <element kind="textField" uuid="64e5c2fc-28ec-48e2-b10a-cbb36b28c6ae" x="300" y="24" width="295" height="15" fontSize="8.0" evaluationTime="Report">
            <expression><![CDATA[$V{PAGE_NUMBER}]]></expression>
        </element>
        <property name="com.jaspersoft.studio.unit.height" value="px"/>
    </pageFooter>
</jasperReport>
