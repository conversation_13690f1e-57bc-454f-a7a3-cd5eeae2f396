<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="Blank_A4" language="java" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="ef942193-e09e-4892-b540-1dd164a7f866">
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<dataset name="DepartDataset" uuid="8fa08454-99e7-47ba-830e-d29119f0d245">
		<field name="Field_1" class="java.lang.String"/>
		<field name="Field_2" class="java.lang.String"/>
		<field name="Field_3" class="java.lang.String"/>
		<field name="Field_4" class="java.lang.String"/>
		<field name="Field_5" class="java.lang.Object"/>
		<field name="Field_6" class="java.lang.Object"/>
		<field name="Field_7" class="java.lang.Object"/>
		<field name="Field_8" class="java.lang.Object"/>
	</dataset>
	<dataset name="returnDataSet" uuid="1f57db8f-7163-485c-8e77-8d7bbc97184d">
		<query language="sql"><![CDATA[]]></query>
		<field name="Field_1" class="java.lang.String"/>
		<field name="Field_2" class="java.lang.String"/>
		<field name="Field_3" class="java.lang.String"/>
		<field name="Field_4" class="java.lang.String"/>
		<field name="Field_5" class="java.lang.Object"/>
		<field name="Field_6" class="java.lang.Object"/>
		<field name="Field_7" class="java.lang.Object"/>
		<field name="Field_8" class="java.lang.Object"/>
	</dataset>
	<parameter name="departDataSet" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
	<parameter name="returnDataSet" class="net.sf.jasperreports.engine.data.JRMapCollectionDataSource"/>
	<query language="sql"><![CDATA[]]></query>
	<detail>
		<band height="63" splitType="Stretch">
			<element kind="frame" uuid="2923b41b-a45a-42c9-bf86-82aa37171ae9" positionType="Float" stretchType="ContainerHeight" x="0" y="10" width="555" height="50" printInFirstWholeBand="true">
				<element kind="component" uuid="cea3a219-9ac3-4d28-8289-bf150919b6ff" stretchType="NoStretch" x="0" y="30" width="555" height="20">
					<component kind="table">
						<datasetRun uuid="accfc344-0bfc-4132-b556-2d66e3252de1" subDataset="DepartDataset">
							<dataSourceExpression><![CDATA[$P{departDataSet}]]></dataSourceExpression>
						</datasetRun>
						<column kind="group" uuid="abfac14f-18dd-42a3-862b-2a78b4bcce74" width="555">
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [4]"/>
							<column kind="single" uuid="cd8e6d7f-57d6-4594-b331-bfb186be0a10" width="140">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="57207f2c-368a-4ed4-b624-e6225cbf8aa7" x="0" y="0" width="140" height="220">
										<element kind="textField" uuid="b30e9d9b-3cb5-48d5-8b22-efb4e26bf5da" x="0" y="0" width="140" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_1}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="723b50cd-b7c5-475d-920f-a11f7ad86af0" x="0" y="20" width="140" height="200" linkType="None" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_5}]]></expression>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							</column>
							<column kind="single" uuid="11ec4fe5-70c8-4320-9423-7b9971a4768e" width="139">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="baa05302-47b5-4465-8f44-a8fba311f072" x="0" y="0" width="139" height="220">
										<element kind="textField" uuid="7b4e2337-410f-4192-864a-22aebdb40f6d" x="0" y="0" width="139" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_2}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="4df2198d-7a2a-4630-86af-89a128d0f5b2" x="0" y="20" width="139" height="200" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_6}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
							</column>
							<column kind="single" uuid="16a7f181-1923-47da-bd71-2f0a3887e552" width="138">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="dfd0e6ae-1d71-4a46-a968-************" x="0" y="0" width="138" height="220">
										<element kind="textField" uuid="b0a8139e-d37e-49e1-8495-201292eb4268" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_3}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="433ba6cd-49b5-4847-a560-dcd961054ffe" x="0" y="20" width="138" height="200" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_7}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
							</column>
							<column kind="single" uuid="e619c6fb-0bca-44dd-8f4f-df9b54a20c05" width="138">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="45e2e03b-d1f1-4724-99de-ad636d7ba84d" x="0" y="0" width="138" height="220">
										<element kind="textField" uuid="7338d67a-bc48-411b-9727-66cbb0e91043" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_4}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="ab95b9ae-4935-4ae5-8495-5146e400485c" x="0" y="20" width="138" height="200" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_8}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
							</column>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.components.autoresize.proportional" value="true"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="net.sf.jasperreports.components.table.repeat.header" value="true"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<element kind="staticText" uuid="1980e306-2167-4cd1-8e59-93dbf8f46104" stretchType="NoStretch" x="0" y="0" width="555" height="30" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[出 車 Depart]]></text>
					<box>
						<pen lineWidth="0.25"/>
					</box>
				</element>
			</element>
		</band>
		<band height="63" splitType="Stretch">
			<element kind="frame" uuid="8a91cecc-2384-4aac-ae1c-492082b920b9" positionType="Float" stretchType="ContainerHeight" x="0" y="10" width="555" height="50" printInFirstWholeBand="true">
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="component" uuid="a341599a-f819-4397-81d0-53de03f9fd43" positionType="Float" stretchType="NoStretch" x="0" y="30" width="555" height="20">
					<component kind="table">
						<datasetRun uuid="d69b354c-64c7-4737-b781-c8ed0276bd2b" subDataset="returnDataSet">
							<dataSourceExpression><![CDATA[$P{returnDataSet}]]></dataSourceExpression>
						</datasetRun>
						<column kind="group" uuid="2bafda2f-4863-4850-8d51-895dbe9d60ee" width="555">
							<property name="com.jaspersoft.studio.components.table.model.column.name" value="Columns [4]"/>
							<column kind="single" uuid="ee6a1d0e-9601-4675-a3ee-91502734c0e6" width="140">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="11766d10-2544-48d8-8d3f-af3467712804" x="0" y="0" width="140" height="220">
										<element kind="textField" uuid="2d82a68d-e11e-46a7-b534-2f07b37c05a4" x="0" y="0" width="140" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_1}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="0e9fd278-abe1-4a58-bd10-3c332c665f75" x="0" y="20" width="140" height="200" backcolor="#FFFFFF" linkType="None" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_5}]]></expression>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
							</column>
							<column kind="single" uuid="*************-4022-a143-73dd0d255571" width="139">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="62bf1420-9fb4-4219-8c09-743c26049e1b" x="0" y="0" width="139" height="220">
										<element kind="textField" uuid="b86421bf-9806-492c-aae3-56f03af23161" x="0" y="0" width="139" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_2}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="e8b4ed1a-c4a6-4c55-8728-1374a92d73b6" x="0" y="20" width="139" height="200" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_6}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
							</column>
							<column kind="single" uuid="ee16102c-0ce6-4855-b2b2-6b7c916a01bd" width="138">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="409ef64e-556d-46ec-bc9a-54b9a665cf60" x="0" y="0" width="138" height="220">
										<element kind="textField" uuid="a8f919f2-b6a4-46e6-9208-0b124e4e6e6e" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_3}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="ca605549-eb2c-4852-aebe-d6d7b19d1c8f" x="0" y="20" width="138" height="200" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_7}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
							</column>
							<column kind="single" uuid="07de5f6b-62b9-4b63-9789-a06837ed1d42" width="138">
								<detailCell height="220" style="Table_TD">
									<element kind="frame" uuid="81f613c4-1f2c-4046-b5cd-08153d5d68ca" x="0" y="0" width="138" height="220">
										<element kind="textField" uuid="e2331a5a-cf49-4211-8e57-f6b7385c929f" x="0" y="0" width="138" height="20" backcolor="#E5F1FB" blankWhenNull="true" hTextAlign="Center" vTextAlign="Middle">
											<expression><![CDATA[$F{Field_4}]]></expression>
											<property name="com.jaspersoft.studio.unit.height" value="px"/>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
											<box>
												<pen lineWidth="0.25"/>
											</box>
										</element>
										<element kind="image" uuid="f25c21a9-6777-4bdf-b152-1bd2c115aeb9" x="0" y="20" width="138" height="200" linkTarget="Self" hImageAlign="Center" vImageAlign="Middle">
											<expression><![CDATA[$F{Field_8}]]></expression>
											<property name="com.jaspersoft.studio.unit.width" value="px"/>
										</element>
									</element>
								</detailCell>
								<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
							</column>
						</column>
					</component>
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.components.autoresize.proportional" value="true"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="net.sf.jasperreports.components.table.repeat.header" value="true"/>
					<property name="com.jaspersoft.studio.components.autoresize.next" value="true"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</element>
				<element kind="staticText" uuid="58e260d0-f235-4444-be82-160079618eea" positionType="Float" stretchType="NoStretch" x="0" y="0" width="555" height="30" fontName="微軟正黑體" fontSize="12.0" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[還 車 Return]]></text>
					<box>
						<pen lineWidth="0.25"/>
					</box>
				</element>
			</element>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
		</band>
	</detail>
</jasperReport>
