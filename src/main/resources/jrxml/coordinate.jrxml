<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="Blank_A4" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="0" uuid="bfe835cc-05db-4ba2-b259-0d154470283b">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="front" class="java.lang.Object"/>
	<parameter name="back" class="java.lang.Object"/>
	<parameter name="right" class="java.lang.Object"/>
	<parameter name="left" class="java.lang.Object"/>
	<parameter name="top" class="java.lang.Object"/>
	<parameter name="insight" class="java.lang.Object"/>
	<query language="sql"><![CDATA[]]></query>
	<title height="282" splitType="Stretch">
		<element kind="frame" uuid="bcf124a7-832c-43b3-8d8d-d7f6dee9705c" x="3" y="0" width="550" height="282">
			<element kind="staticText" uuid="c2a6580e-a044-4c59-a8c8-db886609d3db" mode="Opaque" x="0" y="0" width="550" height="30" backcolor="#D9D9D9" fontName="微軟正黑體" fontSize="14.0" hTextAlign="Center" vTextAlign="Middle">
				<text><![CDATA[位置座標 Location Marker]]></text>
				<box>
					<pen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="frame" uuid="43051203-d3db-4fe6-aea0-1e3723c37672" x="0" y="30" width="274" height="252">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="frame" uuid="eeb61480-a0ae-41ba-970b-6d2be69682ee" x="34" y="1" width="207" height="251">
					<element kind="image" uuid="35cff524-0126-47a6-acfc-2ea6fa2210bf" x="10" y="0" width="87" height="56">
						<expression><![CDATA[$P{front}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="image" uuid="3fbf8b1e-e020-440b-b904-9cf8ff7abd7d" x="110" y="0" width="87" height="56">
						<expression><![CDATA[$P{back}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
					</element>
					<element kind="image" uuid="ad705a7e-c3f2-4a5c-8407-60a111ce0dc3" x="1" y="56" width="206" height="56">
						<expression><![CDATA[$P{right}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<element kind="image" uuid="0860750f-243f-4764-9694-53aa60638fc4" x="0" y="111" width="206" height="56">
						<expression><![CDATA[$P{left}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
					<element kind="image" uuid="4ee37d63-24f0-4d87-9851-2cab77529cee" x="2" y="167" width="204" height="84">
						<expression><![CDATA[$P{top}]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.x" value="px"/>
					</element>
				</element>
				<box>
					<topPen lineWidth="0.25"/>
					<leftPen lineWidth="0.25"/>
					<bottomPen lineWidth="0.25"/>
					<rightPen lineWidth="0.25"/>
				</box>
			</element>
			<element kind="frame" uuid="3f852cea-52f0-431d-aa75-543fe2a537e8" x="275" y="30" width="275" height="252">
				<property name="com.jaspersoft.studio.unit.width" value="px"/>
				<property name="com.jaspersoft.studio.unit.height" value="px"/>
				<element kind="image" uuid="1785dee1-ad53-44a3-bb5a-2a0e3d38ea04" x="31" y="11" width="211" height="217">
					<expression><![CDATA[$P{insight}]]></expression>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</element>
				<box>
					<pen lineWidth="0.25"/>
				</box>
			</element>
		</element>
	</title>
</jasperReport>
