<!-- Created with Jaspersoft Studio version 7.0.0.final using JasperReports Library version 7.0.0-b478feaa9aab4375eba71de77b4ca138ad2f62aa  -->
<jasperReport name="Blank_A4" language="java" columnCount="1" pageWidth="595" pageHeight="842" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="10" bottomMargin="0" uuid="a415e792-1723-492f-bd3e-47559694667e">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="endDate" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="mileageIn" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="fuelIn" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="tireDepth" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="warningLight" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="odor" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="seatAndTrim" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="carpetAndGlovebox" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="engine" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="ac" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="windowAndDoorMirror" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="audioAndMultimedia" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="vehicleLicense" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="hasKey" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="keyCount" class="java.lang.String">
		<defaultValueExpression><![CDATA["   "]]></defaultValueExpression>
	</parameter>
	<parameter name="dashCams" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="trunkLid" class="java.lang.String">
		<defaultValueExpression><![CDATA["□"]]></defaultValueExpression>
	</parameter>
	<parameter name="returnRemark" class="java.lang.String"/>
	<parameter name="returnMemberSignImage" class="java.lang.Object"/>
	<parameter name="returnCustSignImage" class="java.lang.Object"/>
	<parameter name="returnMemberSignDate" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<parameter name="returnCustSignDate" class="java.lang.String">
		<defaultValueExpression><![CDATA[""]]></defaultValueExpression>
	</parameter>
	<query language="sql"><![CDATA[]]></query>
	<title height="217" splitType="Stretch">
		<element kind="frame" uuid="e3068a92-34a8-4a72-bd23-c51b1a6596c0" x="8" y="0" width="554" height="212">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.unit.y" value="px"/>
			<property name="com.jaspersoft.studio.unit.width" value="px"/>
			<property name="com.jaspersoft.studio.unit.x" value="px"/>
			<element kind="frame" uuid="ece5d430-15c8-4bca-b7c8-34146d98f73d" x="0" y="0" width="539" height="213">
				<property name="com.jaspersoft.studio.unit.y" value="px"/>
				<property name="com.jaspersoft.studio.unit.x" value="px"/>
				<element kind="staticText" uuid="74ac4016-35dd-4ba5-97a5-ca2b41d92bfb" x="0" y="0" width="539" height="14" fontName="微軟正黑體" bold="true" hTextAlign="Center" vTextAlign="Middle">
					<text><![CDATA[還 車 簽 收 Return Inspection]]></text>
				</element>
				<element kind="frame" uuid="a68b0cdd-34f0-4422-9946-925d521d8de5" x="0" y="14" width="539" height="199">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<element kind="textField" uuid="a3d55b23-c3b2-4e38-86de-598810f443c3" x="375" y="98" width="164" height="14" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{trunkLid}+"行李箱蓋板 Trunk Lid"]]></expression>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="64261260-b5bf-4211-8dff-c0f66589662e" x="0" y="0" width="105" height="140">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="frame" uuid="db82173b-475b-4906-a873-56e061f8cbf6" x="0" y="0" width="105" height="20">
							<element kind="staticText" uuid="a71ecc59-9d44-4b2b-a711-4cab9256e45e" x="0" y="11" width="105" height="9" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[End Date/Time]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="dbeb696e-cc93-427b-b0fa-221d96fbcedd" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[實際迄租時間]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="787a7fe6-6ed1-4ab3-9c9c-8d1993d65da1" x="0" y="20" width="105" height="20">
							<element kind="staticText" uuid="3c2c9f6e-8297-4030-a14d-e803bb9f136d" x="0" y="11" width="105" height="9" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Fuel in]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="70a56d76-baaf-415f-a974-dc45b2266c58" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[迄租油箱／電量]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="ae6b4b1f-84dd-4388-8436-855f5ea3fa68" x="0" y="40" width="105" height="44">
							<element kind="staticText" uuid="a0bae703-3b60-4f11-bee6-87b17e679f05" x="0" y="22" width="105" height="22" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Interior Inspection]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="f5ebad61-0256-4348-80f6-4f9ed040dd9e" x="0" y="0" width="105" height="22" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[內裝檢查]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="c91a44c4-43e0-4d29-b94e-b7edf78df9ca" x="0" y="84" width="105" height="28">
							<element kind="staticText" uuid="7bc219db-345f-4ed6-bf73-9f1aadfc73f7" x="0" y="14" width="105" height="14" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Necessary Accessories]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="8d909205-f81e-4393-a44b-732b22b2cc18" x="0" y="0" width="105" height="14" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[重要配件]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="bfa8dc96-9cc2-4b24-a8e3-6bf8bd253dfe" x="0" y="112" width="105" height="28">
							<element kind="staticText" uuid="20d04a2f-4539-441c-ad87-a0d6f8073605" x="0" y="0" width="105" height="14" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[檢查備註說明]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="24f57b6c-6e60-4e44-b3ed-5a852bf462c9" x="0" y="14" width="105" height="14" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Remark]]></text>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
					</element>
					<element kind="textField" uuid="042ccb57-a549-481f-aede-b39fee5ce149" x="375" y="0" width="164" height="20" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{mileageIn}+" KM"]]></expression>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="dba4a1ba-fb9b-4229-b7ac-b69655cce139" x="105" y="0" width="165" height="112">
						<element kind="textField" uuid="943c4ef9-31df-4e05-af1d-33902271bead" x="0" y="0" width="165" height="20" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{endDate}]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="08a5446c-6f8d-41b4-a61e-6e8c1435ef98" x="0" y="20" width="165" height="20" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{fuelIn}]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="fbabbdb7-e1d5-4645-9a77-d4c3414fd05a" x="0" y="40" width="165" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{warningLight}+"儀錶板警示燈 Warning Light"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="a7df3624-0444-4e7c-a243-033bf5d61686" x="0" y="84" width="165" height="14" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{vehicleLicense}+"汽車行照 Vehicle License"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="88191cdc-fcd7-493b-963d-4d59b6adf12a" x="0" y="98" width="165" height="14" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{hasKey}+"汽車鑰匙 key  "+$P{keyCount}+"  把"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="15886ac7-d40e-4deb-a576-b563e360f88e" x="0" y="51" width="165" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{odor}+"汽車空間氣味 Odor"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="4798031f-731c-402f-bbcc-0761b7c2bf5e" x="0" y="62" width="165" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{seatAndTrim}+"座椅、飾板 Seat & Trim"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="textField" uuid="6339c3b0-b5bd-4583-b80c-e0e7f5a48490" x="0" y="73" width="165" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
							<expression><![CDATA[" "+$P{carpetAndGlovebox}+"地毯、手套箱 Carpet & Glovebox"]]></expression>
							<box>
								<pen lineWidth="0.25"/>
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
					</element>
					<element kind="textField" uuid="fbc661a5-496c-4ceb-a1c8-82cf476ac96d" x="375" y="84" width="164" height="14" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{dashCams}+"行車紀錄器 Dash cams"]]></expression>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="74471725-93de-4c4d-bc72-99d671bcd2c3" x="375" y="20" width="164" height="20" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{tireDepth}+" mm"]]></expression>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="79988f41-3c4b-45b3-ac89-9909cd30c13b" x="375" y="40" width="164" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{engine}+"引擎(無異音、抖動) Engine"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="9a5dde3b-16fd-42dd-9ec3-b184db846ebf" x="270" y="84" width="105" height="28">
						<element kind="staticText" uuid="83657a8f-ee75-419c-b9f4-798b6a5de8cb" x="0" y="14" width="105" height="14" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
							<text><![CDATA[Other Accessories]]></text>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<box leftPadding="2"/>
						</element>
						<element kind="staticText" uuid="cb0074bd-329a-4c85-8181-686bb7051c55" x="0" y="0" width="105" height="14" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
							<text><![CDATA[其他配件 (若無則免填)]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2"/>
						</element>
						<box>
							<topPen lineWidth="0.25"/>
							<leftPen lineWidth="0.25"/>
							<bottomPen lineWidth="0.25"/>
							<rightPen lineWidth="0.25"/>
						</box>
					</element>
					<element kind="frame" uuid="0ab4aaab-5dc7-445a-8c48-804c1a376ac9" x="270" y="40" width="105" height="44">
						<element kind="staticText" uuid="b5732b9d-8553-4956-96bb-79a9615f89c8" x="0" y="22" width="105" height="22" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
							<text><![CDATA[Device Inspection]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<element kind="staticText" uuid="ca93c7ae-7996-4ed2-9e7f-d7eaad817a25" x="0" y="0" width="105" height="22" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
							<text><![CDATA[設備檢查]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2">
								<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
								<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="0.25"/>
							<leftPen lineWidth="0.25"/>
							<bottomPen lineWidth="0.25"/>
							<rightPen lineWidth="0.25"/>
						</box>
					</element>
					<element kind="frame" uuid="380ffed2-3f21-46be-945f-a20a5a614fef" x="0" y="140" width="270" height="59">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="2080d75c-9515-40b1-abd8-cf27f240e61a" x="105" y="38" width="165" height="21" blankWhenNull="true">
							<expression><![CDATA[" "+$P{returnMemberSignDate}]]></expression>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="a97d5853-d3bd-4114-a8c1-4428efbbda12" x="0" y="0" width="270" height="38">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<element kind="staticText" uuid="aa4b9d7e-22f7-43b2-89fd-3166d21c8f30" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" bold="true" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[迄租服務人員簽名]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="184b2e11-bde0-43bf-a440-4bd6be8ca24f" x="0" y="11" width="105" height="17" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Signature of Rental Representation]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="6f351066-74e6-492f-ba96-40e7802d2fe6" x="0" y="28" width="122" height="10" forecolor="#FF0905" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[須經本公司人員簽名確認，始視為正式還車。]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="c0971537-88bb-4800-a217-bfa409910736" x="122" y="28" width="148" height="10" forecolor="#FF0905" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[Vehicle return confirmation by Lessor's Rep.]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="image" uuid="0588c7b3-3db2-4641-846a-746db9615b28" x="105" y="0" width="165" height="28">
								<expression><![CDATA[$P{returnMemberSignImage}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.x" value="px"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="780cd511-e4ca-4523-905e-7deb71d0ad18" x="0" y="38" width="105" height="21">
							<element kind="staticText" uuid="daec239a-8cf3-4a94-837a-057a4bd3e9f2" x="0" y="11" width="105" height="10" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Signature Date of Rental Representation]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="6d6b5f53-62bc-46ed-aa51-81ca8f1212dc" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" bold="false" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[迄租服務人員簽名時間]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="adb526c8-164a-4b9b-8650-769222c325ca" x="270" y="140" width="269" height="59">
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<element kind="textField" uuid="ce7eda45-bc5d-405e-85da-8cdfc8276139" x="105" y="38" width="164" height="21" blankWhenNull="true">
							<expression><![CDATA[" "+$P{returnCustSignDate}]]></expression>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="c11efacf-2923-4c0a-b180-eec4dd9b36bb" x="0" y="0" width="269" height="38">
							<property name="com.jaspersoft.studio.unit.x" value="px"/>
							<element kind="staticText" uuid="4925d0fa-f2d3-4464-ba71-************" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" bold="true" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[承租人簽名]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="9581f3c6-7331-4218-8e01-ee813cd90cd5" x="0" y="11" width="105" height="17" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Signature of Lessee]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="4182de28-b1eb-45e8-8e82-a09cfa594446" x="0" y="28" width="93" height="10" forecolor="#FF0905" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[本人已確認車輛無遺留任何物品。]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="100dc8c3-3962-4005-a486-88727da652d8" x="93" y="28" width="176" height="10" forecolor="#FF0905" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Middle">
								<text><![CDATA[No personal belongings left in the vehicle.]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="image" uuid="04fd5124-8287-455f-873a-8a548cd9c361" x="105" y="0" width="164" height="28">
								<expression><![CDATA[$P{returnCustSignImage}]]></expression>
								<property name="com.jaspersoft.studio.unit.y" value="px"/>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<element kind="frame" uuid="*************-400b-8160-cce6bb9bddb8" x="0" y="38" width="105" height="21">
							<element kind="staticText" uuid="12fa0177-a935-4c2b-9d0b-2e6bc2e895cc" x="0" y="11" width="105" height="10" fontName="Calibri Light" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
								<text><![CDATA[Signature Date of Lessee]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="px"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<element kind="staticText" uuid="9d06b890-a9d5-43c6-ba29-c3618110f9b5" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" bold="false" hTextAlign="Left" vTextAlign="Bottom">
								<text><![CDATA[承租人簽名時間]]></text>
								<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
								<property name="com.jaspersoft.studio.unit.width" value="px"/>
								<box leftPadding="2"/>
							</element>
							<box>
								<topPen lineWidth="0.25"/>
								<leftPen lineWidth="0.25"/>
								<bottomPen lineWidth="0.25"/>
								<rightPen lineWidth="0.25"/>
							</box>
						</element>
						<box>
							<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="frame" uuid="9ad6d035-cae1-4db3-816a-b91e2df25057" x="270" y="0" width="105" height="20">
						<element kind="staticText" uuid="90c943da-c624-44d9-a7ae-db50ec317b06" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
							<text><![CDATA[迄租里程]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2"/>
						</element>
						<element kind="staticText" uuid="ad855a9b-4416-4f15-a0fd-d9bb1e763474" x="0" y="11" width="105" height="9" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
							<text><![CDATA[Mileage in (km)]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2"/>
						</element>
						<box>
							<pen lineWidth="0.25"/>
						</box>
					</element>
					<element kind="frame" uuid="24d8112e-bd94-488b-b194-386f831698c7" x="270" y="20" width="105" height="20">
						<element kind="staticText" uuid="7d0059c5-1ae4-45ca-843b-dc5099ecb600" x="0" y="0" width="105" height="11" fontName="微軟正黑體" fontSize="8.0" hTextAlign="Left" vTextAlign="Bottom">
							<text><![CDATA[胎紋深]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2"/>
						</element>
						<element kind="staticText" uuid="9d200684-8d14-43d1-9bef-794b99cb267f" x="0" y="11" width="105" height="9" fontName="微軟正黑體" fontSize="6.0" hTextAlign="Left" vTextAlign="Top">
							<text><![CDATA[Tire Depth (mm)]]></text>
							<property name="com.jaspersoft.studio.unit.height" value="px"/>
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<box leftPadding="2"/>
						</element>
					</element>
					<element kind="textField" uuid="01b570ce-e175-45ed-a7d0-9e4ad3103f8b" x="105" y="112" width="434" height="28" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Top">
						<expression><![CDATA[$P{returnRemark}]]></expression>
						<box>
							<topPen lineWidth="0.25"/>
							<leftPen lineWidth="0.25"/>
							<bottomPen lineWidth="0.25"/>
							<rightPen lineWidth="0.25"/>
						</box>
					</element>
					<element kind="textField" uuid="81a1291a-dcf2-465c-8721-f7d286d2e266" x="375" y="51" width="164" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{ac}+"冷氣運轉 A/C"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="d26f22c6-86eb-4515-9ff2-73f4e2e766cd" x="375" y="73" width="164" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{audioAndMultimedia}+"音響、多媒體主機 Audio & Multimedia"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<property name="com.jaspersoft.studio.unit.y" value="px"/>
						<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
					<element kind="textField" uuid="9415c9a2-b153-4ef8-ad99-e7ec7e64150e" x="375" y="62" width="164" height="11" fontName="微軟正黑體" fontSize="8.0" blankWhenNull="true" hTextAlign="Left" vTextAlign="Middle">
						<expression><![CDATA[" "+$P{windowAndDoorMirror}+"電動窗、後視鏡 Window & Door Mirror"]]></expression>
						<property name="com.jaspersoft.studio.unit.height" value="px"/>
						<box>
							<pen lineWidth="0.25"/>
							<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<leftPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
							<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
							<rightPen lineWidth="0.25" lineStyle="Solid" lineColor="#000000"/>
						</box>
					</element>
				</element>
			</element>
			<box>
				<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
			</box>
		</element>
	</title>
</jasperReport>
