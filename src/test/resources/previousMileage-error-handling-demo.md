# PreviousMileage Error Handling Implementation

This document demonstrates the comprehensive error handling and validation messages implemented for the previousMileage functionality in the mileage fee setting API.

## Error Constants Implemented

The following error constants have been added to `SubscribeHttpExceptionCode.java`:

### 1. PREVIOUS_MILEAGE_INVALID (30048)
- **Message**: "前次里程必須為正整數"
- **Condition**: When previousMileage ≤ 0
- **HTTP Status**: 200 (following existing pattern)

### 2. PREVIOUS_MILEAGE_GREATER_THAN_CURRENT (30049)
- **Message**: "前次里程不可大於目前里程"
- **Condition**: When previousMileage > currentMileage
- **HTTP Status**: 200 (following existing pattern)

### 3. PREVIOUS_MILEAGE_TOO_SMALL (30050)
- **Message**: "前次里程不可小於前一期結束里程"
- **Condition**: When previousMileage < previous stage's endMileage
- **HTTP Status**: 200 (following existing pattern)

### 4. STAGE_ONE_PREVIOUS_MILEAGE_CONFLICT (30051)
- **Message**: "第一期前次里程必須與出車里程一致"
- **Condition**: When stage 1 previousMileage doesn't match departMileage
- **HTTP Status**: 200 (following existing pattern)

## Validation Logic Implementation

The validation logic is implemented in the `validatePreviousMileage` method in `PriceInfoService.java`:

```java
private void validatePreviousMileage(Orders orders, OrderPriceInfo mileageOrderPriceInfo, Integer previousMileage, Integer currentMileage) {
    // Validate previousMileage is positive
    if (previousMileage <= 0) {
        throw new SubscribeException(PREVIOUS_MILEAGE_INVALID);
    }

    // Validate previousMileage ≤ currentMileage when both provided
    if (currentMileage != null && previousMileage > currentMileage) {
        throw new SubscribeException(PREVIOUS_MILEAGE_GREATER_THAN_CURRENT);
    }

    // Stage-specific validation logic...
}
```

## Error Response Format

All errors follow the consistent CarPlus API response format:

```json
{
    "code": 30048,
    "message": "前次里程必須為正整數",
    "success": false
}
```

## Test Coverage

Comprehensive tests have been implemented in:
- `PriceInfoServiceTest.java` - Unit tests for validation logic
- `PreviousMileageValidationTest.java` - Dedicated test suite for error handling

### Test Scenarios Covered:

1. **Invalid previousMileage validation**
   - previousMileage = 0
   - previousMileage < 0

2. **Mileage comparison validation**
   - previousMileage > currentMileage

3. **Backward compatibility**
   - API calls without previousMileage parameter
   - Null previousMileage handling

4. **Error message format validation**
   - User-friendly Chinese messages
   - Proper error codes
   - No technical jargon

5. **Data integrity validation**
   - No data modification on validation failure
   - Proper transaction rollback

## Requirements Compliance

✅ **Requirement 4.1**: Validate previousMileage > currentMileage returns proper error
✅ **Requirement 4.2**: Validate attempting to update paid mileage fee record returns proper error
✅ **Requirement 4.3**: Validate previousMileage conflicts with existing mileage chain return proper error
✅ **Requirement 4.4**: Validate validation failures do not modify data and return appropriate error codes

## Usage Examples

### Valid API Call
```bash
PATCH /internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt
{
    "acctId": 12345,
    "orderPriceInfoId": 67890,
    "previousMileage": 1000,
    "currentMileage": 1500
}
```

### Error Response Examples

**Invalid previousMileage:**
```json
{
    "code": 30048,
    "message": "前次里程必須為正整數",
    "success": false
}
```

**previousMileage > currentMileage:**
```json
{
    "code": 30049,
    "message": "前次里程不可大於目前里程", 
    "success": false
}
```

## Implementation Status

✅ Error constants defined
✅ Validation logic implemented
✅ User-friendly error messages
✅ Proper error codes and response formats
✅ Comprehensive test coverage
✅ Backward compatibility maintained
✅ Documentation completed

The comprehensive error handling and validation messages for previousMileage functionality have been successfully implemented and tested.