package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.enums.ETagPayment;
import com.carplus.subscribe.model.etag.ETagInfoRequest;
import com.carplus.subscribe.schedule.ETagTask;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class EtagServiceTest {

    @Autowired
    private ETagTask eTagTask;

    @Autowired
    private ETagService etagService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PriceInfoService priceInfoService;
    @Autowired
    private EtagInfoRepository etagInfoRepository;


    @Test
    public void recallEtagDepartFail() {
        eTagTask.recallEtagDepartFail();
    }

    @Test
    public void etagSettlement() {
        eTagTask.etagSettlement();
    }

    @Test
    public void manualSetEtagAmt() {
        ETagInfoRequest req = ETagInfoRequest.builder()
            .etagInfoId(3533)
            .paidETagAmt(500)
            .eTagPayment(ETagPayment.OFFICIAL)
            .existETagDetail(false)
            .remark("test")
            .build();

        etagService.manualSetEtagAmt("B11206060001", "EC0182", req);
    }

    @Test
    public void test() {
        OrderPriceInfo orderPriceInfo = priceInfoService.get(27353);
        etagService.processETag(orderPriceInfo, "B11209260006P07");
    }

    @Test
    public void test1() {
        etagService.rentCar("B11209230011", null);
    }

    @Test
    public void tesat2() {
        etagInfoRepository.getETagInfosByOrderNo("1").stream().filter(etag -> !etag.isUploaded() && etag.getPaidETagAmt() > 0);
        List<ETagInfo> etagIngoList = etagInfoRepository.getETagInfosByOrderNo("1").stream().filter(etag -> !etag.isUploaded() && Optional.ofNullable(etag.getPaidETagAmt()).orElse(0) > 0).collect(Collectors.toList());

    }

}
