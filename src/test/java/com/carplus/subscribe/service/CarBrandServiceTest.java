package com.carplus.subscribe.service;

import com.carplus.subscribe.exception.SubscribeException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class CarBrandServiceTest {
    @Autowired
    private CarBrandService carBrandService;

    @Test
    @DisplayName("測試找特定車廠牌-Ok")
    public void testFindByCarBrandShouldSuccess() {
        Assertions.assertDoesNotThrow(() -> carBrandService.findByBrandCode("00B02"));
    }

    @Test
    @DisplayName("測試找特定車廠牌-Failed")
    public void testFindByCarBrandShouldFailed() {
        Assertions.assertThrows(SubscribeException.class, () -> carBrandService.findByBrandCode("not_found_car_brand"));
    }
}
