package com.carplus.subscribe.service;

import carplus.common.response.Result;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.TaskClient;
import com.carplus.subscribe.model.request.task.UpdateTaskRequest;
import com.carplus.subscribe.model.response.task.TaskDetailResponse;
import com.carplus.subscribe.schedule.OrderPriceInfoTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TaskServiceTest {

    @Autowired
    private OrderPriceInfoTask orderPriceInfoTask;

    @Autowired
    private BuChangeService buChangeService;

    @Autowired
    private TaskClient taskClient;

    @Test
    public void test() {
        orderPriceInfoTask.notifyPayStageFee();
    }

    @Test
    public void testBuChange() {
        buChangeService.notifyReturn();
    }

    @Test
    public void testUpdateDepartMileage(){
        UpdateTaskRequest updateTaskRequest = new UpdateTaskRequest();
        updateTaskRequest.setId(129);
        updateTaskRequest.setMileage(200);
        Result<TaskDetailResponse> result = taskClient.updateRentalTask(updateTaskRequest);
        if (result.getStatusCode() != 0) {
            throw new SubscribeException(SubscribeHttpExceptionCode.UPDATE_TASK_FAIL);
        }
    }
}
