package com.carplus.subscribe.service;

import com.carplus.subscribe.enums.GeoDefine;
import com.carplus.subscribe.enums.StationDefine;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.station.StationResponse;
import com.carplus.subscribe.server.GoSmartServer;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class StationServiceTest {

    @Autowired
    private StationService stationService;

    @Autowired
    private GoSmartServer goSmartServer;

    @Test
    void getActiveStationMap() {

        stationService.getActiveStationMap().values().forEach(stations -> {
            stations.forEach(station -> {
                assertFalse(station.isDeleted(), "Station should not be deleted");
                assertFalse(Sets.newHashSet(
                    StationDefine.StationCategory.VIRTUAL,
                    StationDefine.StationCategory.COURIER,
                    StationDefine.StationCategory.DEALER
                ).contains(station.getStationCategory()), "Station category should not be in excluded categories");
                assertNotEquals("D", station.getStatus(), "Station status should not be 'D'");
            });
        });
    }

    @Test
    void testGetCarAreaInfosForCrs_CodeShouldBeGeoRegionEnum() {
        stationService.getCarAreaInfosForCrs().forEach(carAreaInfo -> {
            assertTrue(Arrays.stream(GeoDefine.GeoRegion.values())
                    .anyMatch(region -> region.name().equals(carAreaInfo.getCode())), "Code should be GeoRegion enum");
        });
    }

    @Test
    void getAllSubscribeAvailableStationsWithCorrectOrder() {
        List<City> cityArea = goSmartServer.getCityArea();
        Map<Integer, Integer> cityOrderMap = new HashMap<>();
        for (int index = 0; index < cityArea.size(); index++) {
            cityOrderMap.put(cityArea.get(index).getCityId(), index);
        }

        List<StationResponse> stations = stationService.getAllSubscribeAvailableStations();
        // 驗證站點排序規則
        for (int i = 0; i < stations.size() - 1; i++) {
            StationResponse current = stations.get(i);
            StationResponse next = stations.get(i + 1);

            Integer currentOrder = current.getCityId() != null ? cityOrderMap.get(current.getCityId()) : null;
            Integer nextOrder = next.getCityId() != null ? cityOrderMap.get(next.getCityId()) : null;

            // 驗證城市順序：當前城市的順序應小於等於下一個城市的順序（或其中一個為null）
            assert currentOrder == null
                || nextOrder == null
                || currentOrder <= nextOrder;

            // 驗證業務別順序：若城市相同，則中古（PREOWNED）業務應排在前面
            assert !Objects.equals(currentOrder, nextOrder)
                || current.getCarplusService() == next.getCarplusService()
                || (current.getCarplusService() == StationDefine.CarplusService.PREOWNED)
                || (next.getCarplusService() != StationDefine.CarplusService.PREOWNED);
        }

        // 驗證 null 城市 ID 是否都在列表最後
        boolean foundNull = false;
        for (StationResponse station : stations) {
            if (station.getCityId() == null) {
                foundNull = true;
            } else {
                assert !foundNull : "在 null 城市 ID 之後發現非 null 城市 ID";
            }
        }
    }
}