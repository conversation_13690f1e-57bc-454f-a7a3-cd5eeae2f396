package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dao.CampaignRepository;
import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.cars.req.CommonCarCriteria;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.inventory.resp.InventoryResponse;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAMPAIGN_NOT_FOUND;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CRS_CAR_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest
@Transactional
class CarsServiceTest {

    @Autowired
    private CarsService carsService;
    @Autowired
    private OrderService orderService;
    @MockBean
    private CrsService crsService;
    @Autowired
    private CampaignRepository campaignRepository;

    @Test
    void addCarFromCrsByPlateNo_WhenCarNotFound_ShouldThrowException() {
        // Arrange
        String plateNo = "ABC-1234";

        // 模擬三次調用都返回 null
        when(crsService.getCar(plateNo))
            .thenReturn(null);

        // Act & Assert
        SubscribeException exception = assertThrows(
            SubscribeException.class,
            () -> carsService.addCarFromCrsByPlateNo(plateNo)
        );

        // 驗證異常訊息
        assertEquals(CRS_CAR_NOT_FOUND, exception.getCode());

        // 驗證調用次數
        verify(crsService, times(3)).getCar(plateNo);

        // 驗證車籍未被新增
        assertNull(carsService.findByPlateNo(plateNo));
    }

    @Test
    void releaseCar_stolenCar() {
        // Arrange
        String plateNo = "RDW-1597";
        Cars car = carsService.findByPlateNo(plateNo);
        assertNotNull(car);
        assertEquals(CarDefine.CarStatus.Stolen.getCode(), car.getCarStatus());

        String orderNo = "M202411258512";
        Orders order = orderService.getOrder(orderNo);
        assertEquals(order.getStatus(), OrderStatus.STOLEN.getStatus());
        // 模擬還車過程中訂單狀態改變為 CLOSE
        order.setStatus(OrderStatus.CLOSE.getStatus());

        // Act
        carsService.releaseCar(order, car);

        // Assert
        assertEquals(CarDefine.CarStatus.Free.getCode(), car.getCarStatus());
    }

    @Test
    void testCarsCampaignCarsConditionCountMatchesUnlimitedRegionInventoryCount() {
        CommonCarCriteria criteria = new CommonCarCriteria();
        Campaign campaign = campaignRepository.findAll().stream().findAny().orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
        criteria.setCampaignId(campaign.getId());
        criteria.setSkip(0);
        criteria.setLimit(999);
        Page<? extends CarResponse> searchByPage = carsService.searchByPage(new PageRequest(Integer.MAX_VALUE, 0), criteria, null);
        Integer inventoryCount = carsService.getCarsRegionInventory(campaign.getCarsCondition(), 0, 999).stream()
            .filter(inventory -> inventory.getGeoRegion() == null).findAny()
            .map(InventoryResponse::getGeoInventory)
            .orElseThrow(() -> new RuntimeException("找不到不限區域車輛庫存"));
        assertEquals(searchByPage.getTotal(), inventoryCount.longValue(), "預期活動車輛查詢條件篩選過後的車輛查詢列表結果數量與活動車輛查詢條件篩選過後的不限區域車輛庫存相同");

        // 預設條件
        CarsCondition carsCondition = new CarsCondition();
        Integer inventoryCountWithDefaultCondition = carsService.getCarsRegionInventory(carsCondition, null, null).stream()
            .filter(inventory -> inventory.getGeoRegion() == null).findAny()
            .map(InventoryResponse::getGeoInventory)
            .orElseThrow(() -> new RuntimeException("找不到不限區域車輛庫存"));
        assertNotEquals(inventoryCount, inventoryCountWithDefaultCondition, "預期活動車輛查詢條件篩選過後的不限區域車輛庫存與預設條件的不限區域車輛庫存不同");
    }
}