package com.carplus.subscribe.service;

import carplus.common.utils.DateUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.db.mysql.dao.MainContractRepository;
import com.carplus.subscribe.db.mysql.dao.SubscribeLevelRepository;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.SubscribeLevel;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.MainContract;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.OrderStatus;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.cars.req.CarCriteria;
import com.carplus.subscribe.model.cars.resp.CarResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.request.contract.ContractCreateReq;
import com.carplus.subscribe.server.SrentalServer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ContractLogicTest {

    @Autowired
    private SrentalServer srentalServer;
    @Autowired
    private CarsService carsService;
    @Autowired
    private ContractLogic contractLogic;
    @Autowired
    private StationService stationService;
    @Autowired
    private SubscribeLevelRepository subscribeLevelRepository;
    @Autowired
    private MainContractRepository mainContractRepository;
    @Autowired
    private SubscribeCalendarService subscribeCalendarService;

    private CarResponse newCar;
    private CarResponse oldCar;
    private ContractCreateReq contractCreateReq;

    @BeforeAll
    void setUpAll() {
        CarCriteria carCriteria = new CarCriteria();
        carCriteria.setIsDelete(false);
        carCriteria.setIsFilterVirtualCar(true);
        carCriteria.setCarStatuses(Collections.singletonList(CarDefine.CarStatus.Free.getCode()));
        carCriteria.setLaunched(Collections.singletonList(CarDefine.Launched.open));
        carCriteria.setCarState(CarDefine.CarState.NEW);
        carsService.getCarResponse(carCriteria, null, null).stream().findFirst().ifPresent(car -> newCar = car);
        carCriteria.setCarState(CarDefine.CarState.OLD);
        carsService.getCarResponse(carCriteria, null, null).stream().findFirst().ifPresent(car -> oldCar = car);

        // Initialize validRequest and set common required fields
        contractCreateReq = new ContractCreateReq();
        contractCreateReq.setDepartStationCode("801");
        contractCreateReq.setMonth(3);
        contractCreateReq.setDisclaimer(true);
        contractCreateReq.setCustSource(2);
    }

    private Date getMinWorkDateForHolidayTest(boolean fromInternal, Cars car) {
        Calendar firstHolidayCalendar = srentalServer.getNextUpcomingHoliday(10, 0, 0, 0);
        return contractLogic.getMinWorkDate(fromInternal, car, firstHolidayCalendar);
    }

    private boolean theDateCanDepartForHolidayTest(Cars car, Date departDate, boolean fromInternal) {
        Date minWorkDate = getMinWorkDateForHolidayTest(fromInternal, car);
        String departDateStr = DateUtils.toDateString(departDate, "yyyyMMdd");
        List<String> subscribeUnAvailableDate = subscribeCalendarService.getUnavailableCalendarList(new Date()).stream().map(SubscribeCalendarDto::getDate).collect(Collectors.toList());
        return departDateStr.compareTo(DateUtils.toDateString(minWorkDate, "yyyyMMdd")) >= 0
            && !subscribeUnAvailableDate.contains(departDateStr);
    }

    private void contractCreateRequestValidateForHolidayTest(ContractCreateReq req, boolean fromInternal) {
        Stations stations = stationService.findByStationCode(req.getDepartStationCode());
        SubscribeLevel subscribeLevel = subscribeLevelRepository.findByLevel(req.getCarLevel());
        if (stations == null) {
            throw new SubscribeException(STATION_NOT_FOUND, req.getDepartStationCode());
        }
        if (req.getCompanyDriver() != null
            &&
            (StringUtils.isBlank(req.getCompanyDriver().getCompanyName())
                || StringUtils.isBlank(req.getCompanyDriver().getCompanyLocation())
                || StringUtils.isBlank(req.getCompanyDriver().getVatNumber()))
            &&
            !(StringUtils.isBlank(req.getCompanyDriver().getCompanyName())
                && StringUtils.isBlank(req.getCompanyDriver().getCompanyLocation())
                && StringUtils.isBlank(req.getCompanyDriver().getVatNumber()))
        ) {
            throw new SubscribeException(COMPANY_DRIVER_ERROR);
        }
        // 取車時間檢查，若為續約，自動計算取車時間
        if (StringUtils.isBlank(req.getMainContractNo())) {
            if (req.getExpectStartDate() == null) {
                throw new SubscribeException(EXPECT_DEPART_DATE_NOT_FOUND);
            }
            Cars car = carsService.findByPlateNo(req.getPlateNo());
            if (!theDateCanDepartForHolidayTest(car, new Date(req.getExpectStartDate().toEpochMilli()), fromInternal)) {
                throw new SubscribeException(fromInternal ? DEPART_TIME_CONSTRAINT_INTERNAL : CarDefine.CarState.NEW == car.getCarState() ? NEW_CAR_DEPART_TIME_CONSTRAINT : OLD_CAR_DEPART_TIME_CONSTRAINT);
            }
            if (req.getInvoice() == null) {
                req.setInvoice(Invoice.defaultInvoice());
            }
            if (!stations.checkBusinessTime(new Date(req.getExpectStartDate().toEpochMilli()))) {
                throw new SubscribeException(STATION_DEPART_TIME_CONSTRAINT);
            }
            if (subscribeLevel.getType().getMonths() > req.getMonth()) {
                throw new SubscribeException(LESS_THAN_MUST_RENT_DAYS);
            }
        } else {
            // 取母單還車時間
            MainContract mainContract = Optional.ofNullable(mainContractRepository.getMainContractByNo(req.getMainContractNo(), true)).orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.MAIN_CONTRACT_NOT_FOUND));
            Orders order = mainContract.getContracts().stream().map(contract ->
                contract.getOrders().stream().filter(orders ->
                    orders.getStatus() == OrderStatus.DEPART.getStatus()).findAny().orElse(null)).filter(Objects::nonNull).findAny().orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.ORDER_STATUS_NOT_BOOKING_NOT_DEPART));
            // 檢查續約時間是否早於原約預計還車時間
            if (order.getExpectEndDate().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay().isAfter(req.getExpectStartDate().atZone(DateUtils.ZONE_TPE).toLocalDate().atStartOfDay())) {
                throw new SubscribeException(ORDER_RENEW_START_DATE_BEFORE_MAIN_CONTRACT_EXPECT_END_DAY);
            }
            req.setExpectStartDate(order.getExpectEndDate());
        }
    }

    @Test
    void testValidDepartDateFromInternal() {
        // Arrange
        boolean fromInternal = true;
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant());
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertDoesNotThrow(() -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal));
    }

    @Test
    void testValidDepartDateFromInternalForHoliday() {
        // Arrange
        boolean fromInternal = true;
        contractCreateReq.setExpectStartDate(getMinWorkDateForHolidayTest(fromInternal, carsService.findByPlateNo(newCar.getPlateNo())).toInstant());
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertDoesNotThrow(() -> contractCreateRequestValidateForHolidayTest(contractCreateReq, fromInternal));
    }

    @Test
    void testInvalidDepartDateFromInternal() {
        // Arrange
        boolean fromInternal = true;
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant().minus(1, ChronoUnit.DAYS));
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertThrows(SubscribeException.class, () -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal), DEPART_TIME_CONSTRAINT_INTERNAL.getMsg());
    }

    @Test
    void testInvalidDepartDateFromInternalForHoliday() {
        // Arrange
        boolean fromInternal = true;
        contractCreateReq.setExpectStartDate(getMinWorkDateForHolidayTest(fromInternal, carsService.findByPlateNo(newCar.getPlateNo())).toInstant().minus(1, ChronoUnit.DAYS));
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertThrows(SubscribeException.class, () -> contractCreateRequestValidateForHolidayTest(contractCreateReq, fromInternal), DEPART_TIME_CONSTRAINT_INTERNAL.getMsg());
    }

    @Test
    void testValidDepartDateFromNonInternalForNewCar() {
        // Arrange
        boolean fromInternal = false;
        CarDefine.CarState carState = newCar.getCarState();
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant());
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertDoesNotThrow(() -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal));
    }

    @Test
    void testInvalidDepartDateFromNonInternalForNewCar() {
        // Arrange
        boolean fromInternal = false;
        CarDefine.CarState carState = newCar.getCarState();
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant().minus(1, ChronoUnit.DAYS));
        contractCreateReq.setPlateNo(newCar.getPlateNo());
        contractCreateReq.setCarLevel(newCar.getLevel().getLevel());

        // Act & Assert
        assertThrows(SubscribeException.class, () -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal), NEW_CAR_DEPART_TIME_CONSTRAINT.getMsg());
    }

    @Test
    void testValidDepartDateFromNonInternalForOldCar() {
        // Arrange
        boolean fromInternal = false;
        CarDefine.CarState carState = oldCar.getCarState();
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant());
        contractCreateReq.setPlateNo(oldCar.getPlateNo());
        contractCreateReq.setCarLevel(oldCar.getLevel().getLevel());

        // Act & Assert
        assertDoesNotThrow(() -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal));
    }

    @Test
    void testInvalidDepartDateFromNonInternalForOldCar() {
        // Arrange
        boolean fromInternal = false;
        CarDefine.CarState carState = oldCar.getCarState();
        contractCreateReq.setExpectStartDate(contractLogic.getMinWorkDate(fromInternal, newCar.getCarNo()).toInstant().minus(1, ChronoUnit.DAYS));
        contractCreateReq.setPlateNo(oldCar.getPlateNo());
        contractCreateReq.setCarLevel(oldCar.getLevel().getLevel());

        // Act & Assert
        assertThrows(SubscribeException.class, () -> contractLogic.contractCreateRequestValidate(contractCreateReq, fromInternal), OLD_CAR_DEPART_TIME_CONSTRAINT.getMsg());
    }
}