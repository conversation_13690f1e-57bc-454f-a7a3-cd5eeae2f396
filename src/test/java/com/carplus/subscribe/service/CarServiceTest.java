package com.carplus.subscribe.service;

import com.carplus.subscribe.App;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.model.request.CarsAddSingleRequest;
import com.carplus.subscribe.model.request.CarsUpdateRequest;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(classes = App.class)
@RunWith(SpringRunner.class)
public class CarServiceTest {

    @Autowired
    private CarsService carsService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private EntityChangeLogService entityChangeLogService;

    @Test
    public void test() {
        Orders orders = orderService.getOrder("B11210110009");
        Cars cars = carsService.findByPlateNo(orders.getPlateNo());
        carsService.releaseCar(orders, cars);
    }

    @Transactional
    @Test
    public void update_buChangeMasterId_null() {
        String plateNo = "RCX-6225";
        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getBuChangeMasterId() != null;

        CarsUpdateRequest carsUpdateRequest = new CarsUpdateRequest();
        carsUpdateRequest.setBuChangeMasterId(null);
        carsService.update(plateNo, carsUpdateRequest);

        assert carsService.findByPlateNo(plateNo).getBuChangeMasterId() == null;
    }

    @Test
    @DisplayName("當透過非 API 觸發 updateCars 時，應正確記錄車輛變更")
    public void updateCars_SimulateTriggeredByNonApi_ShouldLogCarChanges() {
        // Arrange
        String plateNo = "RDV-6903";
        CarsUpdateRequest request = new CarsUpdateRequest();
        request.setColorDesc("灰");
        request.setIsSealandLaunched(true);
        request.setEtagModel(ETagModelEnum.Headlight);

        List<EntityChangeLog> changeLogs = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, plateNo)
            .stream().filter(changeLog -> changeLog.getChangedBy().equals("SUB")).collect(Collectors.toList());
        int size = changeLogs.size();

        // Act
        carsService.update(plateNo, request);

        // Assert
        List<EntityChangeLog> changeLogsAfterUpdateCar = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, plateNo);
        long count = changeLogsAfterUpdateCar.stream().filter(changeLog -> changeLog.getChangedBy().equals("SUB")).count();
        assert (int) count == size + 1;
    }

    @Test
    @DisplayName("當透過非 API 觸發 addSingle 時，應正確記錄車輛新增")
    public void addSingle_SimulateTriggeredByNonApi_ShouldLogCarInsertion() {
        // Arrange
        String plateNo = "GGG-8000";
        CarsAddSingleRequest request = new CarsAddSingleRequest();
        request.setPlateNo(plateNo);
        request.setCurrentMileage(103784);
        request.setCarModelCode("S0002");
        request.setCarState(CarDefine.CarState.NEW);
        request.setSubscribeLevel(1);
        request.setLaunched(CarDefine.Launched.accident);
        request.setIsSealandLaunched(false);
        request.setTagIds(Collections.singletonList(3));
        request.setEquipIds(Arrays.asList(3, 6, 9, 12, 15));
        request.setGearType(CarDefine.GearType.at);
        request.setColorDesc("灰");
        request.setFuelType(CarDefine.FuelType.petrol95);
        request.setCarType(CarDefine.CarType.sedan);
        request.setEnergyType(CarDefine.EnergyType.ELECTRIC);
        request.setVatNo("12345678");
        request.setDisplacement(2000);
        request.setSeat(5);
        request.setMfgYear("2019");
        request.setMfgMonth("10");
        request.validate();

        List<EntityChangeLog> changeLogs = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, plateNo)
            .stream().filter(changeLog -> changeLog.getChangedBy().equals("SUB")).collect(Collectors.toList());
        int size = changeLogs.size();

        // Act
        carsService.addSingle(request, "K2765");

        // Assert
        List<EntityChangeLog> changeLogsAfterUpdateCar = entityChangeLogService.getByMainEntityAndPrimaryKey(Cars.class, plateNo);
        long count = changeLogsAfterUpdateCar.stream().filter(changeLog -> changeLog.getChangedBy().equals("SUB")).count();
        assert (int) count == size + 1;
    }
}
