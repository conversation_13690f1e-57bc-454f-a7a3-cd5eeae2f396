package com.carplus.subscribe.service;

import com.carplus.subscribe.model.request.dealer.DealerOrderValidate;
import com.carplus.subscribe.model.response.dealer.DealerOrderQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCause;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@Transactional
@Slf4j
class DealerOrderServiceTest {

    @Autowired
    private DealerOrderService dealerOrderService;

    private Workbook workbook;

    @BeforeEach
    public void setUp() {
        String filePath = "C:\\Users\\<USER>\\Downloads\\ImportedDealerOrder.xlsx";

        try (InputStream fis = Files.newInputStream(Paths.get(filePath))) {
            workbook = WorkbookFactory.create(fis);
        } catch (FileNotFoundException e) {
            throw new RuntimeException("文件未找到: " + filePath, e);
        } catch (Exception e) {
            throw new RuntimeException("初始化 workbook 時發生錯誤", e);
        }
    }

    @Test
    void validateDealerOrderWorkbookAndSave() {

        DealerOrderValidate dealerOrderValidate = dealerOrderService.validateDealerOrderWorkbookAndSave(workbook, "test-uuid", "header-member-id");
        log.info("dealerOrderValidate: {}", dealerOrderValidate);
    }

    @Test
    void validateDealerOrderWorkbookAndSave_Twice() throws InterruptedException {
        int threadCount = 2;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        AtomicReference<Exception> caughtException = new AtomicReference<>();

        // 創建兩個執行緒同時進行匯入
        for (int i = 0; i < threadCount; i++) {
            String uuid = "test-uuid-" + i;
            new Thread(() -> {
                try {
                    // 等待統一開始訊號
                    startLatch.await();

                    dealerOrderService.validateDealerOrderWorkbookAndSave(workbook, uuid, "header-member-id");
                } catch (Exception e) {
                    caughtException.set(e);
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        // 同時釋放所有執行緒
        startLatch.countDown();

        // 等待所有執行緒完成
        endLatch.await(10, TimeUnit.SECONDS);

        // 驗證是否有捕獲到預期的異常
        Exception exception = caughtException.get();
        assertNotNull(exception);

        Throwable rootCause = getRootCause(exception);
        assertTrue(rootCause instanceof SQLIntegrityConstraintViolationException);
        assertTrue(rootCause.getMessage().contains("uk_csat_orderNo_source"));
    }

    @Test
    void getProcessingDealerOrdersByPlateNo() {

        for (DealerOrderQueryResponse response : dealerOrderService.getProcessingDealerOrdersByPlateNo("RDX-8952")) {
            assert response.getDealerUserId() != null && response.getDealerUserId() > 0;
            assert response.getCustomerInfo().getIdNo() != null;
        }
    }
}