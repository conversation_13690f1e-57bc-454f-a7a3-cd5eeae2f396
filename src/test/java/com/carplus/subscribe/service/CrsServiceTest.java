package com.carplus.subscribe.service;

import com.carplus.subscribe.feign.CrsClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class CrsServiceTest {

    @Autowired
    private CrsService crsService;

    @Autowired
    private CrsClient crsClient;

    @Test
    public void test() {
        crsService.checkIsSubscribeCars("RDT-7652");

    }
}
