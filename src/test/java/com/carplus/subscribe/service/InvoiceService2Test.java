package com.carplus.subscribe.service;

import carplus.common.enums.HeaderDefine;
import carplus.common.response.Result;
import com.carplus.subscribe.enums.inv.BusinessInfoEnum;
import com.carplus.subscribe.enums.inv.InvoiceDataEnum;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.model.invoice.v2.InvoiceInfoQueue;
import com.carplus.subscribe.model.invoice.v2.InvoiceIssueDetailRequest;
import com.carplus.subscribe.model.invoice.v2.InvoiceIssueRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.Collections;

import static com.carplus.subscribe.constant.CarPlusConstant.CARPLUS_COMPANY_VAT_NO;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class InvoiceService2Test {

    @Autowired
    private FinServiceBusClient finServiceBusClient;

    @Test
    public void test() {
        InvoiceIssueRequest invoiceIssueRequest = new InvoiceIssueRequest();
        invoiceIssueRequest.setIssueTransactionID("test");
        invoiceIssueRequest.setBusinessType(BusinessInfoEnum.BusinessType.E5);
        invoiceIssueRequest.setBusinessSubType(CARPLUS_COMPANY_VAT_NO);
        invoiceIssueRequest.setBuyerName("劉羣冠");
        invoiceIssueRequest.setInvoiceType(InvoiceDataEnum.InvoiceType.EC);
        invoiceIssueRequest.setVatNo(CARPLUS_COMPANY_VAT_NO);
        invoiceIssueRequest.setSellerVatNo(CARPLUS_COMPANY_VAT_NO);
        invoiceIssueRequest.setTaxType(InvoiceDataEnum.TaxType.T);
        invoiceIssueRequest.setTotalVATAmount(10000);
        invoiceIssueRequest.setNotifyBuyerName("劉羣冠");
        invoiceIssueRequest.setNotifyBuyerEmail("<EMAIL>");
        invoiceIssueRequest.setNotifyBuyerMobile("0912345600");
        invoiceIssueRequest.setIsDonate(false);
        invoiceIssueRequest.setDonateCode(null);
        invoiceIssueRequest.setCarrierID1(null);
        invoiceIssueRequest.setCarrierID2(null);
//        invoiceIssueRequest.setCarrierType(InvoiceDataEnum.CarrierType.CarrierType_EG0142.getCode());
        invoiceIssueRequest.setMemberID("0912345600");

        InvoiceIssueDetailRequest invoiceDetail = new InvoiceIssueDetailRequest();
        invoiceDetail.setItemNo(1);
        invoiceDetail.setQty(1);
        invoiceDetail.setTaxType(InvoiceDataEnum.TaxType.T);
        invoiceDetail.setItemVatPrice(10000);
        invoiceDetail.setItemVatAmount(10000);
        invoiceDetail.setDepenceDocType(InvoiceDataEnum.DepenceDocType.orders);
        invoiceDetail.setPlateNo("RAG-2718");

        invoiceIssueRequest.setInvoiceIssueDetailList(Collections.singletonList(invoiceDetail));
        Result<InvoiceInfoQueue> json = finServiceBusClient.createInvoice(HeaderDefine.Platform.SERVER, HeaderDefine.SystemKind.SUB, "K2456", invoiceIssueRequest);

        System.out.println(json);
    }
}
