package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.dao.SkuRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SkuServiceTest {

    @Mock
    private SkuRepository skuRepository;

    @InjectMocks
    private SkuService skuService;

    private List<Sku> mockSkus;

    @BeforeEach
    void setUp() {
        mockSkus = new ArrayList<>();
        
        // 建立測試資料
        Sku sku1 = new Sku();
        sku1.setCode("airFresher001");
        sku1.setType("airFresher");
        sku1.setName("植萃馥郁淨化噴霧");
        sku1.setUnitPrice(238);
        sku1.setDescription("【美國進口】優雅沉醉 百聞不膩的花清香");
        sku1.setOfficial(true);
        sku1.setCashier(true);
        
        Sku sku2 = new Sku();
        sku2.setCode("airFresher002");
        sku2.setType("airFresher");
        sku2.setName("男性魅力淨化噴霧");
        sku2.setUnitPrice(238);
        sku2.setDescription("【美國進口】古龍沉香 燃造車內迷人氛圍");
        sku2.setOfficial(true);
        sku2.setCashier(true);
        
        Sku sku3 = new Sku();
        sku3.setCode("recorder002");
        sku3.setType("recorder");
        sku3.setName("MIO原廠行車紀錄器");
        sku3.setUnitPrice(2980);
        sku3.setDescription("原廠保固1年 限量贈記憶卡&專業吸盤支架");
        sku3.setOfficial(true);
        sku3.setCashier(true);
        
        Sku sku4 = new Sku();
        sku4.setCode("sprayCleaner009");
        sku4.setType("sprayCleaner");
        sku4.setName("玻璃無痕速淨噴霧");
        sku4.setUnitPrice(388);
        sku4.setDescription("【美國進口】常保車窗及後視鏡清晰視野");
        sku4.setOfficial(true);
        sku4.setCashier(true);
        
        Sku sku5 = new Sku();
        sku5.setCode("towel001");
        sku5.setType("towel");
        sku5.setName("車用超細纖維擦拭布");
        sku5.setUnitPrice(39);
        sku5.setDescription("車主必備 車輛全領域展開");
        sku5.setOfficial(true);
        sku5.setCashier(true);
        
        Sku sku6 = new Sku();
        sku6.setCode("voucher002");
        sku6.setType("voucher");
        sku6.setName("晶亮美容兌換劵");
        sku6.setUnitPrice(6500);
        sku6.setDescription("深層美容清潔，讓車輛內外裝亮麗如新");
        sku6.setOfficial(true);
        sku6.setCashier(true);
        
        Sku sku7 = new Sku();
        sku7.setCode("wipe010");
        sku7.setType("wipe");
        sku7.setName("懶人必備專業車用濕巾");
        sku7.setUnitPrice(388);
        sku7.setDescription("【美國進口】隨手一擦 內裝清潔溜溜");
        sku7.setOfficial(true);
        sku7.setCashier(true);
        
        mockSkus.addAll(Arrays.asList(sku1, sku2, sku3, sku4, sku5, sku6, sku7));
    }

    @Test
    @DisplayName("測試生成新的SKU代碼 - 已有相同類型的商品")
    void generateSkuCode_WithExistingType_ShouldIncrementHighestCode() {
        // Arrange - 設置 findByType 方法的模擬行為
        when(skuRepository.findByType("airFresher")).thenReturn(
            mockSkus.stream().filter(s -> "airFresher".equals(s.getType())).collect(Collectors.toList())
        );
        when(skuRepository.findByType("wipe")).thenReturn(
            mockSkus.stream().filter(s -> "wipe".equals(s.getType())).collect(Collectors.toList())
        );
        when(skuRepository.findByType("sprayCleaner")).thenReturn(
            mockSkus.stream().filter(s -> "sprayCleaner".equals(s.getType())).collect(Collectors.toList())
        );
        
        // Act & Assert - 測試已有相同類型的商品
        String newAirFresherCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "airFresher");
        assertEquals("airFresher003", newAirFresherCode, "應該生成下一個序號的代碼");
        
        String newWipeCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "wipe");
        assertEquals("wipe011", newWipeCode, "應該基於最大值 wipe010 生成 wipe011");
        
        String newSprayCleanerCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "sprayCleaner");
        assertEquals("sprayCleaner010", newSprayCleanerCode, "應該基於 sprayCleaner009 生成 sprayCleaner010");
    }

    @Test
    @DisplayName("測試生成新的SKU代碼 - 沒有相同類型的商品")
    void generateSkuCode_WithNewType_ShouldStartWithOne() {
        // Arrange - 設置 findByType 方法的模擬行為，返回空列表表示沒有相同類型的商品
        when(skuRepository.findByType("newType")).thenReturn(new ArrayList<>());
        
        // Act & Assert - 測試沒有相同類型的商品
        String newTypeCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "newType");
        assertEquals("newType001", newTypeCode, "新類型應該從 001 開始");
    }

    @Test
    @DisplayName("測試生成新的SKU代碼 - 處理不連續的代碼")
    void generateSkuCode_WithNonSequentialCodes_ShouldUseHighestValue() {
        // Arrange - 添加不連續的代碼
        Sku skuNonSequential = new Sku();
        skuNonSequential.setCode("recorder005");
        skuNonSequential.setType("recorder");
        
        List<Sku> recorderSkus = mockSkus.stream()
            .filter(s -> "recorder".equals(s.getType()))
            .collect(Collectors.toList());
        recorderSkus.add(skuNonSequential);
        
        // 設置 findByType 方法的模擬行為
        when(skuRepository.findByType("recorder")).thenReturn(recorderSkus);
        
        // Act & Assert
        String newRecorderCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "recorder");
        assertEquals("recorder006", newRecorderCode, "應該基於最大值 recorder005 生成 recorder006");
    }

    @Test
    @DisplayName("測試生成新的SKU代碼 - 處理非標準格式的代碼")
    void generateSkuCode_WithNonStandardCodes_ShouldHandleGracefully() {
        // Arrange - 添加非標準格式的代碼
        Sku skuNonStandard = new Sku();
        skuNonStandard.setCode("towelXYZ");
        skuNonStandard.setType("towel");
        
        List<Sku> towelSkus = mockSkus.stream()
            .filter(s -> "towel".equals(s.getType()))
            .collect(Collectors.toList());
        towelSkus.add(skuNonStandard);
        
        // 設置 findByType 方法的模擬行為
        when(skuRepository.findByType("towel")).thenReturn(towelSkus);
        
        // Act & Assert - 應該忽略非標準格式，使用有效的最大值
        String newTowelCode = ReflectionTestUtils.invokeMethod(skuService, "generateSkuCode", "towel");
        assertEquals("towel002", newTowelCode, "應該基於有效的最大值 towel001 生成 towel002");
    }
}