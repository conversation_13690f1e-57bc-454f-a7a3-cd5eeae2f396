package com.carplus.subscribe.service;

import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.Acquirer;
import com.carplus.subscribe.enums.PayFor;
import com.carplus.subscribe.enums.PaymentCategory;
import com.carplus.subscribe.enums.PriceInfoDefinition;
import com.carplus.subscribe.model.auth.AuthUser;
import com.carplus.subscribe.model.payment.TradeHistory;
import com.carplus.subscribe.model.payment.resp.TradeHistoryResp;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.PaymentServer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class NotifyServiceTest {

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private NotifyToCService notifyToCService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private AuthServer authServer;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private PaymentServiceV2 paymentServiceV2;

    @Autowired
    private PaymentServer paymentServer;

    @Test
    public void notifyReturnCar() {
        Orders orders = orderService.getOrder("M202312227316");
        AuthUser authUser = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        notifyService.notifyReturnCar(orders, authUser);
    }

    @Test
    public void notifyReturnNotClose() {
        Orders orders = orderService.getOrder("M202312227316");
        AuthUser authUser = authServer.getUserWithRetry(orders.getContract().getMainContract().getAcctId());
        notifyService.notifyReturnNotClose(orders, authUser);
    }

    @Test
    public void notifyCancelOrder() {
        Orders orders = orderService.getOrder("M202312227316");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyService.notifyCancelOrder(orders, authUser);
    }


    @Test
    public void notifyEmpDiscount() {
        Orders orders = orderService.getOrder("M202310168748");
        notifyService.addEmpDiscountRequest(orders, priceInfoService.getByUid("e2aa660c-25c4-4266-8011-6e85a006ddd8"), PriceInfoDefinition.PriceInfoCategory.CancelBooking);
    }

    @Test
    public void notifyManualRefundSecurityDeposit() {
        Orders orders = orderService.getOrder("M202311166231");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        PaymentQueue queue = new PaymentQueue();
        queue.setAcquirer(Acquirer.TW_TAISHIN);
        queue.setAmount(10000);
        queue.setTransactionNumber("M202311166231P01");
        PaymentInfo securityPaymentInfo =
            paymentServiceV2.getPaymentsByOrder("M202311166231").stream().filter(paymentInfo -> paymentInfo.getPaymentCategory() == PaymentCategory.PayAuth && paymentInfo.getPayFor() == PayFor.SecurityDeposit).findAny().get();
        queue.setAmount(securityPaymentInfo.getAmount());

        TradeHistoryResp tradeHistoryResp = paymentServer.getTappayHistory(securityPaymentInfo.getTradeId());
        Date transactionTime =
            Optional.ofNullable(tradeHistoryResp).map(TradeHistoryResp::getData).orElse(new ArrayList<>()).stream().filter(tradeHistory -> tradeHistory.getAction() == 1).findAny().map(TradeHistory::getMillis).map(Date::new)
                .orElse(new Date(securityPaymentInfo.getInstantCreateDate().toEpochMilli()));

        notifyService.notifyManualRefundSecurityDeposit(orders, authUser, queue, securityPaymentInfo, transactionTime);
    }

    @Test
    public void notifyEtagDepartFailNotApplyETag() {
        notifyService.notifyEtagDepartFailNotApplyETag(Collections.singletonList("TAA-1234"));
    }

    @Test
    public void notifyContractChange() {
        Orders orders = orderService.getOrder("M202311145076");
        notifyService.notifyContractChange(orders);
    }

    @Test
    public void notifyContractCancel() {
        Orders orders = orderService.getOrder("M202311145076");
        notifyService.notifyContractCancel(orders, orders.getPlateNo(), orders.getPlateNo(), orders.getLrentalContractNo());
    }


    @Test
    public void paidSecurityDepositNotifyEmailToC() {
        Orders orders = orderService.getOrder("B11209130007");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifySecurityDepositPaid(orders, authUser);
    }

    @Test
    public void notifyCreditSuccessToC() {
        Orders orders = orderService.getOrder("B11209130007");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyCreditSuccess(orders, authUser);
    }

    @Test
    public void notifyCreditFailToC() {
        Orders orders = orderService.getOrder("B11209130007");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyCreditFail(orders, authUser);
    }

    @Test
    public void notifyCancelOrderToC() {
        Orders orders = orderService.getOrder("B11209130007");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyCancelOrder(orders, authUser);
    }

    @Test
    public void notifyPayStageFeeToC() {
        Orders orders = orderService.getOrder("M202401108438");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyPayStageFee(orders, authUser);
    }

    @Test
    public void notifyRenewOrderToC() {
        Orders orders = orderService.getOrder("M202311202063");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyRenewOrder(orders, authUser);
    }

    @Test
    public void notifyReturnOrderToC() {
        Orders orders = orderService.getOrder("M202311173696");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyReturnOrder(orders, authUser);
    }

    @Test
    public void notifyModifyOrderToC() {
        Orders orders = orderService.getOrder("M202311202063");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyModifyOrder(orders, authUser);
    }

    @Test
    public void notifyRefundToC() {
        Orders orders = orderService.getOrder("B11209120003");
        AuthUser authUser = authServer.getUserWithRetry(1000214);
        notifyToCService.notifyRefund(orders, authUser, 1000000000);
    }

    @Test
    public void notifyCheckOutFail() {
        notifyService.notifyCheckOutFail(Arrays.asList("test"));
    }

}
