package com.carplus.subscribe.service;

import carplus.common.model.Page;
import carplus.common.model.PageRequest;
import com.carplus.subscribe.db.mysql.dto.SubscribeCalendarDto;
import com.carplus.subscribe.db.mysql.entity.calendar.SubscribeCalendar;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.calendar.CalendarUpdateRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Transactional
class SubscribeCalendarServiceTest {

    @Autowired
    private SubscribeCalendarService subscribeCalendarService;

    @Test
    void testSetUnavailableSubscribeCalendar_Success() {
        CalendarUpdateRequest request = new CalendarUpdateRequest();
        request.setDateList(Collections.singletonList("20250425"));
        request.setStationCodes(Arrays.asList("201", "202", "203"));

        List<SubscribeCalendar> result = subscribeCalendarService.setUnavailableSubscribeCalendar(request);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("20250425", result.get(0).getDate());
        assertEquals(Arrays.asList("201", "202", "203"), result.get(0).getStationCodes());
    }

    @Test
    void testSetUnavailableSubscribeCalendar_Fail_CalendarDateExist() {
        CalendarUpdateRequest initialRequest = new CalendarUpdateRequest();
        initialRequest.setDateList(Collections.singletonList("20250425"));
        initialRequest.setStationCodes(Arrays.asList("201", "202", "203"));

        List<SubscribeCalendar> initialResult = subscribeCalendarService.setUnavailableSubscribeCalendar(initialRequest);
        assertNotNull(initialResult);
        assertEquals(1, initialResult.size());

        // Try to create another calendar with the same date
        CalendarUpdateRequest duplicateRequest = new CalendarUpdateRequest();
        duplicateRequest.setDateList(Collections.singletonList("20250425")); // Same date as before
        duplicateRequest.setStationCodes(Arrays.asList("204", "205")); // Different station codes

        // Assert that a SubscribeException is thrown with CALENDAR_DATE_EXIST code
        SubscribeException exception = assertThrows(SubscribeException.class, () -> subscribeCalendarService.setUnavailableSubscribeCalendar(duplicateRequest));

        // Verify the exception details
        assertEquals(SubscribeHttpExceptionCode.CALENDAR_DATE_EXIST, exception.getCode());
    }

    @Test
    void testSetUnavailableSubscribeCalendar_Fail_StationNotFound() {
        // Create a request with a valid date and an invalid station code "999"
        CalendarUpdateRequest request = new CalendarUpdateRequest();
        request.setDateList(Collections.singletonList("20250425"));
        request.setStationCodes(Arrays.asList("201", "999", "203"));

        // Assert that a SubscribeException is thrown with STATION_NOT_FOUND code
        SubscribeException exception = assertThrows(SubscribeException.class, () -> subscribeCalendarService.setUnavailableSubscribeCalendar(request));

        // Verify the exception details
        assertEquals(SubscribeHttpExceptionCode.STATION_NOT_FOUND, exception.getCode());
        assertTrue(exception.getMessage().contains("999"));
    }

    @Test
    void testUpdateUnavailableSubscribeCalendar_Success() {
        // 首先創建一個不可取車日期
        CalendarUpdateRequest initialRequest = new CalendarUpdateRequest();
        initialRequest.setDateList(Collections.singletonList("20250425"));
        initialRequest.setStationCodes(Arrays.asList("201", "202", "203"));
        subscribeCalendarService.setUnavailableSubscribeCalendar(initialRequest);

        // 嘗試更新該日期的站點代碼
        CalendarUpdateRequest updateRequest = new CalendarUpdateRequest();
        updateRequest.setDateList(Collections.singletonList("20250425")); // 使用相同的日期
        updateRequest.setStationCodes(Arrays.asList("204", "205")); // 更新站點代碼

        List<SubscribeCalendar> result = subscribeCalendarService.updateUnavailableSubscribeCalendar(updateRequest);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("20250425", result.get(0).getDate());
        assertEquals(Arrays.asList("204", "205"), result.get(0).getStationCodes());
    }

    @Test
    void testUpdateUnavailableSubscribeCalendar_Fail_CalendarUpdatableDateNotFound() {
        // 嘗試更新一個不存在的日期
        CalendarUpdateRequest updateRequest = new CalendarUpdateRequest();
        updateRequest.setDateList(Collections.singletonList("20250101")); // 不存在的日期
        updateRequest.setStationCodes(Arrays.asList("201", "202"));

        // Assert that a SubscribeException is thrown with CALENDAR_UPDATABLE_DATE_NOT_FOUND code
        SubscribeException exception = assertThrows(SubscribeException.class, () -> subscribeCalendarService.updateUnavailableSubscribeCalendar(updateRequest));

        // Verify the exception details
        assertEquals(SubscribeHttpExceptionCode.CALENDAR_UPDATABLE_DATE_NOT_FOUND, exception.getCode());
        assertTrue(exception.getMessage().contains("20250101"));
    }

    @Test
    void testGetUnavailableCalendarList_SortedByDateDescending() {

        Date testDate = new Date();
        PageRequest pageRequest = new PageRequest(20, 0);

        Page<SubscribeCalendarDto> result = subscribeCalendarService.getUnavailableCalendarList(testDate, pageRequest);

        assertNotNull(result);
        assertFalse(result.getList().isEmpty(), "結果列表不應為空");

        List<SubscribeCalendarDto> calendars = result.getList();
        if (calendars.size() > 1) {
            for (int i = 0; i < calendars.size() - 1; i++) {
                String currentDate = calendars.get(i).getDate();
                String nextDate = calendars.get(i + 1).getDate();

                assertTrue(currentDate.compareTo(nextDate) >= 0,
                    "日曆日期應按降序排列。當前日期：" + currentDate + " 應大於或等於下一個日期：" + nextDate);
            }
        }
    }
}