package com.carplus.subscribe.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@Transactional
@SpringBootTest
class ConfigServiceTest {

    @Autowired
    private ConfigService configService;

    @Test
    void setPauseEContractStation() {
        List<String> before = configService.getPauseEContractStationList();

        String pausedEContractStations = configService.setPauseEContractStation("206", false);
        assertFalse(pausedEContractStations.contains("206"));

        assertEquals(configService.getPauseEContractStationList().size(), before.size() - 1);

        pausedEContractStations = configService.setPauseEContractStation("206", true);
        assertTrue(pausedEContractStations.contains("206"));

        List<String> after = configService.getPauseEContractStationList();

        assertThat(after).usingRecursiveComparison().isEqualTo(before);
    }
}