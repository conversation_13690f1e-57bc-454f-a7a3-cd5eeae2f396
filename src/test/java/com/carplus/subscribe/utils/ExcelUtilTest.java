package com.carplus.subscribe.utils;

import com.carplus.subscribe.enums.DealerOrderExcelColumn;
import com.carplus.subscribe.model.region.Area;
import com.carplus.subscribe.model.region.City;
import com.carplus.subscribe.model.request.dealer.DealerOrderValidate;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcel;
import com.carplus.subscribe.model.response.dealer.DealerOrderExcelResponse;
import com.carplus.subscribe.server.GoSmartServer;
import com.carplus.subscribe.service.DealerOrderService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest
class ExcelUtilTest {

    private XSSFWorkbook workbook;
    private Sheet sheet;

    @Autowired
    private DealerOrderService dealerOrderService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private GoSmartServer goSmartServer;

    @BeforeEach
    void setUp() throws IOException {
        // 指定 Excel 檔案路徑
        String filePath = "C:\\Users\\<USER>\\Downloads\\經銷商訂單建立.xlsx";

        // 讀取 Excel 檔案
        FileInputStream fileInputStream = new FileInputStream(filePath);
        workbook = new XSSFWorkbook(fileInputStream);

        // 選擇工作表
        sheet = workbook.getSheetAt(0);
    }

    @Transactional
    @Test
    @DisplayName("測試訂單資料型別轉換 - 來自範例檔案")
    void testConvertDealerOrderDataFromFile() throws JsonProcessingException, ParseException {
        assertNotNull(sheet, "應該能讀取工作表");

        int rowCount = sheet.getPhysicalNumberOfRows();
        assertTrue(rowCount > 1, "工作表應該至少有 1 筆資料");

        Row dealerOrderExcelHeaders = sheet.getRow(0);
        DealerOrderExcelColumn [] dealerOrderExcelColumns = DealerOrderExcelColumn.values();
        for (int index = 0; index < dealerOrderExcelColumns.length; index++) {
            assertEquals(dealerOrderExcelColumns[index].getDescription(), dealerOrderExcelHeaders.getCell(index).getStringCellValue(), "匯入經銷商訂單標題名稱應該與 DealerOrderExcelColumn 相符");
        }

        for (DealerOrderExcelColumn column : dealerOrderExcelColumns) {
            assertNotNull(column.getTargetType(), "欄位 " + column.getDescription() + " 應該有目標型別");
        }

        int cellOffset = ExcelUtil.calculateCellOffset(sheet);
        for (int i = 1; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            StringBuilder columnValues = new StringBuilder();
            for (DealerOrderExcelColumn dealerOrderExcelColumn : dealerOrderExcelColumns) {
                Cell cell = row.getCell(dealerOrderExcelColumn.getIndex(cellOffset));
                Object cellValue = ExcelUtil.getCellValue(cell, dealerOrderExcelColumn.getTargetType());
                columnValues.append("欄位名稱: ").append(dealerOrderExcelColumn.getDescription()).append(", ")
                    .append("欄位值: ").append(cellValue).append(", ")
                    .append("欄位型別: ").append(Optional.ofNullable(cellValue).map(obj -> obj.getClass().getSimpleName()).orElse("欄位為空值")).append("; ");
            }
            log.info("columnValues: {}", columnValues);
        }

        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {

            Row row = sheet.getRow(rowNum);

            if (ExcelUtil.isEntireRowEmpty(row, cellOffset)) {
                continue;
            }
            Map<String, Map<City, Area>> postalMap = goSmartServer.getCityAreaMapByPostalCode();
            DealerOrderExcel dealerOrderExcel = dealerOrderService.convertRowToDealerOrderExcel(row, cellOffset, postalMap);
            log.info("dealerOrderExcel: {}", dealerOrderExcel);
        }

        long start = System.currentTimeMillis();

        DealerOrderValidate dealerOrderValidate = dealerOrderService.validateDealerOrderWorkbookAndSave(workbook, null, "K2765");

        long end = System.currentTimeMillis();
        log.info("time: {} ms", end - start);

        DealerOrderExcelResponse dealerOrderExcelResponse = DealerOrderExcelResponse.builder()
            .rowsCount(dealerOrderValidate.getRows().size() + dealerOrderValidate.getErrorRows().size())
            .successCount(dealerOrderValidate.getRows().size())
            .failCount(dealerOrderValidate.getErrorList().size())
            .errMessageList(dealerOrderValidate.getErrorList())
            .uuid(null)
            .build();
        log.info("dealerOrderExcelResponse: {}", objectMapper.writeValueAsString(dealerOrderExcelResponse));
    }
}