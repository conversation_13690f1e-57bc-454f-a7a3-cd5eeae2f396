package com.carplus.subscribe.server;

import carplus.common.response.Result;
import carplus.common.utils.HttpUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.enums.coupon.SourceItem;
import com.carplus.subscribe.model.coupon.Coupon;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = App.class)
public class CouponServerTest {

    @Autowired
    private CouponServer couponServer;

    @Value("${carplus.service.coupon}")
    private String couponUri;

    @Autowired
    private ObjectMapper objectMapper;

    private final int ACCT_ID = 33457250;
    private final String MEMBER_ID = "K2765";

    @Test
    public void testFindValidCoupons() {
        List<Coupon> validCoupons = couponServer.findValidCoupons(ACCT_ID);
        assertNotNull(validCoupons);
        assertFalse(validCoupons.isEmpty());
        System.out.println(validCoupons);
    }

    @Test
    public void testFindCouponBySequenceId() {
        String sequenceId = "7ffdc13d-7030-43ce-bab8-8ef2ad7a71a1";
        Coupon validCoupon = couponServer.findCouponBySequenceId(sequenceId, ACCT_ID);

        assertNotNull(validCoupon);
        assertEquals(sequenceId, validCoupon.getSequenceId());
        System.out.println(validCoupon);
    }

    @Test
    public void testReceiveCouponByCode() {
        String eventCode = "subtest003";
        assertDoesNotThrow(() -> couponServer.receiveCouponByCode(eventCode, ACCT_ID));
    }

    @Test
    public void testCompleteCouponLifecycleFlow() throws Exception {
        String couponName = "訂閱測試專用";
        String eventCode = "subcoupon" + System.currentTimeMillis() % 10000;

        // Step 1: 建立優惠券主檔
        Map<String, Object> createCouponRequest = new HashMap<>();
        createCouponRequest.put("frontName", couponName);
        createCouponRequest.put("name", couponName);
        createCouponRequest.put("description", "測試用優惠券");
        createCouponRequest.put("receivableQty", 1);
        createCouponRequest.put("discountType", "AMOUNT");
        createCouponRequest.put("subDiscountType", "FULL_AMOUNT_DISCOUNT");
        createCouponRequest.put("ruleType", "MASTER_DATE");
        createCouponRequest.put("overTotalPrice", "10000");
        createCouponRequest.put("calendarTypes", Arrays.asList("WEEKDAY", "HOLIDAY", "FESTIVAL"));
        createCouponRequest.put("discount", 100);
        createCouponRequest.put("designatedDays", null);
        createCouponRequest.put("designatedHours", null);
        createCouponRequest.put("designatedMinutes", null);
        createCouponRequest.put("startTime", Instant.now().minus(1, ChronoUnit.DAYS).toEpochMilli());
        createCouponRequest.put("endTime", Instant.now().plus(365, ChronoUnit.DAYS).toEpochMilli());
        createCouponRequest.put("carSeries", Collections.emptyList());
        createCouponRequest.put("sourceItem", SourceItem.SUBSCRIBE.getCode());

        Result<Object> createResult = HttpUtils.post(
                couponUri + "/internal/coupon/v3/coupon",
                HttpUtils.Options.custom()
                    .header("X-MemberId", MEMBER_ID)
                    .entity(() -> new StringEntity(objectMapper.writeValueAsString(createCouponRequest), ContentType.APPLICATION_JSON)))
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("建立優惠券失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        System.out.println("Step 1 - Created coupon: " + objectMapper.writeValueAsString(createResult));
        Thread.sleep(2000);

        // Step 2: 查詢主檔列表取得 couponId
        Result<Object> listResult = HttpUtils.get(
                couponUri + "/internal/coupon/v3/coupons",
                HttpUtils.Options.custom()
                    .queryString("page", "1")
                    .queryString("pageSize", "20")
                    .queryString("hasDeleteAndExpired", "false")
                    .queryString("source", SourceItem.SUBSCRIBE.getCode())
                    .queryString("orderField", "createdTime")
                    .queryString("orderType", "desc"))
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("查詢優惠券列表失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        JsonNode listResponse = objectMapper.valueToTree(listResult);
        String couponId = null;
        JsonNode couponList = listResponse.path("data").path("couponList");
        for (JsonNode coupon : couponList) {
            if (couponName.equals(coupon.path("name").asText())) {
                couponId = coupon.path("id").asText();
                break;
            }
        }

        assertNotNull(couponId, "找不到建立的優惠券");
        System.out.println("Step 2 - Found coupon ID: " + couponId);
        Thread.sleep(2000);

        // Step 3: 建立兌換序號
        Map<String, Object> addEventRequest = new HashMap<>();
        addEventRequest.put("eventCode", eventCode);
        addEventRequest.put("limitQtyOfPeople", 0);
        addEventRequest.put("refid", couponId);
        addEventRequest.put("type", 1);

        Result<Object> addEventResult = HttpUtils.post(
                couponUri + "/internal/coupon/v1/addEvent",
                HttpUtils.Options.custom()
                    .entity(() -> new StringEntity(objectMapper.writeValueAsString(addEventRequest), ContentType.APPLICATION_JSON)))
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("建立兌換序號失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        System.out.println("Step 3 - Created event code: " + eventCode);
        Thread.sleep(2000);

        // Step 4: 會員領取優惠券
        Map<String, Object> receiveRequest = new HashMap<>();
        receiveRequest.put("eventCode", eventCode);

        Result<Object> receiveResult = HttpUtils.post(
                couponUri + "/coupon/v1/receiveCouponByCode",
                HttpUtils.Options.custom()
                    .header(CarPlusConstant.AUTH_HEADER_ACCT, String.valueOf(ACCT_ID))
                    .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, "SUB")
                    .header(CarPlusConstant.AUTH_HEADER_PLATFORM, "SERVER")
                    .entity(() -> new StringEntity(objectMapper.writeValueAsString(receiveRequest), ContentType.APPLICATION_JSON)))
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("會員領取優惠券失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        System.out.println("Step 4 - Member received coupon: " + objectMapper.writeValueAsString(receiveResult));
        Thread.sleep(2000);

        // Step 5: 查詢會員優惠券取得 sequenceId
        Result<Object> memberCouponsResult = HttpUtils.get(
                couponUri + "/internal/coupon/v3/member/coupons",
                HttpUtils.Options.custom()
                    .queryString("acctId", String.valueOf(ACCT_ID))
                    .queryString("readyToTake", "false")
                    .queryString("isExpired", "true")
                    .queryString("source", SourceItem.SUBSCRIBE.getCode()))
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("查詢會員優惠券失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        JsonNode memberCouponsResponse = objectMapper.valueToTree(memberCouponsResult);
        String sequenceId = null;
        JsonNode memberCouponList = memberCouponsResponse.path("data");
        for (JsonNode memberCoupon : memberCouponList) {
            if (couponId.equals(memberCoupon.path("couponId").asText())) {
                sequenceId = memberCoupon.path("sequenceId").asText();
                break;
            }
        }

        assertNotNull(sequenceId, "找不到會員的優惠券");
        System.out.println("Step 5 - Found member coupon sequence ID: " + sequenceId);
        Thread.sleep(2000);

        // Step 6: 驗證優惠券使用前狀態
        Coupon beforeUseCoupon = couponServer.findCouponBySequenceId(sequenceId, ACCT_ID);
        assertNotNull(beforeUseCoupon, "使用前應該能找到優惠券");
        assertEquals(sequenceId, beforeUseCoupon.getSequenceId());
        System.out.println("Step 6 - Found coupon before use: " + beforeUseCoupon);
        Thread.sleep(2000);

        // Step 7: 使用優惠券
        String orderId = "TEST" + System.currentTimeMillis();
        long orderTime = System.currentTimeMillis();
        String phone = "0987654321";
        int totalAmount = 10000;
        int discountAmount = 100;

        String finalSequenceId = sequenceId;
        assertDoesNotThrow(() -> {
            couponServer.useCoupon(finalSequenceId, ACCT_ID, orderId, orderTime, phone, totalAmount, discountAmount);
        });
        System.out.println("Step 7 - Used coupon successfully");
        Thread.sleep(2000);

        // Step 8: 驗證使用後優惠券狀態 (應該找不到)
        Coupon afterUseCoupon = couponServer.findCouponBySequenceId(sequenceId, ACCT_ID);
        assertNull(afterUseCoupon, "使用後應該找不到優惠券");
        System.out.println("Step 8 - Coupon not found after use (expected)");
        Thread.sleep(2000);

        // Step 9: 取消優惠券使用
        assertDoesNotThrow(() -> {
            couponServer.cancelCouponUsed(finalSequenceId, ACCT_ID, orderId);
        });
        System.out.println("Step 9 - Cancelled coupon use successfully");
        Thread.sleep(2000);

        // Step 10: 驗證取消使用後優惠券狀態 (應該能重新找到)
        Coupon afterCancelCoupon = couponServer.findCouponBySequenceId(sequenceId, ACCT_ID);
        assertNotNull(afterCancelCoupon, "取消使用後應該能重新找到優惠券");
        assertEquals(sequenceId, afterCancelCoupon.getSequenceId());
        System.out.println("Step 10 - Found coupon after cancel use: " + afterCancelCoupon);
        Thread.sleep(2000);

        // Step 11: 刪除已派發的優惠券
        Result<Object> deleteUserCouponResult = HttpUtils.delete(
                couponUri + "/internal/coupon/v3/user/coupon/" + sequenceId,
                HttpUtils.Options.custom())
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("刪除會員優惠券失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        System.out.println("Step 11 - Deleted user coupon: " + objectMapper.writeValueAsString(deleteUserCouponResult));
        Thread.sleep(2000);

        // Step 12: 停用優惠券主檔
        Result<Object> disableCouponResult = HttpUtils.delete(
                couponUri + "/internal/coupon/v3/coupon/" + couponId + "/disable",
                HttpUtils.Options.custom())
            .then(res -> {
                TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                
                if (result.getStatusCode() != 0) {
                    throw new RuntimeException("停用優惠券主檔失敗: " + result.getMessage());
                }
                
                return result;
            })
            .fetch();

        System.out.println("Step 12 - Disabled coupon master: " + objectMapper.writeValueAsString(disableCouponResult));

        System.out.println("=== 完整優惠券生命週期測試完成 ===");
        System.out.println("優惠券名稱: " + couponName);
        System.out.println("兌換序號: " + eventCode);
        System.out.println("優惠券ID: " + couponId);
        System.out.println("會員優惠券序號: " + sequenceId);
        System.out.println("訂單ID: " + orderId);
        System.out.println("折扣金額: " + discountAmount);
        System.out.println("測試涵蓋：建立 → 領取 → 使用 → 取消使用 → 清理");
    }
}