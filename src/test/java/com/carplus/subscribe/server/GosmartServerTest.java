package com.carplus.subscribe.server;

import com.carplus.subscribe.model.presign.GcsGetUploadUrlReq;
import com.carplus.subscribe.model.presign.GcsUrlRes;
import com.carplus.subscribe.service.GcsService;
import com.carplus.subscribe.utils.DownloadUtils;
import com.carplus.subscribe.utils.HttpRequestUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.net.URI;

import static com.carplus.subscribe.enums.FileTypeEnum.JPG;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class GosmartServerTest {

    @Autowired
    private GoSmartServer goSmartServer;

    @Autowired
    private GcsService gcsService;


    @Test
    public void gcsUpload() throws Exception {
        gcsService.convertCarModelImg2Gcs();
    }
}
