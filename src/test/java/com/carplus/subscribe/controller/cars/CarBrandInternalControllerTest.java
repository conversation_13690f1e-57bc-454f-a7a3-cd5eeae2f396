package com.carplus.subscribe.controller.cars;

import com.carplus.subscribe.App;
import com.carplus.subscribe.model.cars.resp.CarBrandResponse;
import com.carplus.subscribe.model.request.CarBrandUpdateSeqRequest;
import com.carplus.subscribe.service.CarBrandService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static carplus.common.response.CarPlusCode.API_USE_INCORRECT;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.CAR_BRAND_CODES_DUPLICATE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.matchesPattern;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
class CarBrandInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private CarBrandService carBrandService;

    @Order(1)
    @Test
    void updateCarBrandSeq() throws Exception {
        // 準備測試資料
        CarBrandUpdateSeqRequest request = new CarBrandUpdateSeqRequest();
        request.setBrandCodes(Arrays.asList("00L02", "00N01", "00V01"));

        // 將請求物件轉換為 JSON 字串
        String jsonRequest = objectMapper.writeValueAsString(request);

        // 發送 HTTP 請求
        mockMvc.perform(patch("/internal/subscribe/carBrand/updateSeq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest))
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    @Order(2)
    @Test
    void updateCarBrandSeq_swapSeqNo_shouldSuccess() throws Exception {
        // 取得目前排序
        List<CarBrandResponse> originalBrands = carBrandService.getDescribeCarBrand();
        Map<String, Integer> originalSeqMap = originalBrands.stream()
            .collect(Collectors.toMap(CarBrandResponse::getBrandCode, CarBrandResponse::getSeqNo));

        // 準備交換 seqNo 的請求
        CarBrandUpdateSeqRequest request = new CarBrandUpdateSeqRequest();
        request.setBrandCodes(Arrays.asList("00N01", "00V01", "00L02"));

        String jsonRequest = objectMapper.writeValueAsString(request);

        // 發送請求並確認成功
        mockMvc.perform(patch("/internal/subscribe/carBrand/updateSeq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證更新後的排序
        List<CarBrandResponse> updatedBrands = carBrandService.getDescribeCarBrand();
        Map<String, Integer> updatedSeqMap = updatedBrands.stream()
            .collect(Collectors.toMap(CarBrandResponse::getBrandCode, CarBrandResponse::getSeqNo));

        // 確認 seqNo 已正確交換
        assertThat(updatedSeqMap.get("00N01")).isEqualTo(originalSeqMap.get("00L02"));
        assertThat(updatedSeqMap.get("00V01")).isEqualTo(originalSeqMap.get("00N01"));
        assertThat(updatedSeqMap.get("00L02")).isEqualTo(originalSeqMap.get("00V01"));
    }

    @Order(3)
    @Test
    void updateCarBrandSeq_duplicateSeqNo_shouldReturnError() throws Exception {
        // 準備包含重複廠牌代碼的測試資料
        CarBrandUpdateSeqRequest request = new CarBrandUpdateSeqRequest();
        request.setBrandCodes(Arrays.asList("00L02", "00L02", "00V01"));

        String jsonRequest = objectMapper.writeValueAsString(request);

        // 發送請求並預期得到錯誤回應
        mockMvc.perform(patch("/internal/subscribe/carBrand/updateSeq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest))
            .andExpect(jsonPath("$.statusCode").value(CAR_BRAND_CODES_DUPLICATE.getCode()))
            .andExpect(jsonPath("$.message").value(CAR_BRAND_CODES_DUPLICATE.getMsg()));
    }

    @Order(4)
    @Test
    void updateCarBrandSeq_notExistBrandCode_shouldReturnError() throws Exception {
        // 準備包含不存在廠牌代碼的測試資料
        CarBrandUpdateSeqRequest request = new CarBrandUpdateSeqRequest();
        request.setBrandCodes(Arrays.asList("00L02", "00N01", "00V01", "VVVVV"));

        String jsonRequest = objectMapper.writeValueAsString(request);

        // 發送 HTTP 請求
        mockMvc.perform(patch("/internal/subscribe/carBrand/updateSeq")
                .contentType(MediaType.APPLICATION_JSON)
                .content(jsonRequest))
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message", matchesPattern("\\[廠牌代碼不存在, 廠牌: .*]")));
    }
}