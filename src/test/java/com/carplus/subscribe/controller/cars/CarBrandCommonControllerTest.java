package com.carplus.subscribe.controller.cars;

import com.carplus.subscribe.App;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
class CarBrandCommonControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void listCarBrand() throws Exception {
        // 執行請求並取得回應
        MvcResult result = mockMvc.perform(get("/common/subscribe/carBrand/describe"))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data[*].seqNo").exists())
            .andReturn();

        // 解析回應內容
        String content = result.getResponse().getContentAsString();
        JsonNode root = objectMapper.readTree(content);
        JsonNode dataArray = root.get("data");

        // 驗證排序邏輯
        Integer previousSeqNo = null;
        boolean hasMetNull = false;

        for (JsonNode item : dataArray) {
            JsonNode currentSeqNoNode = item.get("seqNo");

            if (currentSeqNoNode.isNull()) {
                hasMetNull = true;
            } else {
                // 如果已經遇到過 NULL，後面就不應該再有非 NULL 值
                assertFalse(hasMetNull, "在 null 值之後找到非 null seqNo 值");

                int currentSeqNo = currentSeqNoNode.asInt();

                // 如果不是第一個元素，驗證順序
                if (previousSeqNo != null) {
                    assertTrue(currentSeqNo >= previousSeqNo,
                        String.format("SeqNo 不是以升序排列: 前值 = %d, 當前值 = %d", previousSeqNo, currentSeqNo));
                }

                previousSeqNo = currentSeqNo;
            }
        }
    }
}