package com.carplus.subscribe.controller.cars;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.ETagModelEnum;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.SubscribeLevelService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static carplus.common.response.CarPlusCode.API_USE_INCORRECT;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class CarsInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private SubscribeLevelService subscribeLevelService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private ObjectMapper objectMapper;

    private final String MEMBER_ID = "K2765";

    @Test
    void addCars_VirtualCarAndMfgYearNull() throws Exception {

        String plateNo = "RAA0999";
        String carNo = "1809990101";

        String addVirtualCarMfgYearNullRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carState\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"energyType\": \"GASOLINE\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [\n" +
            "    23, 22, 2, 21, 15, 16\n" +
            "  ],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"tagIds\": [\n" +
            "    3\n" +
            "  ],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": 2025,\n" +
            "  \"carNo\": \"" + carNo + "\",\n" +
            "  \"vatNo\": \"12345678\",\n" +
            "  \"prepWorkdays\": 10\n" +
            "}";

        String modifyLaunchedRequest = "{\n" +
            "  \"launched\": \"open\"\n" +
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addVirtualCarMfgYearNullRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = patch("/internal/subscribe/v1/car/{plateNo}/launched", plateNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(modifyLaunchedRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo);
        mockMvc.perform(requestBuilder)
            .andDo(print())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.images").isNotEmpty())
            .andExpect(jsonPath("$.data.images[0].year").isNumber())
            .andExpect(jsonPath("$.data.images[0].paths").isNotEmpty())
            .andExpect(jsonPath("$.data.prepWorkdays").value(10));

        requestBuilder = get("/common/subscribe/cars/carNo/{carNo}", carNo);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.carNo").value(carNo))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.images").isNotEmpty());

        requestBuilder = get("/common/subscribe/car")
            .param("orderBy", "MILEAGE")
            .param("skip", "0")
            .param("limit", "50")
            .param("sort", "ASC")
            .param("tagIds", "3");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[*].plateNo", hasItem(plateNo)))
            .andExpect(jsonPath("$.data.page.list[*].carNo", hasItem(carNo)))
            .andExpect(jsonPath("$.data.page.list[?(@.plateNo == 'RAA0999' && @.carNo == '1809990101')].images").isNotEmpty());
    }

    @Test
    void addCars_Failure_InvalidPrepWorkdays() throws Exception {
        String plateNo = "RAA0998";
        String carNo = "1809990102";

        String invalidPrepWorkdaysRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carStat\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [23, 22, 2, 21, 15, 16],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"tagIds\": [3],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": null,\n" +
            "  \"carNo\": \"" + carNo + "\",\n" +
            "  \"prepWorkdays\": 128\n" + // 傳入無效值
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .contentType(MediaType.APPLICATION_JSON)
            .content(invalidPrepWorkdaysRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.statusCode").value(API_USE_INCORRECT.getCode()))
            .andExpect(jsonPath("$.message").value(allOf(
                containsString("準備工作天數不可大於 127"),
                containsString("車籍統編不可為空"),
                containsString("訂閱類別不可為空"))))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void addCars_Failure_NotVirtualCarAndMfgYearNull() throws Exception {

        String plateNo = "RAG-1999";
        String carNo = "1809990101";

        String addVirtualCarMfgYearNullRequest = "{\n" +
            "  \"plateNo\": \"" + plateNo + "\",\n" +
            "  \"carStat\": \"NEW\",\n" +
            "  \"carModelCode\": \"S000P\",\n" +
            "  \"seat\": 5,\n" +
            "  \"fuelType\": \"petrol95\",\n" +
            "  \"currentMileage\": 1,\n" +
            "  \"equipIds\": [\n" +
            "    23, 22, 2, 21, 15, 16\n" +
            "  ],\n" +
            "  \"locationStationCode\": \"233\",\n" +
            "  \"subscribeLevel\": 29,\n" +
            "  \"energyType\": \"GASOLINE\",\n" +
            "  \"vatNo\": \"12208883\",\n" +
            "  \"stdPrice\": 1500000,\n" +
            "  \"carType\": \"sedan\",\n" +
            "  \"isSealandLaunched\": false,\n" +
            "  \"launched\": \"open\",\n" +
            "  \"gearType\": \"at\",\n" +
            "  \"carState\": \"OLD\",\n" +
            "  \"tagIds\": [\n" +
            "    3\n" +
            "  ],\n" +
            "  \"displacement\": 1997,\n" +
            "  \"mfgYear\": null,\n" +
            "  \"carNo\": \"" + carNo + "\"\n" +
            "}";

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content(addVirtualCarMfgYearNullRequest);
        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(containsString("非虛擬車之出廠年份不可為空")))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void checkCar() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RCE-7558")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.energyType").exists())
            .andExpect(jsonPath("$.data.displacement").exists())
            .andExpect(jsonPath("$.data.seat").exists())
            .andExpect(jsonPath("$.data.mfgYear").exists())
            .andExpect(jsonPath("$.data.mfgMonth").exists())
            .andExpect(jsonPath("$.data.currentMileage").exists())
            .andExpect(jsonPath("$.data.locationStationCode").exists());
    }

    @Test
    void checkCar_Failure_CarAlreadyInSubscribeSystem() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RCZ-7297")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getCode()))
            .andExpect(jsonPath("$.message").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getMsg()))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void checkCar_Failure_CrsCarNotFound() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/check/{plateNo}", "RAA9998")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(CRS_CAR_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value(CRS_CAR_NOT_FOUND.getMsg()))
            .andExpect(jsonPath("$.data").value(nullValue()));
    }

    @Test
    void addSingleCars() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
                "    \"plateNo\": \"RCE-7558\",\n" +
                "    \"currentMileage\": 103784,\n" +
                "    \"carModelCode\": \"S0002\",\n" +
                "    \"carState\": \"NEW\",\n" +
                "    \"subscribeLevel\": 1,\n" +
                "    \"launched\": \"accident\",\n" +
                "    \"isSealandLaunched\": false,\n" +
                "    \"tagIds\": [3],\n" +
                "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
                "    \"gearType\": \"at\",\n" +
                "    \"colorDesc\": \"灰\",\n" +
                "    \"fuelType\": \"petrol95\",\n" +
                "    \"vatNo\": \"12345678\",\n" +
                "    \"displacement\": 1997,\n" +
                "    \"seat\": 5,\n" +
                "    \"mfgYear\": 2019,\n" +
                "    \"prepWorkdays\": 10,\n" +
                "    \"carType\": \"sedan\"\n" +
                "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        requestBuilder = get("/internal/subscribe/cars/RCE-7558")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.carNo").isString())
            .andExpect(jsonPath("$.data.crsNo").doesNotExist())
            .andExpect(jsonPath("$.data.prepWorkdays").value(10));
    }

    @Test
    void addSingleCars_Failure_SubscribeLevelNotFound() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 9999,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"vatNo\": \"12208883\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(SUBSCRIBE_LEVEL_NOT_FOUND.getCode()));
    }

    @Test
    void addSingleCars_Failure_HasMutualExclusiveTagIds() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [3, 4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"vatNo\": \"12208883\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(TAG_IDS_CONTAIN_MONTHLY_DISCOUNTED_AND_LEVEL_DISCOUNTED.getCode()));
    }

    @Test
    void addSingleCars_Failure_MissingCorrespondingDiscountLevel() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
            "    \"plateNo\": \"RCE-7558\",\n" +
            "    \"currentMileage\": 103784,\n" +
            "    \"carModelCode\": \"S0002\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"accident\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [4],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"灰\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"vatNo\": \"12208883\",\n" +
            "    \"carType\": \"sedan\"\n" +
            "}");

        subscribeLevelService.checkSubscribeLevelExistence(1);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(MISSING_CORRESPONDING_DISCOUNT_LEVEL.getCode()));
    }

    @Test
    void addSingleCars_FromCRS_Success_StdPriceNotNull() throws Exception {
        // 使用已存在於 CRS DB 但不存在於 Subscribe DB 的車牌號碼
        String plateNo = "GGG-8989";

        // 準備新增請求 - 使用 CRS 中的車輛資料
        String addSingleCarRequest = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" +
            "    \"currentMileage\": 0,\n" +
            "    \"carModelCode\": \"S002G\",\n" +
            "    \"carState\": \"NEW\",\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"launched\": \"open\",\n" +
            "    \"isSealandLaunched\": false,\n" +
            "    \"tagIds\": [3],\n" +
            "    \"equipIds\": [3, 6, 9, 12, 15],\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"colorDesc\": \"紅色\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"vatNo\": \"12208883\",\n" +
            "    \"displacement\": 1600,\n" +
            "    \"seat\": 5,\n" +
            "    \"mfgYear\": 2025,\n" +
            "    \"prepWorkdays\": 7,\n" +
            "    \"carType\": \"sedan\"\n" +
            "}";

        // 執行新增請求
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addSingleCarRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk());

        // 驗證車輛已成功新增到 Subscribe DB 且 stdPrice 不為 null
        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.stdPrice").isNumber())
            .andExpect(jsonPath("$.data.stdPrice").value(not(nullValue())))
            .andExpect(jsonPath("$.data.carModel.carModelCode").value("S002G"))
            .andExpect(jsonPath("$.data.colorDesc").value("紅色"))
            .andExpect(jsonPath("$.data.prepWorkdays").value(7));

        // 進一步驗證 stdPrice 的值應該來自 CRS 的 stdPrice (10000000)
        Cars addedCar = carsService.findByPlateNo(plateNo);
        assertNotNull(addedCar);
        assertNotNull(addedCar.getStdPrice());
        assertEquals(10000000, addedCar.getStdPrice());
    }

    @Test
    void getCarInfo_Success_WithStatusNameAndPurposeCodeName() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/{plateNo}", "RBX-2073")
            .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.statusName").isString())
            .andExpect(jsonPath("$.data.purposeCodeName").isString());
    }

    @Test
    void getCarInfo_Success_WithETagModelChineseDescription() throws Exception {
        // 準備測試資料
        String plateNo = "RDV-6903";

        // 執行測試
        MockHttpServletRequestBuilder requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);
            
        mockMvc.perform(requestBuilder)
            .andDo(print())
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.changeLogs").isArray())
            .andExpect(jsonPath("$.data.changeLogs[*].changes[*].fieldChanges[?(@.fieldNameEn == 'etagModel')].oldValue",
                hasItem(either(nullValue()).or(anyOf(
                    is(ETagModelEnum.FrontGlass.getDescription()),
                    is(ETagModelEnum.Headlight.getDescription()),
                    is(ETagModelEnum.Plate.getDescription())
                )))))
            .andExpect(jsonPath("$.data.changeLogs[*].changes[*].fieldChanges[?(@.fieldNameEn == 'etagModel')].newValue",
                hasItem(either(nullValue()).or(anyOf(
                    is(ETagModelEnum.FrontGlass.getDescription()),
                    is(ETagModelEnum.Headlight.getDescription()),
                    is(ETagModelEnum.Plate.getDescription())
                )))));
    }

    @Test
    void updateCars_SetPrepWorkdaysToNull_Success() throws Exception {
        // 準備測試資料
        String plateNo = "RAG-2981";
        String updateRequest = "{\n" +
            "    \"carState\": \"OLD\",\n" +
            "    \"carModelCode\": \"S000S\",\n" +
            "    \"seat\": 5,\n" +
            "    \"energyType\": \"GASOLINE\",\n" +
            "    \"fuelType\": \"petrol95\",\n" +
            "    \"displacement\": 1798,\n" +
            "    \"currentMileage\": 59356,\n" +
            "    \"equipIds\": [],\n" +
            "    \"subscribeLevel\": 1,\n" +
            "    \"tagIds\": [],\n" +
            "    \"cnDesc\": null,\n" +
            "    \"type\": \"sedan\",\n" +
            "    \"gearType\": \"at\",\n" +
            "    \"mfgYear\": \"2019\",\n" +
            "    \"colorDesc\": \"銀\",\n" +
            "    \"etagModel\": null,\n" +
            "    \"etagNo\": null,\n" +
            "    \"vatNo\": null,\n" +
            "    \"prepWorkdays\": null,\n" +
            "    \"isSealandLaunched\": false\n" +
            "}";

        // 執行請求
        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(updateRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證 prepWorkdays 已被設為 null
        Cars updatedCar = carsService.findByPlateNo(plateNo);
        assertNull(updatedCar.getPrepWorkdays());
    }

    /**
     * 創建一個基礎的、有效的虛擬車請求資料 Map。
     * @return 包含所有有效預設值的 Map
     */
    private Map<String, Object> getBaseVirtualCarData() {
        Map<String, Object> data = new HashMap<>();
        data.put("currentMileage", 0);
        data.put("carModelCode", "S000P");
        data.put("carState", "NEW");
        data.put("subscribeLevel", 29);
        data.put("launched", "open");
        data.put("isSealandLaunched", false);
        data.put("locationStationCode", "233");
        data.put("tagIds", Collections.singletonList(3));
        data.put("equipIds", Arrays.asList(23, 22, 2, 21, 15, 16));
        data.put("gearType", "at");
        data.put("colorDesc", "白色");
        data.put("fuelType", "petrol95");
        data.put("energyType", "GASOLINE");
        data.put("vatNo", "12208883");
        data.put("displacement", 1997);
        data.put("seat", 5);
        data.put("mfgYear", "2025");
        data.put("mfgMonth", "01");
        data.put("prepWorkdays", 7);
        data.put("carType", "sedan");
        data.put("stdPrice", 1500000);
        return data;
    }

    /**
     * 生成用於新增車輛的 JSON 請求字串。
     * @param plateNo 車牌號碼
     * @param overrides 一個 Map，用於覆蓋或移除基礎資料中的欄位。如果 value 為 null，則該欄位將從 JSON 中移除。
     * @return JSON 格式的請求字串
     * @throws JsonProcessingException 如果 JSON 序列化失敗
     */
    private String createVirtualCarRequest(String plateNo, Map<String, Object> overrides) throws JsonProcessingException {
        Map<String, Object> data = getBaseVirtualCarData();
        data.put("plateNo", plateNo);

        if (overrides != null) {
            for (Map.Entry<String, Object> entry : overrides.entrySet()) {
                if (entry.getValue() == null) {
                    data.remove(entry.getKey());
                } else {
                    data.put(entry.getKey(), entry.getValue());
                }
            }
        }

        return objectMapper.writeValueAsString(data);
    }

    // --- Add Virtual Car Success Test Cases ---

    @Test
    public void addSingleCars_VirtualCar_FullFlow_Success() throws Exception {
        // 準備測試資料 - 使用新的車牌號碼
        final String plateNo = "RAA2000";
        final String colorDesc = "藍色";

        // 使用輔助方法生成請求內容
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("colorDesc", colorDesc);
        overrideMap.put("prepWorkdays", 10);
        overrideMap.put("stdPrice", 1800000);
        String addSingleCarRequest = createVirtualCarRequest(plateNo, overrideMap);

        // 1. 執行新增請求並驗證 API 回應成功
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(addSingleCarRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 2. 透過 API 驗證車輛已成功新增且資料正確
        requestBuilder = get("/internal/subscribe/cars/{plateNo}", plateNo)
            .contentType(MediaType.APPLICATION_JSON);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.carNo").isString())
            .andExpect(jsonPath("$.data.carModel.carModelCode").value("S000P"))
            .andExpect(jsonPath("$.data.colorDesc").value("藍色"))
            .andExpect(jsonPath("$.data.prepWorkdays").value(10))
            .andExpect(jsonPath("$.data.currentMileage").value(0))
            .andExpect(jsonPath("$.data.stdPrice").value(1800000));

        // 3. 直接從 Service/DB 層驗證虛擬車特性與資料持久化
        Cars addedCar = carsService.findByPlateNo(plateNo);
        assertNotNull(addedCar);
        assertTrue(addedCar.isVirtualCar());
        assertEquals(plateNo, addedCar.getPlateNo());
        assertEquals("藍色", addedCar.getColorDesc());
        assertEquals(0, addedCar.getCurrentMileage());
        assertEquals(10, addedCar.getPrepWorkdays());
        assertEquals(Integer.valueOf(1800000), addedCar.getStdPrice());
        assertNotNull(addedCar.getCarNo());
        assertFalse(addedCar.getCarNo().isEmpty());

        // 取得生成的車號供後續驗證使用
        String carNo = addedCar.getCarNo();

        // 4. 驗證虛擬車出現在公開的車輛列表 API 中
        MockHttpServletRequestBuilder listRequestBuilder = get("/common/subscribe/car")
            .param("orderBy", "MILEAGE")
            .param("skip", "0")
            .param("limit", "50")
            .param("sort", "ASC")
            .param("tagIds", "3");

        mockMvc.perform(listRequestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[*].plateNo", hasItem(plateNo)))
            .andExpect(jsonPath("$.data.page.list[*].carNo", hasItem(carNo)))
            .andExpect(jsonPath("$.data.page.list[?(@.plateNo == '" + plateNo + "')].colorDesc", hasItem(colorDesc)));
    }

    // --- Add Virtual Car Failure Test Cases ---

    @Test
    public void addSingleCars_VirtualCar_InvalidPlatePrefix_Failure() throws Exception {
        String plateNo = "XAA2001"; // 無效的前綴
        String invalidRequest = createVirtualCarRequest(plateNo, new HashMap<>());

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(invalidRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.message").value(containsString(CRS_CAR_NOT_FOUND.getMsg())));
    }

    @Test
    public void addSingleCars_VirtualCar_InvalidPlateLength_Failure() throws Exception {
        String plateNo = "RAA200"; // 無效的長度
        String invalidRequest = createVirtualCarRequest(plateNo, new HashMap<>());

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(invalidRequest);

        mockMvc.perform(requestBuilder)
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("車號格式錯誤, 虛擬車車號開頭須為 RAA 且長度為 7 碼")));
    }

    @Test
    public void addSingleCars_VirtualCar_MissingEnergyType_Failure() throws Exception {
        String plateNo = "RAA2002";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("energyType", null);
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(jsonPath("$.message").value(containsString("能源類別不可為空")));
    }

    @Test
    public void addSingleCars_VirtualCar_MissingDisplacement_Failure() throws Exception {
        String plateNo = "RAA2003";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("displacement", null);
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("排氣量不可為空")));
    }

    @Test
    public void addSingleCars_VirtualCar_MissingSeatCount_Failure() throws Exception {
        String plateNo = "RAA2004";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("seat", null);
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("座位數不可為空")));
    }

    @Test
    public void addSingleCars_VirtualCar_MissingMfgYear_Failure() throws Exception {
        String plateNo = "RAA2005";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("mfgYear", null);
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("出廠年份不可為空")));
    }

    @Test
    public void addSingleCars_VirtualCar_MissingLocationStationCode_Failure() throws Exception {
        String plateNo = "RAA2007";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("locationStationCode", null);
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("當前所在站所代碼不可為空")));
    }

    @Test
    public void addSingleCars_VirtualCar_DuplicatePlateNumber_Failure() throws Exception {
        String plateNo = "RAA2008";
        String requestBody = createVirtualCarRequest(plateNo, new HashMap<>());

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/cars/single")
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(requestBody);

        // 第一次建立應該成功
        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 第二次使用相同車牌建立，應該失敗
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getCode()))
            .andExpect(jsonPath("$.message").value(CAR_ALREADY_IN_SUBSCRIBE_SYSTEM.getMsg()));
    }

    @Test
    public void addSingleCars_VirtualCar_NotCarPlusVatNo_Failure() throws Exception {
        String plateNo = "RAA2009";
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("vatNo", "12345678");
        String invalidRequest = createVirtualCarRequest(plateNo, overrideMap);

        mockMvc.perform(post("/internal/subscribe/cars/single")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidRequest))
            .andExpect(status().isInternalServerError())
            .andExpect(jsonPath("$.message").value(containsString("虛擬車僅能為格上車籍")));
    }
}