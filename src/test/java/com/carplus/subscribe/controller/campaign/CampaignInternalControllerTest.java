package com.carplus.subscribe.controller.campaign;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CampaignRepository;
import com.carplus.subscribe.db.mysql.entity.Campaign;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.model.request.campaign.CampaignCriteria;
import com.carplus.subscribe.model.request.campaign.CampaignRequest;
import com.carplus.subscribe.model.request.campaign.CarsCondition;
import com.carplus.subscribe.model.response.campaign.CampaignCommonDetailResponse;
import com.carplus.subscribe.service.CampaignService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class CampaignInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CampaignService campaignService;

    @Autowired
    private CampaignRepository campaignRepository;

    private final String MEMBER_ID = "K2765";

    private static final String CAMPAIGN_DESCRIPTION =
        "<div class=\"campaign-content\">\n" +
            "  <h2>測試活動標題</h2>\n" +
            "  \n" +
            "  <p>這是一個測試活動的詳細介紹，我們提供多種訂閱方案供您選擇。</p>\n" +
            "  \n" +
            "  <p>活動期間內，新用戶可享受首月半價優惠。如需了解更多關於我們的訂閱車服務，請<a href=\"https://www.int.car-plus.cool/subscription/static/introduction\">點擊這裡查看訂閱車介紹</a>。</p>\n" +
            "  \n" +
            "  <ul class=\"benefits\">\n" +
            "      <li>無需負擔車輛折舊</li>\n" +
            "      <li>包含保險與保養</li>\n" +
            "      <li>全天候道路救援服務</li>\n" +
            "      <li>靈活的租期選擇</li>\n" +
            "  </ul>\n" +
            "  \n" +
            "  <div class=\"promotion-box\">\n" +
            "      <h3>限時優惠</h3>\n" +
            "      <p>即日起至活動結束，輸入優惠碼 <strong>TEST2025</strong> 可獲得額外 10% 折扣！</p>\n" +
            "  </div>\n" +
            "  \n" +
            "  <p>如有任何疑問，請<a href=\"mailto:<EMAIL>\">聯繫我們的客服團隊</a>或撥打服務熱線 0800-000-123。</p>\n" +
            "  \n" +
            "  <div class=\"disclaimer\">\n" +
            "      <p>※ 優惠細則依官網公告為準，本公司保留最終解釋權及活動變更權利。</p>\n" +
            "  </div>\n" +
            "</div>";

    private static final String UNSAFE_CAMPAIGN_DESCRIPTION =
        "<div class=\"campaign-content\">\n" +
            "  <h2>測試活動標題</h2>\n" +
            "  \n" +
            "  <p>這是一個測試活動的詳細介紹，我們提供多種訂閱方案供您選擇。</p>\n" +
            "  <script>alert('Hello!');</script>\n" + // Added risky <script> with alert
            "  \n" +
            "  <p>活動期間內，新用戶可享受首月半價優惠。如需了解更多關於我們的訂閱車服務，請<a href=\"javascript:alert('Sneaky!')\" onclick=\"console.log('Clicked!')\">點擊這裡查看訂閱車介紹</a>。</p>\n" + // Added javascript: and onclick
            "  \n" +
            "  <ul class=\"benefits\">\n" +
            "      <li>無需負擔車輛折舊</li>\n" +
            "      <li>包含保險與保養</li>\n" +
            "      <li>全天候道路救援服務</li>\n" +
            "      <li>靈活的租期選擇</li>\n" +
            "  </ul>\n" +
            "  \n" +
            "  <div class=\"promotion-box\">\n" +
            "      <h3>限時優惠</h3>\n" +
            "      <p>即日起至活動結束，輸入優惠碼 <strong>TEST2025</strong> 可獲得額外 10% 折扣！</p>\n" +
            "      <iframe src=\"http://malicious-site.com\" width=\"1\" height=\"1\"></iframe>\n" + // Added risky <iframe>
            "  </div>\n" +
            "  \n" +
            "  <p>如有任何疑問，請<a href=\"mailto:<EMAIL>\" onerror=\"alert('Error!')\">聯繫我們的客服團隊</a>或撥打服務熱線 0800-000-123。</p>\n" + // Added onerror
            "  \n" +
            "  <div class=\"disclaimer\">\n" +
            "      <object data=\"malicious.swf\"></object>\n" + // Added risky <object>
            "      <p>※ 優惠細則依官網公告為準，本公司保留最終解釋權及活動變更權利。</p>\n" +
            "      <embed src=\"http://dangerous-content.com\" type=\"application/x-shockwave-flash\">\n" + // Added risky <embed>
            "  </div>\n" +
            "</div>";

    /**
     * e.g.
     * ?geoRegion=N
     * &brandCode=00L02,00N01,00V02
     * &carState=NEW
     * &energyType=GASOLINE,GASOLINE_ELECTRIC
     * &monthFeeStart=10800
     * &monthFeeEnd=26800
     * &mfgYearFrom=2020
     * &mfgYearTo=2025
     * &tagIds=3
     * &vatNo=CARPLUS_COMPANY_VAT_NO
     * &orderBy=USE_MONTH_FEE (given by frontend)
     * &sort=ASC (given by frontend)
     * &skip=0 (given by frontend)
     * &limit=12 (given by frontend)
     */
    private static CarsCondition buildCarsCondition() {
        CarsCondition carsCondition = new CarsCondition();
        carsCondition.setBrandCode(Arrays.asList("00L02", "00N01", "00V02"));
        carsCondition.setMonthFeeStart(10800);
        carsCondition.setMonthFeeEnd(26800);
        carsCondition.setMfgYearFrom(2020);
        carsCondition.setMfgYearTo(2025);
        return carsCondition;
    }

    @BeforeEach
    void setUp() {
        // 建立測試資料(未刪除)
        Instant now = Instant.now();
        Campaign campaign = new Campaign();
        campaign.setTitle("測試活動");
        campaign.setDescription(CAMPAIGN_DESCRIPTION);
        campaign.setCarsCondition(buildCarsCondition());
        campaign.setBannerCategoryId(BuIdEnum.subscribe.getCode());
        campaign.setStartDate(now.minus(10, ChronoUnit.DAYS));
        campaign.setEndDate(now.plus(20, ChronoUnit.DAYS));
        // 建立測試資料(已刪除)
        Campaign deletedCampaign = new Campaign();
        deletedCampaign.setTitle("已刪除活動");
        deletedCampaign.setDescription(CAMPAIGN_DESCRIPTION);
        deletedCampaign.setCarsCondition(buildCarsCondition());
        deletedCampaign.setBannerCategoryId(BuIdEnum.subscribe.getCode());
        deletedCampaign.setStartDate(now.minus(10, ChronoUnit.DAYS));
        deletedCampaign.setEndDate(now.plus(20, ChronoUnit.DAYS));
        deletedCampaign.setDeleted(true);
        campaignRepository.saveAll(Arrays.asList(campaign, deletedCampaign));
    }

    @Test
    void createCampaign_success() throws Exception {
        // 準備測試資料
        CampaignRequest request = new CampaignRequest();
        request.setTitle("測試活動");
        request.setDescription(UNSAFE_CAMPAIGN_DESCRIPTION);
        CarsCondition carsCondition = buildCarsCondition();
        // 可以只提供月費起始，不提供月費結束
        carsCondition.setMonthFeeEnd(null);
        request.setCarsCondition(carsCondition);
        request.setBannerCategoryId(BuIdEnum.subscribe.getCode());
        Instant now = Instant.now();
        request.setStartDate(now);
        request.setEndDate(now.plus(30, ChronoUnit.DAYS));

        // 執行測試
        mockMvc.perform(post("/internal/subscribe/campaign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.title").value("測試活動"))
            .andExpect(jsonPath("$.data.description", allOf(
                containsString("<p>這是一個測試活動的詳細介紹"),       // 確認 <p> 保留
                containsString("<li>無需負擔車輛折舊</li>"),                   // 確認 <li> 保留
                containsString("<a href=\"mailto:<EMAIL>\""), // 確認 <a> 和 mailto 保留
                not(containsString("<script>")),                            // 確認移除腳本標籤
                not(containsString("</script>")),                           // 確認移除腳本結束標籤
                not(containsString("<iframe")),                             // 確認移除 iframe
                not(containsString("<object")),                             // 確認移除 object
                not(containsString("<embed")),                              // 確認移除 embed
                not(containsString("javascript:")),                         // 確認移除 javascript 協議
                not(containsString("onerror=")),                            // 確認移除 onerror 屬性
                not(containsString("onclick=")),                            // 確認移除 onclick 屬性
                not(containsString("alert"))                                // 確認移除 alert 相關內容
            )))
            .andExpect(jsonPath("$.data.carsCondition").hasJsonPath())
            .andExpect(jsonPath("$.data.carsCondition.brandCode").isNotEmpty())
            .andExpect(jsonPath("$.data.carsCondition.monthFeeStart").value(carsCondition.getMonthFeeStart()))
            .andExpect(jsonPath("$.data.carsCondition.monthFeeEnd").doesNotExist()) // 確認月費結束不會出現
            .andExpect(jsonPath("$.data.carsCondition.mfgYearFrom").value(carsCondition.getMfgYearFrom()))
            .andExpect(jsonPath("$.data.carsCondition.mfgYearTo").value(carsCondition.getMfgYearTo()))
            .andExpect(jsonPath("$.data.bannerCategoryId").value(BuIdEnum.subscribe.getCode()))
            .andExpect(jsonPath("$.data.startDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.endDate").value(notNullValue()))
            .andExpect(jsonPath("$.data.isDeleted").value(false))
            .andExpect(jsonPath("$.data.updater").value(nullValue()));
    }

    @Test
    void createCampaign_fail_endDateIsEarlierThanStartDate() throws Exception {
        // 準備測試資料 - 結束時間早於開始時間
        CampaignRequest request = new CampaignRequest();
        request.setTitle("測試活動");
        request.setDescription(UNSAFE_CAMPAIGN_DESCRIPTION);
        request.setCarsCondition(buildCarsCondition());
        request.setBannerCategoryId(BuIdEnum.subscribe.getCode());
        Instant now = Instant.now();
        request.setStartDate(now);
        request.setEndDate(now.minus(1, ChronoUnit.DAYS));

        // 執行測試
        mockMvc.perform(post("/internal/subscribe/campaign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().is5xxServerError())
            .andExpect(jsonPath("$.message", containsString("結束時間不可早於開始時間")));
    }

    @Test
    void createCampaign_fail_bannerCategoryNotFound() throws Exception {
        // 準備測試資料 - 不存在的 bannerCategoryId
        CampaignRequest request = new CampaignRequest();
        request.setTitle("測試活動");
        request.setDescription(UNSAFE_CAMPAIGN_DESCRIPTION);
        request.setCarsCondition(buildCarsCondition());
        request.setBannerCategoryId(99999);
        Instant now = Instant.now();
        request.setStartDate(now);
        request.setEndDate(now.plus(30, ChronoUnit.DAYS));

        // 執行測試
        mockMvc.perform(post("/internal/subscribe/campaign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(BANNER_CATEGORY_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message", containsString(BANNER_CATEGORY_NOT_FOUND.getMsg())));
    }

    @Test
    void createCampaign_fail_invalidCarsCondition() throws Exception {
        // 準備測試資料 - 不存在的車型代碼
        CampaignRequest request = new CampaignRequest();
        request.setTitle("測試活動");
        request.setDescription(UNSAFE_CAMPAIGN_DESCRIPTION);
        CarsCondition carsCondition = buildCarsCondition();
        carsCondition.setCarModelCode(Collections.singletonList("0020L"));
        request.setCarsCondition(carsCondition);
        request.setBannerCategoryId(BuIdEnum.subscribe.getCode());
        Instant now = Instant.now();
        request.setStartDate(now);
        request.setEndDate(now.plus(30, ChronoUnit.DAYS));

        // 執行測試
        mockMvc.perform(post("/internal/subscribe/campaign")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(CAR_MODEL_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message", containsString("車型代碼不存在")));
    }

    /**
     * return {@link com.carplus.subscribe.model.response.PageResponse<com.carplus.subscribe.model.response.campaign.CampaignInternalBaseResponse>}
     */
    @Test
    void getUndeletedCampaigns_success() throws Exception {
        mockMvc.perform(get("/internal/subscribe/campaigns")
                .param("skip", "0")
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list[*].id").exists())
            .andExpect(jsonPath("$.data.page.list[*].title").exists())
            .andExpect(jsonPath("$.data.page.list[*].startDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].endDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].isDeleted").value(everyItem(is(false))))
            .andExpect(jsonPath("$.data.page.list[*].createDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].updateDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].updater").exists())
            .andExpect(jsonPath("$.data.page.list[*].description").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].carsCondition").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].bannerCategory").doesNotExist());
    }

    /**
     * return {@link com.carplus.subscribe.model.response.PageResponse<com.carplus.subscribe.model.response.campaign.CampaignInternalBaseResponse>}
     */
    @Test
    void getCampaignsIncludingDeleted_success() throws Exception {
        mockMvc.perform(get("/internal/subscribe/campaigns")
                .param("skip", "0")
                .param("limit", "10")
                .param("excludeDeleted", "false")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list[*].id").exists())
            .andExpect(jsonPath("$.data.page.list[*].title").exists())
            .andExpect(jsonPath("$.data.page.list[*].startDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].endDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].isDeleted").exists())
            .andExpect(jsonPath("$.data.page.list[?(@.title=='已刪除活動')].isDeleted").value(true))
            .andExpect(jsonPath("$.data.page.list[?(@.title=='測試活動')].isDeleted").value(false))
            .andExpect(jsonPath("$.data.page.list[*].createDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].updateDate").exists())
            .andExpect(jsonPath("$.data.page.list[*].updater").exists())
            .andExpect(jsonPath("$.data.page.list[*].description").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].carsCondition").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].bannerCategory").doesNotExist());
    }

    /**
     * return {@link com.carplus.subscribe.model.response.PageResponse<com.carplus.subscribe.model.response.campaign.CampaignCommonBaseResponse>}
     */
    @Test
    void getCampaignsFromCommon_success() throws Exception {
        mockMvc.perform(get("/common/subscribe/campaigns")
                .param("skip", "0")
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list[*].id").exists())
            .andExpect(jsonPath("$.data.page.list[*].title").exists())
            .andExpect(jsonPath("$.data.page.list[*].startDate").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].endDate").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].isDeleted").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].createDate").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].updateDate").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].updater").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].description").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].carsCondition").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[*].bannerCategory").doesNotExist());
    }

    /**
     * return {@link com.carplus.subscribe.model.response.campaign.CampaignInternalDetailResponse}
     */
    @Test
    void getDeletedCampaign_success() throws Exception {

        int campaignId = getLatestCampaignId(false);

        mockMvc.perform(get("/internal/subscribe/campaign/{id}", campaignId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.id").value(campaignId))
            .andExpect(jsonPath("$.data.title").exists())
            .andExpect(jsonPath("$.data.startDate").exists())
            .andExpect(jsonPath("$.data.endDate").exists())
            .andExpect(jsonPath("$.data.isDeleted").value(true))
            .andExpect(jsonPath("$.data.description").exists())
            .andExpect(jsonPath("$.data.carsCondition").exists())
            .andExpect(jsonPath("$.data.carsCondition").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory").exists())
            .andExpect(jsonPath("$.data.bannerCategory").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory.bannerCategoryId").isNumber());
    }

    /**
     * return {@link com.carplus.subscribe.model.response.campaign.CampaignInternalDetailResponse}
     */
    @Test
    void getUndeletedCampaign_success() throws Exception {

        int campaignId = getLatestCampaignId(true);

        mockMvc.perform(get("/internal/subscribe/campaign/{id}", campaignId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.id").value(campaignId))
            .andExpect(jsonPath("$.data.title").exists())
            .andExpect(jsonPath("$.data.startDate").exists())
            .andExpect(jsonPath("$.data.endDate").exists())
            .andExpect(jsonPath("$.data.isDeleted").value(false))
            .andExpect(jsonPath("$.data.description").exists())
            .andExpect(jsonPath("$.data.carsCondition").exists())
            .andExpect(jsonPath("$.data.carsCondition").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory").exists())
            .andExpect(jsonPath("$.data.bannerCategory").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory.bannerCategoryId").isNumber());
    }

    /**
     * 測試從 Common API 獲取已刪除活動時應該失敗
     * 預期返回 CAMPAIGN_NOT_FOUND 錯誤
     */
    @Test
    void getDeletedCampaignFromCommon_fail() throws Exception {
        // 獲取已刪除的活動 ID
        int campaignId = getLatestCampaignId(false);

        // 確認該活動確實已被刪除
        Campaign campaign = campaignRepository.findById(campaignId)
            .orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
        assertTrue(campaign.isDeleted(), "測試前提條件不符：活動未被標記為已刪除");

        // 執行測試 - 嘗試從 Common API 獲取已刪除的活動
        mockMvc.perform(get("/common/subscribe/campaign/{id}", campaignId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(CAMPAIGN_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message", containsString(CAMPAIGN_NOT_FOUND.getMsg())));
    }

    /**
     * return {@link com.carplus.subscribe.model.response.campaign.CampaignCommonDetailResponse}
     */
    @Test
    void getUndeletedCampaignFromCommon_success() throws Exception {
        // 獲取未刪除的活動 ID
        int campaignId = getLatestCampaignId(true);

        mockMvc.perform(get("/common/subscribe/campaign/{id}", campaignId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.id").value(campaignId))
            .andExpect(jsonPath("$.data.title").exists())
            // 驗證 Common API 回傳的欄位
            .andExpect(jsonPath("$.data.description").exists())
            .andExpect(jsonPath("$.data.carsCondition").exists())
            .andExpect(jsonPath("$.data.carsCondition").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory").exists())
            .andExpect(jsonPath("$.data.bannerCategory").hasJsonPath())
            .andExpect(jsonPath("$.data.bannerCategory.bannerCategoryId").isNumber())
            // 驗證 Common API 不應該回傳的欄位
            .andExpect(jsonPath("$.data.startDate").doesNotExist())
            .andExpect(jsonPath("$.data.endDate").doesNotExist())
            .andExpect(jsonPath("$.data.isDeleted").doesNotExist())
            .andExpect(jsonPath("$.data.createDate").doesNotExist())
            .andExpect(jsonPath("$.data.updateDate").doesNotExist())
            .andExpect(jsonPath("$.data.updater").doesNotExist());
    }

    private int getLatestCampaignId(boolean excludeDeleted) {
        CampaignCriteria criteria = new CampaignCriteria();
        criteria.setExcludeDeleted(excludeDeleted);
        List<Campaign> campaigns = campaignRepository.findBySearch(criteria, 0, 10);
        return campaigns.stream().mapToInt(Campaign::getId).max().orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
    }

    @Test
    void updateCampaign_success() throws Exception {

        int campaignId = getLatestCampaignId(true);

        CampaignRequest request = new CampaignRequest();
        Campaign campaign = campaignRepository.findById(campaignId).orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND));
        String updatedTitle = campaign.getTitle() + "更新";
        request.setTitle(updatedTitle);
        request.setDescription(campaign.getDescription());
        request.setCarsCondition(buildCarsCondition());
        request.setBannerCategoryId(campaign.getBannerCategoryId());
        request.setStartDate(campaign.getStartDate());
        request.setEndDate(campaign.getEndDate());

        // 執行測試
        mockMvc.perform(patch("/internal/subscribe/campaign/{id}", campaignId)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.id").value(campaignId))
            .andExpect(jsonPath("$.data.title").value(updatedTitle))
            .andExpect(jsonPath("$.data.updater").value(MEMBER_ID));
    }

    @Test
    void deleteCampaign_success() throws Exception {
        int campaignId = getLatestCampaignId(true);
        mockMvc.perform(delete("/internal/subscribe/campaign/{id}", campaignId)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"));
        assertTrue(campaignRepository.findById(campaignId).orElseThrow(() -> new SubscribeException(CAMPAIGN_NOT_FOUND)).isDeleted());
    }

    @Test
    void deleteCampaign_fail() throws Exception {
        int campaignId = getLatestCampaignId(false);
        mockMvc.perform(delete("/internal/subscribe/campaign/{id}", campaignId)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value(CAMPAIGN_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message", containsString("活動刪除失敗")));
    }

    @Test
    void searchCarsWithCampaignIdFromCommon_success_allQueryParamsShouldBeOverwrittenByCarsCondition() throws Exception {

        int campaignId = getLatestCampaignId(true);

        CarsCondition carsCondition = campaignService.<CampaignCommonDetailResponse>get(campaignId, false).getCarsCondition();

        Integer monthlyDiscountedTagId = CarDefine.CarTag.MONTHLY_DISCOUNTED.getId();

        mockMvc.perform(get("/common/subscribe/car")
                .contentType(MediaType.APPLICATION_JSON)
                .param("geoRegion", "N")
                .param("orderBy", "MFG_YEAR")
                .param("energyType", "GASOLINE")
                .param("skip", "0")
                .param("limit", "12")
                .param("sort", "DESC")
                .param("monthFeeStart", "26800")
                .param("monthFeeEnd", "50800")
                .param("campaignId", String.valueOf(campaignId)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            // 驗證所有車輛都符合活動的 carsCondition 條件
            .andExpect(jsonPath("$.data.page.list[*].carBrand.brandCode").value(everyItem(in(carsCondition.getBrandCode()))))
            // 驗證 mfgYear 符合數值範圍
            .andExpect(jsonPath("$.data.page.list[*].mfgYear").value(everyItem(allOf(greaterThanOrEqualTo(String.valueOf(carsCondition.getMfgYearFrom())), lessThanOrEqualTo(String.valueOf(carsCondition.getMfgYearTo()))))))
            // 驗證月費，根據 tagIds 判斷使用 monthlyFee 或 discountMonthlyFee
            .andExpect(jsonPath(String.format("$.data.page.list[?(@.tagIds contains %d)].level.discountMonthlyFee", monthlyDiscountedTagId)).value(everyItem(allOf(greaterThanOrEqualTo(carsCondition.getMonthFeeStart()), lessThanOrEqualTo(carsCondition.getMonthFeeEnd())))))
            .andExpect(jsonPath(String.format("$.data.page.list[?(!@.tagIds contains %d)].level.monthlyFee", monthlyDiscountedTagId)).value(everyItem(allOf(greaterThanOrEqualTo(carsCondition.getMonthFeeStart()), lessThanOrEqualTo(carsCondition.getMonthFeeEnd())))))
            .andExpect(jsonPath(String.format("$.data.page.list[?(!@.tagIds contains %d)].level.monthlyFee", monthlyDiscountedTagId)).value(everyItem(allOf(greaterThanOrEqualTo(carsCondition.getMonthFeeStart()), lessThanOrEqualTo(carsCondition.getMonthFeeEnd())))))
            // 驗證存在 isInWishList 欄位
            .andExpect(jsonPath("$.data.page.list[*].isInWishlist").exists());
    }
}