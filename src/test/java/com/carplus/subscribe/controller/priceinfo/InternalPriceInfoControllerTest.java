package com.carplus.subscribe.controller.priceinfo;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.OrderPriceInfoRepository;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.OrderPriceInfo;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.request.priceinfo.MerchandiseInfoRequest;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.CrsService;
import com.carplus.subscribe.service.OrderService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class InternalPriceInfoControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CrsService crsService;
    @Autowired
    private OrderPriceInfoRepository orderPriceInfoRepository;

    private final String TEST_ORDER_NO = "M202412248755";
    private final String TEST_MEMBER_ID = "K2765";

    @Test
    void setMileage() throws Exception {

        String orderNo = "M202406250689";

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", orderNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
                "    \"currentMileage\": 32894,\n" +
                "    \"orderPriceInfoId\": 76456,\n" +
                "    \"acctId\": 33456914\n" +
                "}");

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.infoDetail.endMileage").value(32894));

        Orders order = orderService.getOrder(orderNo);
        String plateNo = order.getPlateNo();

        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getCrsCarNo() != null && car.getCrsCarNo() > 0;

        CarBaseInfoSearchResponse crsCar = crsService.getCar(plateNo);
        assert crsCar.getCarBase().getKm() == 32894;
    }

    @Test
    void setMerchandise_AddNewItem_Success() throws Exception {
        MerchandiseInfoRequest request = getInvalidMerchandiseInfoRequest(2);

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')]").exists())
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].amount").value(476))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].infoDetail.quantity").value(2))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].infoDetail.actualUnitPrice").value(238));
    }

    private MerchandiseInfoRequest getInvalidMerchandiseInfoRequest(int quantity) {
        MerchandiseInfoRequest request = new MerchandiseInfoRequest();
        List<MerchandiseInfoRequest.MerchandiseInfo> merchandiseList = new ArrayList<>();

        MerchandiseInfoRequest.MerchandiseInfo merchandise = new MerchandiseInfoRequest.MerchandiseInfo();
        merchandise.setSkuCode("airFresher001");
        merchandise.setActualUnitPrice(238);
        merchandise.setQuantity(quantity);
        merchandise.setStage(1);
        merchandise.setDelete(false);

        merchandiseList.add(merchandise);
        request.setMerchandiseList(merchandiseList);
        return request;
    }

    @Test
    void setMerchandise_UpdateExistingItem_Success() throws Exception {
        // First create an item using API
        MerchandiseInfoRequest createRequest = getInvalidMerchandiseInfoRequest(1);

        MvcResult createResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
            .andReturn();

        // Get the created item's ID
        String responseContent = createResult.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseContent);
        JsonNode dataNode = rootNode.path("data");
        List<OrderPriceInfo> createdItems = objectMapper.readValue(
            dataNode.traverse(),
            new TypeReference<List<OrderPriceInfo>>() {}
        );
        Integer priceInfoId = null;
        for (OrderPriceInfo item : createdItems) {
            if ("airFresher001".equals(item.getSkuCode())) {
                priceInfoId = item.getId();
                break;
            }
        }
        if (priceInfoId == null) {
            throw new RuntimeException("No OrderPriceInfo found with skuCode: airFresher001");
        }

        // Update the created item
        MerchandiseInfoRequest updateRequest = new MerchandiseInfoRequest();
        List<MerchandiseInfoRequest.MerchandiseInfo> updateList = new ArrayList<>();

        MerchandiseInfoRequest.MerchandiseInfo updateMerchandise = new MerchandiseInfoRequest.MerchandiseInfo();
        updateMerchandise.setPriceInfoId(priceInfoId);
        updateMerchandise.setSkuCode("airFresher001");
        updateMerchandise.setActualUnitPrice(200); // Updated price
        updateMerchandise.setQuantity(3); // Updated quantity
        updateMerchandise.setDelete(false);

        updateList.add(updateMerchandise);
        updateRequest.setMerchandiseList(updateList);

        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')]").exists())
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].id").value(priceInfoId))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].amount").value(600))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].infoDetail.quantity").value(3))
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise' && @.skuCode == 'airFresher001')].infoDetail.actualUnitPrice").value(200));
    }

    @Test
    void setMerchandise_DeleteItem_Success() throws Exception {
        // First create an item using API
        MerchandiseInfoRequest createRequest = getInvalidMerchandiseInfoRequest(1);

        MvcResult createResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
            .andReturn();

        // Get the created item's ID
        String responseContent = createResult.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseContent);
        JsonNode dataNode = rootNode.path("data");
        List<OrderPriceInfo> createdItems = objectMapper.readValue(
            dataNode.traverse(),
            new TypeReference<List<OrderPriceInfo>>() {}
        );
        Integer priceInfoId = null;
        for (OrderPriceInfo item : createdItems) {
            if ("airFresher001".equals(item.getSkuCode())) {
                priceInfoId = item.getId();
                break;
            }
        }
        if (priceInfoId == null) {
            throw new RuntimeException("No OrderPriceInfo found with skuCode: airFresher001");
        }

        // Delete the created item
        MerchandiseInfoRequest deleteRequest = new MerchandiseInfoRequest();
        List<MerchandiseInfoRequest.MerchandiseInfo> deleteList = new ArrayList<>();

        MerchandiseInfoRequest.MerchandiseInfo deleteMerchandise = new MerchandiseInfoRequest.MerchandiseInfo();
        deleteMerchandise.setPriceInfoId(priceInfoId);
        deleteMerchandise.setActualUnitPrice(238);
        deleteMerchandise.setQuantity(1);
        deleteMerchandise.setDelete(true);

        deleteList.add(deleteMerchandise);
        deleteRequest.setMerchandiseList(deleteList);

        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteRequest)))
            .andExpect(status().isOk());

        // Verify the item is deleted
        Optional<OrderPriceInfo> deletedItem = orderPriceInfoRepository.findById(priceInfoId);
        assertFalse(deletedItem.isPresent());
    }

    @Test
    void setMerchandise_EmptyMerchandiseList_BadRequest() throws Exception {
        MerchandiseInfoRequest request = new MerchandiseInfoRequest();
        request.setMerchandiseList(new ArrayList<>());

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest());
    }

    @Test
    void setMerchandise_InvalidPrice_BadRequest() throws Exception {
        MerchandiseInfoRequest request = getInvalidMerchandiseInfoRequest();

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/priceInfo/{orderNo}/merchandise", TEST_ORDER_NO)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(objectMapper.writeValueAsString(request));

        mockMvc.perform(requestBuilder)
            .andExpect(status().isBadRequest());
    }

    private MerchandiseInfoRequest getInvalidMerchandiseInfoRequest() {
        MerchandiseInfoRequest request = new MerchandiseInfoRequest();
        List<MerchandiseInfoRequest.MerchandiseInfo> merchandiseList = new ArrayList<>();

        MerchandiseInfoRequest.MerchandiseInfo merchandise = new MerchandiseInfoRequest.MerchandiseInfo();
        merchandise.setSkuCode("airFresher001");
        merchandise.setActualUnitPrice(0); // Invalid price
        merchandise.setQuantity(1);
        merchandise.setStage(1);

        merchandiseList.add(merchandise);
        request.setMerchandiseList(merchandiseList);
        return request;
    }

    @Test
    void testSetExtraFee() throws Exception {
        String orderNo = "M202502200568";
        String requestPayload = "{\n" +
            "    \"extraFeeList\": [\n" +
            "        {\n" +
            "            \"priceInfoId\": 83491,\n" +
            "            \"amount\": 2000,\n" +
            "            \"reason\": \"\",\n" +
            "            \"stage\": 1,\n" +
            "            \"payStatus\": \"UNPAID\",\n" +
            "            \"point\": null,\n" +
            "            \"category\": \"Replacement\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"priceInfoId\": 83492,\n" +
            "            \"amount\": 2000,\n" +
            "            \"reason\": \"\",\n" +
            "            \"stage\": 1,\n" +
            "            \"payStatus\": \"UNPAID\",\n" +
            "            \"point\": null,\n" +
            "            \"category\": \"Replacement\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"amount\": 4000,\n" +
            "            \"stage\": 1,\n" +
            "            \"reason\": \"\",\n" +
            "            \"category\": \"Dispatch\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, TEST_MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestPayload))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));
    }
}