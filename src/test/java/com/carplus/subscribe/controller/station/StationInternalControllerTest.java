package com.carplus.subscribe.controller.station;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.Stations;
import com.carplus.subscribe.db.mysql.entity.change.EntityChange;
import com.carplus.subscribe.db.mysql.entity.change.EntityChangeLog;
import com.carplus.subscribe.service.ConfigService;
import com.carplus.subscribe.service.EntityChangeLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class StationInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private EntityChangeLogService entityChangeLogService;

    @Autowired
    private ConfigService configService;

    @Test
    void updateStation_should_log_changes() throws Exception {

        List<EntityChangeLog> changeLogsBeforeUpdate = entityChangeLogService.getByMainEntityAndPrimaryKey(Stations.class, "905");
        assert changeLogsBeforeUpdate.isEmpty();

        mockMvc.perform(patch("/internal/subscribe/stations")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, "K2765")
            .content("{\n" +
                "    \"stationCode\": \"905\",\n" +
                "    \"isSubscribe\": true,\n" + // original: false -> true
                "    \"stationCategory\": \"GENERAL\",\n" + // original: COURIER -> GENERAL
                "    \"status\": \"D\",\n" + // original: A -> D
                "    \"forceOnlineRForm\": true\n" + // original: false -> true
                "}"));

        // 測試以下預期結果須先移除 com.carplus.subscribe.service.AsyncLogger.logChanges @Async annotation
        // 並將 save() 調整為 saveAndFlush()
        List<EntityChangeLog> changeLogsAfterUpdate = entityChangeLogService.getByMainEntityAndPrimaryKey(Stations.class, "905");
        assert changeLogsAfterUpdate.size() == 1;

        List<String> pauseEContractStationList = configService.getPauseEContractStationList();
        assert !pauseEContractStationList.contains("905");
    }

    @Test
    void findByStationCode_should_include_fieldChanges() throws Exception {
        mockMvc.perform(get("/internal/subscribe/stations/201")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, "K2765"))
            .andExpect(result -> {
                String content = result.getResponse().getContentAsString();
                assert content.contains(EntityChange.Fields.fieldChanges);
            });
    }

    /**
     * 測試條件：前端傳入 locateGeoRegion 為 "N1" 且 visible 為 true，
     * 預期只回傳 locateGeoRegion 為 "N1" 且 visible 為 true 的站所資料。
     */
    @Test
    public void testQueryWithLocateGeoRegionSingleValueAndVisibleTrue() throws Exception {
        mockMvc.perform(get("/internal/subscribe/stations/query")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, "K2765")
                // 傳入 locateGeoRegion 為 "N1"
                .param("locateGeoRegion", "N1")
                // 傳入 visible 篩選條件 (多值可複選，此處只傳一筆)
                .param("visible", "true")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            // 檢查回傳結果為 JSON 陣列
            .andExpect(jsonPath("$.data").isArray())
            // 進一步檢查所有回傳站所，其 locateGeoRegion 必須為 "N1"
            .andExpect(jsonPath("$.data[*].locateGeoRegion", everyItem(is("N1"))))
            // 並且 visible 必須為 true
            .andExpect(jsonPath("$.data[*].visible", everyItem(is(true))));
    }

    /**
     * 測試 subscribe/stations/query API：驗證 locateGeoRegion 複選（"N1","N2"）與 visible 複選（true, false）的條件。
     * 預期每筆資料的 locateGeoRegion 屬於 ["N1", "N2"] 且 visible 屬於 [true, false]。
     */
    @Test
    public void testQueryWithLocateGeoRegionMultipleValuesAndVisibleMultiSelect() throws Exception {
        mockMvc.perform(get("/internal/subscribe/stations/query")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, "K2765")
                // 傳入 locateGeoRegion 為 "N1" 與 "N2"
                .param("locateGeoRegion", "N1", "N2")
                // 傳入 visible 複選條件，包含 "true" 與 "false"
                .param("visible", "true", "false")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data").isArray())
            // 驗證每筆回傳資料的 locateGeoRegion 為 "N1" 或 "N2"
            .andExpect(jsonPath("$[*].locateGeoRegion", everyItem(isIn(Arrays.asList("N1", "N2")))))
            // 驗證每筆回傳資料的 visible 值屬於 true 或 false
            .andExpect(jsonPath("$[*].visible", everyItem(isIn(Arrays.asList(true, false)))));
    }
}