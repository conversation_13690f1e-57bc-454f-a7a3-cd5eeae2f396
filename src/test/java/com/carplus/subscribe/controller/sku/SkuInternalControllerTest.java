package com.carplus.subscribe.controller.sku;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.request.sku.SkuAddRequest;
import com.carplus.subscribe.model.request.sku.SkuUpdateRequest;
import com.carplus.subscribe.model.sku.SkuResponse;
import com.carplus.subscribe.service.SkuService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SKU_NAME_DUPLICATE;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.SKU_NOT_FOUND;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class SkuInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SkuService skuService;

    private static final String MEMBER_ID = "K2765";

    private static final String SKU_CODE = "wipe009";
    private static final String SKU_TYPE = "wipe";
    private static final String SKU_NAME = "懶人必備專業車用濕巾9";
    private static final int SKU_UNIT_PRICE = 388;

    @Test
    void addSku_Success() throws Exception {
        // 取得新的 sku code
        String newSkuCode = skuService.generateSkuCode(SKU_TYPE);

        // 準備測試資料
        SkuAddRequest request = new SkuAddRequest();
        request.setType(SKU_TYPE);
        request.setName(SKU_NAME);
        request.setUnitPrice(SKU_UNIT_PRICE);
        request.setDescription("");

        // 執行請求
        mockMvc.perform(post("/internal/subscribe/sku")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // 檢查資料庫
        SkuResponse newSkuResponse = skuService.getByCode(newSkuCode);
        assertNotNull(newSkuResponse);
        assertEquals(SKU_NAME, newSkuResponse.getName());
        assertEquals(SKU_UNIT_PRICE, newSkuResponse.getUnitPrice());
        assertNull(newSkuResponse.getDescription());
        assertFalse(newSkuResponse.getIsOfficial());
        assertTrue(newSkuResponse.getIsCashier());
    }

    @Test
    void addSku_Success_IsOfficialNull() throws Exception {
        // 取得新的 sku code
        String newSkuCode = skuService.generateSkuCode(SKU_TYPE);

        // 準備測試資料
        SkuAddRequest request = new SkuAddRequest();
        request.setType(SKU_TYPE);
        request.setName(SKU_NAME);
        request.setUnitPrice(SKU_UNIT_PRICE);
        request.setIsOfficial(null);
        request.setIsCashier(true);

        // 執行請求
        mockMvc.perform(post("/internal/subscribe/sku")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // 檢查資料庫
        SkuResponse newSkuResponse = skuService.getByCode(newSkuCode);
        assertNotNull(newSkuResponse);
        assertEquals(SKU_NAME, newSkuResponse.getName());
        assertEquals(SKU_UNIT_PRICE, newSkuResponse.getUnitPrice());
        assertFalse(newSkuResponse.getIsOfficial());
        assertTrue(newSkuResponse.getIsCashier());
    }

    @Test
    void addSku_Failure_InvalidRequest() throws Exception {
        // 準備無效的測試資料 (缺少必要欄位)
        SkuAddRequest request = new SkuAddRequest();
        request.setType(SKU_TYPE);
        request.setUnitPrice(-100); // 負數價格

        // 執行請求並期望得到 400 錯誤
        mockMvc.perform(post("/internal/subscribe/sku")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(
                matchesRegex("^(?=.*商品名稱不可為空)(?=.*商品單價不可小於0).*$")
            ));
    }

    @Test
    void addSku_Failure_NameDuplicate() throws Exception {
        // 準備測試資料
        SkuAddRequest request = new SkuAddRequest();
        request.setType(SKU_TYPE);
        request.setName("懶人必備專業車用濕巾");
        request.setUnitPrice(SKU_UNIT_PRICE);
        request.setDescription("");

        // 執行請求並期望得到 400 錯誤
        mockMvc.perform(post("/internal/subscribe/sku")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(SKU_NAME_DUPLICATE.getCode()))
            .andExpect(jsonPath("$.message").value("周邊商品名稱重複"));
    }

    // 取得所有汽車用品分頁列表 測試
    @Test
    void getSkus_Success() throws Exception {
        // 執行請求
        mockMvc.perform(get("/internal/subscribe/sku"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.page.total").value(greaterThan(0)))
            .andExpect(jsonPath("$.data.page.list").isArray());
    }

    // 測試分頁效果
    @Test
    void getSkus_Success_Pagination() throws Exception {
        // 執行請求
        mockMvc.perform(get("/internal/subscribe/sku?limit=3&skip=0"))
            .andExpect(jsonPath("$.statusCode").value(0))
            // greater than 0
            .andExpect(jsonPath("$.data.page.total").value(greaterThan(0)))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list").value(hasSize(3)));
    }

    @Test
    void updateSku_Success() throws Exception {
        // 準備測試資料
        String updateSkuCode = "wipe001";
        int updateUnitPrice = 250;
        String updatedDescription = "測試商品描述";

        SkuUpdateRequest request = new SkuUpdateRequest();
        request.setUnitPrice(updateUnitPrice);
        request.setDescription(updatedDescription);
        request.setIsOfficial(false);
        request.setIsCashier(null);

        // 執行請求
        mockMvc.perform(patch("/internal/subscribe/sku/{code}", updateSkuCode)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // 檢查資料庫
        SkuResponse skuResponse = skuService.getByCode(updateSkuCode);
        assertNotNull(skuResponse);
        assertEquals(updateUnitPrice, skuResponse.getUnitPrice());
        assertEquals(updatedDescription, skuResponse.getDescription());
        assertFalse(skuResponse.getIsOfficial());
        assertTrue(skuResponse.getIsCashier());
    }

    @Test
    void updateSku_Failure_InvalidRequest() throws Exception {
        // 準備無效的測試資料 (負數價格)
        SkuUpdateRequest request = new SkuUpdateRequest();
        request.setUnitPrice(-100); // 負數價格

        // 執行請求並期望得到 400 錯誤
        mockMvc.perform(patch("/internal/subscribe/sku/{code}", SKU_CODE)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(
                matchesRegex("^(?=.*商品單價不可小於0).*$")
            ));
    }

    @Test
    void updateSku_Failure_SkuNotFound() throws Exception {
        // 準備測試資料
        SkuUpdateRequest request = new SkuUpdateRequest();
        request.setUnitPrice(100);

        // 執行請求並期望得到商品不存在的錯誤
        mockMvc.perform(patch("/internal/subscribe/sku/not_exist_code")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value(SKU_NOT_FOUND.getCode()));
    }
} 