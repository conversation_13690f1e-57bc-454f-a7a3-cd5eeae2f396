package com.carplus.subscribe.controller.sku;

import com.carplus.subscribe.App;
import com.carplus.subscribe.db.mysql.dao.SkuRepository;
import com.carplus.subscribe.db.mysql.entity.contract.Sku;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

import static org.hamcrest.Matchers.hasItems;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.not;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class SkuCommonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private SkuRepository skuRepository;

    @BeforeEach
    void setUp() {
        // 清空資料表，確保測試環境乾淨
        skuRepository.deleteAll();

        // 官網可見商品 + 收銀台可見商品
        skuRepository.saveAll(Arrays.asList(
            createSku("wipe", "wipe008", "車用濕紙巾", 200, "車用濕紙巾描述", true, true),
            createSku("phoneholder", "phoneholder008", "車用手機架", 150, "車用手機架描述", true, true)
        ));

        // 官網可見商品 + 非收銀台可見商品
        skuRepository.saveAll(Arrays.asList(
            createSku("towel", "towel008", "車用超細纖維毛巾", 50, "車用超細纖維毛巾描述", true, false),
            createSku("tissue", "tissue008", "車用面紙", 80, "車用面紙描述", true, false)
        ));

        // 非官網可見商品 + 收銀台可見商品
        skuRepository.saveAll(Arrays.asList(
            createSku("wipe", "wipe009", "經銷商濕紙巾", 180, "經銷商濕紙巾描述", false, true),
            createSku("phoneholder", "phoneholder009", "經銷商手機架", 130, "經銷商手機架描述", false, true)
        ));

        // 非官網可見商品 + 非收銀台可見商品
        skuRepository.saveAll(Arrays.asList(
            createSku("towel", "towel009", "經銷商毛巾", 45, "經銷商毛巾描述", false, false),
            createSku("tissue", "tissue009", "經銷商面紙", 75, "經銷商面紙描述", false, false)
        ));
    }

    private Sku createSku(String type, String code, String name, Integer unitPrice,
                          String description, Boolean isOfficial, Boolean isCashier) {
        Sku sku = new Sku();
        sku.setType(type);
        sku.setCode(code);
        sku.setName(name);
        sku.setUnitPrice(unitPrice);
        sku.setDescription(description);
        sku.setOfficial(isOfficial);
        sku.setCashier(isCashier);
        return sku;
    }

    @Test
    void getSkus_ShouldOnlyReturnOfficialSkusAndNotContainOfficialAndCashierFields() throws Exception {
        mockMvc.perform(get("/common/subscribe/sku")
                .param("skip", "0")
                .param("limit", "10"))
            .andDo(print())
            .andExpect(jsonPath("$.statusCode").value(0))
            // 驗證只回傳 4 筆官網可見商品（不管是否為收銀台可見商品）
            .andExpect(jsonPath("$.data.page.list", hasSize(4)))
            // 驗證回傳的商品包含所有官網可見商品的 code
            .andExpect(jsonPath("$.data.page.list[*].code",
                hasItems("wipe008", "phoneholder008", "towel008", "tissue008")))
            // 驗證回傳的商品不包含任何非官網可見商品的 code
            .andExpect(jsonPath("$.data.page.list[*].code",
                not(hasItems("wipe009", "phoneholder009", "towel009", "tissue009"))))
            // 驗證不包含 isOfficial 和 isCashier 欄位
            .andExpect(jsonPath("$.data.page.list[0].isOfficial").doesNotExist())
            .andExpect(jsonPath("$.data.page.list[0].isCashier").doesNotExist())
            // 驗證包含必要欄位
            .andExpect(jsonPath("$.data.page.list[0].type").exists())
            .andExpect(jsonPath("$.data.page.list[0].code").exists())
            .andExpect(jsonPath("$.data.page.list[0].name").exists())
            .andExpect(jsonPath("$.data.page.list[0].unitPrice").exists())
            // 驗證包含 description 欄位
            .andExpect(jsonPath("$.data.page.list[0].description").exists());
    }

    @Test
    void getSkus_WithPagination_ShouldWorkCorrectly() throws Exception {
        // 測試分頁功能
        mockMvc.perform(get("/common/subscribe/sku")
                .param("skip", "0")
                .param("limit", "2"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.page.list", hasSize(2)))
            .andExpect(jsonPath("$.data.page.total").value(4));

        mockMvc.perform(get("/common/subscribe/sku")
                .param("skip", "2")
                .param("limit", "2"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.page.list", hasSize(2)))
            .andExpect(jsonPath("$.data.page.total").value(4));
    }

    @Test
    void getSkus_WithTypeFilter_ShouldWorkCorrectly() throws Exception {
        // 測試類型過濾
        mockMvc.perform(get("/common/subscribe/sku")
                .param("skip", "0")
                .param("limit", "10")
                .param("type", "wipe"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.page.list", hasSize(1)))
            .andExpect(jsonPath("$.data.page.list[0].code").value("wipe008"));
    }
}