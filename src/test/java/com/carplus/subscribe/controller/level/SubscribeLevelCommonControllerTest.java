package com.carplus.subscribe.controller.level;

import com.carplus.subscribe.App;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class SubscribeLevelCommonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void getSubscribeLevel_shouldNotContainInternalFields() throws Exception {
        // 測試 /common/subscribe/subscribeLevel/{id} response 不應包含 SubscribeLevelInternalResponse 私有欄位
        mockMvc.perform(get("/common/subscribe/subscribeLevel/1"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.id").exists())
            .andExpect(jsonPath("$.data.level").exists())
            .andExpect(jsonPath("$.data.name").exists())
            // 確認不包含 SubscribeLevelInternalResponse 私有欄位
            .andExpect(jsonPath("$.data.isDeleted").doesNotExist())
            .andExpect(jsonPath("$.data.createDate").doesNotExist())
            .andExpect(jsonPath("$.data.updateDate").doesNotExist())
            .andExpect(jsonPath("$.data.createdBy").doesNotExist())
            .andExpect(jsonPath("$.data.updatedBy").doesNotExist())
            .andExpect(jsonPath("$.data.creatorName").doesNotExist())
            .andExpect(jsonPath("$.data.updaterName").doesNotExist());
    }
}
