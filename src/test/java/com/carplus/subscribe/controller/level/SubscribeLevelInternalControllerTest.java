package com.carplus.subscribe.controller.level;

import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoResponse;
import com.carplus.subscribe.model.request.contract.InternalContractCreateReq;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.CrsService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.DISCOUNT_LEVEL_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class SubscribeLevelInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private ObjectMapper objectMapper;

    private final String MEMBER_ID = "K2765";

    @Test
    void addSubscribeLevel_success() throws Exception {

        mockMvc.perform(post("/internal/subscribe/subscribeLevel")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content("{\n" +
                    "    \"level\": 50,\n" +
                    "    \"name\": \"方案50\",\n" +
                    "    \"securityDeposit\": 5000,\n" +
                    "    \"monthlyFee\": 3000,\n" +
                    "    \"mileageFee\": 1.5,\n" +
                    "    \"autoCredit\": true,\n" +
                    "    \"mileageDiscount\": [{\"stage\": 2, \"discountMileage\": 200}, {\"stage\": 3, \"discountMileage\": 200}, {\"stage\": 4, \"discountMileage\": 400}],\n" +
                    "    \"discountMonthlyFee\": 2800,\n" +
                    "    \"type\": \"SEASON\",\n" +
                    "    \"disclaimerIns\": 1980,\n" +
                    "    \"premiumIns\": 0,\n" +
                    "    \"disclaimerAndPremiumIns\": 0\n" +
                    "}"))
                .andExpect(jsonPath("$.statusCode").value(0));

        // 驗證新增後查詢，id 應等於 level
        mockMvc.perform(get("/internal/subscribe/subscribeLevel/50"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.id").value(50))
            .andExpect(jsonPath("$.data.level").value(50))
            .andExpect(jsonPath("$.data.createdBy").value(MEMBER_ID));
    }

    @Test
    void addSubscribeLevel_fail_idExists() throws Exception {

        mockMvc.perform(post("/internal/subscribe/subscribeLevel")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content("{\n" +
                    "    \"level\": 1,\n" +
                    "    \"name\": \"新方案1\",\n" +
                    "    \"securityDeposit\": 5000,\n" +
                    "    \"monthlyFee\": 3000,\n" +
                    "    \"mileageFee\": 1.5,\n" +
                    "    \"autoCredit\": true,\n" +
                    "    \"mileageDiscount\": [{\"stage\": 2, \"discountMileage\": 200}, {\"stage\": 3, \"discountMileage\": 200}, {\"stage\": 4, \"discountMileage\": 400}],\n" +
                    "    \"discountMonthlyFee\": 2800,\n" +
                    "    \"type\": \"SEASON\",\n" +
                    "    \"disclaimerIns\": 1980,\n" +
                    "    \"premiumIns\": 0,\n" +
                    "    \"disclaimerAndPremiumIns\": 0\n" +
                    "}"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("[訂閱方案id已存在, id: 1]"));
    }

    @Test
    void addSubscribeLevel_fail_nameExists() throws Exception {

        mockMvc.perform(post("/internal/subscribe/subscribeLevel")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content("{\n" +
                    "    \"level\": 50,\n" +
                    "    \"name\": \"方案1\",\n" +
                    "    \"securityDeposit\": 5000,\n" +
                    "    \"monthlyFee\": 3000,\n" +
                    "    \"mileageFee\": 1.5,\n" +
                    "    \"autoCredit\": true,\n" +
                    "    \"mileageDiscount\": [{\"stage\": 2, \"discountMileage\": 200}, {\"stage\": 3, \"discountMileage\": 200}, {\"stage\": 4, \"discountMileage\": 400}],\n" +
                    "    \"discountMonthlyFee\": 2800,\n" +
                    "    \"type\": \"SEASON\",\n" +
                    "    \"disclaimerIns\": 1980,\n" +
                    "    \"premiumIns\": 0,\n" +
                    "    \"disclaimerAndPremiumIns\": 0\n" +
                    "}"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("[訂閱方案名稱已存在, name: 方案1]"));
    }

    @Test
    void updateSubscribeLevel_fail_discountLevelNotExists() throws Exception {

        mockMvc.perform(patch("/internal/subscribe/subscribeLevel")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"新方案1\",\n" +
                "    \"discountLevel\": 9999,\n" +
                "    \"securityDeposit\": 10000,\n" +
                "    \"monthlyFee\": 6800,\n" +
                "    \"mileageFee\": 2.24,\n" +
                "    \"autoCredit\": true,\n" +
                "    \"discountMonthlyFee\": 6800,\n" +
                "    \"type\": \"SEASON\"\n" +
                "}"))
            .andExpect(jsonPath("$.statusCode").value(DISCOUNT_LEVEL_NOT_FOUND.getCode()));
    }

    @Test
    void updateSubscribeLevel_success() throws Exception {

        mockMvc.perform(patch("/internal/subscribe/subscribeLevel")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"新方案1\",\n" +
                "    \"securityDeposit\": 10000,\n" +
                "    \"monthlyFee\": 6800,\n" +
                "    \"mileageFee\": 2.24,\n" +
                "    \"autoCredit\": true,\n" +
                "    \"discountMonthlyFee\": 6800,\n" +
                "    \"type\": \"SEASON\"\n" +
                "}"))
            .andExpect(jsonPath("$.statusCode").value(0));

        // 驗證更新後查詢，updatedBy 應等於 memberId
        mockMvc.perform(get("/internal/subscribe/subscribeLevel/1"))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.id").value(1))
            .andExpect(jsonPath("$.data.level").value(1))
            .andExpect(jsonPath("$.data.name").value("新方案1"))
            .andExpect(jsonPath("$.data.updatedBy").value(MEMBER_ID));
    }

    @Test
    void updateSubscribeLevel_fail_nameExists() throws Exception {

        mockMvc.perform(patch("/internal/subscribe/subscribeLevel")
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content("{\n" +
                "    \"id\": 1,\n" +
                "    \"name\": \"方案2\",\n" +
                "    \"securityDeposit\": 10000,\n" +
                "    \"monthlyFee\": 6800,\n" +
                "    \"mileageFee\": 2.24,\n" +
                "    \"autoCredit\": true,\n" +
                "    \"discountMonthlyFee\": 6800,\n" +
                "    \"type\": \"SEASON\"\n" +
                "}"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("[訂閱方案名稱已存在, name: 方案2]"));
    }

    @Test
    void verifySubscribeLevelUpdateDoesNotAffectExistingOrdersPriceInfo() throws Exception {
        // 創建測試上下文
        OrderTestContext context = new OrderTestContext();
        
        // 1. 使用 ContractInternalControllerTest#createOrder 建立訂單
        // 1.1 找一個閒置的車輛
        List<Cars> idleCars = carsService.getIdleCar();
        assertFalse(idleCars.isEmpty(), "應該要有閒置車輛");

        Cars idleCar = null;
        for (Cars car : idleCars) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(car.getPlateNo());
            if (carBaseInfoSearchResponse != null
                && carBaseInfoSearchResponse.getCarLicense().getLicenseStatus().equals("0")
                && carBaseInfoSearchResponse.getBuId().equals(BuIdEnum.subscribe.getCode())
                && Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(car.getLaunched())) {
                idleCar = car;
                break;
            }
        }
        assertNotNull(idleCar, "找不到閒置車輛");
        context.idleCar = idleCar;

        // 1.2 建立訂單所需的請求對象
        InternalContractCreateReq contractCreateReq = new InternalContractCreateReq();
        contractCreateReq.setPlateNo(context.idleCar.getPlateNo());
        contractCreateReq.setCarLevel(context.idleCar.getSubscribeLevel());
        contractCreateReq.setDepartStationCode("203");
        contractCreateReq.setReturnStationCode("203");
        contractCreateReq.setExpectStartDate(Instant.ofEpochMilli(getExpectStartDate()));
        contractCreateReq.setMonth(12);
        contractCreateReq.setDisclaimer(false);
        contractCreateReq.setPremium(false);
        context.contractCreateReq = contractCreateReq;

        Invoice invoice = new Invoice();
        invoice.setCategory(5);
        invoice.setId("A193477449");
        invoice.setTitle("李志宏");
        invoice.setType(2);
        contractCreateReq.setInvoice(invoice);

        contractCreateReq.setCustSource(2);
        contractCreateReq.setOrderPlatform(OrderPlatform.CASHIER);
        contractCreateReq.setAcctId(33456914);

        // 1.3 建立訂單
        MvcResult createContractResult = mockMvc.perform(post("/internal/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(contractCreateReq)))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andReturn();
        context.createContractResult = createContractResult;

        JsonNode dataNode = objectMapper.readTree(context.createContractResult.getResponse().getContentAsString()).get("data");
        context.orderNo = dataNode.path("orders").get(0).path("orderNo").asText();
        context.mainContractNo = dataNode.get("mainContractNo").asText();
        Integer originalCarLevel = contractCreateReq.getCarLevel();

        // 2. 呼叫 ContractPublicController#getMainContractOrders API 獲取 Page<MainContractResponse>
        MvcResult getMainContractOrdersResult = mockMvc.perform(get("/subscribe/mainContract")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, contractCreateReq.getAcctId())
                .param("detail", "true")
                .param("skip", "0")
                .param("limit", "10"))
            .andExpect(status().isOk())
            .andReturn();

        // 保存初始的查詢結果為參照
        String initialResponse = getMainContractOrdersResult.getResponse().getContentAsString();
        JsonNode initialResponseNode = objectMapper.readTree(initialResponse);
        JsonNode initialListNode = initialResponseNode.path("data").path("list");

        // 2.1 同時呼叫 internal/subscribe/v2/priceInfo/{orderNo} API 獲取 orderPriceInfoList 做比較
        MvcResult getInternalPriceInfoResult = mockMvc.perform(get("/internal/subscribe/v2/priceInfo/{orderNo}", context.orderNo)
                .param("credit", "false"))
            .andExpect(status().isOk())
            .andReturn();

        String initialInternalPriceInfoResponse = getInternalPriceInfoResult.getResponse().getContentAsString();
        JsonNode initialInternalPriceInfoNode = objectMapper.readTree(initialInternalPriceInfoResponse);

        // 2.2 呼叫 /internal/subscribe/priceInfo/{orderNo}/unpaid?history=true API 獲取未付款資訊
        MvcResult getUnpaidPriceInfoResult = mockMvc.perform(get("/internal/subscribe/priceInfo/{orderNo}/unpaid", context.orderNo)
                .param("history", "true")
                .param("isCredit", "true"))
            .andExpect(status().isOk())
            .andReturn();

        String initialUnpaidPriceInfoResponse = getUnpaidPriceInfoResult.getResponse().getContentAsString();
        List<OrderPriceInfoResponse> initialUnpaidPriceInfoList = objectMapper.readValue(
            initialUnpaidPriceInfoResponse,
            objectMapper.getTypeFactory().constructCollectionType(List.class, OrderPriceInfoResponse.class)
        );

        // 3. 成功 update subscribeLevel (方案的 int level 等於 contractCreateReq.getCarLevel())

        // 3.1 先取得該訂閱方案的資訊
        MvcResult getSubscribeLevelResult = mockMvc.perform(get("/internal/subscribe/subscribeLevel/{id}", originalCarLevel)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        JsonNode subscribeLevelNode = objectMapper.readTree(getSubscribeLevelResult.getResponse().getContentAsString()).get("data");

        // 3.2 更新該訂閱方案 (增加保證金、月費和里程費)
        Map<String, Object> updateRequest = new HashMap<>();
        updateRequest.put("id", originalCarLevel);
        updateRequest.put("name", subscribeLevelNode.get("name").asText());
        updateRequest.put("securityDeposit", subscribeLevelNode.get("securityDeposit").asInt() + 1000);
        updateRequest.put("monthlyFee", subscribeLevelNode.get("monthlyFee").asInt() + 200);
        updateRequest.put("mileageFee", subscribeLevelNode.get("mileageFee").asDouble() + 0.1);
        updateRequest.put("autoCredit", subscribeLevelNode.get("autoCredit").asBoolean());
        updateRequest.put("discountMonthlyFee", subscribeLevelNode.get("discountMonthlyFee").asInt() + 100);
        updateRequest.put("type", subscribeLevelNode.get("type").asText());
        mockMvc.perform(patch("/internal/subscribe/subscribeLevel")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsBytes(updateRequest)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // 4. 再次呼叫 getMainContractOrders API，確認結果不變
        MvcResult getMainContractOrdersAgainResult = mockMvc.perform(get("/subscribe/mainContract")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, contractCreateReq.getAcctId())
                .param("contractStatuses", "CREATE,GOING")
                .param("detail", "true")
                .param("skip", "0")
                .param("limit", "10"))
            .andExpect(status().isOk())
            .andReturn();

        String subsequentResponse = getMainContractOrdersAgainResult.getResponse().getContentAsString();
        JsonNode subsequentResponseNode = objectMapper.readTree(subsequentResponse);
        JsonNode subsequentListNode = subsequentResponseNode.path("data").path("list");

        // 4.1 再次呼叫 internal/subscribe/v2/priceInfo/{orderNo} API，確認結果不變
        MvcResult getInternalPriceInfoAgainResult = mockMvc.perform(get("/internal/subscribe/v2/priceInfo/{orderNo}", context.orderNo)
                .param("credit", "false"))
            .andExpect(status().isOk())
            .andReturn();

        String subsequentInternalPriceInfoResponse = getInternalPriceInfoAgainResult.getResponse().getContentAsString();
        JsonNode subsequentInternalPriceInfoNode = objectMapper.readTree(subsequentInternalPriceInfoResponse);

        // 4.2 再次呼叫 /internal/subscribe/priceInfo/{orderNo}/unpaid?history=true API，確認結果不變
        MvcResult getUnpaidPriceInfoAgainResult = mockMvc.perform(get("/internal/subscribe/priceInfo/{orderNo}/unpaid", context.orderNo)
                .param("history", "true")
                .param("isCredit", "true"))
            .andExpect(status().isOk())
            .andReturn();

        String subsequentUnpaidPriceInfoResponse = getUnpaidPriceInfoAgainResult.getResponse().getContentAsString();
        List<OrderPriceInfoResponse> subsequentUnpaidPriceInfoList = objectMapper.readValue(
            subsequentUnpaidPriceInfoResponse,
            objectMapper.getTypeFactory().constructCollectionType(List.class, OrderPriceInfoResponse.class)
        );

        // 比較 /internal/subscribe/priceInfo/{orderNo}/unpaid 的結果
        assertEquals(initialUnpaidPriceInfoList.size(), subsequentUnpaidPriceInfoList.size(), 
            "方案更新後，未付款資訊列表大小應該不變");

        // 使用 Map 根據 ID 對應費用項目
        Map<Integer, OrderPriceInfoResponse> initialUnpaidMap = initialUnpaidPriceInfoList.stream()
            .collect(Collectors.toMap(OrderPriceInfoResponse::getId, Function.identity()));
        
        // 比較每個未付款資訊的關鍵欄位
        for (OrderPriceInfoResponse subsequentInfo : subsequentUnpaidPriceInfoList) {
            OrderPriceInfoResponse initialInfo = initialUnpaidMap.get(subsequentInfo.getId());
            assertNotNull(initialInfo, "更新後的未付款資訊應該在更新前也存在");

            // 比較 infoDetail 中的金額相關欄位
            if (initialInfo.getInfoDetail() != null && subsequentInfo.getInfoDetail() != null) {
                PriceInfoDetail initialDetail = initialInfo.getInfoDetail();
                PriceInfoDetail subsequentDetail = subsequentInfo.getInfoDetail();

                // 比較月費相關欄位
                if (initialDetail.getMonthlyFee() != null) {
                    assertEquals(initialDetail.getMonthlyFee(), subsequentDetail.getMonthlyFee(),
                        "方案更新後，未付款資訊中的月費應該不變");
                }

                // 比較里程費相關欄位
                if (initialDetail.getMileageFee() != null) {
                    assertEquals(initialDetail.getMileageFee(), subsequentDetail.getMileageFee(),
                        "方案更新後，未付款資訊中的里程費應該不變");
                }
            }
        }

        // 其他比較保持不變...
        // 比較 internal/subscribe/v2/priceInfo/{orderNo} 的結果
        // 檢查 key set 大小是否一致 (即期數數量)
        assertEquals(
            initialInternalPriceInfoNode.size(),
            subsequentInternalPriceInfoNode.size(),
            "方案更新後，priceInfo 期數數量應該不變"
        );

        // 逐一比較每個期數的 orderPriceInfoList (原有的邏輯保持不變)
        initialInternalPriceInfoNode.fieldNames().forEachRemaining(stage -> {
            JsonNode initialStageNode = initialInternalPriceInfoNode.get(stage);
            JsonNode subsequentStageNode = subsequentInternalPriceInfoNode.get(stage);

            assertNotNull(initialStageNode, "初始回應中缺少期數 " + stage);
            assertNotNull(subsequentStageNode, "後續回應中缺少期數 " + stage);

            // 獲取 orderPriceInfoList 節點
            JsonNode initialOrderPriceInfoList = initialStageNode.path("orderPriceInfoList");
            JsonNode subsequentOrderPriceInfoList = subsequentStageNode.path("orderPriceInfoList");

            assertEquals(
                initialOrderPriceInfoList.size(),
                subsequentOrderPriceInfoList.size(),
                "方案更新後，期數 " + stage + " 的 orderPriceInfoList 大小應該不變"
            );

            // 比較每個 orderPriceInfo 的金額和詳細資訊
            for (int i = 0; i < initialOrderPriceInfoList.size(); i++) {
                JsonNode initialOrderPriceInfo = initialOrderPriceInfoList.get(i);
                JsonNode subsequentOrderPriceInfo = subsequentOrderPriceInfoList.get(i);

                // 比較關鍵欄位 id、category、amount
                assertEquals(
                    initialOrderPriceInfo.path("id").asInt(),
                    subsequentOrderPriceInfo.path("id").asInt(),
                    "方案更新後，orderPriceInfo id 應該不變"
                );

                assertEquals(
                    initialOrderPriceInfo.path("category").asText(),
                    subsequentOrderPriceInfo.path("category").asText(),
                    "方案更新後，orderPriceInfo category 應該不變"
                );

                assertEquals(
                    initialOrderPriceInfo.path("amount").asInt(),
                    subsequentOrderPriceInfo.path("amount").asInt(),
                    "方案更新後，orderPriceInfo amount 應該不變"
                );

                // 比較 infoDetail 中的關鍵欄位
                if (initialOrderPriceInfo.has("infoDetail") && !initialOrderPriceInfo.path("infoDetail").isMissingNode()) {
                    JsonNode initialInfoDetail = initialOrderPriceInfo.path("infoDetail");
                    JsonNode subsequentInfoDetail = subsequentOrderPriceInfo.path("infoDetail");

                    if (initialInfoDetail.has("monthlyFee")) {
                        assertEquals(
                            initialInfoDetail.path("monthlyFee").asInt(),
                            subsequentInfoDetail.path("monthlyFee").asInt(),
                            "方案更新後，orderPriceInfo infoDetail.monthlyFee 應該不變"
                        );
                    }

                    if (initialInfoDetail.has("mileageFee")) {
                        assertEquals(
                            initialInfoDetail.path("mileageFee").asDouble(),
                            subsequentInfoDetail.path("mileageFee").asDouble(),
                            "方案更新後，orderPriceInfo infoDetail.mileageFee 應該不變"
                        );
                    }
                }
            }
        });

        // 5. 比較兩個回應的 originalPriceInfo 欄位
        JsonNode initialContractNode = initialListNode.get(0);
        JsonNode subsequentContractNode = subsequentListNode.get(0);

        // 剩餘比較部分保持不變
        // 取得 originalPriceInfo 節點
        JsonNode initialPriceInfo = initialContractNode.path("originalPriceInfo");
        JsonNode subsequentPriceInfo = subsequentContractNode.path("originalPriceInfo");

        // 確保 originalPriceInfo 節點存在
        assertFalse(initialPriceInfo.isMissingNode(), "初始回應中缺少 originalPriceInfo 欄位");
        assertFalse(subsequentPriceInfo.isMissingNode(), "後續回應中缺少 originalPriceInfo 欄位");

        // 比較 monthlyFee 相關欄位
        assertEquals(initialPriceInfo.path("monthlyFee").asInt(),
            subsequentPriceInfo.path("monthlyFee").asInt(),
            "方案更新後，已建立訂單的 monthlyFee 應該不變");

        assertEquals(initialPriceInfo.path("discountMonthlyFee").asInt(),
            subsequentPriceInfo.path("discountMonthlyFee").asInt(),
            "方案更新後，已建立訂單的 discountMonthlyFee 應該不變");

        assertEquals(initialPriceInfo.path("totalMonthlyFee").asInt(),
            subsequentPriceInfo.path("totalMonthlyFee").asInt(),
            "方案更新後，已建立訂單的 totalMonthlyFee 應該不變");

        // 比較 mileageFee 相關欄位
        assertEquals(initialPriceInfo.path("originalMileageFee").asDouble(),
            subsequentPriceInfo.path("originalMileageFee").asDouble(),
            "方案更新後，已建立訂單的 originalMileageFee 應該不變");

        assertEquals(initialPriceInfo.path("mileageFee").asDouble(),
            subsequentPriceInfo.path("mileageFee").asDouble(),
            "方案更新後，已建立訂單的 mileageFee 應該不變");

        // 比較 securityDepositInfo
        JsonNode initialSecurityDepositInfo = initialPriceInfo.path("securityDepositInfo");
        JsonNode subsequentSecurityDepositInfo = subsequentPriceInfo.path("securityDepositInfo");

        assertEquals(initialSecurityDepositInfo.path("securityDeposit").asInt(),
            subsequentSecurityDepositInfo.path("securityDeposit").asInt(),
            "方案更新後，已建立訂單的 securityDeposit 應該不變");

        assertEquals(initialSecurityDepositInfo.path("realSecurityDeposit").asInt(),
            subsequentSecurityDepositInfo.path("realSecurityDeposit").asInt(),
            "方案更新後，已建立訂單的 realSecurityDeposit 應該不變");

        assertEquals(initialSecurityDepositInfo.path("unpaidSecurityDeposit").asInt(),
            subsequentSecurityDepositInfo.path("unpaidSecurityDeposit").asInt(),
            "方案更新後，已建立訂單的 unpaidSecurityDeposit 應該不變");

        // 比較 mileageDiscount
        JsonNode initialMileageDiscount = initialPriceInfo.path("mileageDiscount");
        JsonNode subsequentMileageDiscount = subsequentPriceInfo.path("mileageDiscount");

        if (!initialMileageDiscount.isMissingNode() && !subsequentMileageDiscount.isMissingNode()) {
            assertEquals(initialMileageDiscount.size(), subsequentMileageDiscount.size(),
                "方案更新後，mileageDiscount 列表大小應該不變");

            // 比較每個 mileageDiscount 元素
            for (int i = 0; i < initialMileageDiscount.size(); i++) {
                assertEquals(initialMileageDiscount.get(i).path("stage").asInt(),
                    subsequentMileageDiscount.get(i).path("stage").asInt(),
                    "方案更新後，第 " + i + " 個 mileageDiscount 的 stage 應該不變");

                assertEquals(initialMileageDiscount.get(i).path("discountMileage").asInt(),
                    subsequentMileageDiscount.get(i).path("discountMileage").asInt(),
                    "方案更新後，第 " + i + " 個 mileageDiscount 的 discountMileage 應該不變");
            }
        }
    }

    @Test
    void listSubscribeLevel_success() throws Exception {
        // 建立一個查詢條件，查詢全部
        mockMvc.perform(get("/internal/subscribe/subscribeLevel")
                .param("skip", "0")
                .param("limit", "10"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list.length()").isNotEmpty())
            .andExpect(jsonPath("$.data.page.list[*].discountLevelName").exists());
    }

    @Test
    void listSubscribeLevel_fail_invalidMinMax() throws Exception {
        // 最低保證金大於最高保證金，應回傳 400
        mockMvc.perform(get("/internal/subscribe/subscribeLevel")
                .param("securityDepositMin", "10000")
                .param("securityDepositMax", "1000"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("最低保證金不可大於最高保證金"));
    }

    @Test
    void listSubscribeLevel_fail_negativeValue() throws Exception {
        // 傳入負數，應觸發 PositiveOrZero 驗證失敗
        mockMvc.perform(get("/internal/subscribe/subscribeLevel")
                .param("securityDepositMin", "-1"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("最低保證金必須大於等於0"));

        mockMvc.perform(get("/internal/subscribe/subscribeLevel")
                .param("monthlyFeeMin", "-100"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("最低基本月費必須大於等於0"));

        mockMvc.perform(get("/internal/subscribe/subscribeLevel")
                .param("mileageFeeMin", "-0.1"))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value("最低里程費率必須大於等於0"));
    }

    private long getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(1)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant()
            .toEpochMilli();
    }

    private static class OrderTestContext {
        Cars idleCar;
        InternalContractCreateReq contractCreateReq;
        MvcResult createContractResult;
        String orderNo;
        String mainContractNo;
    }
}