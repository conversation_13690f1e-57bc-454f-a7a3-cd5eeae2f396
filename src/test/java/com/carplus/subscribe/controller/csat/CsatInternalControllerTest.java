package com.carplus.subscribe.controller.csat;

import com.carplus.subscribe.App;
import com.carplus.subscribe.db.mysql.dao.CsatRepository;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.enums.CsatOrderSource;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.csat.CsatAssignYearMonthUpdateRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
class CsatInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CsatRepository csatRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL = "/internal/subscribe/csat/assignYearMonth";
    private static final List<String> ORDER_NOS = Arrays.asList("M202411259999", "20241125999");

    @BeforeEach
    void setUp() {
        // 準備測試資料
        Csat csat1 = createCsat("M202411259999", 202412);
        Csat csat2 = createCsat("20241125999", 202412);
        csatRepository.saveAll(Arrays.asList(csat1, csat2));
    }

    private Csat createCsat(String orderNo, Integer assignYearMonth) {
        Csat csat = new Csat();
        csat.setOrderNo(orderNo);
        csat.setStatus(CsatStatus.NOT_CALLED.getCode());
        csat.setSource(CsatOrderSource.SUBSCRIBE.getCode());
        csat.setQuestStatus(CsatQuestStatus.NOT_SURVEYED.getCode());
        csat.setAssignYearMonth(assignYearMonth);
        return csat;
    }

    @Test
    void updateCsatAssignYearMonth_Success() throws Exception {
        // 準備請求資料
        CsatAssignYearMonthUpdateRequest request = new CsatAssignYearMonthUpdateRequest();
        request.setOrderNos(ORDER_NOS);
        request.setAssignYearMonth("202509");

        // 執行請求
        mockMvc.perform(patch(BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // 驗證結果
        List<Csat> updatedCsats = csatRepository.findByOrders(ORDER_NOS);
        updatedCsats.forEach(csat -> 
            assertEquals(202509, csat.getAssignYearMonth())
        );
    }

    @Test
    void updateCsatAssignYearMonth_OrderNotFound() throws Exception {
        // 準備不存在的訂單編號
        CsatAssignYearMonthUpdateRequest request = new CsatAssignYearMonthUpdateRequest();
        request.setOrderNos(Arrays.asList("O999999999"));
        request.setAssignYearMonth("202509");

        // 執行請求並期望得到 CSAT_TASK_NOT_FOUND 錯誤
        mockMvc.perform(patch(BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode").value(SubscribeHttpExceptionCode.CSAT_TASK_NOT_FOUND.getCode()));
    }

    @Test
    void updateCsatAssignYearMonth_InvalidYearMonth() throws Exception {
        // 準備無效的年月格式
        CsatAssignYearMonthUpdateRequest request = new CsatAssignYearMonthUpdateRequest();
        request.setOrderNos(ORDER_NOS);
        request.setAssignYearMonth("999999"); // 無效的年月

        // 執行請求並期望得到400錯誤
        mockMvc.perform(patch(BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateCsatAssignYearMonth_EmptyOrderNos() throws Exception {
        // 準備空的訂單編號列表
        CsatAssignYearMonthUpdateRequest request = new CsatAssignYearMonthUpdateRequest();
        request.setOrderNos(Collections.emptyList());
        request.setAssignYearMonth("202509");

        // 執行請求並期望得到400錯誤
        mockMvc.perform(patch(BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void updateCsatAssignYearMonth_InvalidMonthNumber() throws Exception {
        // 準備無效的月份
        CsatAssignYearMonthUpdateRequest request = new CsatAssignYearMonthUpdateRequest();
        request.setOrderNos(ORDER_NOS);
        request.setAssignYearMonth("202513"); // 13月是無效的

        // 執行請求並期望得到400錯誤
        mockMvc.perform(patch(BATCH_UPDATE_ASSIGN_YEAR_MONTH_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
}