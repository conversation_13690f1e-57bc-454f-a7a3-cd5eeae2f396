package com.carplus.subscribe.controller.carwishlist;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CarWishlistRepository;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.model.cars.resp.CarCommonResponse;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.StationService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.WISHLIST_RATE_LIMIT_EXCEEDED;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional(transactionManager = "mysqlTransactionManager")
class CarWishlistPublicControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CarsService carsService;

    @Autowired
    private StationService stationService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private CarWishlistRepository carWishlistRepository;

    @Qualifier("mysqlEntityManager")
    @Autowired
    private EntityManager entityManager;

    private static final int ACCT_ID = 33456914;

    @Test
    void add_success_and_query() throws Exception {

        Cars car = carsService.getIdleCar().stream().filter(Cars::isOrderableOfficially).findFirst()
            .orElseThrow(() -> new SubscribeException(SubscribeHttpExceptionCode.CAR_NOT_FOUND));

        CarWishlistRequest carWishlistRequest = new CarWishlistRequest();
        carWishlistRequest.setPlateNo(car.getPlateNo());

        mockMvc.perform(MockMvcRequestBuilders.post("/subscribe/carWishlist")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, ACCT_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carWishlistRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        entityManager.clear();

        CarCommonResponse carInfoByCarNo = carsService.getCommonCarInfoByCarNo(car.getCarNo(), ACCT_ID);

        MvcResult queryCarWishlistResult = mockMvc.perform(MockMvcRequestBuilders.get("/subscribe/carWishlist")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, ACCT_ID)
                .param("skip", "0")
                .param("limit", "10")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list[0].plateNo").value(carInfoByCarNo.getPlateNo()))
            .andExpect(jsonPath("$.data.page.list[0].carNo").value(carInfoByCarNo.getCarNo()))
            .andExpect(jsonPath("$.data.page.list[0].brandNameEn").value(carInfoByCarNo.getCarBrand().getBrandNameEn()))
            .andExpect(jsonPath("$.data.page.list[0].carModelName").value(carInfoByCarNo.getCarModel().getCarModelName()))
            .andExpect(jsonPath("$.data.page.list[0].monthlyFee").value(carInfoByCarNo.getUseMonthlyFee()))
            .andExpect(jsonPath("$.data.page.list[0].mileageFee").value(carInfoByCarNo.isMonthlyDiscounted()
                ? carInfoByCarNo.getDiscountLevel().getMileageFee()
                : carInfoByCarNo.getLevel().getMileageFee()))
            .andExpect(jsonPath("$.data.page.list[0].type").value(carInfoByCarNo.isMonthlyDiscounted()
                ? carInfoByCarNo.getDiscountLevel().getType().name()
                : carInfoByCarNo.getLevel().getType().name()))
            .andExpect(jsonPath("$.data.page.list[0].tagIds").value(carInfoByCarNo.getTagIds()))
            .andExpect(jsonPath("$.data.page.list[0].locationName").value(stationService.findByStationCode(carInfoByCarNo.getLocationStationCode()).getStationName()))
            .andExpect(jsonPath("$.data.page.list[0].mfgYear").value(carInfoByCarNo.getMfgYear()))
            .andExpect(jsonPath("$.data.page.list[0].carState").value(carInfoByCarNo.getCarState().name()))
            .andExpect(jsonPath("$.data.page.list[0].isLaunched").value(car.isOrderableOfficially()))
            .andExpect(jsonPath("$.data.page.list[0].imgUrl").value(Optional.ofNullable(carInfoByCarNo.getImages())
                .filter(images -> !images.isEmpty())
                .map(images -> images.get(0).getPaths().get(0))
                .orElse(null)))
            .andReturn();

        JsonNode wishlistItems = objectMapper.readTree(queryCarWishlistResult.getResponse().getContentAsString()).path("data").path("page").path("list");

        if (wishlistItems.size() > 1) {
            // 收集所有非 NULL 的 updateDate 值
            List<Long> nonNullUpdateDates = new ArrayList<>();
            
            for (int i = 0; i < wishlistItems.size(); i++) {
                JsonNode updateDateNode = wishlistItems.get(i).path("updateDate");
                if (!updateDateNode.isNull()) {
                    nonNullUpdateDates.add(updateDateNode.asLong());
                }
            }
            
            // 檢查非 NULL 的日期是否按照降序排列
            for (int i = 0; i < nonNullUpdateDates.size() - 1; i++) {
                long currentUpdateDate = nonNullUpdateDates.get(i);
                long nextUpdateDate = nonNullUpdateDates.get(i + 1);
                
                Assertions.assertTrue(currentUpdateDate >= nextUpdateDate,
                    "清單未按照 updateDate 降序排列: " + currentUpdateDate + " 應該晚於或等於 " + nextUpdateDate);
            }
        }
    }


    @Test
    void testWishlistRateLimitExceeded() throws Exception {
        carWishlistRepository.deleteAll();
        // 清理 Redis 中的計數器，確保測試從 0 開始
        String carWishlistRedisKey = CarPlusConstant.CAR_WISHLIST_REDIS_KEY + ACCT_ID;
        redisTemplate.delete(carWishlistRedisKey);

        // 獲取 21 輛不同的可用車輛
        List<Cars> availableCars = carsService.getIdleCar().stream()
            .filter(Cars::isOrderableOfficially)
            .distinct() // 確保不重複
            .limit(21) // 需要至少 21 輛車
            .collect(Collectors.toList());

        if (availableCars.size() < 21) {
            throw new IllegalStateException("測試需要至少 21 輛不同的可用車輛，但僅找到 " + availableCars.size() + " 輛");
        }

        // 模擬 21 次 POST 請求
        for (int i = 0; i < 21; i++) {
            CarWishlistRequest carWishlistRequest = new CarWishlistRequest();
            carWishlistRequest.setPlateNo(availableCars.get(i).getPlateNo());

            ResultActions result = mockMvc.perform(MockMvcRequestBuilders.post("/subscribe/carWishlist")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, ACCT_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carWishlistRequest)));

            if (i < 20) {
                // 前 20 次應成功
                result.andExpect(status().isOk())
                    .andExpect(jsonPath("$.statusCode").value("0"));
            } else {
                // 第 21 次應返回錯誤代碼 94004
                result.andExpect(status().isOk())
                    .andExpect(jsonPath("$.statusCode").value(WISHLIST_RATE_LIMIT_EXCEEDED.getCode()))
                    .andExpect(jsonPath("$.message").value(WISHLIST_RATE_LIMIT_EXCEEDED.getMsg()));
            }
        }

        entityManager.clear();

        // 驗證 Redis 中的計數器達到 21
        Object requestCount = redisTemplate.opsForValue().get(carWishlistRedisKey);
        Assertions.assertEquals(21, requestCount, "Redis 計數器應為 21");

        // 使用 GET 請求驗證資料庫中的收藏清單有 20 條記錄
        MvcResult getResult = mockMvc.perform(MockMvcRequestBuilders.get("/subscribe/carWishlist")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, ACCT_ID)
                .param("skip", "0")
                .param("limit", "50") // 設置足夠大的 limit 以獲取所有記錄
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        // 解析回應並驗證記錄數
        String responseContent = getResult.getResponse().getContentAsString();
        JsonNode responseJson = objectMapper.readTree(responseContent);
        JsonNode wishlistList = responseJson.path("data").path("page").path("list");

        Assertions.assertEquals(20, wishlistList.size(), "收藏清單應包含 20 條記錄");
    }
}