package com.carplus.subscribe.controller.calendar;

import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.model.calendar.CalendarDeleteRequest;
import com.carplus.subscribe.model.calendar.CalendarUpdateRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
@Slf4j
class SubscribeCalendarInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private final String MEMBER_ID = "K2765";

    @Test
    void testSetAndDeleteUnavailableCalendarDateWorkflow() throws Exception {
        String carNo = "1856360304";
        int days = 6;
        String stationCode = "201";

        // 1. 第一次呼叫 GET /common/subscribe/availableDate API 取得可用日期
        MvcResult firstAvailableDateResult = mockMvc.perform(get("/common/subscribe/availableDate")
                .param("carNo", carNo)
                .param("days", String.valueOf(days))
                .param("stationCode", stationCode)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        JsonNode firstAvalableDateDataNode = objectMapper.readTree(firstAvailableDateResult.getResponse().getContentAsString()).get("data");
        // 確保有可用日期
        assert firstAvalableDateDataNode.isArray();
        assert !firstAvalableDateDataNode.isEmpty();

        Date firstAvailableDate = new Date(firstAvalableDateDataNode.get(0).asLong());

        // 將日期轉換為 "yyyyMMdd" 格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(firstAvailableDate);

        // 2. 呼叫 POST /internal/subscribe/calendar/unavailable API 設定不可用日期
        CalendarUpdateRequest updateRequest = new CalendarUpdateRequest();
        updateRequest.setDateList(Collections.singletonList(dateStr));
        updateRequest.setStationCodes(Collections.singletonList(stationCode));

        mockMvc.perform(post("/internal/subscribe/calendar/unavailable")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 3. 第二次呼叫 GET /common/subscribe/availableDate API 取得可用日期，記錄結果到快取
        mockMvc.perform(get("/common/subscribe/availableDate")
                .param("carNo", carNo)
                .param("days", String.valueOf(days))
                .param("stationCode", stationCode)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 4. 呼叫 DELETE /internal/subscribe/calendar/unavailable API 刪除不可用日期
        CalendarDeleteRequest deleteRequest = new CalendarDeleteRequest();
        deleteRequest.setDateList(Collections.singletonList(dateStr));

        mockMvc.perform(delete("/internal/subscribe/calendar/unavailable")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(deleteRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 5. 第三次呼叫 GET /common/subscribe/availableDate API 取得可用日期
        MvcResult thirdAvailableDateResult = mockMvc.perform(get("/common/subscribe/availableDate")
                .param("carNo", carNo)
                .param("days", String.valueOf(days))
                .param("stationCode", stationCode)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        JsonNode thirdAvailableDateDataNode = objectMapper.readTree(thirdAvailableDateResult.getResponse().getContentAsString()).get("data");
        assert thirdAvailableDateDataNode.isArray();
        assert !thirdAvailableDateDataNode.isEmpty();

        Date thirdAvailableDate = new Date(thirdAvailableDateDataNode.get(0).asLong());

        // 6. 驗證第三次呼叫 API 所獲取的第一個日期等於原本第一次呼叫 API 所獲取的第一個日期
        assertEquals(dateFormat.format(firstAvailableDate), dateFormat.format(thirdAvailableDate));
    }
}