package com.carplus.subscribe.controller.contract;

import carplus.common.enums.CRS.CRS;
import carplus.common.utils.DateUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.Orders;
import com.carplus.subscribe.enums.BuIdEnum;
import com.carplus.subscribe.enums.CarDefine;
import com.carplus.subscribe.enums.OrderPlatform;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.carplus.subscribe.model.request.contract.ContractCreateReq;
import com.carplus.subscribe.model.request.contract.MerchandiseReq;
import com.carplus.subscribe.service.CarsService;
import com.carplus.subscribe.service.CrsService;
import com.carplus.subscribe.service.OrderService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.hasItem;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
//@Transactional
class ContractPublicControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CarsService carsService;
    @Autowired
    private CrsService crsService;

    private final Integer ACCT_ID = 33456914;

    private Instant getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(10)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant();
    }

    @Test
    void shouldCreateContract_ExternalApi_CheckSysRemarker() throws Exception {
        // 先將車輛加入收藏清單以測試完整的備註邏輯
        String plateNo = "RDJ-7801";
        Integer acctId = 33456914;
        CarWishlistRequest wishlistRequest = new CarWishlistRequest();
        wishlistRequest.setPlateNo(plateNo);

        mockMvc.perform(post("/subscribe/carWishlist")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(objectMapper.writeValueAsString(wishlistRequest)))
            .andExpect(jsonPath("$.statusCode").value(0));

        // Arrange: Prepare contract creation JSON
        String createContractJson = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" +
            "    \"carLevel\": 9,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"returnStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + getExpectStartDate().toEpochMilli() + ",\n" +
            "    \"month\": 12,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"invoice\": {\n" +
            "        \"category\": 5,\n" +
            "        \"id\": \"A193477449\",\n" +
            "        \"title\": \"李志宏\",\n" +
            "        \"type\": 2\n" +
            "    },\n" +
            "    \"custSource\": 2,\n" +
            "    \"custRemark\": \"客戶備註測試\",\n" +
            "    \"merchList\": []\n" + // Empty merchList
            "}";

        // Act and Assert: 分別驗證各個備註條件
        mockMvc.perform(post("/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(createContractJson))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.mainContract.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem("客戶備註測試")))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("原始收訂車輛"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("車體描述"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("有加入用戶收藏清單"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].remarkerName", hasItem("sys")));
    }

    @Test
    @DisplayName("建立訂單後檢查 orders.getIsUnpaid() 不為 null")
    void createOrderThenCheckIsUnpaidNotNull() throws Exception {
        // 建立訂單
        String orderNo = createOrder(12);

        // 檢查訂單
        Orders order = orderService.getOrder(orderNo);
        assertNotNull(order.getIsUnpaid(), "訂單的 isUnpaid 狀態不應為 null");
        System.out.println("訂單編號: " + order.getOrderNo());
        System.out.println("訂單狀態: " + order.getStatus());
        System.out.println("isUnpaid 值: " + order.getIsUnpaid());
    }

    private String createOrder(int month) throws Exception {
        // 找到一台閒置車輛
        Cars idleCar = findIdleCar();

        // 建立合約請求
        ContractCreateReq contractCreateReq = new ContractCreateReq();
        contractCreateReq.setPlateNo(idleCar.getPlateNo());
        contractCreateReq.setCarLevel(idleCar.getSubscribeLevel());
        contractCreateReq.setDepartStationCode("203");
        contractCreateReq.setReturnStationCode("203");
        contractCreateReq.setExpectStartDate(getExpectStartDate());
        contractCreateReq.setMonth(month);
        contractCreateReq.setDisclaimer(false);
        contractCreateReq.setPremium(false);

        Invoice invoice = new Invoice();
        invoice.setCategory(5);
        invoice.setId("A193477449");
        invoice.setTitle("李志宏");
        invoice.setType(2);
        contractCreateReq.setInvoice(invoice);

        contractCreateReq.setCustSource(2);
        contractCreateReq.setOrderPlatform(OrderPlatform.WEB);

        List<MerchandiseReq> merchList = new ArrayList<>();
        MerchandiseReq merch1 = new MerchandiseReq();
        merch1.setSkuCode("wipe001");
        merch1.setQuantity(2);
        merch1.setActualUnitPrice(38);
        merchList.add(merch1);

        MerchandiseReq merch2 = new MerchandiseReq();
        merch2.setSkuCode("voucher001");
        merch2.setQuantity(1);
        merch2.setActualUnitPrice(19000);
        merchList.add(merch2);

        contractCreateReq.setMerchList(merchList);

        // 執行建立合約 API
        MvcResult createResult = mockMvc.perform(post("/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, ACCT_ID)
                .content(objectMapper.writeValueAsString(contractCreateReq)))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andReturn();

        // 解析回應取得訂單編號
        JsonNode dataNode = objectMapper.readTree(createResult.getResponse().getContentAsString()).get("data");
        JsonNode ordersNode = dataNode.get("orders");
        JsonNode orderNode = ordersNode.get(0);

        return orderNode.get("orderNo").asText();
    }

    private Cars findIdleCar() {
        List<Cars> idleCars = carsService.getIdleCar();
        assertFalse(idleCars.isEmpty(), "應該有閒置車輛");

        final int batchSize = 50;

        for (int i = 0; i < idleCars.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, idleCars.size());
            List<Cars> batch = idleCars.subList(i, endIndex);

            List<String> plateNos = batch.stream()
                .map(Cars::getPlateNo)
                .collect(Collectors.toList());

            Map<String, CarBaseInfoSearchResponse> carBaseInfoMap = crsService.getCars(plateNos);

            for (Cars car : batch) {
                CarBaseInfoSearchResponse carBaseInfo = carBaseInfoMap.get(car.getPlateNo());

                if (carBaseInfo == null) {
                    continue;
                }

                boolean isLicenseValid = CRS.LicenseStatus.BUY.getCode().equals(carBaseInfo.getCarLicense().getLicenseStatus());
                boolean isBuValid = BuIdEnum.subscribe.getCode().equals(carBaseInfo.getBuId());
                boolean isLaunchedValid = CarDefine.Launched.open == car.getLaunched();

                if (isLicenseValid && isBuValid && isLaunchedValid) {
                    return car;
                }
            }
        }

        throw new RuntimeException("找不到符合條件的閒置車輛");
    }
}