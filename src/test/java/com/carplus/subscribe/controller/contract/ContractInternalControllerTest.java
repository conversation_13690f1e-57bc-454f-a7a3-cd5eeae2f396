package com.carplus.subscribe.controller.contract;

import carplus.common.enums.CRS.CRS;
import carplus.common.response.CarPlusCode;
import carplus.common.response.Result;
import carplus.common.utils.BeanUtils;
import carplus.common.utils.DateUtils;
import carplus.common.utils.HttpUtils;
import carplus.common.utils.StringUtils;
import com.carplus.subscribe.App;
import com.carplus.subscribe.config.AppProperties;
import com.carplus.subscribe.constant.CarPlusConstant;
import com.carplus.subscribe.db.mysql.dao.CsatQuestRepository;
import com.carplus.subscribe.db.mysql.dao.CsatRepository;
import com.carplus.subscribe.db.mysql.dao.EtagInfoRepository;
import com.carplus.subscribe.db.mysql.dto.OrderPriceInfoDTO;
import com.carplus.subscribe.db.mysql.entity.ETagInfo;
import com.carplus.subscribe.db.mysql.entity.Notify;
import com.carplus.subscribe.db.mysql.entity.cars.Cars;
import com.carplus.subscribe.db.mysql.entity.contract.*;
import com.carplus.subscribe.db.mysql.entity.csat.Csat;
import com.carplus.subscribe.db.mysql.entity.csat.CsatQuest;
import com.carplus.subscribe.db.mysql.entity.invoice.Invoices;
import com.carplus.subscribe.db.mysql.entity.payment.Account;
import com.carplus.subscribe.db.mysql.entity.payment.PaymentInfo;
import com.carplus.subscribe.enums.*;
import com.carplus.subscribe.enums.csat.CsatQuestStatus;
import com.carplus.subscribe.enums.csat.CsatStatus;
import com.carplus.subscribe.event.car.CarDepartEvent;
import com.carplus.subscribe.event.car.CarReturnEvent;
import com.carplus.subscribe.event.order.OrderBookingEvent;
import com.carplus.subscribe.event.order.OrderClosedEvent;
import com.carplus.subscribe.event.order.OrderReopenedEvent;
import com.carplus.subscribe.event.order.OrderReturnedEvent;
import com.carplus.subscribe.exception.SubscribeException;
import com.carplus.subscribe.exception.SubscribeHttpExceptionCode;
import com.carplus.subscribe.feign.FinServiceBusClient;
import com.carplus.subscribe.feign.FinanceClient;
import com.carplus.subscribe.model.EmpMileageDiscount;
import com.carplus.subscribe.model.audit.BlackListQueryRes;
import com.carplus.subscribe.model.authority.MemberInfo;
import com.carplus.subscribe.model.crs.CarBaseInfoSearchResponse;
import com.carplus.subscribe.model.etag.EtagInfoResponse;
import com.carplus.subscribe.model.etag.EtagPdfExportRequest;
import com.carplus.subscribe.model.finance.ICBCRep;
import com.carplus.subscribe.model.invoice.Invoice;
import com.carplus.subscribe.model.invoice.InvoiceNewRequest;
import com.carplus.subscribe.model.invoice.InvoiceRequest;
import com.carplus.subscribe.model.invoice.v2.InvoiceDataSearchResponse;
import com.carplus.subscribe.model.invoice.v2.InvoiceMasterSearchResponse;
import com.carplus.subscribe.model.lrental.ContractSearchRep;
import com.carplus.subscribe.model.order.CachedReturnEarlyOrder;
import com.carplus.subscribe.model.order.LrentalContractRequest;
import com.carplus.subscribe.model.order.Remark;
import com.carplus.subscribe.model.payment.AdditionalData;
import com.carplus.subscribe.model.payment.PayAuthResultUrl;
import com.carplus.subscribe.model.payment.req.AccountRecord;
import com.carplus.subscribe.model.payment.req.AccountSettlementRequest;
import com.carplus.subscribe.model.payment.req.PayAuthRequest;
import com.carplus.subscribe.model.payment.req.PaymentRequest;
import com.carplus.subscribe.model.payment.resp.AccountSettlementResponse;
import com.carplus.subscribe.model.priceinfo.CalculateStage;
import com.carplus.subscribe.model.priceinfo.PriceInfoDetail;
import com.carplus.subscribe.model.priceinfo.PriceInfoInterface;
import com.carplus.subscribe.model.priceinfo.PriceInfoWrapper;
import com.carplus.subscribe.model.priceinfo.resp.OrderPriceInfoInternalResponse;
import com.carplus.subscribe.model.queue.PaymentQueue;
import com.carplus.subscribe.model.request.carwishlist.CarWishlistRequest;
import com.carplus.subscribe.model.request.contract.*;
import com.carplus.subscribe.model.request.depart.CarDepartRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffCompleteRequest;
import com.carplus.subscribe.model.request.dropoff.CarDropOffRequest;
import com.carplus.subscribe.model.request.dropoff.OrderCloseAgreeRequest;
import com.carplus.subscribe.model.request.dropoff.OrderCloseRequest;
import com.carplus.subscribe.model.request.order.*;
import com.carplus.subscribe.model.request.priceinfo.ExtraFeeRequest;
import com.carplus.subscribe.model.request.priceinfo.MerchandiseInfoRequest;
import com.carplus.subscribe.model.request.priceinfo.MileageDiscountRequest;
import com.carplus.subscribe.model.request.priceinfo.OrderPriceInfoCriteriaRequest;
import com.carplus.subscribe.rabbitmq.PaymentListener;
import com.carplus.subscribe.server.AuthServer;
import com.carplus.subscribe.server.AuthorityServer;
import com.carplus.subscribe.server.PushServer;
import com.carplus.subscribe.server.cars.LrentalServer;
import com.carplus.subscribe.service.*;
import com.carplus.subscribe.utils.CarsUtil;
import com.carplus.subscribe.utils.DateUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.nio.charset.Charset;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.carplus.subscribe.enums.LegalOperationReason.RETURNED_WITH_PAID;
import static com.carplus.subscribe.enums.PayFor.Depart;
import static com.carplus.subscribe.enums.PayFor.Return;
import static com.carplus.subscribe.enums.PaymentCategory.PayAuth;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoCategory.*;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Discount;
import static com.carplus.subscribe.enums.PriceInfoDefinition.PriceInfoType.Pay;
import static com.carplus.subscribe.exception.SubscribeHttpExceptionCode.*;
import static com.carplus.subscribe.service.OrderService.ORDER_STATE;
import static com.carplus.subscribe.utils.DateUtil.DASH_FORMATTER;
import static com.carplus.subscribe.utils.DateUtil.SLASH_FORMATTER_WITHOUT_TIME;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(classes = App.class)
@AutoConfigureMockMvc
@Transactional
@EnableAspectJAutoProxy
@Slf4j
class ContractInternalControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private OrderService orderService;

    @Autowired
    private PriceInfoService priceInfoService;

    @Autowired
    private SkuService skuService;

    @Autowired
    private LrentalServer lrentalServer;

    @Autowired
    private AuthorityServer authorityServer;

    @Autowired
    private PushServer pushServer;

    @Autowired
    private PaymentListener paymentListener;

    @Autowired
    private CarsService carsService;

    @Autowired
    private CrsService crsService;

    @Autowired
    private InvoiceServiceV2 invoiceService;

    @Autowired
    private PaymentServiceV2 paymentService;

    @Autowired
    private CheckoutService checkoutService;

    @Autowired
    private EtagInfoRepository etagInfoRepository;

    @Autowired
    private ContractLogic contractLogic;

    @Autowired
    private ETagService etagService;

    @Autowired
    private FinServiceBusClient finServiceBusClient;

    @Autowired
    private FinanceClient financeClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CsatRepository csatRepository;

    @Autowired
    private com.carplus.subscribe.server.CouponServer couponServer;

    @Autowired
    private CsatQuestRepository csatQuestRepository;

    @Autowired
    private AuthServer authServer;

    @Value("${station.subscribe}")
    private String subscribeStationCode;

    @Value("${lrental.userId}")
    private String lrentalUserId;

    private final String MEMBER_ID = "K2765";
    private final String ORDER_NO = "M202408059608";
    private final String UPDATE_DEPART_MILEAGE_PATH = "/internal/subscribe/{orderNo}/departMileage";
    private final String CARD_KEY = "4048ee192c24f0ab397f5f8ce997d035737412bbef44ebd8c2112a4ed645c3a3";

    private final String CAR_DEPART_FLOW_REQUEST = "{\n" +
        "  \"departMemberId\": \"" + MEMBER_ID + "\"\n" +
        "}";

    private final String CAR_DROP_OFF_COMPLETE_REQUEST = "{\n" +
        "  \"returnMemberId\": \"" + MEMBER_ID + "\"\n" +
        "}";

    private NumberFormat numberFormat = NumberFormat.getNumberInstance();

    @Test
    void queryOrder() throws Exception {
        MvcResult result = mockMvc.perform(get("/internal/subscribe/order/query")
                .param("orderNo", "M202502058581")
                .param("limit", "10")
                .param("skip", "0")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        String jsonResponse = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(jsonResponse);

        JsonNode listNode = rootNode.path("data").path("page").path("list");
        assertTrue(listNode.isArray() && !listNode.isEmpty(), "訂單列表不應為空");
        JsonNode orderNode = listNode.get(0);

        // 驗證 contractNo
        assertTrue(orderNode.has("contractNo"), "應包含 contractNo 欄位");
        assertNotNull(orderNode.get("contractNo"), "contractNo 不應為 null");

        // 驗證 isEContractSigned
        assertTrue(orderNode.has("isEContractSigned"), "應包含 isEContractSigned 欄位");
        assertNotNull(orderNode.get("isEContractSigned"), "isEContractSigned 不應為 null");
        assertTrue(orderNode.get("isEContractSigned").isBoolean(), "isEContractSigned 應為布林值");

        ArrayNode remarksArray = (ArrayNode) orderNode.path("remarks");

        // 取得 remarks createTime 並存入 List
        List<Long> createTimes = new ArrayList<>();
        for (JsonNode remark : remarksArray) {
            createTimes.add(remark.get("createTime").asLong());
        }

        // 驗證 createTime 是否為降序排序
        assertEquals(createTimes.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList()), createTimes, "remarks createTime 應為降序排序");
    }

    @Test
    void departCar() throws Exception {

        // 須先建立一筆已訂車的訂單，第一期費用也要先付款 以及 手動開立發票及收支登打，不手動建立車籍契約
        Orders order = orderService.getOrdersByStatus(OrderStatus.BOOKING).stream().max(Comparator.comparing(Orders::getInstantCreateDate))
            .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        Contract contract = order.getContract();
        String plateNo = contract.getMainContract().getPlateNo();
        String orderNo = order.getOrderNo();
        CarBaseInfoSearchResponse crsCar = crsService.getCar(plateNo);

        // 建立原始長租契約
        String originalLrentalContractNo = createOriginalLrentalContract(order);
        order = orderService.getOrder(orderNo);
        assertEquals(originalLrentalContractNo, order.getLrentalContractNo());

        // 記錄原始長租契約資訊
        ContractSearchRep originalContract = lrentalServer.getContractInfo(originalLrentalContractNo);
        String originalStartDate = originalContract.getDadt1().trim();
        String originalEndDate = originalContract.getDadt2().trim();

        Thread.sleep(2000);

        // 出車資料確認更新訂單，並設定與原長租契約不同的日期
        CarDepartRequest departRequest = new CarDepartRequest();
        departRequest.setDepartDate(Date.from(order.getExpectStartDate().plus(-10, ChronoUnit.DAYS))); // 設定不同的出車日期
        departRequest.setDepartMileage(crsCar.getCarBase().getKm() + 1);
        departRequest.setPlateNo(plateNo);
        departRequest.setDepartRemark("測試出車備註改儲存至訂單備註");
        ArrayList<String> errorMessages = orderService.departUpdateOrder(orderNo, departRequest, MEMBER_ID);
        assert errorMessages.isEmpty();

        // 出車 - 此時應該會觸發重建長租契約
        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/depart", orderNo)
            .contentType(MediaType.APPLICATION_JSON)
            .content(CAR_DEPART_FLOW_REQUEST);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證長租契約已重建
        order = orderService.getOrder(orderNo);
        assertNotNull(order.getLrentalContractNo());
        assertNotEquals(originalLrentalContractNo, order.getLrentalContractNo());

        // 驗證新建長租契約日期是否正確更新
        ContractSearchRep newContract = lrentalServer.getContractInfo(order.getLrentalContractNo());
        // 驗證新契約的起始日期是否與實際出車日期一致
        String newStartDate = newContract.getDadt1().trim();
        String newEndDate = newContract.getDadt2().trim();
        assertEquals(
            DateUtil.transferADDateToMinguoDate(departRequest.getDepartDate().toInstant()),
            newStartDate
        );
        // 驗證新契約的結束日期是否與訂單結束日期一致
        assertEquals(
            DateUtil.transferADDateToMinguoDate(order.getExpectEndDate()),
            newEndDate
        );
        // 確認新契約日期與原契約不同
        assertNotEquals(originalStartDate, newStartDate);
        assertNotEquals(originalEndDate, newEndDate);
        // 驗證出車備註改儲存至訂單備註
        assertThat(order.getRemarks(), hasItem(hasProperty("content", containsString(departRequest.getDepartRemark()))));
        // 驗證新契約業務名稱是否為訂閱營業副理名稱
        MemberInfo memberInfo = authorityServer.getMemberInfos(lrentalUserId).stream().findFirst().orElseThrow(() -> new SubscribeException(MEMBER_INFO_NOT_FOUND));
        assertEquals(memberInfo.getMemberName(), newContract.getDasalena().trim());

        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getCrsCarNo() != null && car.getCrsCarNo() > 0;
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(plateNo);
        // 確認出車後車輛里程數已與 CRS 同步
        assert Objects.equals(car.getCurrentMileage(), carBaseInfoSearchResponse.getCarBase().getKm());
    }

    private String createOriginalLrentalContract(Orders order) {
        LrentalContractRequest lrentalContractRequest = new LrentalContractRequest();
        lrentalContractRequest.setOrderNo(order.getOrderNo());
        lrentalContractRequest.setMemo(String.format("格上官網／新單／中古車\n" +
                "%s：%s - %s\n" +
                "%s：%s - %s（租期%d個月）\n",
            order.getContractNo(),
            DateUtil.getFormatString(order.getContract().getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME),
            DateUtil.getFormatString(order.getContract().getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME),
            order.getOrderNo(),
            DateUtil.getFormatString(order.getExpectStartDate(), SLASH_FORMATTER_WITHOUT_TIME),
            DateUtil.getFormatString(order.getExpectEndDate(), SLASH_FORMATTER_WITHOUT_TIME),
            order.getMonth()));

        Orders updatedOrder = orderService.createLrentalContract(lrentalContractRequest, MEMBER_ID);
        return updatedOrder.getLrentalContractNo();
    }

    @Test
    void returnCar() throws Exception {

        Orders order = orderService.getOrder(ORDER_NO);
        String plateNo = order.getPlateNo();

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/return", ORDER_NO)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .contentType(MediaType.APPLICATION_JSON)
            .content(CAR_DROP_OFF_COMPLETE_REQUEST);

        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));

        Cars car = carsService.findByPlateNo(plateNo);
        assert car.getCrsCarNo() != null && car.getCrsCarNo() > 0;
        CarBaseInfoSearchResponse crsCar = crsService.getCar(plateNo);
        // 確認完成還車後車輛里程數已與 CRS 同步
        assert Objects.equals(car.getCurrentMileage(), crsCar.getCarBase().getKm());
    }

    private String getRequestJson(int month) {
        long expectStartDate = getExpectStartDate();

        return "{\n" +
            "    \"plateNo\": \"RAP-2003\",\n" +
            "    \"carLevel\": 2,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + expectStartDate + ",\n" +
            "    \"month\": " + month + ",\n" +
            "    \"mainContractNo\": \"\",\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"mileageDiscounts\": null,\n" +
            "    \"yesChargingPoint\": null,\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"airFresher001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 220\n" + // 設定自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"recorder001\",\n" +
            "            \"quantity\": 1,\n" +
            "            \"actualUnitPrice\": 2800\n" + // 設定自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"towel001\",\n" +
            "            \"quantity\": 2\n" + // 未設定 actualUnitPrice,使用原始單價
            "        }\n" +
            "    ]\n" +
            "}";
    }

    private String getRequestJsonWithInvalidActualUnitPrice() {
        long expectStartDate = getExpectStartDate();

        return "{\n" +
            "    \"plateNo\": \"RAP-2003\",\n" +
            "    \"carLevel\": 2,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + expectStartDate + ",\n" +
            "    \"month\": 6,\n" +
            "    \"mainContractNo\": \"\",\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"mileageDiscounts\": null,\n" +
            "    \"yesChargingPoint\": null,\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"wipe001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 0\n" + // 設定無效單價
            "        }\n" +
            "    ]\n" +
            "}";
    }

    private long getExpectStartDate() {
        return LocalDateTime.now(DateUtils.ZONE_TPE)
            .plusDays(1)
            .atZone(DateUtils.ZONE_TPE)
            .toInstant()
            .toEpochMilli();
    }

    @Test
    void calculatePrice_6_months() throws Exception {

        int month = 6;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 2 && @.category == 'MonthlyFee')].infoDetail.month").value(3))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(600, 600, 600)));
    }

    @Test
    void calculatePrice_6_months_enableDiscountLevel() throws Exception {

        MockHttpServletRequestBuilder requestBuilder = patch("/internal/subscribe/subscribeLevel")
            .contentType(MediaType.APPLICATION_JSON)
            .content("{\n" +
                "    \"id\": 2,\n" +
                "    \"level\": 2,\n" +
                "    \"discountLevel\": 1,\n" + // 修改對照超激優惠方案為 1
                "    \"securityDeposit\": 10000,\n" +
                "    \"monthlyFee\": 8800,\n" +
                "    \"mileageFee\": 2.8,\n" +
                "    \"autoCredit\": true,\n" +
                "    \"discountMonthlyFee\": 6800,\n" +
                "    \"type\": \"SEASON\"\n" +
                "}");
        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk());

        mockMvc.perform(patch("/internal/subscribe/cars/{plateNo}", "RAP-2087")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\n" +
                    "    \"carState\": \"OLD\",\n" +
                    "    \"carModelCode\": \"S001H\",\n" +
                    "    \"seat\": 5,\n" +
                    "    \"energyType\": \"GASOLINE\",\n" +
                    "    \"fuelType\": \"petrol95\",\n" +
                    "    \"displacement\": 1499,\n" +
                    "    \"currentMileage\": 10,\n" +
                    "    \"equipIds\": null,\n" +
                    "    \"subscribeLevel\": 2,\n" +
                    "    \"tagIds\": [4],\n" +
                    "    \"cnDesc\": null,\n" +
                    "    \"type\": \"sedan\",\n" +
                    "    \"gearType\": \"at\",\n" +
                    "    \"mfgYear\": \"2020\",\n" +
                    "    \"colorDesc\": \"灰\",\n" +
                    "    \"prepWorkdays\": 10,\n" +
                    "    \"isSealandLaunched\": false\n" +
                    "}"))
            .andExpect(status().isOk());

        int month = 6;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.category == 'MonthlyFee')].infoDetail.monthlyFee", hasItems(6800, 6800, 6800))) // 啟用超激優惠 月費 8800 -> 6800
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.mileageFee", hasItems(2.24, 2.24, 2.24))); // 啟用超激優惠 里程費 2.8 -> 2.24
    }

    @Test
    void calculatePrice_7_months() throws Exception {

        int month = 7;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(1))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(200, 200, 200)));
    }

    @Test
    void calculatePrice_8_months() throws Exception {

        int month = 8;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(2))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(200, 200, 200)));
    }

    @Test
    void calculatePrice_9_months() throws Exception {

        int month = 9;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 3 && @.category == 'MonthlyFee')].infoDetail.month").value(3))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_10_months() throws Exception {

        int month = 10;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 4 && @.category == 'MonthlyFee')].infoDetail.month").value(1))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_11_months() throws Exception {

        int month = 11;
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == 4 && @.category == 'MonthlyFee')].infoDetail.month").value(2))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(300, 300, 300)));
    }

    @Test
    void calculatePrice_12_months() throws Exception {

        int month = 12;
        int discountMileage = getDiscountMileage(month);
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJson(month)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data.size()").value(getDataSize(month)))
            .andExpect(jsonPath("$.data[?(@.stage == " + getStages(month) + " && @.category == 'MonthlyFee')].infoDetail.month").value(getMonthLastStage(month)))
            .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(discountMileage, discountMileage, discountMileage)));
    }

    @Test
    void calculatePrice_3_to_12_months() throws Exception {

        Map<String, Sku> skuMap = skuService.getMapByCodes(Sets.newHashSet("airFresher001", "recorder001", "towel001"));

        for (int month = 3; month <= 12; month++) {
            int discountMileage = getDiscountMileage(month);
            mockMvc.perform(post("/internal/subscribe/calculate")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(getRequestJson(month)))
                .andExpect(jsonPath("$.statusCode").value(0))
                .andExpect(jsonPath("$..data[?(@.category != 'Merchandise')]", hasSize(getDataSize(month)))) // 排除汽車用品資料
                .andExpect(jsonPath("$.data[?(@.stage == " + getStages(month) + " && @.category == 'MonthlyFee')].infoDetail.month").value(getMonthLastStage(month)))
                .andExpect(jsonPath("$.data[?(@.category == 'MileageFee')].infoDetail.discountMileage", hasItems(discountMileage, discountMileage, discountMileage)))
                // 新增驗證加購汽車用品
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].skuCode", hasItems("airFresher001", "recorder001", "towel001"))) // 驗證商品代碼
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].amount", hasItems(440, 2800, 78))) // 驗證商品總額 (實際單價 * 數量)
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.unitPrice", hasItems(skuMap.get("airFresher001").getUnitPrice(), skuMap.get("recorder001").getUnitPrice(), skuMap.get("towel001").getUnitPrice()))) // 驗證商品原始單價
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.actualUnitPrice", hasItems(220, 2800, 39))) // 驗證商品實際單價
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].infoDetail.quantity", hasItems(2, 1, 2))) // 驗證商品數量
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].categoryName", hasItems("汽車用品", "汽車用品", "汽車用品"))) // 驗證商品 categoryName
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].memo", hasItems("植萃馥郁淨化噴霧", "快譯通原廠行車記錄器", "車用超細纖維擦拭布"))) // 驗證商品 memo
                .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')].formulaDescription", hasItems(
                    String.format("($%sx%d)", numberFormat.format(220), 2),
                    String.format("($%sx%d)", numberFormat.format(2800), 1),
                    String.format("($%sx%d)", numberFormat.format(39), 2)))); // 驗證商品 formulaDescription 應該要以實際單價呈現
        }
    }

    @Test
    void calculatePrice_WithInvalidActualUnitPrice() throws Exception {
        mockMvc.perform(post("/internal/subscribe/calculate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(getRequestJsonWithInvalidActualUnitPrice()))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(containsString("商品實際單價必須大於0")));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("完整優惠券流程測試 - 建立訂單 -> 領取優惠券 -> 套用優惠券 -> 付款出車")
    void completeCouponFlowTest_CreateOrder_ReceiveCoupon_ApplyCoupon_PayAndDepart() throws Exception {
        final int ACCT_ID = 33456914;
        OrderTestContext context = new OrderTestContext();
        
        try {
            // 步驟 1: 建立測試優惠券
            System.out.println("=== 步驟 1: 建立測試優惠券 ===");
            createTestCoupon(context);
            
            // 步驟 2: 會員領取優惠券
            System.out.println("=== 步驟 2: 會員領取優惠券 ===");
            receiveCoupon(context, ACCT_ID);
            
            // 步驟 3: 建立訂單
            System.out.println("=== 步驟 3: 建立訂單 ===");
            createOrder(context, ACCT_ID, 12, true, null, false, CarDefine.CarState.NEW, CarPlusCode.SUCCESS);
            
            // 步驟 4: 人工授信審核通過
            System.out.println("=== 步驟 4: 人工授信審核通過 ===");
            approveCreditManually(context);
            
            // 步驟 5: 支付保證金
            System.out.println("=== 步驟 5: 支付保證金 ===");
            payForSecurityDeposit(context);
            
            // 步驟 6: 查詢可用優惠券
            System.out.println("=== 步驟 6: 查詢可用優惠券 ===");
            queryAvailableCoupons(context, ACCT_ID);
            
            // 步驟 7: 套用優惠券到訂單
            System.out.println("=== 步驟 7: 套用優惠券到訂單 ===");
            applyCouponToOrder(context, ACCT_ID);
            
            // 步驟 8: 驗證優惠券套用狀態
            System.out.println("=== 步驟 8: 驗證優惠券套用狀態 ===");
            verifyCouponApplication(context);
            
            // 步驟 9: 支付出車費用並出車 (使用優惠券場景的付款方法)
            System.out.println("=== 步驟 9: 支付出車費用並出車 ===");
            payForDepartFeeAndCheckOutWithCoupon(context);
            
            // 步驟 9.1: 驗證付款後的優惠券效果
            System.out.println("=== 步驟 9.1: 驗證付款後的優惠券折扣效果 ===");
            verifyCouponDiscountEffectAfterPayment(context);
            
            createOriginalLrentalContract(context);
            departUpdateOrder(context, 180);
            accountSettlementAndCheckOutForDepart(context);
            depart(context);
            
            // 步驟 10: 驗證最終狀態
            System.out.println("=== 步驟 10: 驗證最終狀態 ===");
            Orders finalOrder = orderService.getOrder(context.orderNo);
            assertEquals(OrderStatus.DEPART.getStatus(), finalOrder.getStatus(), "訂單應為出車狀態");
            
            // 檢查優惠券相關費用已正確處理
            List<OrderPriceInfo> allPriceInfos = priceInfoService.getPriceInfosByOrder(context.orderNo);
            List<OrderPriceInfo> couponPriceInfos = allPriceInfos.stream()
                .filter(priceInfo -> priceInfo.getCategory() == CouponDiscount)
                .collect(Collectors.toList());
            
            assertFalse(couponPriceInfos.isEmpty(), "應存在優惠券相關費用明細");
            assertTrue(couponPriceInfos.stream().allMatch(OrderPriceInfo::isPaid), 
                "所有優惠券相關費用應已處理完成");
            
            System.out.println("=== 完整優惠券流程測試成功完成 ===");
            
        } finally {
            // 清理測試資料
            System.out.println("=== 清理測試優惠券 ===");
            cleanupTestCoupon(context);
        }
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("優惠券應用場景測試 - 有現有優惠券時不應返回可用優惠券")
    void couponApplicationScenarioTest_ExistingCouponShouldPreventNewCoupons() throws Exception {
        final int ACCT_ID = 33456914;
        OrderTestContext context = new OrderTestContext();
        
        try {
            // 建立兩張不同的測試優惠券
            System.out.println("=== 建立第一張測試優惠券 ===");
            createTestCoupon(context);
            receiveCoupon(context, ACCT_ID);
            String firstCouponSequenceId = context.couponSequenceId;
            
            // 建立訂單
            createOrder(context, ACCT_ID, 12, true, null, false, CarDefine.CarState.NEW, CarPlusCode.SUCCESS);
            approveCreditManually(context);
            payForSecurityDeposit(context);
            
            // 套用第一張優惠券
            System.out.println("=== 套用第一張優惠券 ===");
            applyCouponToOrder(context, ACCT_ID);
            
            // 建立第二張優惠券
            System.out.println("=== 建立第二張測試優惠券 ===");
            String firstCouponId = context.couponId;
            String firstEventCode = context.couponEventCode;
            
            // 重置優惠券相關資訊以建立第二張
            context.couponId = null;
            context.couponEventCode = null;
            context.couponSequenceId = null;
            
            createTestCoupon(context);
            receiveCoupon(context, ACCT_ID);
            
            // 查詢可用優惠券 - 應該回傳空清單，因為第一張優惠券的關聯費用尚未付款
            System.out.println("=== 驗證有現有優惠券時不應返回新的可用優惠券 ===");
            List<com.carplus.subscribe.model.coupon.Coupon> availableCoupons = 
                orderService.getAvailableCoupons(context.orderNo, ACCT_ID, null, null);
            
            assertTrue(availableCoupons.isEmpty(), 
                "當存在未付款的優惠券關聯費用時，不應返回任何可用優惠券");
            
            System.out.println("=== 優惠券應用場景驗證成功 ===");
            
            // 恢復清理用的資訊
            context.couponId = firstCouponId;
            context.couponEventCode = firstEventCode;
            context.couponSequenceId = firstCouponSequenceId;
            
        } finally {
            // 清理測試資料
            cleanupTestCoupon(context);
        }
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("設定里程費 - 測試第一期和後續期數的里程費用設定")
    void setMileageFee_Success_WithPreviousAndCurrentMileage() throws Exception {
        // Arrange
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 9, true, null, false, null);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 180);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);
        verifyCsatTaskCreation(context);

        // Test Case 1: First stage (stage=1) mileage fee setting
        OrderPriceInfo firstStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(1).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

        // 第一期里程費用：previousMileage 必須等於 departMileage
        int firstStagePreviousMileage = context.departRequest.getDepartMileage();
        int firstStageCurrentMileage = firstStagePreviousMileage + 1000;

        String firstStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), firstStageMileageFee.getId(), firstStagePreviousMileage, firstStageCurrentMileage);

        // Act & Assert for first stage
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(firstStagePayload))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.infoDetail.startMileage").value(firstStagePreviousMileage))
            .andExpect(jsonPath("$.data.infoDetail.endMileage").value(firstStageCurrentMileage));

        // Verify first stage database updates
        OrderPriceInfo updatedFirstStageMileageFee = priceInfoService.get(firstStageMileageFee.getId());
        assertEquals(firstStagePreviousMileage, updatedFirstStageMileageFee.getInfoDetail().getStartMileage());
        assertEquals(firstStageCurrentMileage, updatedFirstStageMileageFee.getInfoDetail().getEndMileage());

        // Verify first stage amount recalculation
        int expectedFirstStageTotalMileage = Math.max(firstStageCurrentMileage - firstStagePreviousMileage - updatedFirstStageMileageFee.getInfoDetail().getDiscountMileage(), 0);
        assertEquals(expectedFirstStageTotalMileage, updatedFirstStageMileageFee.getInfoDetail().getTotalMileage());
        int expectedFirstStageMileageFeeAmount = (int) Math.round(updatedFirstStageMileageFee.getInfoDetail().getMileageFee() * expectedFirstStageTotalMileage);
        assertEquals(expectedFirstStageMileageFeeAmount, updatedFirstStageMileageFee.getAmount());

        // Test Case 2: Second stage (stage=2) mileage fee setting
        OrderPriceInfo secondStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(2).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

        // 第二期里程費用：previousMileage 必須大於等於第一期的 endMileage
        int secondStagePreviousMileage = firstStageCurrentMileage; // 使用第一期的結束里程
        int secondStageCurrentMileage = secondStagePreviousMileage + 800;

        String secondStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), secondStageMileageFee.getId(), secondStagePreviousMileage, secondStageCurrentMileage);

        // Act & Assert for second stage
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(secondStagePayload))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.infoDetail.startMileage").value(secondStagePreviousMileage))
            .andExpect(jsonPath("$.data.infoDetail.endMileage").value(secondStageCurrentMileage));

        // Verify second stage database updates
        OrderPriceInfo updatedSecondStageMileageFee = priceInfoService.get(secondStageMileageFee.getId());
        assertEquals(secondStagePreviousMileage, updatedSecondStageMileageFee.getInfoDetail().getStartMileage());
        assertEquals(secondStageCurrentMileage, updatedSecondStageMileageFee.getInfoDetail().getEndMileage());

        // Verify second stage amount recalculation
        int expectedSecondStageTotalMileage = Math.max(secondStageCurrentMileage - secondStagePreviousMileage - updatedSecondStageMileageFee.getInfoDetail().getDiscountMileage(), 0);
        assertEquals(expectedSecondStageTotalMileage, updatedSecondStageMileageFee.getInfoDetail().getTotalMileage());
        int expectedSecondStageMileageFeeAmount = (int) Math.round(updatedSecondStageMileageFee.getInfoDetail().getMileageFee() * expectedSecondStageTotalMileage);
        assertEquals(expectedSecondStageMileageFeeAmount, updatedSecondStageMileageFee.getAmount());
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("設定里程費 - 測試第二期非連續里程記錄成功設定")
    void setMileageFee_Success_NonContinuousSecondStage() throws Exception {
        // Arrange
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 9, true, null, false, null);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 180);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);
        verifyCsatTaskCreation(context);

        // Test Case 1: First stage (stage=1) mileage fee setting
        OrderPriceInfo firstStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(1).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

        // 第一期里程費用：previousMileage 必須等於 departMileage
        int firstStagePreviousMileage = context.departRequest.getDepartMileage();
        int firstStageCurrentMileage = firstStagePreviousMileage + 1000;

        String firstStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), firstStageMileageFee.getId(), firstStagePreviousMileage, firstStageCurrentMileage);

        // Act & Assert for first stage
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(firstStagePayload))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.infoDetail.startMileage").value(firstStagePreviousMileage))
            .andExpect(jsonPath("$.data.infoDetail.endMileage").value(firstStageCurrentMileage));

        // Verify first stage database updates
        OrderPriceInfo updatedFirstStageMileageFee = priceInfoService.get(firstStageMileageFee.getId());
        assertEquals(firstStagePreviousMileage, updatedFirstStageMileageFee.getInfoDetail().getStartMileage());
        assertEquals(firstStageCurrentMileage, updatedFirstStageMileageFee.getInfoDetail().getEndMileage());

        // Test Case 2: Second stage (stage=2) mileage fee setting with non-continuous mileage
        OrderPriceInfo secondStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(2).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

        // 第二期里程費用：previousMileage 大於第一期的 endMileage (非連續里程記錄)
        int secondStagePreviousMileage = firstStageCurrentMileage + 500; // 非連續
        int secondStageCurrentMileage = secondStagePreviousMileage + 800;

        String secondStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), secondStageMileageFee.getId(), secondStagePreviousMileage, secondStageCurrentMileage);

        // Act & Assert for second stage
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(secondStagePayload))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.infoDetail.startMileage").value(secondStagePreviousMileage))
            .andExpect(jsonPath("$.data.infoDetail.endMileage").value(secondStageCurrentMileage));

        // Verify second stage database updates
        OrderPriceInfo updatedSecondStageMileageFee = priceInfoService.get(secondStageMileageFee.getId());
        assertEquals(secondStagePreviousMileage, updatedSecondStageMileageFee.getInfoDetail().getStartMileage());
        assertEquals(secondStageCurrentMileage, updatedSecondStageMileageFee.getInfoDetail().getEndMileage());

        // Verify second stage amount recalculation
        int expectedSecondStageTotalMileage = Math.max(secondStageCurrentMileage - secondStagePreviousMileage - updatedSecondStageMileageFee.getInfoDetail().getDiscountMileage(), 0);
        assertEquals(expectedSecondStageTotalMileage, updatedSecondStageMileageFee.getInfoDetail().getTotalMileage());
        int expectedSecondStageMileageFeeAmount = (int) Math.round(updatedSecondStageMileageFee.getInfoDetail().getMileageFee() * expectedSecondStageTotalMileage);
        assertEquals(expectedSecondStageMileageFeeAmount, updatedSecondStageMileageFee.getAmount());
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("設定里程費 - 測試第二期previousMileage小於第一期endMileage失敗")
    void setMileageFee_Failure_SecondStagePreviousMileageInvalid() throws Exception {
        // Arrange
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 9, true, null, false, null);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 180);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);
        verifyCsatTaskCreation(context);

        // Test Case 1: First stage (stage=1) mileage fee setting
        OrderPriceInfo firstStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(1).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

        // 第一期里程費用：previousMileage 必須等於 departMileage
        int firstStagePreviousMileage = context.departRequest.getDepartMileage();
        int firstStageCurrentMileage = firstStagePreviousMileage + 1000;

        String firstStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), firstStageMileageFee.getId(), firstStagePreviousMileage, firstStageCurrentMileage);

        // Act & Assert for first stage
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(firstStagePayload))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // Test Case 2: Second stage (stage=2) mileage fee setting with invalid previousMileage
        OrderPriceInfo secondStageMileageFee = priceInfoService.getPriceInfoWrapper(context.orderNo)
            .toFilter().category(MileageFee).type(Pay).stage(2).wrap().getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));

        // 第二期里程費用：previousMileage 小於第一期的 endMileage (無效里程)
        int invalidSecondStagePreviousMileage = firstStageCurrentMileage - 100; // 小於第一期結束里程
        int secondStageCurrentMileage = invalidSecondStagePreviousMileage + 800;

        String secondStagePayload = String.format("{\n" +
            "    \"acctId\": %d,\n" +
            "    \"orderPriceInfoId\": %d,\n" +
            "    \"previousMileage\": %d,\n" +
            "    \"currentMileage\": %d\n" +
            "}", context.contractCreateReq.getAcctId(), secondStageMileageFee.getId(), invalidSecondStagePreviousMileage, secondStageCurrentMileage);

        // Act & Assert for second stage - should fail with appropriate error code
        mockMvc.perform(patch("/internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(secondStagePayload))
            .andExpect(jsonPath("$.statusCode").value(PREVIOUS_MILEAGE_SMALLER_THAN_PREVIOUS_STAGE_END_MILEAGE.getCode()));

        // Verify second stage was not updated
        OrderPriceInfo unchangedSecondStageMileageFee = priceInfoService.get(secondStageMileageFee.getId());
        assertEquals(firstStageCurrentMileage, unchangedSecondStageMileageFee.getInfoDetail().getStartMileage());
        assertNull(unchangedSecondStageMileageFee.getInfoDetail().getEndMileage());
    }

    private int getDataSize(int month) {
        return 1 + 3 + (int) Math.ceil(month / 3.0d) * 2;
    }

    private int getStages(int month) {
        return (int) Math.ceil(month / 3.0d);
    }

    private int getMonthLastStage(int month) {
        return month % 3 == 0 ? 3 : month % 3;
    }

    private int getDiscountMileage(int month) {
        switch (month / 3) {
            case 2:
            case 3:
                return 600;
            case 4:
                return 1200;
            default:
                return 0;
        }
    }

    @Test
    void returnCarConfirm() throws Exception {
        String orderNo = "M202501101237";
        CarDropOffRequest request = new CarDropOffRequest();
        request.setReturnDate(new Date());
        request.setReturnMileage(75000);
        request.setReturnRemark("測試還車備註改儲存至訂單備註");

        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        Orders order = orderService.getOrder(orderNo);
        // 驗證還車備註改儲存至訂單備註
        assertThat(order.getRemarks(), hasItem(hasProperty("content", containsString(request.getReturnRemark()))));
    }

    @Test
    void dropOffCarDiscounts() throws Exception {

        String requestJson = "[\n" +
            "    {\n" +
            "        \"priceInfoPayId\":80482,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 2000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    },\n" +
            "    {\n" +
            "        \"priceInfoPayId\":80483,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 3000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    },\n" +
            "    {\n" +
            "        \"priceInfoPayId\":80484,\n" +
            "        \"category\": \"EmpDiscount\",\n" +
            "        \"discount\": 3000,\n" +
            "        \"reason\": \"test monthlyFee\",\n" +
            "        \"afterDiscountAmount\": null,\n" +
            "        \"originAmount\": null,\n" +
            "        \"agree\": null\n" +
            "    }\n" +
            "]";

        mockMvc.perform(post("/internal/subscribe/{orderNo}/returnCarDiscounts", "M202501221480")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(requestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data[-3:].category", everyItem(is("EmpDiscount"))))
            .andExpect(jsonPath("$.data[-3:].refPriceInfoNo", everyItem(anyOf(is(80482), is(80483), is(80484)))))
            .andExpect(jsonPath("$.data[-3:].updater", everyItem(is(MEMBER_ID))));
    }

    @Test
    void legalOperation_overdueNoReturn_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411074263",
            LegalOperationReason.OVERDUE_NO_RETURN,
            10000,
            Collections.emptyList(),
            null,
            false
        );
    }

    @Test
    void legalOperation_returnedWithDamage_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202502085233",
            LegalOperationReason.RETURNED_WITH_DAMAGE,
            9903,
            Collections.singletonList(82798),
            CarDefine.Launched.accident,
            true
        );
    }

    @Test
    void legalOperation_returnedWithUnpaid_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202504308183",
            LegalOperationReason.RETURNED_WITH_UNPAID,
            8960,
            Collections.singletonList(84765),
            null,
            true
        );
    }

    @Test
    void legalOperation_returnedWithUnpaidAndDamage_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411072799",
            LegalOperationReason.RETURNED_WITH_UNPAID_AND_DAMAGE,
            8000,
            Collections.emptyList(),
            CarDefine.Launched.accident,
            true
        );
    }

    @Test
    void legalOperation_returnedWithPaid_shouldSuccess() throws Exception {
        testLegalOperation(
            "M202411138402",
            RETURNED_WITH_PAID,
            10000,
            Collections.emptyList(),
            null,
            true
        );
    }

    private void testLegalOperation(
        String orderNo,
        LegalOperationReason reason,
        int forfeitedForRentalAmount,
        List<Integer> etagPriceInfoIdsToWriteOff,
        CarDefine.Launched expectedLaunched,
        boolean needReturnMember
    ) throws Exception {
        // Arrange
        Orders order = orderService.getOrdersByStatus(OrderStatus.DEPART).stream()
            .filter(o -> RenewType.AUTO_RENEW != o.getRenewType()
                && RenewType.RENEW != o.getRenewType()
                && o.getOrderNo().equals(orderNo))
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        // 記錄法務作業前的長租契約編號
        String lrentalContractNoBeforeLegalOperation = order.getLrentalContractNo();

        MainContract mainContract = order.getContract().getMainContract();
        int paidSecurityDeposit = mainContract.getOriginalPriceInfo()
            .getSecurityDepositInfo()
            .getPaidSecurityDeposit();

        // 初始化沖銷 etag 金額
        int etagWriteOffAmount = 0;

        LegalOperationRequest request = new LegalOperationRequest();
        request.setReason(reason);
        request.setForfeitedForRentalAmount(forfeitedForRentalAmount);
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo);
        if (CollectionUtils.isNotEmpty(etagPriceInfoIdsToWriteOff)) {
            request.setEtagPriceInfoIdsToWriteOff(etagPriceInfoIdsToWriteOff);
            etagWriteOffAmount = priceInfoWrapper.toFilter()
                .category(ETag).type(Pay).unpaid().add(opi -> etagPriceInfoIdsToWriteOff.contains(opi.getId())).collect()
                .stream().mapToInt(PriceInfoInterface::getActualPrice).sum();
        }

        if (needReturnMember) {
            request.setReturnDate(new Date());
            request.setReturnMileage(order.getDepartMileage() + 2000);
            request.setReturnMemberId(MEMBER_ID);
        }

        // Act
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/legalOperation", order.getOrderNo())
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk());

        // Assert
        // 1. 驗證訂單狀態
        order = orderService.getOrder(order.getOrderNo());
        int expectedOrderStatus = orderService.inferTargetOrderStatus(order, reason);
        assertEquals(expectedOrderStatus, order.getStatus());

        // 2. 驗證車輛狀態
        Cars car = carsService.findByPlateNo(order.getPlateNo());
        assertEquals(reason.getCarStatus().getCode(), car.getCarStatus());
        if (expectedLaunched != null) {
            assertEquals(expectedLaunched, car.getLaunched());
        }

        // 3. 驗證保證金沒收金額 (若 order 為續約單，須先取得母單來做以下操作)
        if (!order.getIsNewOrder()) {
            order = mainContract.getContracts().stream()
                .map(c -> orderService.getOrdersByContractNo(c.getContractNo()))
                .flatMap(List::stream)
                .min(Comparator.comparing(Orders::getCreateDate))
                .orElseThrow(() -> new SubscribeException(ORDER_NOT_FOUND));
        }
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(order.getOrderNo());

        // 沒收金額 = 沒收租金金額 + 沖銷 etag 金額
        int forfeitedAmount = forfeitedForRentalAmount + etagWriteOffAmount;
        if (forfeitedAmount == paidSecurityDeposit) {
            // 當 沒收金額比例 = 100% 時，應該只有一筆 Others 類別的紀錄（原保證金紀錄被轉換）
            OrderPriceInfo othersOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == Others && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getAmount());
            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getReceivedAmount());

            // 確認沒有 SecurityDeposit 類別的紀錄
            assertTrue(orderPriceInfoList.stream()
                .noneMatch(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode()));
        } else if (forfeitedAmount > 0 && forfeitedAmount < paidSecurityDeposit) {
            // 當 沒收金額比例 < 100% 時，應該有兩筆紀錄，一筆 Others 類別，一筆 SecurityDeposit 類別
            OrderPriceInfo othersOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == Others && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getAmount());
            assertEquals(forfeitedForRentalAmount, othersOrderPriceInfo.getReceivedAmount());

            OrderPriceInfo securityDepositOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(paidSecurityDeposit - forfeitedAmount, securityDepositOrderPriceInfo.getAmount());
            assertEquals(paidSecurityDeposit - forfeitedAmount, securityDepositOrderPriceInfo.getReceivedAmount());
        } else if (forfeitedAmount == 0) {
            // 當 沒收金額比例 = 0% 時，應該只有一筆 SecurityDeposit 類別的紀錄
            OrderPriceInfo securityDepositOrderPriceInfo = orderPriceInfoList.stream()
                .filter(info -> info.getCategory() == SecurityDeposit && info.getType() == Pay.getCode())
                .findFirst()
                .orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));

            assertEquals(paidSecurityDeposit, securityDepositOrderPriceInfo.getAmount());
            assertEquals(paidSecurityDeposit, securityDepositOrderPriceInfo.getReceivedAmount());
        }

        // 4. 驗證訂單備註
        String expectedRemarkContent = String.format("完成法務作業 (事由：%s)，保證金%s %s",
            reason.getDescription(),
            reason == RETURNED_WITH_PAID ? "轉收入" : "沒收",
            String.format("$%,d (租金 $%,d + etag $%,d)", forfeitedAmount, forfeitedForRentalAmount, etagWriteOffAmount));
        assertTrue(order.getRemarks().stream()
            .anyMatch(remark -> expectedRemarkContent.equals(remark.getContent())));

        // 5. 驗證發票開立
        if (forfeitedForRentalAmount > 0) {
            List<Invoices> invoices = invoiceService.getInvoice(orderNo);
            assertTrue(invoices.stream()
                .anyMatch(invoice -> invoice.getAmount() == forfeitedForRentalAmount));
        }

        // 6. 驗證 etag 費用及 ETagInfo
        if (CollectionUtils.isNotEmpty(etagPriceInfoIdsToWriteOff)) {
            orderPriceInfoList.stream().filter(opi -> etagPriceInfoIdsToWriteOff.contains(opi.getId()))
                .forEach(opi -> assertTrue(opi.isPaid()));
            etagInfoRepository.getETagInfosByOrderNo(orderNo).stream().filter(etag -> etagPriceInfoIdsToWriteOff.contains(etag.getOrderPriceInfoId()))
                .forEach(etag -> assertTrue(etag.isPaymentCompleted()));
        }

        // 7. 驗證部門約建立
        if (reason.isCenterContractRequired()) {
            // 檢查車輛是否為格上車輛，非格上車輛不會建立部門約
            if (CarsUtil.isCarPlusCar(car.getVatNo())) {
                // 驗證長租契約編號已被更新 (表示新建立了部門約)
                assertNotNull(order.getLrentalContractNo(), "長租契約編號應該已被設定");
                assertFalse(order.getLrentalContractNo().isEmpty(), "長租契約編號不應為空");

                // 根據車輛最終狀態判斷是否應該成功建立部門約
                Set<CarDefine.CarStatus> allowedStatusesForCenterContract = new HashSet<>(Arrays.asList(CarDefine.CarStatus.Free, CarDefine.CarStatus.Scrapped));

                if (allowedStatusesForCenterContract.contains(reason.getCarStatus())) {
                    // 車輛狀態為空車或報廢時，應該成功建立新的部門約
                    assertNotEquals(lrentalContractNoBeforeLegalOperation, order.getLrentalContractNo(),
                        String.format("車輛狀態為 %s 時，應該建立新的部門約，長租契約編號應該改變", reason.getCarStatus()));
                } else if (reason.getCarStatus() == CarDefine.CarStatus.Stolen) {
                    // 車輛狀態為失竊時，不允許建立部門約，長租契約編號應該保持不變
                    assertEquals(lrentalContractNoBeforeLegalOperation, order.getLrentalContractNo(),
                        "車輛狀態為失竊時，不應該建立新的部門約，長租契約編號應該保持不變");
                }

                // 驗證新建立的部門約資訊 (如果有建立的話)
                if (!lrentalContractNoBeforeLegalOperation.equals(order.getLrentalContractNo())) {
                    ContractSearchRep newCenterContract = lrentalServer.getContractInfo(order.getLrentalContractNo());
                    assertNotNull(newCenterContract, "應該能夠查詢到新建立的部門約");
                }
            } else {
                // 非格上車輛，長租契約編號應該保持不變
                assertEquals(lrentalContractNoBeforeLegalOperation, order.getLrentalContractNo(),
                    "非格上車輛不應該建立部門約，長租契約編號應該保持不變");
            }
        } else {
            // 對於不需要建立部門約的法務事由 (目前只有逾期不還車)，長租契約編號應該保持不變
            assertEquals(lrentalContractNoBeforeLegalOperation, order.getLrentalContractNo(),
                "不需要建立部門約的事由不應該變更長租契約編號");
        }
    }

    @Test
    void calculateRenewPrice_shouldHaveInsuranceForEachStage() throws Exception {
        // Arrange
        String orderNo = "M202411140724";
        Orders order = orderService.getOrder(orderNo);
        assert order.getMonth() == 6; // 原訂單租期 6 個月
        OrderPriceInfoCriteriaRequest request = OrderPriceInfoCriteriaRequest.builder().category(Collections.singletonList(Insurance)).build();
        assert priceInfoService.getPriceInfosByOrder(orderNo, request).stream().findFirst().isPresent();

        String renewRequestJson = String.format("{\"acctId\": %d, \"month\": %d}", order.getContract().getMainContract().getAcctId(), 9); // 續約租期 9 個月

        // Act & Assert
        mockMvc.perform(post("/internal/subscribe/{orderNo}/calculateRenew", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .content(renewRequestJson))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.data[?(@.category == 'Insurance')]", hasSize(3))); // 應該有 3 個 stage 的 Insurance
    }

    @Test
    void findOrders() throws Exception {
        mockMvc.perform(get("/internal/subscribe/{orderNo}", "M202411253893")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print());
    }

    @Test
    void getMainContractAndSubInfosV2() throws Exception {
        mockMvc.perform(get("/internal/subscribe/v2/mainContract/{mainContractNo}", "U2025092600903")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(print());
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 訂單尚未出車")
    void updateDepartMileage_Failure_OrderNotDeparted() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成符合條件的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_STATUS_NOT_DEPART.getMsg()));
    }

    @Test
    @DisplayName("成功更新出車里程數 - 新訂單且已出車未還車")
    void updateDepartMileage_Success() throws Exception {
        // Arrange
        String orderNo = "M202501148266"; // 請替換成符合條件的訂單編號 (已執行還車資料確認 && 第一筆里程費尚未付款 && 尚未確認還車)
        int newDepartMileage = 75500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);
        request.setMsgForCustomer(String.format("實際出車里程為 %d km。", newDepartMileage));

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證訂單出車里程是否更新
        Orders updatedOrder = orderService.getOrder(orderNo);
        assertEquals(newDepartMileage, updatedOrder.getDepartMileage());

        // 驗證備註是否正確新增
        updatedOrder.getRemarks().stream()
            .filter(remark -> remark.getContent().matches("實際出車里程異動：調整前 \\d+ km；調整後 \\d+ km"))
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_REMARK_NOT_FOUND));
        // 驗證留言給客戶是否正確新增
        assertEquals(String.format("實際出車里程為 %d km。", newDepartMileage), updatedOrder.getContract().getMainContract().getMsgForCust().get(0).getContent());

        // 驗證里程費用資訊是否正確更新
        PriceInfoWrapper priceInfoWrapper = priceInfoService.getPriceInfoWrapper(orderNo).toFilter().category(MileageFee).type(Pay).stage(1).wrap();
        OrderPriceInfo updatedMileageFee = priceInfoWrapper.getList().stream()
            .findFirst()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_NOT_FOUND));

        PriceInfoDetail updatedDetail = updatedMileageFee.getInfoDetail();

        // 驗證起始里程是否更新
        assertEquals(newDepartMileage, updatedDetail.getStartMileage());

        // 驗證總里程是否正確計算
        int expectedTotalMileage = Math.max(updatedDetail.getEndMileage() - newDepartMileage - updatedDetail.getDiscountMileage(), 0);
        assertEquals(expectedTotalMileage, updatedDetail.getTotalMileage());

        // 驗證費用金額是否正確計算
        int expectedAmount = (int) Math.round(updatedDetail.getMileageFee() * expectedTotalMileage);
        assertEquals(expectedAmount, updatedMileageFee.getAmount());
    }

    /**
     * * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("更新出車里程數失敗 - 異動出車里程不應大於第一期里程費結算里程")
    void updateDepartMileage_Failure_UpdatedDepartMileageShouldNotGreaterThanFirstStageMileageFeeEndMileage() throws Exception {
        // Arrange
        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        // 開始異動出車里程(大於結算里程)
        int newDepartMileage = context.carDropOffRequest.getReturnMileage() + 1000;
        UpdateDepartMileageRequest updateDepartMileageRequest = new UpdateDepartMileageRequest();
        updateDepartMileageRequest.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateDepartMileageRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE.getCode()))
            .andExpect(jsonPath("$.message").value(UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE.getMsg()));
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 第一筆里程費已付款")
    void updateDepartMileage_Failure_FirstMileageFeePaid() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成第一筆里程費已付款的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_FIRST_STAGE_MILEAGE_ALREADY_PAID.getMsg()));
    }

    @Test
    @DisplayName("更新出車里程數失敗 - 訂單已還車")
    void updateDepartMileage_Failure_OrderReturned() throws Exception {
        // Arrange
        String orderNo = "M202412240930"; // 請替換成符合條件的訂單編號
        int newDepartMileage = 72500;

        UpdateDepartMileageRequest request = new UpdateDepartMileageRequest();
        request.setNewDepartMileage(newDepartMileage);

        // Act & Assert
        mockMvc.perform(patch(UPDATE_DEPART_MILEAGE_PATH, orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value(FORBIDDEN.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_STATUS_NOT_DEPART.getMsg()));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("出車前異動租期 - 續約訂單成功案例")
    void modifyRentalPeriod_RenewalOrder_Success() throws Exception {
        // 測試流程：建立原始訂單(6個月) -> 出車 -> 續約(6個月) -> 測試租期異動功能
        // 這個測試驗證續約訂單 (status=10, isNewOrder=false) 的租期異動功能
        OrderTestContext context = new OrderTestContext();

        // 1. 建立原始訂單 (6個月) - 原始訂單6個月，續約訂單6個月，總共12個月
        createOrder(context, 6, true, null, false, CarDefine.CarState.NEW);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 160);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);

        // 2. 建立續約訂單 (6個月) - 這將產生 status=10, isNewOrder=false 的訂單
        renewOrder(context, SubscribeRenewMonth.SIX);

        // 3. 處理續約訂單到 BOOKING 狀態但保持費用未付款狀態
        assertNotNull(context.renewalOrderNo, "續約訂單編號不應為空");
        context.orderNo = context.renewalOrderNo; // 切換到續約訂單
        approveCreditManually(context);
        setMileageDiscount(context.orderNo); // 設定續約訂單的里程優惠

        // 驗證續約訂單符合 modifyRentalPeriod 的前置條件
        Orders renewalOrder = orderService.getOrder(context.renewalOrderNo);
        Integer actualStatus = renewalOrder.getStatus();
        Boolean actualIsNewOrder = renewalOrder.getIsNewOrder();
        Integer actualMonth = renewalOrder.getMonth();

        assertEquals(OrderStatus.BOOKING.getStatus(), actualStatus, "續約訂單狀態應為 BOOKING");
        assertFalse(actualIsNewOrder, "續約訂單 isNewOrder 應為 false");
        assertEquals(6, actualMonth, "續約訂單原始租期應為 6 個月");

        // 驗證續約訂單6個月時的新車折價邏輯
        log.info("=== 驗證續約訂單6個月時的新車折價 ===");
        validateInitialNewCarDiscountForRenewalOrder(context.renewalOrderNo, 6);

        // 捕獲初始狀態 (6個月) 的費用列表
        List<OrderPriceInfo> feesFor6Months = priceInfoService.getPriceInfosByOrder(context.renewalOrderNo);
        
        // 捕獲租期異動前的 EmpMileageDiscount 狀態 (6個月)
        Map<Integer, Integer> mileageDiscountsBefore6To9 = captureEmpMileageDiscountsBefore(context.renewalOrderNo);
        
        // 捕獲租期異動前的 Insurance, Replacement, Dispatch 費用狀態 (6個月)
        Map<String, List<OrderPriceInfo>> preservedFeesBefore6To9 = capturePreservedFeesBefore(context.renewalOrderNo);

        // =================================================================================================================
        // 4.【增加租期】執行租期異動測試 (6 -> 9 個月)
        // =================================================================================================================
        Integer increasedRentalPeriod = 9; // 新租期月數
        ModifyRentalPeriodRequest increaseRequest = new ModifyRentalPeriodRequest();
        increaseRequest.setNewSubscribeMonth(increasedRentalPeriod);

        log.info("=== 執行租期異動：6個月 -> 9個月 ===");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/modifyRentalPeriod", context.renewalOrderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(increaseRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 5. 驗證訂單更新與備註
        Orders increasedOrder = orderService.getOrder(context.renewalOrderNo);
        Integer actualIncreasedMonth = increasedOrder.getMonth();
        List<Remark> increasedOrderRemarks = increasedOrder.getRemarks();

        assertEquals(increasedRentalPeriod, actualIncreasedMonth, "訂單租期應已更新為 " + increasedRentalPeriod + " 個月");
        assertTrue(increasedOrderRemarks.stream().anyMatch(r -> r.getContent().contains("出車前異動租期：調整前 6 月、調整後 9 月")), "應有租期增加的備註");

        // 6. 驗證費用列表已正確更新
        log.info("=== 驗證租期異動後的費用結構 (9個月) ===");
        validatePriceInfoChange(context.renewalOrderNo, increasedRentalPeriod, feesFor6Months);
        
        // 7. 驗證 EmpMileageDiscount 記錄的正確性 (6→9個月)
        log.info("=== 驗證租期異動後的 EmpMileageDiscount 狀態 (6→9個月) ===");
        validateEmpMileageDiscountChange(context.renewalOrderNo, increasedRentalPeriod, mileageDiscountsBefore6To9);
        
        // 8. 驗證 Insurance, Replacement, Dispatch 費用保留的正確性 (6→9個月)
        log.info("=== 驗證租期異動後的 Insurance/Replacement/Dispatch 費用保留狀態 (6→9個月) ===");
        validatePreservedFeesChange(context.renewalOrderNo, preservedFeesBefore6To9);

        // 捕獲下一次異動前的狀態 (9個月)
        List<OrderPriceInfo> feesFor9Months = priceInfoService.getPriceInfosByOrder(context.renewalOrderNo);
        
        // 捕獲租期異動前的 EmpMileageDiscount 狀態 (9個月)
        Map<Integer, Integer> mileageDiscountsBefore9To6 = captureEmpMileageDiscountsBefore(context.renewalOrderNo);
        
        // 捕獲租期異動前的 Insurance, Replacement, Dispatch 費用狀態 (9個月)
        Map<String, List<OrderPriceInfo>> preservedFeesBefore9To6 = capturePreservedFeesBefore(context.renewalOrderNo);

        // =================================================================================================================
        // 7.【減少租期】執行租期異動測試 (9 -> 6 個月)
        // =================================================================================================================
        Integer decreasedRentalPeriod = 6; // 新租期月數
        ModifyRentalPeriodRequest decreaseRequest = new ModifyRentalPeriodRequest();
        decreaseRequest.setNewSubscribeMonth(decreasedRentalPeriod);

        log.info("=== 執行租期異動：9個月 -> 6個月 ===");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/modifyRentalPeriod", context.renewalOrderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(decreaseRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 8. 驗證訂單更新與備註
        Orders decreasedOrder = orderService.getOrder(context.renewalOrderNo);
        Integer actualDecreasedMonth = decreasedOrder.getMonth();
        List<Remark> decreasedOrderRemarks = decreasedOrder.getRemarks();

        assertEquals(decreasedRentalPeriod, actualDecreasedMonth, "訂單租期應已更新為 " + decreasedRentalPeriod + " 個月");
        assertTrue(decreasedOrderRemarks.stream().anyMatch(r -> r.getContent().contains("出車前異動租期：調整前 9 月、調整後 6 月")), "應有租期減少的備註");

        // 9. 驗證費用列表已正確更新
        log.info("=== 驗證租期異動後的費用結構 (6個月) ===");
        validatePriceInfoChange(context.renewalOrderNo, decreasedRentalPeriod, feesFor9Months);
        
        // 10. 驗證 EmpMileageDiscount 記錄的正確性 (9→6個月)
        log.info("=== 驗證租期異動後的 EmpMileageDiscount 狀態 (9→6個月) ===");
        validateEmpMileageDiscountChange(context.renewalOrderNo, decreasedRentalPeriod, mileageDiscountsBefore9To6);
        
        // 11. 驗證 Insurance, Replacement, Dispatch 費用在期數減少時的正確處理 (9→6個月)
        log.info("=== 驗證租期異動後的 Insurance/Replacement/Dispatch 費用處理狀態 (9→6個月，期數減少) ===");
        // 但前三期的 Insurance/Replacement/Dispatch 費用應該保留
        validatePreservedFeesAfterPeriodDecrease(context.renewalOrderNo, preservedFeesBefore9To6, decreasedRentalPeriod);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("租期異動超過 12 個月限制 - 應保持新合約不合併到原合約 (忽略已取消續約)")
    void testModifyRentalPeriodExceedsLimitShouldNotMergeToOriginalContract() throws Exception {
        // 測試包含已取消續約訂單的情境：
        // 1. 原始訂單 (6個月) 完成出車並還車
        // 2. 第一次續約訂單 (6個月) 被取消
        // 3. 第二次續約訂單 (12個月)，然後修改為 9個月
        // 預期：原合約計算租期時應忽略已取消的第一次續約，6+9=15個月 > 12，不可以合併
        OrderTestContext context = new OrderTestContext();

        // 1. 建立原始訂單 (6個月) 並完成出車還車流程
        createOrder(context, 6, true, null, false, CarDefine.CarState.NEW);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 160);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);

        String originalOrderNo = context.orderNo;
        String originalContractNo = context.order.getContractNo();

        log.info("=== 原始訂單完成出車還車：{} (6個月), 合約: {} ===", originalOrderNo, originalContractNo);

        // 2. 建立第一次續約訂單 (6個月) 然後取消
        renewOrder(context, SubscribeRenewMonth.SIX);
        String firstRenewalOrderNo = context.renewalOrderNo;
        log.info("=== 第一次續約訂單：{} (6個月) ===", firstRenewalOrderNo);
        
        // 取消第一次續約訂單
        cancelOrderInternal(firstRenewalOrderNo);
        log.info("=== 第一次續約訂單已取消：{} ===", firstRenewalOrderNo);

        // 驗證第一次續約訂單確實被取消
        Orders cancelledOrder = orderService.getOrder(firstRenewalOrderNo);
        assertEquals(OrderStatus.CANCEL.getStatus(), cancelledOrder.getStatus().intValue(), 
            "第一次續約訂單應該已被取消");

        // 3. 建立第二次續約訂單 (12個月)
        renewOrder(context, SubscribeRenewMonth.TWELVE);
        String secondRenewalOrderNo = context.renewalOrderNo;
        log.info("=== 第二次續約訂單：{} (12個月) ===", secondRenewalOrderNo);

        // 處理第二次續約訂單到 BOOKING 狀態
        context.orderNo = secondRenewalOrderNo; // 切換到第二次續約訂單
        approveCreditManually(context);
        setMileageDiscount(context.orderNo); // 設定續約訂單的里程優惠

        // 驗證第二次續約訂單符合條件
        Orders secondRenewalOrder = orderService.getOrder(secondRenewalOrderNo);
        assertEquals(OrderStatus.BOOKING.getStatus(), secondRenewalOrder.getStatus().intValue(), 
            "第二次續約訂單狀態應為 BOOKING");
        assertEquals(12, secondRenewalOrder.getMonth().intValue(), "第二次續約訂單原始租期應為 12 個月");

        String renewalContractNo = secondRenewalOrder.getContractNo();
        Integer renewalContractStage = secondRenewalOrder.getContract().getStage();

        log.info("=== 執行租期異動前狀態 ===");
        log.info("原合約 {}: 有效訂單6個月", originalContractNo);
        log.info("已取消續約 {}: 6個月 (應被忽略)", firstRenewalOrderNo);
        log.info("新續約合約 {} (stage={}): 第二次續約訂單12個月", renewalContractNo, renewalContractStage);

        // 捕獲租期異動前的 EmpMileageDiscount 狀態
        Map<Integer, Integer> mileageDiscountsBefore = captureEmpMileageDiscountsBefore(secondRenewalOrderNo);
        
        // 捕獲租期異動前的 Insurance, Replacement, Dispatch 費用狀態
        Map<String, List<OrderPriceInfo>> preservedFeesBefore = capturePreservedFeesBefore(secondRenewalOrderNo);

        // 4. 執行租期異動：12個月 -> 9個月
        // 這應該導致總租期 6 + 9 = 15 > 12，不可以合併到原合約 (忽略已取消的6個月)
        ModifyRentalPeriodRequest request = new ModifyRentalPeriodRequest();
        request.setNewSubscribeMonth(9);

        log.info("=== 執行租期異動：12個月 -> 9個月 ===");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/modifyRentalPeriod", secondRenewalOrderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 5. 驗證：第二次續約訂單應該合併到原合約（忽略已取消的第一次續約）
        Orders secondRenewalOrderAfter = orderService.getOrder(secondRenewalOrderNo);
        String finalContractNo = secondRenewalOrderAfter.getContractNo();
        Integer finalContractStage = secondRenewalOrderAfter.getContract().getStage();

        log.info("=== 修改後狀態檢查 ===");
        log.info("第二次續約訂單最終合約: {}, 階段: {}", finalContractNo, finalContractStage);

        // 核心驗證：訂單不應該合併到原合約 (因為 6+9=15 > 12)
        if (finalContractStage == 1 && originalContractNo.equals(finalContractNo)) {
            // 如果訂單被合併到原合約，這應該是錯誤的
            log.error("=== Bug 檢查 ===");
            log.error("訂單被錯誤地合併到原合約 {} (stage=1)", finalContractNo);
            log.error("原合約有效租期6個月 + 新租期9個月 = 15個月 > 12個月限制");
            fail("Bug 重現：訂單不應該合併到原合約，因為 6+9=15 > 12個月限制");
        } else {
            // 如果邏輯正確，訂單應該留在續約合約中或新建立的合約中
            log.info("=== 邏輯正確：訂單在合約 {} (stage={}) ===", finalContractNo, finalContractStage);
        }
        assertEquals(9, secondRenewalOrderAfter.getMonth().intValue(), "租期應已更新為9個月");

        // 6. 驗證 EmpMileageDiscount 記錄的正確性
        log.info("=== 驗證租期異動後的 EmpMileageDiscount 狀態 (9個月) ===");
        validateEmpMileageDiscountChange(secondRenewalOrderNo, 9, mileageDiscountsBefore);
        
        // 7. 驗證 Insurance, Replacement, Dispatch 費用保留的正確性
        log.info("=== 驗證租期異動後的 Insurance/Replacement/Dispatch 費用保留狀態 ===");
        validatePreservedFeesChange(secondRenewalOrderNo, preservedFeesBefore);

        log.info("=== 測試通過：已取消續約訂單被正確忽略，租期異動不合併到原合約 ===");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("租期異動符合 12 個月限制 - 應正確合併到原合約 (忽略已取消續約)")
    void testModifyRentalPeriodWithinLimitShouldMergeToOriginalContract() throws Exception {
        // 測試已取消續約訂單的合併情境：
        // 1. 原始訂單 (6個月) 完成出車並還車
        // 2. 第一次續約訂單 (9個月) 被取消
        // 3. 第二次續約訂單 (12個月)，然後修改為 6個月
        // 預期：原合約計算租期時應忽略已取消的第一次續約，6+6=12個月 ≤ 12，可以合併
        OrderTestContext context = new OrderTestContext();

        // 1. 建立原始訂單 (6個月) 並完成出車還車流程
        createOrder(context, 6, true, null, false, CarDefine.CarState.NEW);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 160);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);

        String originalOrderNo = context.orderNo;
        String originalContractNo = context.order.getContractNo();

        log.info("=== 原始訂單完成出車還車：{} (6個月), 合約: {} ===", originalOrderNo, originalContractNo);

        // 2. 建立第一次續約訂單 (9個月) 然後取消
        renewOrder(context, SubscribeRenewMonth.NINE);
        String firstRenewalOrderNo = context.renewalOrderNo;
        log.info("=== 第一次續約訂單：{} (9個月) ===", firstRenewalOrderNo);
        
        // 取消第一次續約訂單
        cancelOrderInternal(firstRenewalOrderNo);
        log.info("=== 第一次續約訂單已取消：{} ===", firstRenewalOrderNo);

        // 驗證第一次續約訂單確實被取消
        Orders cancelledOrder = orderService.getOrder(firstRenewalOrderNo);
        assertEquals(OrderStatus.CANCEL.getStatus(), cancelledOrder.getStatus().intValue(), 
            "第一次續約訂單應該已被取消");

        // 3. 建立第二次續約訂單 (12個月)
        renewOrder(context, SubscribeRenewMonth.TWELVE);
        String secondRenewalOrderNo = context.renewalOrderNo;
        log.info("=== 第二次續約訂單：{} (12個月) ===", secondRenewalOrderNo);

        // 處理第二次續約訂單到 BOOKING 狀態
        context.orderNo = secondRenewalOrderNo; // 切換到第二次續約訂單
        approveCreditManually(context);
        setMileageDiscount(context.orderNo); // 設定續約訂單的里程優惠

        // 驗證第二次續約訂單符合條件
        Orders secondRenewalOrder = orderService.getOrder(secondRenewalOrderNo);
        assertEquals(OrderStatus.BOOKING.getStatus(), secondRenewalOrder.getStatus().intValue(), 
            "第二次續約訂單狀態應為 BOOKING");
        assertEquals(12, secondRenewalOrder.getMonth().intValue(), "第二次續約訂單原始租期應為 12 個月");

        String renewalContractNo = secondRenewalOrder.getContractNo();
        Integer renewalContractStage = secondRenewalOrder.getContract().getStage();

        log.info("=== 執行租期異動前狀態 ===");
        log.info("原合約 {}: 有效訂單6個月", originalContractNo);
        log.info("已取消續約 {}: 9個月 (應被忽略)", firstRenewalOrderNo);
        log.info("新續約合約 {} (stage={}): 第二次續約訂單12個月", renewalContractNo, renewalContractStage);

        // 捕獲租期異動前的 EmpMileageDiscount 狀態
        Map<Integer, Integer> mileageDiscountsBefore = captureEmpMileageDiscountsBefore(secondRenewalOrderNo);
        
        // 捕獲租期異動前的 Insurance, Replacement, Dispatch 費用狀態
        Map<String, List<OrderPriceInfo>> preservedFeesBefore = capturePreservedFeesBefore(secondRenewalOrderNo);

        // 4. 執行租期異動：12個月 -> 6個月
        // 這應該導致總租期 6 + 6 = 12 ≤ 12，可以合併到原合約 (忽略已取消的9個月)
        ModifyRentalPeriodRequest request = new ModifyRentalPeriodRequest();
        request.setNewSubscribeMonth(6);

        log.info("=== 執行租期異動：12個月 -> 6個月 ===");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/modifyRentalPeriod", secondRenewalOrderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 5. 驗證：第二次續約訂單應該合併到原合約（忽略已取消的第一次續約）
        Orders secondRenewalOrderAfter = orderService.getOrder(secondRenewalOrderNo);
        String finalContractNo = secondRenewalOrderAfter.getContractNo();
        Integer finalContractStage = secondRenewalOrderAfter.getContract().getStage();

        log.info("=== 修改後狀態檢查 ===");
        log.info("第二次續約訂單最終合約: {}, 階段: {}", finalContractNo, finalContractStage);

        // 核心驗證：訂單應該合併到原合約 (已取消的第一次續約不應影響計算)
        assertEquals(1, finalContractStage.intValue(), 
            "第二次續約訂單應該合併到原合約 (stage=1)，已取消的第一次續約應被忽略");
        assertEquals(originalContractNo, finalContractNo, 
            "第二次續約訂單應該合併到原合約，已取消的第一次續約不應影響計算");
        assertEquals(6, secondRenewalOrderAfter.getMonth().intValue(), "租期應已更新為6個月");

        // 6. 驗證 EmpMileageDiscount 記錄的正確性
        log.info("=== 驗證租期異動後的 EmpMileageDiscount 狀態 (6個月) ===");
        validateEmpMileageDiscountChange(secondRenewalOrderNo, 6, mileageDiscountsBefore);
        
        // 7. 驗證 Insurance, Replacement, Dispatch 費用保留的正確性
        log.info("=== 驗證租期異動後的 Insurance/Replacement/Dispatch 費用保留狀態 ===");
        validatePreservedFeesChange(secondRenewalOrderNo, preservedFeesBefore);

        log.info("=== 測試通過：已取消續約訂單被正確忽略，第二次續約正確合併到原合約 ===");
        
        // 額外驗證：檢查第一次續約訂單所在的新合約是否仍然存在
        Orders firstRenewalOrderAfter = orderService.getOrder(firstRenewalOrderNo);
        Contract firstRenewalContract = firstRenewalOrderAfter.getContract();
        assertNotNull(firstRenewalContract, "第一次續約訂單的合約應該仍然存在（即使訂單已取消）");
        
        log.info("=== 驗證通過：已取消續約訂單的合約仍然保留，合約編號: {}, 階段: {} ===", 
            firstRenewalContract.getContractNo(), firstRenewalContract.getStage());
    }

    /**
     * 取消訂單的輔助方法
     */
    private void cancelOrderInternal(String orderNo) throws Exception {
        CancelRequest cancelRequest = new CancelRequest();
        cancelRequest.setForceRemitRefund(false);
        cancelRequest.setRemark("測試用取消續約訂單");

        mockMvc.perform(delete("/internal/subscribe/{orderNo}", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(cancelRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    /**
     * 驗證續約訂單初始狀態的新車折價邏輯
     */
    private void validateInitialNewCarDiscountForRenewalOrder(String orderNo, int rentalPeriod) {
        Orders order = orderService.getOrder(orderNo);
        Cars car = carsService.findByPlateNo(order.getPlateNo());
        MainContract mainContract = order.getContract().getMainContract();

        // 計算主合約總月數
        int mainContractDiffMonth = DateUtil.calculateDiffMonth(Optional.ofNullable(mainContract.getStartDate()).orElseGet(mainContract::getExpectStartDate), order.getExpectEndDate());
        log.info("續約訂單初始狀態 - 主合約總月數: {}, 訂單租期: {}", mainContractDiffMonth, rentalPeriod);

        // 驗證續約訂單應該符合新車折價條件
        assertSame(CarDefine.CarState.NEW, car.getCarState(), "應為新車");
        assertFalse(order.getIsNewOrder(), "應為續約訂單");
        assertTrue(mainContractDiffMonth >= 12, "主合約總月數應 >= 12個月");

        // 驗證每個stage都有新車折價
        PriceInfoWrapper priceInfo = priceInfoService.getPriceInfoWrapper(orderNo);
        int expectedStages = (int) Math.ceil(rentalPeriod / 3.0d);
        int monthDiscountUnit = Math.min(mainContractDiffMonth / 12, 2);

        for (int stage = 1; stage <= expectedStages; stage++) {
            int monthsInStage = (stage == expectedStages) ? getMonthLastStage(rentalPeriod) : 3;
            int expectedDiscountAmount = monthsInStage * monthDiscountUnit * 1000;

            int finalStage = stage;
            Optional<OrderPriceInfo> discountFee = priceInfo.getList().stream()
                .filter(p -> p.getCategory() == MonthlyFee
                    && p.getType() == Discount.getCode()
                    && p.getStage() == finalStage)
                .findFirst();

            assertTrue(discountFee.isPresent(),
                String.format("續約訂單初始狀態應存在新車折價：stage=%d", stage));
            assertEquals(expectedDiscountAmount, discountFee.get().getAmount().intValue(),
                String.format("續約訂單初始狀態新車折價金額：stage=%d, 期望=%d, 實際=%d",
                    stage, expectedDiscountAmount, discountFee.get().getAmount()));

            log.info("續約訂單初始狀態驗證通過 - 新車折價：stage={}, 月數={}, 折價單位={}, 折價金額={}",
                stage, monthsInStage, monthDiscountUnit, discountFee.get().getAmount());
        }
    }

    private void validatePriceInfoChange(String orderNo, int newRentalPeriod, List<OrderPriceInfo> feesBeforeChange) {
        PriceInfoWrapper priceInfoAfterChange = priceInfoService.getPriceInfoWrapper(orderNo);

        // 1. 分離一次性費用和分期費用
        List<OrderPriceInfo> oneTimeFeesBefore = feesBeforeChange.stream()
            .filter(p -> p.getCategory() == Merchandise)
            .collect(Collectors.toList());
        List<OrderPriceInfo> oneTimeFeesAfter = priceInfoAfterChange.getList().stream()
            .filter(p -> p.getCategory() == Merchandise)
            .collect(Collectors.toList());

        // 2. 驗證一次性費用不變
        validateOneTimeFeesUnchanged(oneTimeFeesBefore, oneTimeFeesAfter);

        // 3. 驗證分期費用結構是否符合新租期
        List<OrderPriceInfo> periodicFeesAfter = priceInfoAfterChange.getList().stream()
            .filter(p -> p.getCategory() != Merchandise && p.getType() == Pay.getCode())
            .collect(Collectors.toList());

        Map<PriceInfoDefinition.PriceInfoCategory, List<OrderPriceInfo>> feesByCategory = periodicFeesAfter.stream()
            .collect(Collectors.groupingBy(OrderPriceInfo::getCategory));

        int expectedStages = (int) Math.ceil(newRentalPeriod / 3.0d);

        assertFalse(feesByCategory.isEmpty(), "應至少存在一種類型的分期費用");

        feesByCategory.forEach((category, fees) -> {
            assertEquals(expectedStages, fees.size(), "費用類別 [" + category + "] 的 stage 數量應為 " + expectedStages);

            for (int i = 0; i < expectedStages; i++) {
                int currentStage = i + 1;
                int expectedMonthsInStage = (currentStage == expectedStages) ? getMonthLastStage(newRentalPeriod) : 3;

                OrderPriceInfo fee = fees.stream().filter(f -> f.getStage() == currentStage).findFirst()
                    .orElseThrow(() -> new AssertionError("找不到費用類別 [" + category + "] 的 stage " + currentStage));

                assertNotNull(fee.getInfoDetail(), "費用類別 [" + category + "] stage " + currentStage + " 的 infoDetail 不應為 null");
                if (MonthlyFee == fee.getCategory()) {
                    assertEquals(expectedMonthsInStage, fee.getInfoDetail().getMonth(), "費用類別 [" + category + "] stage " + currentStage + " 的 month 數應為 " + expectedMonthsInStage);
                }
            }
        });

        // 4. 驗證不應存在多餘的 stage
        assertTrue(periodicFeesAfter.stream().noneMatch(p -> p.getStage() > expectedStages), "不應存在 stage > " + expectedStages + " 的分期費用");

        // 5. 驗證新車滿一年折價一千邏輯
        validateNewCarOneYearDiscountAfterModification(orderNo, newRentalPeriod, priceInfoAfterChange);
    }

    /**
     * 驗證一次性費用未改變
     */
    private void validateOneTimeFeesUnchanged(List<OrderPriceInfo> feesBefore, List<OrderPriceInfo> feesAfter) {
        assertEquals(feesBefore.size(), feesAfter.size(), "一次性費用數量不應改變");

        // 使用 ID 和關鍵屬性進行比較，避免觸發 toString()
        for (OrderPriceInfo feeBefore : feesBefore) {
            boolean found = feesAfter.stream().anyMatch(feeAfter ->
                Objects.equals(feeBefore.getId(), feeAfter.getId()) &&
                    Objects.equals(feeBefore.getCategory(), feeAfter.getCategory()) &&
                    Objects.equals(feeBefore.getAmount(), feeAfter.getAmount()) &&
                    Objects.equals(feeBefore.getType(), feeAfter.getType()) &&
                    Objects.equals(feeBefore.getStage(), feeAfter.getStage()) &&
                    Objects.equals(feeBefore.getSkuCode(), feeAfter.getSkuCode())
            );
            assertTrue(found, "找不到匹配的一次性費用: category=" + feeBefore.getCategory() +
                ", amount=" + feeBefore.getAmount() + ", id=" + feeBefore.getId());
        }
    }

    /**
     * 驗證租期異動後的新車滿一年折價邏輯
     *
     * 關鍵邏輯說明：
     * 1. 原始訂單6個月 + 續約訂單6個月 = 主合約12個月（monthDiscountUnit = 1）
     * 2. 租期異動到9個月：原始6個月 + 續約9個月 = 主合約15個月（monthDiscountUnit = 1）
     * 3. 租期異動到6個月：原始6個月 + 續約6個月 = 主合約12個月（monthDiscountUnit = 1）
     * 4. 無論如何異動，monthDiscountUnit 都是1，所以每個stage都應該有新車折價
     */
    private void validateNewCarOneYearDiscountAfterModification(String orderNo, int newRentalPeriod, PriceInfoWrapper priceInfoAfterChange) {
        Orders order = orderService.getOrder(orderNo);
        Cars car = carsService.findByPlateNo(order.getPlateNo());

        // 只有新車的續約訂單才需要驗證此邏輯
        if (car.getCarState() != CarDefine.CarState.NEW || order.getIsNewOrder()) {
            log.info("非新車續約訂單，跳過新車滿一年折價驗證：車籍狀態={}, isNewOrder={}", car.getCarState(), order.getIsNewOrder());
            return;
        }

        // 計算主合約總月數（主合約開始到此訂單結束的時間）
        MainContract mainContract = order.getContract().getMainContract();
        int mainContractDiffMonth = DateUtil.calculateDiffMonth(Optional.ofNullable(mainContract.getStartDate()).orElseGet(mainContract::getExpectStartDate), order.getExpectEndDate());

        log.info("驗證租期異動後新車滿一年折價：訂單={}, 主合約總月數={}, 新租期={}", orderNo, mainContractDiffMonth, newRentalPeriod);

        // 根據業務邏輯，續約訂單的主合約總月數必然 >= 12
        // 因為原始訂單至少6個月，續約訂單至少6個月
        assertTrue(mainContractDiffMonth >= 12,
            String.format("續約訂單的主合約總月數應該 >= 12個月，實際: %d", mainContractDiffMonth));

        // 計算折價單位數（mainContractDiffMonth / 12，最多2個單位）
        int monthDiscountUnit = Math.min(mainContractDiffMonth / 12, 2);

        // 根據測試場景分析：
        // - 6個月時：原始6 + 續約6 = 12個月，monthDiscountUnit = 1
        // - 9個月時：原始6 + 續約9 = 15個月，monthDiscountUnit = 1
        // - 回到6個月：原始6 + 續約6 = 12個月，monthDiscountUnit = 1
        assertEquals(1, monthDiscountUnit,
            String.format("在當前測試場景下，折價單位應為1，實際: %d（主合約總月數: %d）", monthDiscountUnit, mainContractDiffMonth));

        // 驗證每個 stage 都有對應的折價費用
        int expectedStages = (int) Math.ceil(newRentalPeriod / 3.0d);

        for (int stage = 1; stage <= expectedStages; stage++) {
            int monthsInStage = (stage == expectedStages) ? getMonthLastStage(newRentalPeriod) : 3;
            int expectedDiscountAmount = monthsInStage * monthDiscountUnit * 1000;

            // 查找該 stage 的折價費用
            int finalStage = stage;
            Optional<OrderPriceInfo> discountFee = priceInfoAfterChange.getList().stream()
                .filter(p -> p.getCategory() == MonthlyFee
                    && p.getType() == Discount.getCode()
                    && p.getStage() == finalStage)
                .findFirst();

            assertTrue(discountFee.isPresent(),
                String.format("租期異動後應存在新車滿一年折價費用：stage=%d, 預期折價金額=%d", stage, expectedDiscountAmount));

            assertEquals(expectedDiscountAmount, discountFee.get().getAmount().intValue(),
                String.format("租期異動後新車滿一年折價金額不正確：stage=%d, 期數月數=%d, 折價單位=%d, 預期=%d, 實際=%d",
                    stage, monthsInStage, monthDiscountUnit, expectedDiscountAmount, discountFee.get().getAmount()));

            log.info("驗證通過 - 租期異動後新車滿一年折價：stage={}, 月數={}, 折價單位={}, 折價金額={}",
                stage, monthsInStage, monthDiscountUnit, discountFee.get().getAmount());
        }

        // 額外驗證：確保沒有多餘的折價費用
        List<OrderPriceInfo> allDiscountFees = priceInfoAfterChange.getList().stream()
            .filter(p -> p.getCategory() == MonthlyFee && p.getType() == Discount.getCode())
            .collect(Collectors.toList());

        assertEquals(expectedStages, allDiscountFees.size(),
            String.format("折價費用的數量應該等於期數：預期=%d, 實際=%d", expectedStages, allDiscountFees.size()));
    }

    @Test
    void shouldCreateContractAndExportCsv() throws Exception {
        // 先將車輛加入收藏清單
        String plateNo = "RDJ-7801";
        Integer acctId = 33456914;
        CarWishlistRequest wishlistRequest = new CarWishlistRequest();
        wishlistRequest.setPlateNo(plateNo);

        mockMvc.perform(post("/subscribe/carWishlist")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, acctId)
                .content(objectMapper.writeValueAsString(wishlistRequest)))
            .andExpect(status().isOk());

        // 建立合約
        String createContractJson = "{\n" +
            "    \"plateNo\": \"" + plateNo + "\",\n" + // 車籍已補上車體描述
            "    \"carLevel\": 9,\n" +
            "    \"departStationCode\": \"201\",\n" +
            "    \"returnStationCode\": \"201\",\n" +
            "    \"expectStartDate\": " + getExpectStartDate() + ",\n" +
            "    \"month\": 12,\n" +
            "    \"disclaimer\": false,\n" +
            "    \"premium\": false,\n" +
            "    \"invoice\": {\n" +
            "        \"category\": 5,\n" +
            "        \"id\": \"A193477449\",\n" +
            "        \"title\": \"李志宏\",\n" +
            "        \"type\": 2\n" +
            "    },\n" +
            "    \"custSource\": 2,\n" +
            "    \"custRemark\": \"客戶備註測試\",\n" +
            "    \"acctId\": " + acctId + ",\n" +
            "    \"merchList\": [\n" +
            "        {\n" +
            "            \"skuCode\": \"wipe001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": 360\n" + // 加入自訂單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"sprayCleaner001\",\n" +
            "            \"quantity\": 2,\n" +
            "            \"actualUnitPrice\": null\n" + // 使用商品原始單價
            "        },\n" +
            "        {\n" +
            "            \"skuCode\": \"voucher001\",\n" +
            "            \"quantity\": 1,\n" +
            "            \"actualUnitPrice\": 19000\n" + // 加入自訂單價
            "        }\n" +
            "    ]\n" +
            "}";

        // 執行建立合約
        MvcResult createResult = mockMvc.perform(post("/internal/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(createContractJson))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andExpect(jsonPath("$.data.mainContract.plateNo").value(plateNo))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem("客戶備註測試")))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("原始收訂車輛"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("有加入用戶收藏清單"))))
            .andExpect(jsonPath("$.data.orders[0].remarks[*].content", hasItem(containsString("車體描述"))))
            .andReturn();

        // 準備查詢 CSV 的請求
        JsonNode rootNode = objectMapper.readTree(createResult.getResponse().getContentAsString());
        JsonNode orderNode = rootNode.get("data").get("orders");
        String orderNo = orderNode.get(0).get("orderNo").asText();
        OrdersCriteria queryRequest = new OrdersCriteria();
        queryRequest.setOrderNo(orderNo);

        // 執行 CSV 匯出
        MvcResult csvResult = mockMvc.perform(get("/internal/subscribe/order/csv")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(queryRequest)))
            .andExpect(status().isOk())
            .andExpect(header().string("Content-Disposition",
                matchesPattern("attachment; filename=SUB_order_\\{\\d{8}\\}.csv")))
            .andReturn();

        // 驗證 CSV 內容
        byte[] content = csvResult.getResponse().getContentAsByteArray();
        String csvContent = new String(content, Charset.forName("big5"));
        BufferedReader reader = new BufferedReader(new StringReader(csvContent));

        // 讀取標題行
        String headerLine = reader.readLine();
        assertTrue(headerLine.contains("加購商品明細"));
        String[] headers = headerLine.split(",");
        int remarkColumnIndex = -1;
        for (int i = 0; i < headers.length; i++) {
            if (headers[i].trim().equals("訂單備註")) {
                remarkColumnIndex = i;
                break;
            }
        }
        if (remarkColumnIndex == -1) {
            fail("CSV header does not contain '訂單備註' column");
        }

        // 讀取資料行並驗證加購商品明細和收藏清單備註
        String dataLine;
        boolean foundMerchandise = false;
        boolean foundWishlistRemark = false;
        List<LocalDateTime> remarkTimestamps = new ArrayList<>();

        while ((dataLine = reader.readLine()) != null) {
            String[] cells = dataLine.split(",");
            if (!foundMerchandise && dataLine.contains("懶人必備專業車用濕巾($360) x 2;玻璃無痕速淨噴霧($388) x 2;G'ZOX石英鍍膜禮券($19,000) x 1")) {
                foundMerchandise = true;
            }

            if (cells.length > remarkColumnIndex) {
                String remarkCell = cells[remarkColumnIndex];
                if (StringUtils.isNotBlank(remarkCell)) {
                    // 檢查是否包含收藏清單相關備註
                    if (remarkCell.contains("原始收訂車輛") && remarkCell.contains("有加入用戶收藏清單")) {
                        foundWishlistRemark = true;
                    }

                    // 正則表達式驗證格式: yyyy-mm-dd HH:MM {備註者名稱}：{備註內容}
                    String expectedRemarkPattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2} .*：.*";
                    assertTrue(remarkCell.matches(expectedRemarkPattern), "CSV 訂單備註 should match expected format: yyyy-mm-dd HH:MM {備註者名稱}：{備註內容}");

                    // 解析時間並存入列表
                    String timestampStr = remarkCell.substring(0, 16); // 取 yyyy-MM-dd HH:mm
                    LocalDateTime timestamp = LocalDateTime.parse(timestampStr, DASH_FORMATTER);
                    remarkTimestamps.add(timestamp);
                }
            } else {
                fail("Data line does not have enough columns to contain '訂單備註'");
            }
        }

        assertTrue(foundMerchandise, "CSV should contain correct merchandise details");
        assertTrue(foundWishlistRemark, "CSV should contain wishlist remark: '有加入用戶收藏清單'");

        // 驗證備註排序是否為降序（最新的在前面）
        for (int i = 1; i < remarkTimestamps.size(); i++) {
            assertTrue(remarkTimestamps.get(i - 1).isAfter(remarkTimestamps.get(i)) ||
                    remarkTimestamps.get(i - 1).isEqual(remarkTimestamps.get(i)),
                "備註的 createTime 應該是降序排列");
        }
    }

    @Test
    void msgForCustomer_Success_CustomerCanReadIt() throws Exception {
        // Arrange
        String orderNo = "M202412301946";
        String msgForCustomer = "This is a test message for customer.";
        String updateMsgForCustomerRequestBody = String.format("{\"content\":\"%s\"}", msgForCustomer);

        // Act: 執行更新訊息的 API
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/msgForCustomer", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(updateMsgForCustomerRequestBody))
            .andExpect(status().isOk());

        // Act: 執行客戶查詢訂單資訊的 API
        MvcResult result = mockMvc.perform(get("/subscribe/mainContract?contractStatuses=CREATE,GOING&detail=true&skip=0&limit=10")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914))
            .andExpect(status().isOk())
            .andReturn();

        // Assert: 驗證客戶是否能讀取到正確的訊息
        String responseJson = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseJson);
        JsonNode dataNode = rootNode.path("data");
        JsonNode listNode = dataNode.path("list");

        boolean found = false;
        for (JsonNode mainContractNode : listNode) {
            JsonNode contractsNode = mainContractNode.path("contracts");
            for (JsonNode contractNode : contractsNode) {
                JsonNode orderResponsesNode = contractNode.path("orderResponses");
                for (JsonNode orderResponseNode : orderResponsesNode) {
                    if (orderResponseNode.path("orderNo").asText().equals(orderNo)) {
                        JsonNode msgForCustomerNode = mainContractNode.path("msgForCust");
                        if (msgForCustomerNode.isArray() && !msgForCustomerNode.isEmpty()) {
                            assertEquals(msgForCustomer, msgForCustomerNode.get(0).path("content").asText());
                            found = true;
                            break;
                        }
                    }
                }
                if (found) {
                    break;
                }
            }
            if (found) {
                break;
            }
        }
        assertTrue(found, "Order with specified orderNo not found in the response.");
    }

    @Test
    void withdrawMsgForCustomer_Success_CustomerCanNotReadIt() throws Exception {
        // Arrange
        String orderNo = "M202412301946";

        // Act: 執行清除訊息的 API
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/withdrawMsgForCustomer", orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // Act: 執行客戶查詢訂單資訊的 API
        MvcResult result = mockMvc.perform(get("/subscribe/mainContract?contractStatuses=CREATE,GOING&detail=true&skip=0&limit=10")
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914))
            .andExpect(status().isOk())
            .andReturn();

        // Assert: 驗證客戶是否無法讀取到訊息
        String responseJson = result.getResponse().getContentAsString();
        JsonNode rootNode = objectMapper.readTree(responseJson);
        JsonNode dataNode = rootNode.path("data");
        JsonNode listNode = dataNode.path("list");

        boolean found = false;
        for (JsonNode mainContractNode : listNode) {
            JsonNode contractsNode = mainContractNode.path("contracts");
            for (JsonNode contractNode : contractsNode) {
                JsonNode orderResponsesNode = contractNode.path("orderResponses");
                for (JsonNode orderResponseNode : orderResponsesNode) {
                    if (orderResponseNode.path("orderNo").asText().equals(orderNo)) {
                        JsonNode msgForCustomerNode = mainContractNode.path("msgForCust");
                        if (msgForCustomerNode.isMissingNode() || msgForCustomerNode.isNull() || (msgForCustomerNode.isArray() && msgForCustomerNode.isEmpty())) {
                            found = true;
                        } else if (msgForCustomerNode.isArray()) {
                            for (JsonNode messageNode : msgForCustomerNode) {
                                assertTrue(messageNode.path("content").isMissingNode() || messageNode.path("content").isNull(), "Customer message content should be null or missing.");
                            }
                            found = true;
                        }
                        break;
                    }
                }
                if (found) {
                    break;
                }
            }
            if (found) {
                break;
            }
        }
        assertTrue(found, "Order with specified orderNo not found in the response.");
    }

    private MvcResult getPayResult(int amount, String orderNo, boolean isSecurityDeposit, PayFor payFor, String payPath, String mainContractNo) throws Exception {
        PayAuthRequest payAuthRequest = new PayAuthRequest();
        payAuthRequest.setAmount(amount);
        payAuthRequest.setCardKey(CARD_KEY); // 從 `int-payment`.payment_cards 取得 (memberId = '33456914')
        payAuthRequest.setBusinessType("SUBV2");
        payAuthRequest.setDefaultCard("Y");
        payAuthRequest.setPayFrom(PayFrom.Internet.getCode());
        payAuthRequest.setPaymentType(PaymentType.CreditCard.getCode());
        PayAuthResultUrl payAuthResultUrl = new PayAuthResultUrl();
        payAuthResultUrl.setFrontendRedirectUrl(String.format("%s/subscription/payment_pay?orderNo=%s&isSecurityDeposit=%b", AppProperties.getOfficialHost(), orderNo, isSecurityDeposit));
        payAuthRequest.setResultUrl(payAuthResultUrl);
        payAuthRequest.setType(PayAuthRequest.PayType.card);
        payAuthRequest.setPayFor(payFor.name());

        return mockMvc.perform(post("/subscribe/" + payPath, payFor == PayFor.SecurityDeposit ? mainContractNo : orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_ACCT, 33456914)
                .header(CarPlusConstant.AUTH_HEADER_SYSTEM_KIND, CustSource.WEB.getSystemKind())
                .header("X-CARPLUS-ROUTE", "be-int-gateway")
                .header(CarPlusConstant.AUTH_HEADER_PLATFORM, CustSource.WEB.name())
                .header("X-ROLE", "MEMBER")
                .content(objectMapper.writeValueAsString(payAuthRequest)))
            .andExpect(jsonPath("$.statusCode").value(0))
            .andReturn();
    }

    private void validateOtp(String paymentUrl) {
        // TapPay otp validate
        RestTemplate restTemplate = new RestTemplate();

        // 設置請求頭
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 設置請求參數
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("pin", "1234567");
        map.add("urlId", paymentUrl.substring(paymentUrl.lastIndexOf("/") + 1));

        // 創建請求實體
        HttpEntity<MultiValueMap<String, String>> otpValidateRequest = new HttpEntity<>(map, headers);

        // 發送請求
        ResponseEntity<String> response = restTemplate.postForEntity(
            "https://sandbox-redirect.tappaysdk.com/redirect/three-domain-secure/otp-validate",
            otpValidateRequest,
            String.class
        );

        // 驗證響應
        assertTrue(response.getStatusCode().is3xxRedirection());
        System.out.println("Response body: " + response.getBody());
    }

    private static class OrderTestContext {
        Cars idleCar;
        InternalContractCreateReq contractCreateReq;
        MvcResult createContractResult;
        String orderNo;
        Orders order;
        String mainContractNo;
        MainContract mainContract;
        int realSecurityDepositAmount;
        JsonNode payForSecurityDepositDataNode;
        String paySecurityDepositUrl;
        String originalLrentalContractNo;
        String recreatedLrentalContractNo;
        CarDepartRequest departRequest;
        CarBaseInfoSearchResponse crsCar;
        Cars car;
        CarDropOffRequest carDropOffRequest;
        String renewalOrderNo;
        String renewalOrderLrentalContractNo;
        // 優惠券相關屬性
        String couponId;
        String couponEventCode;
        String couponSequenceId;
        List<com.carplus.subscribe.model.coupon.Coupon> availableCoupons;
        List<OrderPriceInfo> couponPriceInfos;
        int originalDiscountableAmount;
        int appliedCouponDiscount;
    }

    void createOrder(OrderTestContext context,
                     Integer acctId,
                     int month,
                     boolean needAutoCredit,
                     String autoCreditBypassReason,
                     boolean enableBuChangeValidation,
                     CarDefine.CarState carState,
                     CarPlusCode expectedCarPlusCode) throws Exception {
        List<Cars> idleCars = carsService.getIdleCar();
        assertFalse(idleCars.isEmpty(), "There should be idle cars");

        context.idleCar = null;

        // 批次處理 CRS 查詢，使用固定的批次大小以避免一次查詢過多車輛
        final int batchSize = 50; // 設定批次大小為 50

        for (int i = 0; i < idleCars.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, idleCars.size());
            List<Cars> batch = idleCars.subList(i, endIndex);

            // 收集該批次的車牌號碼
            List<String> plateNos = batch.stream()
                .map(Cars::getPlateNo)
                .collect(Collectors.toList());

            // 批次查詢 CRS 車輛資訊
            Map<String, CarBaseInfoSearchResponse> carBaseInfoMap = crsService.getCars(plateNos);

            // 檢查該批次中是否有符合條件的車輛
            for (Cars car : batch) {
                if (carState != null && carState != car.getCarState()) {
                    continue;
                }
                CarBaseInfoSearchResponse carBaseInfo = carBaseInfoMap.get(car.getPlateNo());

                if (carBaseInfo == null) {
                    continue;
                }

                boolean isLicenseValid = CRS.LicenseStatus.BUY.getCode().equals(carBaseInfo.getCarLicense().getLicenseStatus());
                boolean isBuValid = BuIdEnum.subscribe.getCode().equals(carBaseInfo.getBuId());
                boolean isLaunchedValid = Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(car.getLaunched());

                if (!isLicenseValid || !isBuValid || !isLaunchedValid) {
                    continue;
                }

                // 如果需要 CRS 驗證，進行額外檢查
                if (enableBuChangeValidation) {
                    if (car.getBuChangeMasterId() == null) {
                        continue;
                    }
                    try {
                        crsService.getChangeBuInfos(car.getBuChangeMasterId());
                    } catch (SubscribeException e) {
                        continue;
                    }
                }

                context.idleCar = car;
                break;
            }

            // 如果找到合適的車輛，跳出外層迴圈
            if (context.idleCar != null) {
                break;
            }
        }

        if (context.idleCar == null) {
            throw new RuntimeException("No valid idle cars found");
        }

        context.contractCreateReq = new InternalContractCreateReq();
        context.contractCreateReq.setPlateNo(context.idleCar.getPlateNo());
        context.contractCreateReq.setCarLevel(context.idleCar.getSubscribeLevel());
        context.contractCreateReq.setDepartStationCode("41"); // 模擬產生調度費用
        context.contractCreateReq.setReturnStationCode("41"); // 模擬產生調度費用
        context.contractCreateReq.setExpectStartDate(Instant.ofEpochMilli(getExpectStartDate()));
        context.contractCreateReq.setMonth(month);
        context.contractCreateReq.setDisclaimer(false);
        context.contractCreateReq.setPremium(false);

        Invoice invoice = new Invoice();
        invoice.setCategory(5);
        invoice.setId("A193477449");
        invoice.setTitle("李志宏");
        invoice.setType(2);
        context.contractCreateReq.setInvoice(invoice);

        context.contractCreateReq.setCustSource(2);
        context.contractCreateReq.setOrderPlatform(OrderPlatform.CASHIER);
        context.contractCreateReq.setAcctId(acctId);

        List<MerchandiseReq> merchList = new ArrayList<>();
        MerchandiseReq merch1 = new MerchandiseReq();
        merch1.setSkuCode("wipe001");
        merch1.setQuantity(2);
        merch1.setActualUnitPrice(38);
        merchList.add(merch1);

        MerchandiseReq merch2 = new MerchandiseReq();
        merch2.setSkuCode("voucher001");
        merch2.setQuantity(1);
        merch2.setActualUnitPrice(19000);
        merchList.add(merch2);

        context.contractCreateReq.setMerchList(merchList);

        context.contractCreateReq.setNeedAutoCredit(needAutoCredit);
        context.contractCreateReq.setAutoCreditBypassReason(autoCreditBypassReason);

        context.createContractResult = mockMvc.perform(post("/internal/subscribe/contract")
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(context.contractCreateReq)))
            .andExpect(jsonPath("$.statusCode").value(expectedCarPlusCode.getCode()))
            .andReturn();

        if (CarPlusCode.SUCCESS == expectedCarPlusCode) {
            JsonNode dataNode = objectMapper.readTree(context.createContractResult.getResponse().getContentAsString()).get("data");
            JsonNode ordersNode = dataNode.get("orders");
            JsonNode orderNode = ordersNode.get(0);
            assert !orderNode.get("orderPlatform").isNull();
            context.orderNo = orderNode.get("orderNo").asText();
            context.mainContractNo = dataNode.get("mainContractNo").asText();
        } else {
            Exception resolvedException = context.createContractResult.getResolvedException();
            if (!(resolvedException instanceof SubscribeException)) {
                fail("Expected SubscribeException, but got " + Objects.requireNonNull(resolvedException).getClass().getName());
            }
            throw resolvedException;
        }
    }

    void createOrder(OrderTestContext context, int month, boolean needAutoCredit, String autoCreditBypassReason, boolean enableBuChangeValidation, CarDefine.CarState carState) throws Exception {
        createOrder(context, 33456914, month, needAutoCredit, autoCreditBypassReason, enableBuChangeValidation, carState, CarPlusCode.SUCCESS);
    }

    void approveCreditManually(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        String manualCreditApprovalRemark = "授信通過原因測試";
        CreditApprovalRequest creditApprovalRequest = new CreditApprovalRequest(manualCreditApprovalRemark);
        context.order = orderService.approveCredit(context.orderNo, MEMBER_ID, creditApprovalRequest);
        // 提取具體值進行比較，避免 JUnit 錯誤訊息觸發 toString()
        CreditRemarkType actualCreditRemarkType = context.order.getCreditInfo().getManualCreditInfo().getCreditRemarkType();
        String actualManualCreditRemark = context.order.getCreditInfo().getManualCreditInfo().getManualCreditRemark();

        assert CreditRemarkType.MANUAL_PASS == actualCreditRemarkType;
        assertEquals(manualCreditApprovalRemark, actualManualCreditRemark);
        Thread.sleep(1000);
    }

    private void setMileageDiscount(String orderNo) throws Exception {
        assertNotNull(orderNo, "orderNo should not be null"); // 確保 orderNo 已被初始化
        // 設定里程優惠 patch /internal/subscribe/{orderNo}/mileageDiscount
        MileageDiscountRequest mileageDiscountRequest = new MileageDiscountRequest();
        Map<Integer, EmpMileageDiscount> mileageDiscounts = new HashMap<>();
        mileageDiscounts.put(1, new EmpMileageDiscount(100, "testMileageDiscount"));
        mileageDiscounts.put(2, new EmpMileageDiscount(200, "testMileageDiscount"));
        mileageDiscounts.put(3, new EmpMileageDiscount(300, "testMileageDiscount"));
        mileageDiscounts.put(4, new EmpMileageDiscount(400, "testMileageDiscount"));
        mileageDiscountRequest.setMileageDiscounts(mileageDiscounts);
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/mileageDiscount", orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(mileageDiscountRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    void payForSecurityDeposit(OrderTestContext context) throws Exception {
        assertNotNull(context.createContractResult, "createContractResult in context should not be null"); // 確保 createContractResult 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化

        // 建立一個 mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        JsonNode dataNode = objectMapper.readTree(context.createContractResult.getResponse().getContentAsString()).get("data");
        context.realSecurityDepositAmount = dataNode.get("mainContract").get("originalPriceInfo").get("securityDepositInfo").get("realSecurityDeposit").asInt();
        MvcResult payForSecurityDepositResult = getPayResult(context.realSecurityDepositAmount, context.order.getOrderNo(), true, PayFor.SecurityDeposit, "{mainContractNo}/payForSecurityDeposit", context.mainContractNo);

        context.payForSecurityDepositDataNode = objectMapper.readTree(payForSecurityDepositResult.getResponse().getContentAsString()).get("data");
        context.paySecurityDepositUrl = context.payForSecurityDepositDataNode.get("paymentUrl").asText();

        // 模擬支付 OTP 驗證
        Thread.sleep(1000);
        validateOtp(context.paySecurityDepositUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(context.payForSecurityDepositDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");
        List<Integer> orderPriceInfoIdsToPay = objectMapper.readValue(paymentInfos.get(0).getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();

        // Local 再 Consume 一次 PaymentQueue
        PaymentInfo securityDepositPayment = paymentInfos.get(0);
        PaymentQueue queue = new PaymentQueue();
        BeanUtils.copyProperties(securityDepositPayment, queue);
        paymentListener.subscribeReceive(queue, 0, null);
        Thread.sleep(2000);

        // 確認保證金費用付款完成
        OrderPriceInfo securityDepositPriceInfo = priceInfoService.getPriceInfoWrapper(context.orderNo).toFilter()
            .category(SecurityDeposit).type(Pay).paid().add(orderPriceInfo -> orderPriceInfoIdsToPay.contains(orderPriceInfo.getId()) && orderPriceInfo.getReceivedAmount() > 0).collect()
            .stream().min(Comparator.comparing(OrderPriceInfo::getStage)).orElseThrow(() -> new SubscribeException(SECURITY_DEPOSIT_PRICE_INFO_NOT_FOUND));
        assertTrue(securityDepositPriceInfo.isPaid(), "保證金應為已付款");

        // 檢查訂單是否包含汽車用品項目
        if (context.contractCreateReq != null && context.contractCreateReq.getMerchList() != null && !context.contractCreateReq.getMerchList().isEmpty()) {
            // 驗證 OrderBookingEvent 是否被發佈
            ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
            verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());

            List<OrderBookingEvent> bookingEvents = eventCaptor.getAllValues().stream()
                .filter(OrderBookingEvent.class::isInstance)
                .map(OrderBookingEvent.class::cast)
                .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
                .collect(Collectors.toList());

            assertFalse(bookingEvents.isEmpty(), "應至少有一個 OrderBookingEvent 被發佈");

            OrderBookingEvent event = bookingEvents.get(0);
            assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試訂單編號相符");
            assertEquals(context.order.getMemberId(), event.getOperator(), "事件中的 operator 應與測試中使用的 memberId 相符");
        }
    }

    void payForDepartFeeAndCheckOut(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        // 模擬門市折扣審核通過後
        OrderPriceInfo merchandiseOrderPriceInfo = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory() == Merchandise)
            .findAny()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo discountOpi = priceInfoService.generateNegativeOrderPriceInfo(merchandiseOrderPriceInfo, merchandiseOrderPriceInfo.getAmount() / 10, EmpDiscount);
        discountOpi.setUid("testUid");
        PriceInfoDetail infoDetail = new PriceInfoDetail();
        infoDetail.setIsAgree(true);
        discountOpi.setInfoDetail(infoDetail);
        discountOpi.setReceivedAmount(discountOpi.getAmount());
        priceInfoService.addOrUpdate(discountOpi);

        // 查詢繳完保證金後開放付款的應付費用
        List<OrderPriceInfo> unpaidPriceInfos = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid())
            .collect(Collectors.toList());
        
        int unpaidAmount = unpaidPriceInfos.stream()
            .map(PriceInfoInterface::getActualPrice)
            .reduce(Integer::sum)
            .orElse(0);
        unpaidAmount += discountOpi.getActualPrice();
        
        // 檢查是否包含優惠券折扣項目 - 如果有，也需要包含在付款金額中
        List<OrderPriceInfo> couponDiscounts = priceInfoService.getPriceInfosByOrder(context.orderNo, CouponDiscount, Discount);
        if (!couponDiscounts.isEmpty()) {
            int couponDiscountAmount = couponDiscounts.stream()
                .filter(priceInfo -> !priceInfo.isPaid())
                .mapToInt(PriceInfoInterface::getActualPrice)
                .sum();
            unpaidAmount += couponDiscountAmount;
            System.out.println("包含優惠券折扣金額: " + couponDiscountAmount + "，總付款金額: " + unpaidAmount);
        }

        if (unpaidAmount <= 0) {
            throw new RuntimeException("應存在未支付費用");
        }

        MvcResult payResult = getPayResult(unpaidAmount, context.orderNo, false, Depart, "{orderNo}/pay", context.mainContractNo);
        JsonNode payResultDataNode = objectMapper.readTree(payResult.getResponse().getContentAsString()).get("data");
        String paymentUrl = payResultDataNode.get("paymentUrl").asText();

        // 模擬支付 OTP 驗證
        Thread.sleep(1000);
        validateOtp(paymentUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(payResultDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");

        // Local 再 Consume 一次 PaymentQueue 並立帳
        PaymentQueue departPaymentQueue = new PaymentQueue();
        BeanUtils.copyProperties(paymentInfos.get(0), departPaymentQueue);
        paymentService.consumePayment(departPaymentQueue);
        paymentService.afterPay(departPaymentQueue);
        checkoutService.checkOut(context.order, null);

        // 等待處理完成，並檢查付款狀態
        Thread.sleep(2000);
        priceInfoService.getCurrentReceivedPriceInfo(context.orderNo)
            .forEach(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "所有應付費用應為已支付"));
    }
    
    /**
     * 專門處理有優惠券場景的出車費用支付
     */
    void payForDepartFeeAndCheckOutWithCoupon(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null");
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null");
        assertNotNull(context.order, "order in context should not be null");

        // 模擬門市折扣審核通過後
        OrderPriceInfo merchandiseOrderPriceInfo = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> orderPriceInfo.getCategory() == Merchandise)
            .findAny()
            .orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        OrderPriceInfo discountOpi = priceInfoService.generateNegativeOrderPriceInfo(merchandiseOrderPriceInfo, merchandiseOrderPriceInfo.getAmount() / 10, EmpDiscount);
        discountOpi.setUid("testUid");
        PriceInfoDetail infoDetail = new PriceInfoDetail();
        infoDetail.setIsAgree(true);
        discountOpi.setInfoDetail(infoDetail);
        discountOpi.setReceivedAmount(discountOpi.getAmount());
        priceInfoService.addOrUpdate(discountOpi);

        // 計算所有應付款項目（getUnPaidPriceInfoByOrder 已包含所有未付款費用，包括優惠券折扣）
        List<OrderPriceInfo> allUnpaidPriceInfos = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(orderPriceInfo -> !orderPriceInfo.isPaid())
            .collect(Collectors.toList());

        int totalUnpaidAmount = allUnpaidPriceInfos.stream()
            .mapToInt(PriceInfoInterface::getActualPrice)
            .sum();

        if (totalUnpaidAmount <= 0) {
            throw new RuntimeException("應存在未支付費用");
        }
        
        // 計算優惠券折扣項目數量用於日誌輸出
        long couponDiscountCount = allUnpaidPriceInfos.stream()
            .filter(priceInfo -> priceInfo.getCategory() == CouponDiscount)
            .count();

        System.out.println("總付款金額: " + totalUnpaidAmount +
                          " (包含 " + couponDiscountCount + " 項優惠券折扣)");

        MvcResult payResult = getPayResult(totalUnpaidAmount, context.orderNo, false, Depart, "{orderNo}/pay", context.mainContractNo);
        JsonNode payResultDataNode = objectMapper.readTree(payResult.getResponse().getContentAsString()).get("data");
        String paymentUrl = payResultDataNode.get("paymentUrl").asText();

        // 模擬支付 OTP 驗證
        Thread.sleep(1000);
        validateOtp(paymentUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        priceInfoService.getCurrentReceivedPriceInfo(context.orderNo)
            .forEach(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "所有應付費用應為已支付"));
    }

    void createOriginalLrentalContract(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        context.originalLrentalContractNo = createOriginalLrentalContract(context.order);
        context.order = orderService.getOrder(context.orderNo);
        // 提取具體值進行比較，避免 JUnit 錯誤訊息觸發 toString()
        String actualLrentalContractNo = context.order.getLrentalContractNo();
        assertEquals(context.originalLrentalContractNo, actualLrentalContractNo);
        Thread.sleep(1000);
    }

    /**
     * 出車資料確認更新訂單，並設定與原長租契約不同的日期
     */
    void departUpdateOrder(OrderTestContext context, int offsetFromExpectedStart) {
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        MainContract mainContract = context.order.getContract().getMainContract();
        // 初始化 context.mainContract
        context.mainContract = mainContract;
        String plateNo = mainContract.getPlateNo();
        context.crsCar = crsService.getCar(plateNo);
        Cars car = Optional.ofNullable(carsService.findByPlateNo(plateNo)).orElseThrow(() -> new SubscribeException(CAR_NOT_FOUND));

        context.departRequest = new CarDepartRequest();
        context.departRequest.setDepartDate(Date.from(context.order.getExpectStartDate().minus(offsetFromExpectedStart, ChronoUnit.DAYS)));
        int departMileage = Math.max(car.getCurrentMileage(), context.crsCar.getCarBase().getKm()) + 1;
        context.departRequest.setDepartMileage(departMileage);
        context.departRequest.setPlateNo(plateNo);
        ArrayList<String> errorMessages = orderService.departUpdateOrder(context.orderNo, context.departRequest, MEMBER_ID);
        assertTrue(errorMessages.isEmpty(), errorMessages.toString());
    }

    private AccountSettlementResponse accountSettlementAndCheckOut(OrderTestContext context, PayFor payFor) throws JsonProcessingException {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContract, "mainContract in context should not be null"); // 確保 mainContract 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化

        AccountSettlementRequest accountSettlementRequest = new AccountSettlementRequest();

        PaymentInfo paymentInfo = paymentService.getPaymentsByOrder(context.orderNo).stream()
            .filter(payment -> PayAuth == payment.getPaymentCategory() && payFor == payment.getPayFor())
            .findFirst().orElseThrow(() -> new SubscribeException(PAYMENT_INFO_NOT_FOUND));
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setAccountRecords(new ArrayList<>());
        paymentRequest.setStationCode(Depart == payFor ? context.mainContract.getDepartStationCode() : context.mainContract.getReturnStationCode());
        paymentRequest.getAccountRecords().add(orderService.buildAccountRecord(paymentInfo.getAmount(), context.orderNo, paymentInfo, payFor));
        accountSettlementRequest.setPaymentRequest(paymentRequest);

        InvoiceNewRequest invoiceNewRequest = new InvoiceNewRequest();
        invoiceNewRequest.setStationCode(subscribeStationCode);
        invoiceNewRequest.setPayFor(payFor.name());
        List<Integer> orderPriceInfoIdsToPay = objectMapper.readValue(paymentInfo.getAdditionalData(), AdditionalData.class).getOrderPriceInfoIds();
        Map<Boolean, List<OrderPriceInfo>> groupedPaidPriceInfos = priceInfoService.getPriceInfosByOrder(context.orderNo).stream()
            .filter(opi -> orderPriceInfoIdsToPay.contains(opi.getId()) && SecurityDeposit != opi.getCategory() && ETag != opi.getCategory() && opi.isPaid())
            .collect(Collectors.partitioningBy(orderPriceInfo -> orderPriceInfo.isMerchandise() || orderPriceInfo.getUid() != null));

        groupedPaidPriceInfos.forEach((isMerchandiseRelated, orderPriceInfos) -> {
            InvoiceRequest invoiceRequest = new InvoiceRequest();
            int receivedAmount = orderPriceInfos.stream()
                .mapToInt(OrderPriceInfo::getActualReceivePrice)
                .sum();
            if (receivedAmount == 0) {
                return;
            }
            invoiceRequest.setAmount(receivedAmount);
            List<Integer> ids = orderPriceInfos.stream()
                .map(OrderPriceInfo::getId)
                .collect(Collectors.toList());
            invoiceRequest.setRefPriceInfoIds(ids);
            invoiceRequest.setInvoice(context.order.getInvoice());
            invoiceRequest.setMemo("測試發票備註");
            invoiceNewRequest.getInvoices().add(invoiceRequest);
        });
        accountSettlementRequest.setInvoiceNewRequest(invoiceNewRequest);

        AccountSettlementResponse accountSettlementResponse = paymentService.accountSettlement(accountSettlementRequest, context.orderNo, MEMBER_ID);
        paymentService.payAndDiscountBalance(context.orderNo);
        checkoutService.checkOut(context.order, null);

        return accountSettlementResponse;
    }

    void accountSettlementAndCheckOutForDepart(OrderTestContext context) throws JsonProcessingException {

        AccountSettlementResponse accountSettlementResponse = accountSettlementAndCheckOut(context, Depart);

        // 驗證訂閱發票檔備註
        List<Invoices> invoicesList = invoiceService.getInvoice(context.orderNo);
        assertEquals("測試發票備註", invoicesList.get(invoicesList.size() - 1).getMemo());

        String invNo = accountSettlementResponse.getInvoicesList().get(accountSettlementResponse.getInvoicesList().size() - 1).getInvNo();

        // 驗證發票主檔備註
        Result<List<InvoiceMasterSearchResponse>> invoiceMasterList = finServiceBusClient.getInvoiceMaster(Collections.singletonList(invNo));
        InvoiceMasterSearchResponse invoiceMasterSearchResponse = invoiceMasterList.getData().stream().findFirst().orElseThrow(() -> new RuntimeException("找不到發票主檔"));
        assertEquals("測試發票備註", invoiceMasterSearchResponse.getMemo());

        // 驗證發票完整資訊備註
        Result<List<InvoiceDataSearchResponse>> invoiceDataList = finServiceBusClient.getInvoiceData(Collections.singletonList(invNo));
        InvoiceDataSearchResponse invoiceDataSearchResponse = invoiceDataList.getData().stream().findFirst().orElseThrow(() -> new RuntimeException("找不到發票完整資訊"));
        assertEquals("測試發票備註", invoiceDataSearchResponse.getMemo());
    }

    void accountSettlementAndCheckOutForReturn(OrderTestContext context) throws JsonProcessingException {
        accountSettlementAndCheckOut(context, Return);
    }

    void depart(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MockHttpServletRequestBuilder requestBuilder = post("/internal/subscribe/{orderNo}/depart", context.orderNo)
            .contentType(MediaType.APPLICATION_JSON)
            .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
            .content(CAR_DEPART_FLOW_REQUEST);
        mockMvc.perform(requestBuilder)
            .andExpect(jsonPath("$.statusCode").value("0"));
    }

    /**
     * 驗證電訪任務是否正確建立
     */
    private void verifyCsatTaskCreation(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.departRequest, "departRequest in context should not be null"); // 確保 departRequest 已被初始化

        context.car = carsService.findByPlateNo(context.departRequest.getPlateNo());

        // 檢查車輛是否為格上的車
        boolean isCarPlusCar = CarsUtil.isCarPlusCar(context.car.getVatNo());

        // 查詢該訂單的電訪任務
        List<Csat> csatList = csatRepository.findByOrders(Collections.singletonList(context.orderNo));

        if (isCarPlusCar) {
            // 如果是格上的車，應該要有電訪任務
            assertFalse(csatList.isEmpty(), "格上車輛應該建立電訪任務");
            Csat csat = csatList.get(0);
            assertEquals(context.orderNo, csat.getOrderNo(), "電訪任務的訂單編號應該正確");
            assertEquals(context.car.getPlateNo(), csat.getPlateNo(), "電訪任務的車牌號碼應該正確");
            assertEquals(CsatStatus.NOT_CALLED.getCode(), csat.getStatus(), "電訪任務狀態應為未電訪");
            assertEquals(CsatQuestStatus.NOT_SURVEYED.getCode(), csat.getQuestStatus(), "電訪問卷狀態應為未填寫");
            assertEquals(CsatOrderSource.SUBSCRIBE.getCode(), csat.getSource(), "電訪任務來源應為訂閱");

            // 驗證電訪問卷是否正確建立
            CsatQuest csatQuest = csatQuestRepository.findById(csat.getId()).orElse(null);
            assertNotNull(csatQuest, "電訪問卷應該存在");
            assertEquals(0, csatQuest.getVersion(), "電訪問卷版本應為 0");
        } else {
            // 如果不是格上的車，不應該有電訪任務
            assertTrue(csatList.isEmpty(), "非格上車輛不應建立電訪任務");
        }
    }

    /**
     * 驗證長租契約已重建
     */
    void verifyLrentalContractReCreated(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.order, "order in context should not be null"); // 確保 order 已被初始化
        assertNotNull(context.originalLrentalContractNo, "originalLrentalContractNo in context should not be null"); // 確保 originalLrentalContractNo 已被初始化
        assertNotNull(context.departRequest, "departRequest in context should not be null"); // 確保 departRequest 已被初始化

        // 檢查車輛是否為格上的車，非格上車輛不需要驗證長租契約已重建
        if (!CarsUtil.isCarPlusCar(context.departRequest.getPlateNo())) {
            return;
        }

        context.order = orderService.getOrder(context.orderNo);
        // 提取具體值進行比較，避免 JUnit 錯誤訊息觸發 toString()
        String actualLrentalContractNo = context.order.getLrentalContractNo();
        assertNotNull(actualLrentalContractNo);
        assertNotEquals(context.originalLrentalContractNo, actualLrentalContractNo);
        context.recreatedLrentalContractNo = context.order.getLrentalContractNo();

        // 驗證新建長租契約日期是否正確更新
        ContractSearchRep newContract = lrentalServer.getContractInfo(context.order.getLrentalContractNo());
        String newStartDate = newContract.getDadt1().trim();
        String newEndDate = newContract.getDadt2().trim();
        // 驗證新契約的起始日期是否與實際出車日期一致
        assertEquals(DateUtil.transferADDateToMinguoDate(context.departRequest.getDepartDate().toInstant()), newStartDate);
        // 驗證新契約的結束日期是否與訂單結束日期一致
        // 提取具體值進行比較，避免 JUnit 錯誤訊息觸發 toString()
        Instant actualExpectEndDate = context.order.getExpectEndDate();
        assertEquals(DateUtil.transferADDateToMinguoDate(actualExpectEndDate), newEndDate);

        ContractSearchRep originalContract = lrentalServer.getContractInfo(context.originalLrentalContractNo);
        String originalStartDate = originalContract.getDadt1().trim();
        String originalEndDate = originalContract.getDadt2().trim();
        // 確認新契約日期與原契約日期不同
        assertNotEquals(originalStartDate, newStartDate);
        assertNotEquals(originalEndDate, newEndDate);
    }

    void verifyMileageSynchronizedWithCrs(OrderTestContext context) {
        assertNotNull(context.mainContract, "mainContract in context should not be null"); // 確保 mainContract 已被初始化
        String plateNo = context.mainContract.getPlateNo();
        context.car = carsService.findByPlateNo(plateNo);
        assertNotNull(context.car.getCrsCarNo(), "car.getCrsCarNo() should not be null"); // 確保 car.getCrsCarNo() 已被初始化
        assertTrue(context.car.getCrsCarNo() > 0, "car.getCrsCarNo() should be greater than 0"); // 確保 car.getCrsCarNo() > 0
        CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(plateNo);
        assert Objects.equals(context.car.getCurrentMileage(), carBaseInfoSearchResponse.getCarBase().getKm());
    }

    void returnCarConfirm(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.car, "car in context should not be null"); // 確保 car 已被初始化

        context.carDropOffRequest = new CarDropOffRequest();
        context.carDropOffRequest.setReturnDate(DateUtils.toDate(DateUtils.toLocalDateTime(!context.order.getIsNewOrder() ? Date.from(context.order.getStartDate().plus(10, ChronoUnit.DAYS)) : new Date())));
        context.carDropOffRequest.setReturnMileage(context.car.getCurrentMileage() + 1612);
        context.carDropOffRequest.setReturnRemark("測試還車備註");
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return", context.orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(context.carDropOffRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // get /internal/subscribe/v2/priceInfo/{orderNo}?credit=false
        MvcResult result = mockMvc.perform(get("/internal/subscribe/v2/priceInfo/{orderNo}?credit=false", context.orderNo)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        JsonNode jsonNode = objectMapper.readTree(responseContent);
        JsonNode dataNode = jsonNode.get("data");

        // 找到最後一個元素 (最大的 key 值)
        String lastKey = null;
        Iterator<String> fieldNames = dataNode.fieldNames();
        int maxKey = -1;
        while (fieldNames.hasNext()) {
            String key = fieldNames.next();
            try {
                int keyInt = Integer.parseInt(key);
                if (keyInt > maxKey) {
                    maxKey = keyInt;
                    lastKey = key;
                }
            } catch (NumberFormatException e) {
                // 忽略非數字的 key
            }
        }

        // 計算所有 MileageFee 的 saveRealMileage - (discountMileage - rentalDiscountMileage) 加總
        int totalDiff = 0;
        Iterator<String> allStageKeys = dataNode.fieldNames();
        while (allStageKeys.hasNext()) {
            String stageKey = allStageKeys.next();
            JsonNode priceInfoList = dataNode.get(stageKey).get("orderPriceInfoList");
            for (JsonNode item : priceInfoList) {
                if ("MileageFee".equals(item.get("category").asText())) {
                    JsonNode infoDetail = item.get("infoDetail");
                    if (infoDetail != null && infoDetail.has("saveRealMileage") && infoDetail.get("saveRealMileage").asInt(0) > 0) {
                        int saveRealMileage = infoDetail.get("saveRealMileage").asInt(0);
                        int discountMileage = infoDetail.get("discountMileage").asInt(0);
                        int rentalDiscountMileage = infoDetail.get("rentalDiscountMileage").asInt(0);
                        totalDiff += saveRealMileage - (discountMileage - rentalDiscountMileage);
                    }
                }
            }
        }

        if (lastKey != null) {
            JsonNode priceInfoList = dataNode.get(lastKey).get("orderPriceInfoList");
            if (totalDiff > 0) {
                // 應存在 category 為 Others 的回收里程優惠，且 reason 的 km 數字等於 totalDiff
                boolean found = false;
                for (JsonNode item : priceInfoList) {
                    if (item.get("category").asText().contains("Others")) {
                        String reason = item.get("infoDetail").get("reason").asText();
                        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile("(\\d+)\\s*km").matcher(reason);
                        if (matcher.find()) {
                            int kmValue = Integer.parseInt(matcher.group(1));
                            assertEquals(totalDiff, kmValue, "回收里程優惠 km 數字應等於所有 MileageFee 的 saveRealMileage - (discountMileage - rentalDiscountMileage) 加總");
                            found = true;
                        }
                    }
                }
                assertTrue(found, "應存在 category 為 Others 的回收里程優惠項目");
            } else {
                // 不應有回收里程優惠
                for (JsonNode item : priceInfoList) {
                    assertFalse(item.get("category").asText().contains("Others") &&
                            item.get("infoDetail").has("reason") &&
                            item.get("infoDetail").get("reason").asText().contains("回收里程優惠"),
                        "當 totalDiff <= 0 時，不應有回收里程優惠");
                }
            }
        }
    }

    void payForReturnFee(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.mainContractNo, "mainContractNo in context should not be null"); // 確保 mainContractNo 已被初始化

        int unpaidAmountAfterReturnCarConfirm = calculateReturnPaymentAmount(context.orderNo);

        if (unpaidAmountAfterReturnCarConfirm <= 0) {
            throw new RuntimeException("應存在未支付費用");
        }

        MvcResult payResult = getPayResult(unpaidAmountAfterReturnCarConfirm, context.orderNo, false, Return, "{orderNo}/pay", context.mainContractNo);
        JsonNode payResultDataNode = objectMapper.readTree(payResult.getResponse().getContentAsString()).get("data");
        String paymentUrl = payResultDataNode.get("paymentUrl").asText();

        Thread.sleep(1000);
        validateOtp(paymentUrl);
        Thread.sleep(1000);

        // 確認付款資訊
        List<PaymentInfo> paymentInfos = paymentService.getPaymentInfosByTradeId(payResultDataNode.get("recTradeId").asText());
        assertNotNull(paymentInfos, "付款資訊不應為空");
        assertFalse(paymentInfos.isEmpty(), "付款資訊列表不應為空");

        // Local 再 Consume 一次 PaymentQueue 並立帳
        PaymentQueue returnPaymentQueue = new PaymentQueue();
        BeanUtils.copyProperties(paymentInfos.get(0), returnPaymentQueue);
        paymentService.consumePayment(returnPaymentQueue);
        paymentService.afterPay(returnPaymentQueue);
        checkoutService.checkOut(context.order, null);

        // 等待處理完成，並檢查付款狀態
        Thread.sleep(2000);
        List<OrderPriceInfo> receivedPriceInfos = priceInfoService.getCurrentReceivedPriceInfo(context.orderNo);
        receivedPriceInfos.forEach(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "所有應付費用應為已支付"));
    }

    void setAccidentPriceInfo(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        ExtraFeeRequest extraFeeRequest = new ExtraFeeRequest();
        extraFeeRequest.setCarDamaged(true);
        extraFeeRequest.setReturnNego("N");
        List<ExtraFeeRequest.ExtraFee> extraFeeList = new ArrayList<>();
        ExtraFeeRequest.ExtraFee extraFee = new ExtraFeeRequest.ExtraFee();
        extraFee.setAmount(20000);
        extraFee.setARCarLossAmt(20000);
        extraFee.setReason("車損");
        Orders order = orderService.getOrder(context.orderNo);
        int currentStage = order.getCurrentStage().getStage();
        extraFee.setStage(currentStage);
        extraFee.setCategory(CarAccident);
        extraFeeList.add(extraFee);
        extraFeeRequest.setExtraFeeList(extraFeeList);
        MvcResult setAccidentPriceInfoResult = mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(extraFeeRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        JsonNode accidentPriceInfoResultDataNode = objectMapper.readTree(setAccidentPriceInfoResult.getResponse().getContentAsString()).get("data");
        assertNotNull(accidentPriceInfoResultDataNode, "費用資訊不應為空");
        assertTrue(accidentPriceInfoResultDataNode.isArray(), "費用資訊應為 array");
        accidentPriceInfoResultDataNode.forEach(node -> {
            // 確保返回的費用資訊列表中，當前階段的車損費用如果存在，也應該是處於未開放的狀態
            assert !node.get("category").asText().equals(CarAccident.name())
                || node.get("stage").asInt() != currentStage
                || node.get("receivableDate").asLong() > Instant.now().getEpochSecond();
        });

        // 測試更新車損費用期數邏輯
        List<OrderPriceInfo> carAccidentPriceInfos = priceInfoService.getPriceInfosByOrder(context.orderNo)
            .stream()
            .filter(opi -> opi.getCategory() == CarAccident)
            .collect(Collectors.toList());

        if (!carAccidentPriceInfos.isEmpty()) {
            OrderPriceInfo carAccidentOpi = carAccidentPriceInfos.get(0);
            Integer originalStage = carAccidentOpi.getStage();
            Instant originalLastPayDate = carAccidentOpi.getLastPayDate();

            // 準備更新車損費用，將 stage 改為下一期
            int newStage = Math.min(originalStage + 1, order.getMonth() / 3);
            ExtraFeeRequest updateStageRequest = new ExtraFeeRequest();
            updateStageRequest.setCarDamaged(true);
            updateStageRequest.setReturnNego("N");
            
            ExtraFeeRequest.ExtraFee updateFee = new ExtraFeeRequest.ExtraFee();
            updateFee.setPriceInfoId(carAccidentOpi.getId());
            updateFee.setAmount(25000); // 更新金額
            updateFee.setARCarLossAmt(25000);
            updateFee.setReason("車損更新");
            updateFee.setStage(newStage); // 更新期數
            updateFee.setCategory(CarAccident);
            
            List<ExtraFeeRequest.ExtraFee> updateFeeList = new ArrayList<>();
            updateFeeList.add(updateFee);
            updateStageRequest.setExtraFeeList(updateFeeList);

            // 執行更新車損費用的請求
            mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(updateStageRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode").value("0"));

            // 驗證期數更新結果
            OrderPriceInfo updatedCarAccidentOpi = priceInfoService.get(carAccidentOpi.getId());
            
            // 驗證期數已更新
            assertEquals(newStage, updatedCarAccidentOpi.getStage(), "車損費用期數應已更新");
            
            // 驗證金額已更新
            assertEquals(25000, updatedCarAccidentOpi.getAmount(), "車損費用金額應已更新");
            
            // 驗證 lastPayDate 根據新期數重新計算
            if (newStage != originalStage) {
                assertNotEquals(originalLastPayDate, updatedCarAccidentOpi.getLastPayDate(), "期數變更時，lastPayDate 應該重新計算");
                
                // 驗證 lastPayDate 是根據新期數正確計算的
                List<CalculateStage> stages = DateUtil.calculateStageAndDate(order);
                if (newStage > 0 && newStage <= stages.size()) {
                    CalculateStage expectedTargetStage = stages.get(newStage - 1);
                    Instant expectedLastPayDate = DateUtil.convertToEndOfInstant(expectedTargetStage.getEndDate());
                    assertEquals(expectedLastPayDate, updatedCarAccidentOpi.getLastPayDate(), "lastPayDate 應該根據新期數正確計算");
                }
            }
            
            // 驗證 reason 已更新
            assertEquals("車損更新", updatedCarAccidentOpi.getInfoDetail().getReason(), "車損原因應已更新");
                
            log.info("車損費用期數更新測試完成: 原期數={}, 新期數={}, 原金額={}, 新金額={}", originalStage, newStage, 20000, 25000);
        }

        // 測試無效期數的異常處理
        if (!carAccidentPriceInfos.isEmpty()) {
            OrderPriceInfo carAccidentOpi = carAccidentPriceInfos.get(0);
            List<CalculateStage> stages = DateUtil.calculateStageAndDate(order);
            
            // 測試期數超出範圍 (大於最大期數)
            int invalidStage = stages.size() + 1;
            ExtraFeeRequest invalidStageRequest = new ExtraFeeRequest();
            invalidStageRequest.setCarDamaged(true);
            invalidStageRequest.setReturnNego("N");
            
            ExtraFeeRequest.ExtraFee invalidFee = new ExtraFeeRequest.ExtraFee();
            invalidFee.setPriceInfoId(carAccidentOpi.getId());
            invalidFee.setAmount(30000);
            invalidFee.setARCarLossAmt(30000);
            invalidFee.setReason("測試無效期數");
            invalidFee.setStage(invalidStage);
            invalidFee.setCategory(CarAccident);
            
            List<ExtraFeeRequest.ExtraFee> invalidFeeList = new ArrayList<>();
            invalidFeeList.add(invalidFee);
            invalidStageRequest.setExtraFeeList(invalidFeeList);

            // 期數過大應該拋出 BadRequestException
            mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidStageRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value(containsString("期數設定無效")));

            // 測試期數為 0 的情況
            invalidFee.setStage(0);
            invalidStageRequest.setExtraFeeList(Collections.singletonList(invalidFee));

            mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidStageRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value(containsString("期數設定無效")));

            // 測試負數期數的情況
            invalidFee.setStage(-1);
            invalidStageRequest.setExtraFeeList(Collections.singletonList(invalidFee));

            mockMvc.perform(post("/internal/subscribe/v1/priceInfo/{orderNo}/accident", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(invalidStageRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value(containsString("期數設定無效")));
            
            log.info("車損費用無效期數異常測試完成: 最大有效期數={}, 測試期數=[{}, 0, -1]", 
                stages.size(), invalidStage);
        }
    }

    void returnCarSuccess(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        CarDropOffCompleteRequest carDropOffCompleteRequest = new CarDropOffCompleteRequest();
        carDropOffCompleteRequest.setReturnMemberId(MEMBER_ID);
        mockMvc.perform(post("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(carDropOffCompleteRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));
        context.car = carsService.findByPlateNo(context.mainContract.getPlateNo());
        Thread.sleep(1000);
    }

    @Transactional(noRollbackFor = SubscribeException.class)
    void returnCarExpectUnpaidError(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        CarDropOffCompleteRequest carDropOffCompleteRequest = new CarDropOffCompleteRequest();
        carDropOffCompleteRequest.setReturnMemberId(MEMBER_ID);
        try {
            mockMvc.perform(post("/internal/subscribe/{orderNo}/return", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(carDropOffCompleteRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.statusCode").value(ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE.getCode()))
                .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_AND_AMOUNT_NOT_BALANCE.getMsg()));
            Thread.sleep(1000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    void verifyWithoutPendingUnpaidPriceInfo(OrderTestContext context) throws Exception {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MvcResult priceInfoResult = mockMvc.perform(get("/internal/subscribe/priceInfo/{orderNo}/unpaid", context.orderNo)
                .param("history", "false")
                .param("isCredit", "true"))
            .andExpect(status().isOk())
            .andReturn();
        List<OrderPriceInfoInternalResponse> userPriceInfos = objectMapper.readValue(
            priceInfoResult.getResponse().getContentAsString(),
            new TypeReference<List<OrderPriceInfoInternalResponse>>() {
            }
        );
        assertFalse(userPriceInfos.isEmpty(), "未付費用資訊列表不應為空");
        userPriceInfos.forEach(priceInfo -> {
            assertNotEquals(PayStatus.PENDING, priceInfo.getPayStatus(),
                String.format("費用 id %d 付款狀態不應為未開放", priceInfo.getId()));
            if (priceInfo.getCategory() == CarAccident) {
                assertNotNull(priceInfo.getUpdater(), "車損費用 id " + priceInfo.getId() + " 應該有更新者");
            }
        });
    }

    /**
     * POST /internal/subscribe/{orderNo}/renew (使用 mockMvc 會失敗)
     * SubscribeRenewMonth 參數化的續約方法，支援不同的租期
     */
    private void renewOrder(OrderTestContext context, SubscribeRenewMonth month) {
        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化
        assertNotNull(context.contractCreateReq, "contractCreateReq in context should not be null"); // 確保 contractCreateReq 已被初始化
        OrderRenewRequest orderRenewRequest = new OrderRenewRequest();
        orderRenewRequest.setMonth(month);
        orderRenewRequest.setAcctId(context.contractCreateReq.getAcctId());
        Orders renewalOrder = contractLogic.renewOrder(context.orderNo, orderRenewRequest, true, MEMBER_ID, true, null);
        // 提取具體值進行比較，避免 JUnit 錯誤訊息觸發 toString()
        String actualRenewalOrderNo = renewalOrder.getOrderNo();
        assertNotEquals(context.orderNo, actualRenewalOrderNo);
        context.renewalOrderNo = renewalOrder.getOrderNo();
    }

    /**
     * 建立優惠券主檔並取得券號
     */
    void createTestCoupon(OrderTestContext context) throws Exception {
        // 使用類似 CouponServerTest 的邏輯建立測試用優惠券
        String couponName = "訂閱測試專用_" + System.currentTimeMillis();
        context.couponEventCode = "test_coupon_" + System.currentTimeMillis() % 10000;
        
        // 建立優惠券主檔
        Map<String, Object> createCouponRequest = new HashMap<>();
        createCouponRequest.put("frontName", couponName);
        createCouponRequest.put("name", couponName);
        createCouponRequest.put("description", "ContractInternalControllerTest 測試用優惠券");
        createCouponRequest.put("receivableQty", 1);
        createCouponRequest.put("discountType", "AMOUNT");
        createCouponRequest.put("subDiscountType", "FULL_AMOUNT_DISCOUNT");
        createCouponRequest.put("ruleType", "MASTER_DATE");
        createCouponRequest.put("overTotalPrice", "5000"); // 設定最低消費門檻
        createCouponRequest.put("calendarTypes", Arrays.asList("WEEKDAY", "HOLIDAY", "FESTIVAL"));
        createCouponRequest.put("discount", 500); // 折扣 500 元
        createCouponRequest.put("designatedDays", null);
        createCouponRequest.put("designatedHours", null);
        createCouponRequest.put("designatedMinutes", null);
        createCouponRequest.put("startTime", Instant.now().minus(1, ChronoUnit.DAYS).toEpochMilli());
        createCouponRequest.put("endTime", Instant.now().plus(365, ChronoUnit.DAYS).toEpochMilli());
        createCouponRequest.put("carSeries", Collections.emptyList());
        createCouponRequest.put("sourceItem", com.carplus.subscribe.enums.coupon.SourceItem.SUBSCRIBE.getCode());

        try {
            String couponServiceUrl = (String) ReflectionTestUtils.getField(couponServer, "couponUri");
            
            Result<Object> createResult = HttpUtils.post(
                    couponServiceUrl + "/internal/coupon/v3/coupon",
                    HttpUtils.Options.custom()
                        .header("X-MemberId", MEMBER_ID)
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(createCouponRequest), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    
                    if (result.getStatusCode() != 0) {
                        throw new RuntimeException("建立優惠券失敗: " + result.getMessage());
                    }
                    
                    return result;
                })
                .fetch();

            Thread.sleep(1000); // 等待資料同步

            // 查詢優惠券ID
            Result<Object> listResult = HttpUtils.get(
                    couponServiceUrl + "/internal/coupon/v3/coupons",
                    HttpUtils.Options.custom()
                        .queryString("page", "1")
                        .queryString("pageSize", "20")
                        .queryString("hasDeleteAndExpired", "false")
                        .queryString("source", com.carplus.subscribe.enums.coupon.SourceItem.SUBSCRIBE.getCode())
                        .queryString("orderField", "createdTime")
                        .queryString("orderType", "desc"))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    
                    if (result.getStatusCode() != 0) {
                        throw new RuntimeException("查詢優惠券列表失敗: " + result.getMessage());
                    }
                    
                    return result;
                })
                .fetch();

            JsonNode listResponse = objectMapper.valueToTree(listResult);
            JsonNode couponList = listResponse.path("data").path("couponList");
            for (JsonNode coupon : couponList) {
                if (couponName.equals(coupon.path("name").asText())) {
                    context.couponId = coupon.path("id").asText();
                    break;
                }
            }
            
            assertNotNull(context.couponId, "找不到建立的優惠券");
            
            // 建立兌換序號
            Map<String, Object> addEventRequest = new HashMap<>();
            addEventRequest.put("eventCode", context.couponEventCode);
            addEventRequest.put("limitQtyOfPeople", 0);
            addEventRequest.put("refid", context.couponId);
            addEventRequest.put("type", 1);

            HttpUtils.post(
                    couponServiceUrl + "/internal/coupon/v1/addEvent",
                    HttpUtils.Options.custom()
                        .entity(() -> new StringEntity(objectMapper.writeValueAsString(addEventRequest), ContentType.APPLICATION_JSON)))
                .then(res -> {
                    TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                    Result<Object> result = objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    
                    if (result.getStatusCode() != 0) {
                        throw new RuntimeException("建立兌換序號失敗: " + result.getMessage());
                    }
                    
                    return result;
                })
                .fetch();

            Thread.sleep(1000); // 等待資料同步
            
            System.out.println("成功建立測試優惠券: " + couponName + ", 兌換序號: " + context.couponEventCode);
            
        } catch (Exception e) {
            log.error("建立測試優惠券失敗", e);
            throw e;
        }
    }
    
    /**
     * 會員領取優惠券
     */
    void receiveCoupon(OrderTestContext context, Integer acctId) throws Exception {
        assertNotNull(context.couponEventCode, "couponEventCode should not be null");
        
        try {
            couponServer.receiveCouponByCode(context.couponEventCode, acctId);
            Thread.sleep(1000); // 等待資料同步
            
            // 查詢會員優惠券取得 sequenceId
            List<com.carplus.subscribe.model.coupon.Coupon> memberCoupons = couponServer.findValidCoupons(acctId);
            context.couponSequenceId = memberCoupons.stream()
                .filter(coupon -> context.couponId.equals(coupon.getCouponId()))
                .findFirst()
                .map(com.carplus.subscribe.model.coupon.Coupon::getSequenceId)
                .orElseThrow(() -> new RuntimeException("找不到會員的優惠券 sequenceId"));
            
            System.out.println("會員 " + acctId + " 成功領取優惠券, sequenceId: " + context.couponSequenceId);
            
        } catch (Exception e) {
            log.error("會員領取優惠券失敗: eventCode={}, acctId={}", context.couponEventCode, acctId, e);
            throw e;
        }
    }
    
    /**
     * 查詢訂單可用優惠券
     */
    void queryAvailableCoupons(OrderTestContext context, Integer acctId) throws Exception {
        assertNotNull(context.orderNo, "orderNo should not be null");
        
        // 記錄優惠券套用前的可折扣金額
        context.originalDiscountableAmount = priceInfoService.getCouponDiscountableFeesWrapper(context.orderNo).getActualPrice();
        
        context.availableCoupons = orderService.getAvailableCoupons(context.orderNo, acctId, null, null);
        assertNotNull(context.availableCoupons, "查詢可用優惠券失敗");
        
        // 驗證包含我們建立的優惠券
        boolean containsTestCoupon = context.availableCoupons.stream()
            .anyMatch(coupon -> context.couponSequenceId.equals(coupon.getSequenceId()));
        assertTrue(containsTestCoupon, "可用優惠券清單應包含測試用優惠券");
        
        System.out.println("查詢到 " + context.availableCoupons.size() + " 張可用優惠券，包含測試優惠券");
    }
    
    /**
     * 套用優惠券到訂單
     */
    void applyCouponToOrder(OrderTestContext context, Integer acctId) throws Exception {
        assertNotNull(context.orderNo, "orderNo should not be null");
        assertNotNull(context.couponSequenceId, "couponSequenceId should not be null");
        
        try {
            context.couponPriceInfos = orderService.applyCouponToOrder(context.orderNo, context.couponSequenceId, acctId, null);
            assertNotNull(context.couponPriceInfos, "套用優惠券失敗");
            assertFalse(context.couponPriceInfos.isEmpty(), "優惠券費用明細不應為空");
            
            // 計算實際套用的折扣金額
            context.appliedCouponDiscount = context.couponPriceInfos.stream()
                .filter(priceInfo -> priceInfo.getCategory() == CouponDiscount)
                .mapToInt(PriceInfoInterface::getActualPrice)
                .sum();
            
            // 驗證折扣金額為負數（因為是折扣）
            assertTrue(context.appliedCouponDiscount < 0, "優惠券折扣金額應為負數");
            
            System.out.println("成功套用優惠券，折扣金額: " + Math.abs(context.appliedCouponDiscount));
            
        } catch (Exception e) {
            log.error("套用優惠券失敗: orderNo={}, sequenceId={}", context.orderNo, context.couponSequenceId, e);
            throw e;
        }
    }
    
    /**
     * 驗證優惠券套用後的費用狀態
     */
    void verifyCouponApplication(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo should not be null");
        assertNotNull(context.couponPriceInfos, "couponPriceInfos should not be null");
        
        // 檢查優惠券 OrderPriceInfo 已正確建立
        List<OrderPriceInfo> couponDiscountPriceInfos = priceInfoService.getPriceInfosByOrder(context.orderNo, CouponDiscount, Discount);
        assertFalse(couponDiscountPriceInfos.isEmpty(), "應存在優惠券折扣費用明細");
        
        // 檢查是否有關聯的費用項目
        boolean hasReferencedFees = couponDiscountPriceInfos.stream()
            .anyMatch(priceInfo -> priceInfo.getRefPriceInfoNo() != null);
        assertTrue(hasReferencedFees, "優惠券應關聯到原始費用項目");
        
        // 注意：在此階段優惠券已套用但關聯費用尚未付款，所以可折扣金額不會減少
        // 這是正確的行為，因為折扣只有在關聯費用實際付款時才生效
        int currentDiscountableAmount = priceInfoService.getCouponDiscountableFeesWrapper(context.orderNo).getActualPrice();
        
        // 驗證優惠券折扣項目存在且金額正確
        int totalCouponDiscountAmount = couponDiscountPriceInfos.stream()
            .mapToInt(PriceInfoInterface::getActualPrice)
            .sum();
        
        assertEquals(context.appliedCouponDiscount, totalCouponDiscountAmount, 
            "優惠券折扣項目的總金額應等於預期折扣金額");
        
        // 在套用優惠券但關聯費用尚未付款時，可折扣金額應保持不變
        assertEquals(context.originalDiscountableAmount, currentDiscountableAmount,
            "在關聯費用尚未付款前，可折扣金額應保持不變");
        
        System.out.println("優惠券套用驗證成功 - 可折扣金額: " + currentDiscountableAmount + 
                          ", 優惠券折扣金額: " + Math.abs(context.appliedCouponDiscount) +
                          ", 關聯費用尚未付款，折扣待生效");
    }

    /**
     * 驗證付款後的優惠券折抵效果
     */
    void verifyCouponDiscountEffectAfterPayment(OrderTestContext context) {
        assertNotNull(context.orderNo, "orderNo 不應為 null");

        // 驗證優惠券折扣項目存在
        List<OrderPriceInfo> couponDiscountPriceInfos =
            priceInfoService.getPriceInfosByOrder(context.orderNo, CouponDiscount, null);
        assertFalse(couponDiscountPriceInfos.isEmpty(), "應存在優惠券折扣項目");

        // 驗證優惠券折扣是否已套用（使用專門的方法檢查折抵狀態）
        boolean allCouponDiscountsApplied = couponDiscountPriceInfos.stream()
            .allMatch(priceInfoService::isCouponDiscountApplied);
        assertTrue(allCouponDiscountsApplied, "所有優惠券折扣項目的折抵應已生效");

        // 計算優惠券折扣總金額
        int couponDiscountAmount = couponDiscountPriceInfos.stream()
            .mapToInt(PriceInfoInterface::getActualPrice)
            .sum();

        // 驗證優惠券折扣金額等於預期折扣金額
        assertEquals(
            context.appliedCouponDiscount,
            couponDiscountAmount,
            "優惠券折扣金額應等於預期折扣金額"
        );

        System.out.println(
            "付款後優惠券折抵效果驗證成功 - " +
                "折扣項目數: " + couponDiscountPriceInfos.size() +
                ", 折抵已生效: " + allCouponDiscountsApplied +
                ", 實際折扣金額: " + Math.abs(couponDiscountAmount) +
                ", 預期折扣金額: " + Math.abs(context.appliedCouponDiscount)
        );
    }


    /**
     * 清理測試優惠券
     */
    void cleanupTestCoupon(OrderTestContext context) {
        if (context.couponSequenceId != null && context.couponId != null) {
            try {
                String couponServiceUrl = (String) ReflectionTestUtils.getField(couponServer, "couponUri");
                
                // 刪除會員優惠券
                HttpUtils.delete(
                        couponServiceUrl + "/internal/coupon/v3/user/coupon/" + context.couponSequenceId,
                        HttpUtils.Options.custom())
                    .then(res -> {
                        TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                        return objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    })
                    .fetch();
                
                Thread.sleep(500);
                
                // 停用優惠券主檔
                HttpUtils.delete(
                        couponServiceUrl + "/internal/coupon/v3/coupon/" + context.couponId + "/disable",
                        HttpUtils.Options.custom())
                    .then(res -> {
                        TypeReference<Result<Object>> typeReference = new TypeReference<Result<Object>>() {};
                        return objectMapper.readValue(res.getEntity().getContent(), typeReference);
                    })
                    .fetch();
                    
                System.out.println("成功清理測試優惠券: " + context.couponId);
                
            } catch (Exception e) {
                log.warn("清理測試優惠券時發生錯誤，但不影響測試結果", e);
            }
        }
    }

    /**
     * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     * 4. {@link com.carplus.subscribe.service.InvoiceServiceV2#getInvoiceWithNewTransaction(java.lang.String)} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     */
    private void fromCreateOrderToReturnCarConfirm(OrderTestContext context, CarDefine.CarState carState) throws Exception {
        // 步驟 1: 建立訂單
        // 呼叫 createOrder 方法，模擬收銀台建立訂閱制車輛租賃訂單的流程。
        // 此步驟會選擇一台閒置車輛，設定合約相關資訊，並透過 API 建立訂單，
        // 同時取得訂單編號 (orderNo) 和主約編號 (mainContractNo) 等上下文資訊。
        createOrder(context, 12, true, null, false, carState);

        // 步驟 2: 人工授信
        // 呼叫 approveCreditManually 方法，模擬人工審核通過會員的信用。
        // 此步驟會更新訂單的授信狀態為人工審核通過，為後續支付流程做準備。
        approveCreditManually(context);

        // 步驟 3: 支付保證金
        // 呼叫 payForSecurityDeposit 方法，模擬會員支付訂單的保證金。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證保證金付款是否成功。
        payForSecurityDeposit(context);

        // 步驟 4: 支付出車費用並確認已付款
        // 呼叫 payForDepartFeeAndCheckOut 方法，模擬會員支付出車費用。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證出車費用付款是否成功。
        payForDepartFeeAndCheckOut(context);

        // 步驟 5: 建立原始長租契約
        // 呼叫 createOriginalLrentalContract 方法，模擬系統根據訂單資料建立原始長租契約。
        // 此步驟會調用 LrentalServer API 建立契約，並將契約編號更新回訂單。
        createOriginalLrentalContract(context);

        // 步驟 6: 出車資料確認更新訂單，模擬與原長租契約不同的出車日期
        // 呼叫 departUpdateOrder 方法，模擬因故需要更新訂單的出車日期。
        // 此步驟會模擬更新訂單的出車日期和出車里程，並驗證更新是否成功。
        departUpdateOrder(context, 10);

        // 步驟 7: 帳務結算、開立發票及立帳
        // 呼叫 accountSettlementAndCheckOut 方法，模擬系統進行帳務結算、開立發票及立帳流程。
        accountSettlementAndCheckOutForDepart(context);

        // 步驟 8: 觸發實際出車流程
        // 呼叫 depart 方法，模擬觸發實際出車的流程。
        // 此步驟會調用出車 API，將訂單狀態變更為已出車。
        depart(context);

        // 步驟 9: 驗證電訪任務已建立
        verifyCsatTaskCreation(context);

        // 步驟 10: 驗證長租契約已重建，並檢查日期是否正確更新
        // 呼叫 verifyLrentalContractReCreated 方法，驗證系統是否已根據更新後的出車日期重建長租契約。
        // 此步驟會檢查新契約的日期是否與訂單的實際出車日期和結束日期一致，並與原始契約日期進行比較。
        verifyLrentalContractReCreated(context);

        // 步驟 11: 驗證里程已同步至 CRS
        // 呼叫 verifyMileageSynchronizedWithCrs 方法，驗證系統是否已將車輛里程同步至 CRS。
        // 此步驟會檢查 Cars 資料庫中的車輛里程是否與 CRS 中的里程一致。
        // 已改成事件驅動，因此暫不驗證 mileage 是否同步至 CRS
        // verifyMileageSynchronizedWithCrs(context);

        // 步驟 12: 還車資料異動
        // 呼叫 returnCarConfirm 方法，模擬收銀台還車資料異動流程。
        // 此步驟會調用還車資料異動 API，設定還車日期和還車里程等資訊，並將訂單狀態變更為已還車。
        returnCarConfirm(context);
    }

    @Test
    @DisplayName("用戶為訂閱停權，建立訂單失敗")
    void createOrder_Failure_WhenUserIsSubscriptionSuspended() {
        // Arrange
        Integer notAllowedAcctId = 389154;
        assertTrue(authServer.isBlacklistedOrSubSuspended(notAllowedAcctId), "用戶應為訂閱停權");

        // Act & Assert
        OrderTestContext context = new OrderTestContext();
        SubscribeException subscribeException = assertThrows(SubscribeException.class, () ->
            createOrder(context, notAllowedAcctId, 12, true, null, false, null, BACK_LIST));
        assertEquals(BACK_LIST.getCode(), subscribeException.getCode().getCode());
        String subscribeExceptionReasonPattern = Pattern.compile(".*" + BlackListQueryRes.Fields.inBlackList + ".*" + BlackListQueryRes.Fields.subSuspended + ".*").pattern();
        assertNotNull(subscribeException.getReason());
        assertTrue(subscribeException.getReason().matches(subscribeExceptionReasonPattern));
    }

    @Test
    @DisplayName("建立訂單後，人工授信通過失敗 - 原因為空")
    void createOrder_thenApproveCreditManually_Fail_WhenReasonIsBlank() throws Exception {
        // 建立訂單
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 6, true, null, false, null);
        assertNotNull(context.orderNo, "訂單編號不應為空");

        // 授信通過原因為空
        CreditApprovalRequest creditApprovalRequest = new CreditApprovalRequest("");

        mockMvc.perform(post("/internal/subscribe/{orderNo}/credit/approve", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(creditApprovalRequest)))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.message").value(containsString("授信通過原因不可為空")));
    }

    /**
     * 整合測試案例：從訂單建立到還車並驗證存在未開放付款的車損費用情境
     * <p>
     * 此測試案例涵蓋了從訂單建立、人工授信、保證金支付、出車、建立長租契約、
     * 更新訂單出車資訊、支付出車費用、實際出車、驗證長租契約重建、里程同步 CRS、
     * 確認還車、支付還車費用，到最後設定車損費用並驗證在存在未開放付款車損費用的情況下，
     * 無法完成還車流程，並驗證最終未付費用列表中不應包含未開放付款的費用。
     * <p>
     * 驗證重點：
     * 1. 訂單建立和相關資料的正確性 (車輛、合約、訂單編號等)。
     * 2. 人工授信流程的模擬和結果驗證。
     * 3. 保證金和出車費用的支付流程模擬和驗證 (包含 TapPay OTP 驗證模擬)。
     * 4. 長租契約的成功建立和重建，以及契約日期的驗證。
     * 5. 出車和還車流程的模擬，包含里程同步和費用結算。
     * 6. 車損費用的設定和在未支付車損費用的情況下，還車流程的阻斷驗證。
     * 7. 最終未付費用狀態的驗證，確保沒有未開放付款的費用。
     * <p>
     * 測試步驟：
     * 1. 建立訂單 (createOrder)。
     * 2. 人工授信 (approveCreditManually)。
     * 3. 支付保證金 (payForSecurityDeposit)。
     * 4. 支付出車費用並確認已付款 (payForDepartFeeAndCheckOut)。
     * 5. 建立原始長租契約 (createOriginalLrentalContract)。
     * 6. 出車資料確認更新訂單，模擬與原長租契約不同的出車日期 (departUpdateOrder)。
     * 7. 帳務結算、開立發票及立帳和出車檢查 (accountSettlementAndCheckOut)。
     * 8. 觸發實際出車流程 (depart)。
     * 9. 驗證長租契約已重建，並檢查日期是否正確更新 (verifyLrentalContractReCreated)。
     * 10. 驗證里程已同步至 CRS (verifyMileageSynchronizedWithCrs)。
     * 11. 還車資料異動 (returnCarConfirm)。
     * 12. 支付還車費用 (payForReturnFee)。
     * 13. 設定車損費用 (setAccidentPriceInfo)。
     * 14. 驗證在存在未支付費用的情況下，預期還車會失敗 (returnCarExpectUnpaidError)。
     * 15. 驗證最終未付費用列表中不應包含未開放付款的費用 (verifyWithoutPendingUnpaidPriceInfo)。
     * <p>
     * 注意事項：
     * - 使用 @Transactional(isolation = Isolation.READ_COMMITTED)，確保測試方法在 transaction 中執行。
     * - 使用 `OrderTestContext` 類來管理測試過程中需要共享的上下文資料。
     * - 為了使測試能夠順利進行，需要在測試前先將以下程式碼修改：
     * 1. {@link com.carplus.subscribe.service.CheckoutService#checkOut(java.lang.String)} 和
     * {@link com.carplus.subscribe.service.CheckoutService#checkOut(com.carplus.subscribe.db.mysql.entity.contract.Orders)}
     * 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 移除 `Propagation.REQUIRES_NEW` 是為了讓 `CheckoutService` 的 `checkOut` 方法與呼叫它的方法共享同一個 transaction 。
     * 2. {@link com.carplus.subscribe.service.InvoiceServiceV2#createInvoice} 中的 `Invoices invoice = self.create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 修改為 `Invoices invoice = create(orders, user, subscribeStationCode, payFor, request, memberId);`
     * 原因： `self.create()` 是內部方法調用，在 Spring 的 AOP 代理機制下，直接的內部方法調用不會被 transaction 切面增強，
     * 導致 `create` 方法可能沒有在 transaction 中執行，修改為直接調用 `create()` 方法 (假設 `createInvoice` 方法本身是 transaction 方法) 可以確保 `create` 方法參與到外部方法 (`createInvoice`) 的 transaction 中。
     * 3. {@link com.carplus.subscribe.service.InvoiceServiceV2#setCheckout} 方法上的 `@Transactional` 註解，移除 `propagation = Propagation.REQUIRES_NEW` 屬性。
     * 原因： 與 `CheckoutService` 類似，移除 `Propagation.REQUIRES_NEW` 是為了讓 `setCheckout` 方法與呼叫它的方法共享同一個 transaction
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void from_createOrder_to_returnWithPendingAccidentPriceInfo() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        // 步驟 13: 支付還車費用
        // 呼叫 payForReturnFee 方法，模擬會員支付還車產生的費用。
        // 此步驟會調用支付 API，模擬 TapPay OTP 驗證，並驗證還車費用付款是否成功。
        payForReturnFee(context);

        // 步驟 14: 設定車損費用
        // 呼叫 setAccidentPriceInfo 方法，模擬在收銀台設定車損費用。
        // 此步驟會調用設定車損費用 API，為訂單新增車損費用，模擬車輛在租賃期間發生事故產生額外費用的情境。
        setAccidentPriceInfo(context);

        // 步驟 15: 驗證在存在未支付費用的情況下，預期還車會失敗
        // 呼叫 returnCarExpectUnpaidError 方法，驗證在存在未支付的車損費用的情況下，
        // 系統是否會拒絕完成還車流程，並返回預期的錯誤碼。
        // 此步驟模擬在未支付車損費用的情況下嘗試完成還車，驗證系統的費用檢查機制。
        returnCarExpectUnpaidError(context);

        // 步驟 16: 驗證最終未付費用列表中不應包含未開放付款的費用
        // 呼叫 verifyWithoutPendingUnpaidPriceInfo 方法，驗證最終的未付費用列表中，
        // 是否不存在付款狀態為 "PENDING" (未開放) 的費用，確保所有未付費用都是可以支付的狀態。
        // 此步驟驗證系統在流程結束時的費用狀態，確保沒有遺留未處理的費用問題。
        verifyWithoutPendingUnpaidPriceInfo(context);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("新訂單出車前換車")
    void changeCarBeforeDepart() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 6, true, null, false, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        createOriginalLrentalContract(context);

        // 取得原始調度費
        OrderPriceInfo originalDispatchFee  = priceInfoService.getPriceInfoWrapper(context.orderNo).toFilter().category(Dispatch).type(Pay).stage(1).collect()
            .stream().findFirst().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        // 取得原始調度費開放付款時間
        Instant originalDispatchReceivableDate = originalDispatchFee.getReceivableDate();

        Cars newCar = null;
        List<Cars> idleCars = carsService.getIdleCar();
        for (Cars idleCar : idleCars) {
            CarBaseInfoSearchResponse carBaseInfoSearchResponse = crsService.getCar(idleCar.getPlateNo());
            if (carBaseInfoSearchResponse != null
                && BuIdEnum.subscribe.getCode().equals(carBaseInfoSearchResponse.getBuId())
                && Arrays.asList(CarDefine.Launched.open, CarDefine.Launched.close).contains(idleCar.getLaunched())
                && context.idleCar.getSubscribeLevel().equals(idleCar.getSubscribeLevel())) {
                newCar = idleCar;
                break;
            }
        }
        assertNotNull(newCar, "newCar should not be null");

        String newPlateNo = newCar.getPlateNo();
        OrderUpdateRequest orderUpdateRequest = new OrderUpdateRequest();
        orderUpdateRequest.setExpectDepartDate(context.order.getExpectStartDate());
        orderUpdateRequest.setDepartStationCode(context.contractCreateReq.getDepartStationCode());
        orderUpdateRequest.setReturnStationCode(context.contractCreateReq.getReturnStationCode());
        orderUpdateRequest.setPlateNo(newPlateNo);
        orderUpdateRequest.setMonth(9);

        mockMvc.perform(patch("/internal/subscribe/{orderNo}", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(orderUpdateRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        OrderPriceInfo updatedDispatchFee = priceInfoService.getPriceInfoWrapper(context.orderNo).toFilter().category(Dispatch).type(Pay).stage(1).collect()
            .stream().findFirst().orElseThrow(() -> new SubscribeException(ORDER_PRICE_INFO_NOT_FOUND));
        // 驗證調度費開放付款時間未異動
        assertEquals(originalDispatchReceivableDate, updatedDispatchFee.getReceivableDate());
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("測試更新已付款汽車用品費用失敗")
    void updatePaidMerchandisePriceInfoShouldFail() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 12, true, null, false, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);

        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        MerchandiseInfoRequest merchandiseInfoRequest = new MerchandiseInfoRequest();
        merchandiseInfoRequest.setMerchandiseList(new ArrayList<>());

        // 獲取現有所有的汽車用品費用
        priceInfoService.getUnPaidPriceInfoByOrderResponse(context.orderNo, true, true).stream()
            .filter(opi -> opi.getCategory() == Merchandise)
            .forEach(opi -> {
                MerchandiseInfoRequest.MerchandiseInfo merchandiseInfo = new MerchandiseInfoRequest.MerchandiseInfo();
                merchandiseInfo.setPriceInfoId(opi.getId());
                merchandiseInfo.setActualUnitPrice(opi.getInfoDetail().getActualUnitPrice());
                merchandiseInfo.setQuantity(opi.getInfoDetail().getQuantity());
                merchandiseInfo.setStage(opi.getStage());
                merchandiseInfo.setSkuCode(opi.getSkuCode());
                merchandiseInfo.setDelete(false);
                merchandiseInfoRequest.getMerchandiseList().add(merchandiseInfo);
            });

        // 新增汽車用品費用
        MerchandiseInfoRequest.MerchandiseInfo merchandiseInfo = new MerchandiseInfoRequest.MerchandiseInfo();
        merchandiseInfo.setPriceInfoId(null);
        merchandiseInfo.setActualUnitPrice(2880);
        merchandiseInfo.setQuantity(1);
        merchandiseInfo.setStage(1);
        merchandiseInfo.setSkuCode("recorder001");
        merchandiseInfo.setDelete(false);
        merchandiseInfoRequest.getMerchandiseList().add(merchandiseInfo);
        MvcResult addMerchandiseResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            // 確保 category 為 "Merchandise" 的項目數量為 3
            .andExpect(jsonPath("$.data[?(@.category == 'Merchandise')]", hasSize(3)))
            // 確保特定 skuCode 存在
            .andExpect(jsonPath("$.data[*].skuCode").value(hasItems("wipe001", "voucher001", "recorder001")))
            .andReturn();

        Thread.sleep(1000);

        // 異動所有待付款汽車用品費用實際單價，成功
        JsonNode dataNode = objectMapper.readTree(addMerchandiseResult.getResponse().getContentAsString()).get("data");
        List<Integer> unpaidMerchandisePriceInfoId = new ArrayList<>();
        for (JsonNode priceInfoNode : dataNode) {
            OrderPriceInfo orderPriceInfo = objectMapper.treeToValue(priceInfoNode, OrderPriceInfo.class);
            if (Merchandise == orderPriceInfo.getCategory() && !orderPriceInfo.isPaid()) {
                unpaidMerchandisePriceInfoId.add(orderPriceInfo.getId());
            }
        }
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> {
            if (unpaidMerchandisePriceInfoId.contains(m.getPriceInfoId())) {
                m.setActualUnitPrice(m.getActualUnitPrice() + 1);
            }
        });
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // 刪除所有待付款汽車用品費用，成功
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> {
            if (unpaidMerchandisePriceInfoId.contains(m.getPriceInfoId())) {
                m.setDelete(true);
            }
        });
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(1000);

        // 異動所有汽車用品費用實際單價(包含已付款)，報錯：該款項明細已付款，不可異動
        merchandiseInfoRequest.getMerchandiseList().forEach(m -> m.setActualUnitPrice(m.getActualUnitPrice() + 1));
        mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/merchandise", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(merchandiseInfoRequest)))
            .andExpect(jsonPath("$.statusCode").value(ORDER_PRICE_INFO_HAVE_PAID.getCode()))
            .andExpect(jsonPath("$.message").value(ORDER_PRICE_INFO_HAVE_PAID.getMsg()));
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void from_createOrder_to_executeLegalOperation_ReturnWithDamage() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(context.orderNo);
        List<Integer> etagPriceInfoIdsToWriteOff = orderPriceInfoList.stream()
            .filter(opi -> ETag == opi.getCategory() && Pay.getCode() == opi.getType() && !opi.isPaid())
            .map(OrderPriceInfo::getId)
            .collect(Collectors.toList());

        testLegalOperation(
            context.orderNo,
            LegalOperationReason.RETURNED_WITH_DAMAGE,
            9903,
            etagPriceInfoIdsToWriteOff,
            CarDefine.Launched.accident,
            true
        );
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    void from_createOrder_to_executeLegalOperation_ReturnWithUnpaid_ZeroAmount() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        assertNotNull(context.orderNo, "orderNo in context should not be null"); // 確保 orderNo 已被初始化

        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(context.orderNo);
        List<Integer> etagPriceInfoIdsToWriteOff = orderPriceInfoList.stream()
            .filter(opi -> ETag == opi.getCategory() && Pay.getCode() == opi.getType() && !opi.isPaid())
            .map(OrderPriceInfo::getId)
            .collect(Collectors.toList());

        // 測試保證金轉租金金額為 0 的情況，使用 testLegalOperation 但強制設為 0
        testLegalOperation(
            context.orderNo,
            LegalOperationReason.RETURNED_WITH_UNPAID,
            0, // 保證金轉租金金額設為 0
            etagPriceInfoIdsToWriteOff,
            null,
            true
        );
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("續約訂單出車後不會再次建立訂閱客戶約，續約訂單還車資料確認後檢查提前還車優惠回收里程是否正確")
    void renewalOrderDepartShouldNotRecreateLrentalContract() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 6, true, null, false, null);
        approveCreditManually(context);
        setMileageDiscount(context.orderNo);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 160);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);
        verifyCsatTaskCreation(context);
        verifyLrentalContractReCreated(context);
        verifyMileageSynchronizedWithCrs(context);

        // 續約
        renewOrder(context, SubscribeRenewMonth.SIX);

        returnCarConfirm(context);
        payForReturnFee(context);
        // 原訂單還車成功
        returnCarSuccess(context);

        assertNotNull(context.renewalOrderNo, "renewalOrderNo in context should not be null"); // 確保 renewalOrderNo 已被初始化
        // 使 context.orderNo 為續約訂單號
        context.orderNo = context.renewalOrderNo;
        approveCreditManually(context);
        // 續約且人工授信後，context.order 設為續約訂單，且其長租契約編號不為 null
        assertNotNull(context.order.getLrentalContractNo());
        context.renewalOrderLrentalContractNo = context.order.getLrentalContractNo();

        setMileageDiscount(context.orderNo);

        payForDepartFeeAndCheckOut(context);

        assertNotEquals(context.recreatedLrentalContractNo, context.renewalOrderLrentalContractNo, "續約訂單長租契約編號應該和原訂單長租契約編號不同");

        depart(context);

        assertEquals(context.renewalOrderLrentalContractNo, orderService.getOrder(context.orderNo).getLrentalContractNo(), "續約訂單出車後不應再重建訂閱客戶約");

        returnCarConfirm(context);
    }

    @Test
    @DisplayName("建立訂單，不需要自動授信，填寫不需要自動授信的原因")
    void createOrderWithAutoCreditBypass() throws Exception {
        OrderTestContext context = new OrderTestContext();
        String autoCreditBypassReason = "超級 VIP 客戶免送中華徵信";
        createOrder(context, 12, false, autoCreditBypassReason, false, null);
        Orders order = orderService.getOrder(context.orderNo);
        assertEquals(CreditRemarkType.AUTO_CREDIT_BYPASS, order.getCreditInfo().getAutoCreditInfo().get(0).getCreditRemarkType());
        assertEquals(autoCreditBypassReason, order.getCreditInfo().getManualCreditInfo().getManualCreditRemark());
    }

    @Test
    @DisplayName("建立訂單，需要自動授信")
    void createOrderWithAutoCredit() throws Exception {
        OrderTestContext context = new OrderTestContext();
        createOrder(context, 12, true, null, false, null);
        Orders order = orderService.getOrder(context.orderNo);
        assertNotNull(order.getCreditInfo().getAutoCreditInfo(), "自動授信資訊不應為空");
        assertNotEquals(CreditRemarkType.AUTO_CREDIT_BYPASS, order.getCreditInfo().getAutoCreditInfo().get(0).getCreditRemarkType());
        assertNull(order.getCreditInfo().getManualCreditInfo(), "人工授信資訊應為空");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），驗證 CarDepartEvent, OrderReturnedEvent, CarReturnEvent 已被發佈")
    void createOrderToReturnCarSuccess() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context, null);
        payForReturnFee(context);
        returnCarSuccess(context);

        assertNotNull(context.order.getLrentalContractNo(), "訂單長租契約編號不應為 null");

        // 驗證 CarDepartEvent, OrderReturnedEvent, CarReturnEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<CarDepartEvent> departEvents = eventCaptor.getAllValues().stream()
            .filter(CarDepartEvent.class::isInstance)
            .map(CarDepartEvent.class::cast)
            .filter(e -> e.getCarUpdateKmDto().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(departEvents.isEmpty(), "應至少有一個 CarDepartEvent 被發佈");
        CarDepartEvent departEvent = departEvents.get(0);
        assertEquals(context.orderNo, departEvent.getCarUpdateKmDto().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, departEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        List<OrderReturnedEvent> returnedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderReturnedEvent.class::isInstance)
            .map(OrderReturnedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(returnedEvents.isEmpty(), "應至少有一個 OrderReturnedEvent 被發佈");
        OrderReturnedEvent returnedEvent = returnedEvents.get(0);
        assertEquals(context.orderNo, returnedEvent.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, returnedEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        List<CarReturnEvent> returnEvents = eventCaptor.getAllValues().stream()
            .filter(CarReturnEvent.class::isInstance)
            .map(CarReturnEvent.class::cast)
            .filter(e -> e.getCarUpdateKmDto().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());
        assertFalse(returnEvents.isEmpty(), "應至少有一個 CarReturnEvent 被發佈");
        CarReturnEvent returnEvent = returnEvents.get(0);
        assertEquals(context.orderNo, returnEvent.getCarUpdateKmDto().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, returnEvent.getMemberId(), "事件中的 memberId 應與測試中使用的相符");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），再取消已還車，驗證 OrderReopenedEvent 已被發佈")
    void createOrderToReturnCarThenCancelReturnCarShouldPublishOrderReopenedEvent() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context, null);
        payForReturnFee(context);
        returnCarSuccess(context);

        // 取消已還車
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // 驗證 OrderReopenedEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<OrderReopenedEvent> reopenedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderReopenedEvent.class::isInstance)
            .map(OrderReopenedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());

        assertFalse(reopenedEvents.isEmpty(), "應至少有一個 OrderReopenedEvent 被發佈");

        OrderReopenedEvent event = reopenedEvents.get(0);
        assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getMemberId(), "事件中的 memberId 應與測試中使用的相符");

        int status = event.getOrder().getStatus();
        assertEquals(OrderStatus.ARRIVE_NO_CLOSE.getStatus(), status, "事件中的訂單狀態應為 ARRIVE_NO_CLOSE");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("從建立訂單到執行還車成功（不含續約），再取消已還車，然後結案，驗證 OrderClosedEvent 已被發佈")
    void createOrderToReturnCarThenCancelReturnCarThenOrderCloseShouldPublishOrderClosedEvent() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy/mock ApplicationEventPublisher 並注入到 OrderService
        ApplicationEventPublisher mockPublisher = Mockito.mock(ApplicationEventPublisher.class);
        ReflectionTestUtils.setField(orderService, "eventPublisher", mockPublisher);

        fromCreateOrderToReturnCarConfirm(context, null);
        payForReturnFee(context);
        returnCarSuccess(context);

        // 取消已還車
        mockMvc.perform(delete("/internal/subscribe/{orderNo}/return", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());

        // 執行訂單結案
        OrderCloseAgreeRequest orderCloseAgreeRequest = new OrderCloseAgreeRequest();
        String closeRemark = "取消已還車再結案";
        orderCloseAgreeRequest.setCloseRemark(closeRemark);
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/orderClose", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(orderCloseAgreeRequest)))
            .andExpect(status().isOk());

        // 驗證 OrderClosedEvent 是否被發佈
        ArgumentCaptor<ApplicationEvent> eventCaptor = ArgumentCaptor.forClass(ApplicationEvent.class);
        verify(mockPublisher, atLeastOnce()).publishEvent(eventCaptor.capture());
        List<OrderClosedEvent> closedEvents = eventCaptor.getAllValues().stream()
            .filter(OrderClosedEvent.class::isInstance)
            .map(OrderClosedEvent.class::cast)
            .filter(e -> e.getOrder().getOrderNo().equals(context.orderNo))
            .collect(Collectors.toList());

        assertFalse(closedEvents.isEmpty(), "應至少有一個 OrderClosedEvent 被發佈");

        OrderClosedEvent event = closedEvents.get(0);
        assertEquals(context.orderNo, event.getOrder().getOrderNo(), "事件中的訂單編號應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getMemberId(), "事件中的 memberId 應與測試中使用的相符");
        assertEquals(MEMBER_ID, event.getOrder().getCloseUser(), "事件中的 closeUser 應與測試中使用的相符");

        int status = event.getOrder().getStatus();
        assertTrue(
            status == OrderStatus.CLOSE.getStatus() || status == OrderStatus.ARRIVE_NO_CLOSE.getStatus(),
            "事件中的訂單狀態應為 CLOSE 或 ARRIVE_NO_CLOSE"
        );

        assertEquals(closeRemark, event.getCloseRemark(), "事件中的訂單結案備註應與測試中使用的相符");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("取消提前還車應該清除提前還車所產生的費用、清空 ETagInfo orderPriceInfoId，並且後續還車流程應該正常執行")
    void cancelEarlyReturnShouldClearPriceInfoAndETagInfoReferencesAndAllowSubsequentReturnFlow() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        String redisKey = ORDER_STATE + context.orderNo;

        CachedReturnEarlyOrder state = null;
        if (stringRedisTemplate.hasKey(redisKey)) {
            String stateJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (stateJson != null) {
                try {
                    state = objectMapper.readValue(stateJson, new TypeReference<CachedReturnEarlyOrder>() {
                    });
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        assert state != null;

        // 取消提前還車
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return/cancelReturnEarly", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證取消提前還車後的費用資訊
        List<OrderPriceInfo> orderPriceInfoList = priceInfoService.getPriceInfosByOrder(context.orderNo);
        assertEquals(state.getOrderPriceInfos().size(), orderPriceInfoList.size(), "取消提前還車後應該恢復為提前還車前的費用資訊");
        for (int i = 0; i < state.getOrderPriceInfos().size(); i++) {
            OrderPriceInfo orderPriceInfo = orderPriceInfoList.get(i);
            OrderPriceInfoDTO stateOrderPriceInfo = state.getOrderPriceInfos().get(i);
            assertEquals(stateOrderPriceInfo.getId(), orderPriceInfo.getId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getStage(), orderPriceInfo.getStage(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getCategory(), orderPriceInfo.getCategory(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getType(), orderPriceInfo.getType(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getAmount(), orderPriceInfo.getAmount(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getReceivedAmount(), orderPriceInfo.getReceivedAmount(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getInfoDetail(), orderPriceInfo.getInfoDetail(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRemitAccountIds(), orderPriceInfo.getRemitAccountIds(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getPaymentId(), orderPriceInfo.getPaymentId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRecTradeId(), orderPriceInfo.getRecTradeId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRefundId(), orderPriceInfo.getRefundId(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getRefPriceInfoNo(), orderPriceInfo.getRefPriceInfoNo(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertEquals(stateOrderPriceInfo.getUid(), orderPriceInfo.getUid(), "取消提前還車後應該恢復為提前還車前的費用資訊");
            assertTrue((orderPriceInfo.isPaid() && stateOrderPriceInfo.revertToEntity().isPaid()
                || !orderPriceInfo.isPaid() && !stateOrderPriceInfo.revertToEntity().isPaid()));
        }

        Orders order = orderService.getOrder(context.orderNo);
        assertNull(order.getEndDate(), "取消提前還車後應該清除提前還車所產生的實際還車時間");
        assertNull(order.getReturnMileage(), "取消提前還車後應該清除提前還車所產生的實際還車里程");

        ETagInfo etagInfo = etagService.getLatestNotReturnETagInfo(order, true);
        assertEquals(state.getEtagInfo().revertToEntity(), etagInfo, "ETagInfo 應該還原為提前還車前的狀態");
        
        // 驗證任何 ETagInfo 記錄都不應該引用已刪除的 OrderPriceInfo
        List<ETagInfo> allEtagInfosForOrder = etagInfoRepository.getETagInfosByOrderNo(context.orderNo);
        for (ETagInfo info : allEtagInfosForOrder) {
            if (info.getOrderPriceInfoId() != null) {
                OrderPriceInfo referencedPriceInfo = priceInfoService.get(info.getOrderPriceInfoId());
                assertNotNull(referencedPriceInfo, 
                    "ETagInfo (id=" + info.getId() + ") references OrderPriceInfo (id=" + info.getOrderPriceInfoId() + ") that doesn't exist");
            }
        }
        
        // 驗證取消提前還車後能正常執行還車流程 - 模擬原始問題情境
        CarDropOffRequest returnRequest = new CarDropOffRequest();
        returnRequest.setReturnDate(new Date());
        returnRequest.setReturnMileage(context.car.getCurrentMileage() + 2000); // 基於車輛當前里程數動態計算
        returnRequest.setReturnRemark("取消提前還車後的正常還車");
        
        // 這個操作在修復前會拋出 EntityNotFoundException，修復後應該能正常執行
        assertDoesNotThrow(() -> {
            mockMvc.perform(patch("/internal/subscribe/{orderNo}/return", context.orderNo)
                    .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(returnRequest)))
                .andExpect(jsonPath("$.statusCode").value("0"));
        }, "取消提前還車後應該能正常執行還車確認，不應該出現 EntityNotFoundException");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("取消提前還車應該清除提前還車所產生的費用、提前還車後有付款則不應清空 order 實際還車時間 & 里程和 ETagInfo orderPriceInfoId")
    void cancelEarlyReturnShouldClearPriceInfoGeneratedAfterReturnCarConfirmAndClearOrderAndETagInfoStateIfNoPriceInfoIsPaid() throws Exception {

        OrderTestContext context = new OrderTestContext();

        fromCreateOrderToReturnCarConfirm(context, null);

        String redisKey = ORDER_STATE + context.orderNo;

        CachedReturnEarlyOrder state = null;
        if (stringRedisTemplate.hasKey(redisKey)) {
            String stateJson = stringRedisTemplate.opsForValue().get(redisKey);
            if (stateJson != null) {
                try {
                    state = objectMapper.readValue(stateJson, new TypeReference<CachedReturnEarlyOrder>() {
                    });
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        assert state != null;

        // 步驟 13: 支付還車費用
        payForReturnFee(context);

        Map<Integer, OrderPriceInfo> priceInfoMapAfterPayingReturnFee = priceInfoService.getPriceInfosByOrder(context.orderNo).stream()
            .peek(orderPriceInfo -> assertTrue(orderPriceInfo.isPaid(), "支付還車費用後所有里程費應為已付款"))
            .collect(Collectors.toMap(OrderPriceInfo::getId, Function.identity()));

        // 新增額外費用 - 測試不受 stage 限制
        int currentStage = orderService.getOrder(context.orderNo).getCurrentStage().getStage();
        int earlierStage = Math.max(1, currentStage - 1); // 使用早於當前期數的 stage
        assertNotEquals(earlierStage, currentStage, "earlierStage 不應該等於 currentStage");

        ExtraFeeRequest extraFeeRequest = new ExtraFeeRequest();
        extraFeeRequest.setExtraFeeList(Collections.singletonList(ExtraFeeRequest.ExtraFee.builder()
            .amount(1000)
            .stage(earlierStage) // 使用早於當前期數的 stage 來驗證不受限制
            .reason("test early stage fee")
            .category(Others).build()));
        MvcResult setExtraFeeResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(extraFeeRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        List<OrderPriceInfo> extraFeeInfos = objectMapper.readValue(objectMapper.readTree(setExtraFeeResult.getResponse().getContentAsString()).get("data").toString(), new TypeReference<List<OrderPriceInfo>>() {
        });
        OrderPriceInfo extraFee = extraFeeInfos.stream().max(Comparator.comparing(OrderPriceInfo::getId)).get();
        assertFalse(extraFee.isPaid());
        assertNotNull(extraFee.getInfoDetail().getAdminId());
        assertEquals(MEMBER_ID, extraFee.getInfoDetail().getAdminId());
        assertNotNull(extraFee.getUpdater());
        assertEquals(MEMBER_ID, extraFee.getUpdater());

        // 新增的額外費用第一次更新，確保 infoDetail.adminId 和 updater 不會被更新
        ExtraFeeRequest updateExtraFeeRequest = new ExtraFeeRequest();
        updateExtraFeeRequest.setExtraFeeList(Collections.singletonList(ExtraFeeRequest.ExtraFee.builder()
            .priceInfoId(extraFee.getId())
            .amount(1000)
            .stage(earlierStage)
            .reason("test early stage fee")
            .category(Others).build()));
        MvcResult updatedExtraFeeResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", context.orderNo)
                // 使用不同的 memberId 來模擬不同的人更新費用
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID + "1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateExtraFeeRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        List<OrderPriceInfo> updatedExtraFees = objectMapper.readValue(objectMapper.readTree(updatedExtraFeeResult.getResponse().getContentAsString()).get("data").toString(), new TypeReference<List<OrderPriceInfo>>() {
        });
        OrderPriceInfo updatedExtraFee = updatedExtraFees.stream().max(Comparator.comparing(OrderPriceInfo::getId)).get();
        assertEquals(MEMBER_ID, updatedExtraFee.getInfoDetail().getAdminId());
        assertEquals(MEMBER_ID, updatedExtraFee.getUpdater());

        // 第二次更新，再次確認 adminId 不變，updater 會更新
        ExtraFeeRequest secondUpdateRequest = new ExtraFeeRequest();
        secondUpdateRequest.setExtraFeeList(Collections.singletonList(ExtraFeeRequest.ExtraFee.builder()
            .priceInfoId(updatedExtraFee.getId())
            .amount(2000)
            .stage(earlierStage)
            .reason("test early stage fee")
            .category(Others).build()));
        MvcResult secondUpdatedResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", context.orderNo)
                // 使用第三個不同的 memberId 來模擬第三個人更新費用
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID + "2")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(secondUpdateRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        List<OrderPriceInfo> secondUpdatedExtraFees = objectMapper.readValue(objectMapper.readTree(secondUpdatedResult.getResponse().getContentAsString()).get("data").toString(), new TypeReference<List<OrderPriceInfo>>() {
        });
        OrderPriceInfo secondUpdatedExtraFee = secondUpdatedExtraFees.stream().max(Comparator.comparing(OrderPriceInfo::getId)).get();
        assertEquals(2000, secondUpdatedExtraFee.getAmount());
        // 確保 adminId 仍然保持為第一次新增的人
        assertEquals(MEMBER_ID, secondUpdatedExtraFee.getInfoDetail().getAdminId());
        // 確保 updater 被更新為第二次更新的人
        assertEquals(MEMBER_ID + "2", secondUpdatedExtraFee.getUpdater());

        // 驗證可以在第 1 期新增額外費用 (測試不受 stage 限制)
        ExtraFeeRequest firstStageExtraFeeRequest = new ExtraFeeRequest();
        firstStageExtraFeeRequest.setExtraFeeList(Collections.singletonList(ExtraFeeRequest.ExtraFee.builder()
            .amount(500)
            .stage(1) // 明確使用第 1 期
            .reason("first stage fee - no stage restriction test")
            .category(Dispatch).build())); // 使用不同的 category 來區分
        MvcResult firstStageResult = mockMvc.perform(post("/internal/subscribe/priceInfo/{orderNo}/extraFee", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(firstStageExtraFeeRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andReturn();
        List<OrderPriceInfo> firstStageExtraFees = objectMapper.readValue(
            objectMapper.readTree(firstStageResult.getResponse().getContentAsString()).get("data").toString(),
            new TypeReference<List<OrderPriceInfo>>() {});
        OrderPriceInfo firstStageExtraFee = firstStageExtraFees.stream()
            .filter(fee -> fee.getCategory() == Dispatch)
            .max(Comparator.comparing(OrderPriceInfo::getId))
            .orElseThrow(() -> new AssertionError("應該要能成功新增第 1 期的調度費"));
        assertEquals(500, firstStageExtraFee.getAmount());
        assertEquals(1, firstStageExtraFee.getStage());
        assertFalse(firstStageExtraFee.isPaid());

        // 取消提前還車
        mockMvc.perform(patch("/internal/subscribe/{orderNo}/return/cancelReturnEarly", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證取消提前還車後的費用資訊
        Map<Boolean, List<OrderPriceInfo>> orderPriceInfoListAfterCancelReturnEarly = priceInfoService.getPriceInfosByOrder(context.orderNo)
            .stream().collect(Collectors.groupingBy(OrderPriceInfo::isPaid));
        for (OrderPriceInfo paidOrderPriceInfoAfterCancelReturnEarly : orderPriceInfoListAfterCancelReturnEarly.get(true)) {
            OrderPriceInfo orderPriceInfoAfterPayingReturnFee = priceInfoMapAfterPayingReturnFee.get(paidOrderPriceInfoAfterCancelReturnEarly.getId());
            assertEquals(orderPriceInfoAfterPayingReturnFee, paidOrderPriceInfoAfterCancelReturnEarly, "取消提前還車後應該恢復為支付還車費用後的費用資訊");
            assertEquals(orderPriceInfoAfterPayingReturnFee.getInfoDetail(), paidOrderPriceInfoAfterCancelReturnEarly.getInfoDetail(), "取消提前還車後應該恢復為支付還車費用後的費用資訊");
        }

        // 取消提前還車後的費用資訊中，未付款的費用應該有兩筆額外費用 (Others 和 Dispatch)
        List<OrderPriceInfo> unpaidOrderPriceInfosAfterCancelReturnEarly = orderPriceInfoListAfterCancelReturnEarly.get(false);
        assertEquals(2, unpaidOrderPriceInfosAfterCancelReturnEarly.size(), "取消提前還車後應該有兩筆未付款的額外費用");

        // 驗證 Others 費用存在
        boolean hasOthersFee = unpaidOrderPriceInfosAfterCancelReturnEarly.stream()
            .anyMatch(fee -> Objects.equals(fee.getId(), extraFee.getId()));
        assertTrue(hasOthersFee, "取消提前還車後，在提前還車且支付費用後新增的 Others 額外費用應該不變");

        // 驗證 Dispatch 費用存在
        boolean hasDispatchFee = unpaidOrderPriceInfosAfterCancelReturnEarly.stream()
            .anyMatch(fee -> Objects.equals(fee.getId(), firstStageExtraFee.getId()));
        assertTrue(hasDispatchFee, "取消提前還車後，新增的第1期 Dispatch 額外費用應該存在");

        Orders order = orderService.getOrder(context.orderNo);
        assertNotNull(order.getEndDate(), "取消提前還車後若有付款不應清除提前還車所產生的實際還車時間");
        assertNotNull(order.getReturnMileage(), "取消提前還車後若有付款不應清除提前還車所產生的實際還車里程");

        ETagInfo etagInfo = etagService.getLatestNotReturnETagInfo(order, true);
        assertNotEquals(state.getEtagInfo().revertToEntity(), etagInfo, "取消提前還車後若有付款 ETagInfo 不應還原為提前還車前的狀態");
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("測試出車中換車功能")
    void replaceCarDuringRental() throws Exception {
        // 1. 建立測試上下文
        OrderTestContext context = new OrderTestContext();

        // 2. 建立訂單並完成出車流程
        createOrder(context, 6, true, null, false, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);
        createOriginalLrentalContract(context);
        departUpdateOrder(context, 10);
        accountSettlementAndCheckOutForDepart(context);
        depart(context);

        // 3. 確認車輛已出車
        Orders order = orderService.getOrder(context.orderNo);
        assertEquals(OrderStatus.DEPART.getStatus(), order.getStatus());

        // 4. 找一台可替換的車輛 (相同訂閱方案，buId 為 1 或 4)
        String originalPlateNo = context.mainContract.getPlateNo();
        Cars originalCar = carsService.findByPlateNo(originalPlateNo);
        assertNotNull(originalCar, "原始車輛不應為null");

        // 找一台閒置且符合條件的車輛
        List<Cars> idleCars = carsService.getIdleCar();
        Cars replacementCar = null;
        for (Cars car : idleCars) {
            if ((car.getLaunched() != CarDefine.Launched.open && car.getLaunched() != CarDefine.Launched.close)
                || car.getPlateNo().equals(originalPlateNo)
                || !car.getSubscribeLevel().equals(originalCar.getSubscribeLevel())) {
                continue;
            }
            CarBaseInfoSearchResponse carInfo = crsService.getCar(car.getPlateNo());
            if (carInfo != null
                && (carInfo.getBuId().equals(BuIdEnum.lRental.getCode()) || carInfo.getBuId().equals(BuIdEnum.subscribe.getCode()))
                && CRS.LicenseStatus.BUY.getCode().equals(carInfo.getCarLicense().getLicenseStatus())) {
                replacementCar = car;
                break;
            }
        }

        assertNotNull(replacementCar, "找不到符合條件的替換車輛");

        // 5. 建立換車請求
        CarReplaceRequest carReplaceRequest = new CarReplaceRequest();
        carReplaceRequest.setReplaceDate(Date.from(Instant.now()));
        carReplaceRequest.setInCarPlateNo(replacementCar.getPlateNo());
        carReplaceRequest.setInCarStartMileage(replacementCar.getCurrentMileage());
        carReplaceRequest.setOutCarEndMileage(originalCar.getCurrentMileage() + 1000);
        carReplaceRequest.setLrContractReplaceCode(Arrays.asList("1", "2", "3"));
        carReplaceRequest.setLrContractMemo("出車中換車備註測試");

        // 6. 呼叫換車API
        mockMvc.perform(post("/internal/subscribe/{orderNo}/{outCarPlateNo}/replace", context.orderNo, originalPlateNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.HEADER_COMPANY_CODE, "carplus")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(objectMapper.writeValueAsString(carReplaceRequest)))
            .andExpect(jsonPath("$.statusCode").value("0"));

        Thread.sleep(3000);

        // 7. 驗證換車後訂單狀態
        Orders updatedOrder = orderService.getOrder(context.orderNo);
        assertEquals(OrderStatus.DEPART.getStatus(), updatedOrder.getStatus());
        assertEquals(replacementCar.getPlateNo(), updatedOrder.getPlateNo());

        // 8. 呼叫收銀台訂單查詢API，驗證 replacedPlateNo 欄位
        OrdersCriteria criteria = new OrdersCriteria();
        criteria.setOrderNo(context.orderNo);
        criteria.setSkip(0);
        criteria.setLimit(10);
        criteria.setPlateNo(Collections.singleton(replacementCar.getPlateNo()));

        mockMvc.perform(get("/internal/subscribe/order/query")
                .contentType(MediaType.APPLICATION_JSON)
                .param("orderNo", criteria.getOrderNo())
                .param("skip", String.valueOf(criteria.getSkip()))
                .param("limit", String.valueOf(criteria.getLimit()))
                .param("plateNo", criteria.getPlateNo().iterator().next()))
            .andExpect(jsonPath("$.statusCode").value("0"))
            .andExpect(jsonPath("$.data.page.list").isArray())
            .andExpect(jsonPath("$.data.page.list.length()").value(1))
            .andExpect(jsonPath("$.data.page.list[0].orderNo").value(context.orderNo))
            .andExpect(jsonPath("$.data.page.list[0].plateNo").value(replacementCar.getPlateNo()))
            .andExpect(jsonPath("$.data.page.list[0].replacedPlateNo").value(originalPlateNo));

        // 9. 驗證原車狀態已更新
        Cars originalCarAfterReplace = carsService.findByPlateNo(originalPlateNo);
        assertNotEquals(CarDefine.CarStatus.BizOut.getCode(), originalCarAfterReplace.getCarStatus());

        // 10. 驗證新車狀態已更新
        Cars replacementCarAfterReplace = carsService.findByPlateNo(replacementCar.getPlateNo());
        assertEquals(CarDefine.CarStatus.BizOut.getCode(), replacementCarAfterReplace.getCarStatus());

        // 11. 取得所有 ETagInfo
        List<EtagInfoResponse> etagInfoList = etagService.getETagIntoByOrderNo(context.orderNo);

        // 12. 所有 ETagInfo 出車之車牌號碼不應為空
        etagInfoList.forEach(etagInfo -> assertNotNull(etagInfo.getPlateNo(), "ETagInfo 出車之車牌號碼不應為空"));

        // 13. 至少有一筆 ETagInfo 的 orderPriceInfoId 不為空
        assertTrue(etagInfoList.stream().anyMatch(etagInfo -> etagInfo.getOrderPriceInfoId() != null), "至少應有一筆 ETagInfo 的 orderPriceInfoId 不為空");

        // 14. 下載 ETagInfo.orderPriceInfoId 不為空的遠通通行費明細報表
        etagInfoList.stream().filter(etagInfo -> etagInfo.getOrderPriceInfoId() != null).forEach(etagInfo -> {
            EtagPdfExportRequest request = new EtagPdfExportRequest();
            request.setEtagPriceInfoId(etagInfo.getOrderPriceInfoId());
            try {
                MvcResult result = mockMvc.perform(get("/internal/subscribe/etag/export/pdf")
                        .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                        .param("etagPriceInfoId", String.valueOf(request.getEtagPriceInfoId()))
                        .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                    .andExpect(header().string("Content-Disposition", Matchers.containsString("attachment; filename=")))
                    .andReturn();

                // 驗證回應內容不為空
                byte[] content = result.getResponse().getContentAsByteArray();
                assertNotNull(content, "PDF 內容不應為空");
                assertTrue(content.length > 0, "PDF 內容長度應大於 0");

                // PDFBox 3.x 使用 Loader.loadPDF()
                try (PDDocument document = Loader.loadPDF(content)) {
                    PDFTextStripper pdfStripper = new PDFTextStripper();
                    String pdfText = pdfStripper.getText(document);

                    log.info("解析出的 PDF 文字內容: {}", pdfText);

                    // 驗證關鍵內容
                    assertTrue(pdfText.contains("格上汽車租賃股份有限公司"), "應包含公司名稱");
                    assertTrue(pdfText.contains("遠通電收計價明細表"), "應包含明細表標題");
                    assertTrue(pdfText.contains("合約編號"), "應包含合約編號欄位");
                    assertTrue(pdfText.contains("車牌號碼"), "應包含車牌號碼欄位");
                    assertTrue(pdfText.contains("計價區間"), "應包含計價區間");
                    assertTrue(pdfText.contains("門架牌價總金額"), "應包含總金額");
                    assertTrue(pdfText.contains("過站時間"), "應包含過站時間");
                    assertTrue(pdfText.contains("通行費"), "應包含通行費");

                    // 驗證數據內容
                    assertTrue(pdfText.contains("南下") || pdfText.contains("北上"), "應包含行車方向");
                    assertTrue(pdfText.contains("國三") || pdfText.contains("國五"), "應包含高速公路資訊");

                } catch (IOException e) {
                    fail("無法解析 PDF 文件: " + e.getMessage());
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("建立長租契約 - 車籍庫位為訂閱且有撥車申請單編號")
    void createLrentalContract_WithSubscriptionBuIdAndDispatchNumber_ShouldSucceed() throws Exception {
        OrderTestContext context = new OrderTestContext();

        createOrder(context, 12, true, null, true, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);

        // 建立長租契約
        assertDoesNotThrow(() -> {
            createOriginalLrentalContract(context);
            log.info("測試成功 - 車籍庫位為訂閱且有撥車申請單編號時，成功建立長租契約: {}", context.order.getLrentalContractNo());
        });
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("建立長租契約 - 車籍庫位為訂閱但無撥車申請單編號")
    void createLrentalContract_WithSubscriptionBuIdButNoDispatchNumber_ShouldSucceed() throws Exception {
        OrderTestContext context = new OrderTestContext();

        createOrder(context, 12, true, null, false, null);
        approveCreditManually(context);
        payForSecurityDeposit(context);
        payForDepartFeeAndCheckOut(context);

        // 建立長租契約
        assertDoesNotThrow(() -> {
            createOriginalLrentalContract(context);
            log.info("測試成功 - 車籍庫位為訂閱但無撥車申請單編號時，成功建立長租契約: {}", context.order.getLrentalContractNo());
        });
    }

    @Test
    void shouldExportCarWishlistReportCsv() throws Exception {
        // 1. 準備測試資料 - 取得所有非空且不重複的 acctId
        List<Integer> acctIds = Arrays.asList(2400051, 1000440, 33456914, 1000343, 1000644, 218209, 389154, 1000214, 242699, 33457250);

        // 取得閒置車輛列表
        List<Cars> idleCars = carsService.getIdleCar().stream()
            .filter(Cars::isOrderableOfficially)
            .limit(100) // 取得足夠的車輛數量
            .collect(Collectors.toList());

        assertFalse(idleCars.isEmpty(), "應該要有可用的閒置車輛");

        // 2. 建立收藏清單資料
        Random random = new Random();

        for (Cars car : idleCars) {
            // 隨機選擇一個 acctId
            Integer randomAcctId = acctIds.get(random.nextInt(acctIds.size()));

            CarWishlistRequest wishlistRequest = new CarWishlistRequest();
            wishlistRequest.setPlateNo(car.getPlateNo());

            try {
                mockMvc.perform(post("/subscribe/carWishlist")
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(CarPlusConstant.AUTH_HEADER_ACCT, randomAcctId)
                        .content(objectMapper.writeValueAsString(wishlistRequest)))
                    .andExpect(jsonPath("$.statusCode").value("0"));

                Thread.sleep(200); // 避免請求過快

            } catch (SubscribeException e) {
                // 若遇到車輛已在收藏清單的異常則跳過
                if (e.getCode().equals(SubscribeHttpExceptionCode.CAR_ALREADY_IN_WISHLIST)) {
                    log.info("車輛已在收藏清單中，略過：{}", car.getPlateNo());
                }
            }
        }

        // 3. 測試報表匯出 API
        MvcResult result = mockMvc.perform(get("/internal/subscribe/carWishlist/csv/export")
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .param("startTime", String.valueOf(Instant.parse("2025-04-09T00:00:00Z").toEpochMilli()))
                .param("endTime", String.valueOf(Instant.now().toEpochMilli())))
            .andExpect(status().isOk())
            .andReturn();

        // 驗證 Content-Disposition header
        String contentDisposition = result.getResponse().getHeader("Content-Disposition");
        assertNotNull(contentDisposition, "Content-Disposition 標頭不可為 null");

        String expectedFileNamePattern = "SUB_car_wishlist_report_\\d{8}\\.csv";
        assertTrue(contentDisposition.matches(".*" + expectedFileNamePattern + ".*"),
            "Content-Disposition 應包含正確的檔案名稱格式");

        // 4. 驗證 CSV 內容
        byte[] csvBytes = result.getResponse().getContentAsByteArray();
        assertTrue(csvBytes.length > 0, "CSV 內容不應為空");

        // 使用 big5 編碼解析 CSV
        String csvContent = new String(csvBytes, Charset.forName("big5"));
        String[] lines = csvContent.split("\n");
        assertTrue(lines.length > 1, "CSV 應包含標題列與至少一筆資料列");

        // 驗證 CSV 標題列
        String headerLine = lines[0];
        String[] expectedHeaders = {
            "客戶姓名", "手機號碼", "收藏總台數", "加入收藏日期", "車牌號碼",
            "廠牌名稱", "車型名稱", "訂閱類別", "出廠年份", "訂閱方案名稱",
            "庫存狀態", "是否已訂閱", "收藏時月費", "收藏時里程費率", "收藏時保證金"
        };

        for (String expectedHeader : expectedHeaders) {
            assertTrue(headerLine.contains(expectedHeader),
                "CSV 標題應包含欄位：「" + expectedHeader + "」");
        }

        // 驗證資料列
        for (int i = 1; i < lines.length; i++) {
            String dataLine = lines[i].trim();
            if (dataLine.isEmpty()) continue;

            String[] columns = dataLine.split(",");
            assertTrue(columns.length >= expectedHeaders.length,
                "資料列應包含所有必要欄位");

            // 驗證客戶姓名不為空
            assertFalse(columns[0].trim().isEmpty(), "客戶姓名不可為空");

            // 驗證手機號碼不為空且格式正確
            String phoneNumber = columns[1].trim().replaceAll("\"", "");
            assertFalse(phoneNumber.isEmpty(), "手機號碼不可為空");
            assertTrue(phoneNumber.matches("\\d{10}"), "手機號碼應為 10 位數字");

            // 驗證收藏總台數為正整數
            String totalCount = columns[2].trim().replaceAll("\"", "");
            assertTrue(Integer.parseInt(totalCount) > 0, "收藏總台數應為正整數");

            // 驗證車牌號碼不為空
            String plateNo = columns[4].trim().replaceAll("\"", "");
            assertFalse(plateNo.isEmpty(), "車牌號碼不可為空");

            // 驗證是否已訂閱欄位值
            String isSubscribed = columns[11].trim().replaceAll("\"", "");
            assertTrue(isSubscribed.equals("是") || isSubscribed.equals("否"),
                "「是否已訂閱」欄位應為「是」或「否」");

            // 驗證數值欄位（月費、里程費率、保證金）
            for (int j = 12; j < 15; j++) {
                String numericValue = columns[j].trim().replaceAll("\"", "");
                if (!numericValue.isEmpty()) {
                    try {
                        Double.parseDouble(numericValue);
                    } catch (NumberFormatException e) {
                        fail("第 " + j + " 欄的數值欄位應為有效數字或空白：" + numericValue);
                    }
                }
            }
        }

        log.info("成功匯出收藏車輛報表，共 {} 筆資料列", lines.length - 1);
    }

    /**
     * 建立 AccountSettlementRequest，支援混合付款方式
     */
    private AccountSettlementRequest createMixedPaymentAccountSettlementRequest(OrderTestContext context, List<AccountRecord> accountRecords, PayFor payFor) {

        AccountSettlementRequest accountSettlementRequest = new AccountSettlementRequest();

        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setStationCode(payFor == Depart ?
            context.mainContract.getDepartStationCode() :
            context.mainContract.getReturnStationCode());
        paymentRequest.setAccountRecords(accountRecords);
        accountSettlementRequest.setPaymentRequest(paymentRequest);

        // 建立發票請求
        InvoiceNewRequest invoiceNewRequest = new InvoiceNewRequest();
        invoiceNewRequest.setStationCode("233");
        invoiceNewRequest.setPayFor(payFor.name());

        List<InvoiceRequest> invoiceRequests = new ArrayList<>();

        // 如果是還車且有需要開發票的費用項目，則創建發票請求
        if (payFor == Return && accountRecords != null && !accountRecords.isEmpty()) {
            // 計算實際支付的總金額（使用 AccountRecord 的 amount 欄位）
            int totalPaidAmount = accountRecords.stream()
                .filter(record -> !record.isDeleted())
                .mapToInt(AccountRecord::getAmount)
                .sum();

            // 收集所有 AccountRecord 中涉及的費用項目ID
            Set<Integer> paidPriceInfoIds = accountRecords.stream()
                .filter(record -> !record.isDeleted())
                .map(AccountRecord::getOrderPriceInfoAmounts)
                .filter(Objects::nonNull)
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toSet());

            if (totalPaidAmount > 0 && !paidPriceInfoIds.isEmpty()) {
                InvoiceRequest invoiceRequest = new InvoiceRequest();
                invoiceRequest.setInvoice(context.order.getInvoice());
                invoiceRequest.setAmount(totalPaidAmount);
                invoiceRequest.setRefPriceInfoIds(new ArrayList<>(paidPriceInfoIds));
                invoiceRequest.setMemo(payFor.name());
                invoiceRequests.add(invoiceRequest);

                log.info("創建還車發票請求: 金額={}, 費用項目ID={}", totalPaidAmount, paidPriceInfoIds);
            }
        }

        invoiceNewRequest.setInvoices(invoiceRequests);
        accountSettlementRequest.setInvoiceNewRequest(invoiceNewRequest);

        return accountSettlementRequest;
    }

    /**
     * 構建包含 originalAmount 的 JSON 字串
     * 解決 @JsonProperty(access = JsonProperty.Access.WRITE_ONLY) 的序列化問題
     */
    private String buildJsonWithOriginalAmount(AccountSettlementRequest request) throws JsonProcessingException {
        // 先序列化到 JsonNode
        JsonNode jsonNode = objectMapper.valueToTree(request);

        // 檢查並手動設置 originalAmount 值
        if (jsonNode.has("paymentRequest") &&
            jsonNode.get("paymentRequest").has("accountRecords")) {

            ArrayNode accountRecords = (ArrayNode) jsonNode.get("paymentRequest").get("accountRecords");
            List<AccountRecord> originalRecords = request.getPaymentRequest().getAccountRecords();

            for (int i = 0; i < accountRecords.size() && i < originalRecords.size(); i++) {
                ObjectNode accountRecord = (ObjectNode) accountRecords.get(i);
                AccountRecord originalRecord = originalRecords.get(i);

                // 手動設置 originalAmount 值
                Integer originalAmount = originalRecord.getOriginalAmount();
                if (originalAmount != null) {
                    accountRecord.put("originalAmount", originalAmount);
                    log.debug("為 AccountRecord[{}] 設置 originalAmount: {}", i, originalAmount);
                }
            }
        }

        // 轉換回字串
        String result = objectMapper.writeValueAsString(jsonNode);
        log.debug("構建的完整 JSON: {}", result);
        return result;
    }


    /**
     * 實際查詢匯款資料
     */
    private List<ICBCRep> queryRemittanceData(String startDate, String endDate) {
        try {
            Result<List<ICBCRep>> result = financeClient.getRemitInfo(
                "", // 客戶統編
                startDate, // 匯款起日
                endDate, // 匯款迄日
                "", // 部門代碼 - 空字串表示查詢所有部門
                null // null 表示查詢所有匯款編號
            );

            if (result.getStatusCode() == 0 && result.getData() != null) {
                log.info("查詢到 {} 筆匯款資料", result.getData().size());
                return result.getData();
            } else {
                log.warn("查詢匯款資料失敗: statusCode={}, message={}",
                    result.getStatusCode(), result.getMessage());
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("呼叫匯款查詢 API 發生錯誤", e);
            return new ArrayList<>();
        }
    }

    /**
     * 計算還車實際需要支付的金額
     */
    private int calculateReturnPaymentAmount(String orderNo) {
        List<OrderPriceInfo> unpaidPriceInfosAfterReturnCarConfirm = priceInfoService.getUnPaidPriceInfoByOrder(orderNo, false);
        int unpaidAmountAfterReturnCarConfirm = unpaidPriceInfosAfterReturnCarConfirm.stream()
            .filter(orderPriceInfo -> orderPriceInfo.getType() == Pay.getCode() && !orderPriceInfo.isPaid())
            .map(PriceInfoInterface::getActualPrice)
            .reduce(Integer::sum)
            .orElse(0);

        log.info("計算還車需要支付金額: {} 元", unpaidAmountAfterReturnCarConfirm);
        return unpaidAmountAfterReturnCarConfirm;
    }

    /**
     * 選擇匯款資料，確保總金額完全符合目標金額
     */
    private List<ICBCRep> selectExactMatchRemittanceData(List<ICBCRep> remittanceList, int exactAmount) {
        // 篩選可用的匯款資料 (狀態為已匯入且金額大於0)
        List<ICBCRep> availableData = remittanceList.stream()
            .filter(icbc -> "已匯入".equals(icbc.getStatus()) && icbc.getTxAmount() != null && icbc.getTxAmount() > 0)
            .collect(Collectors.toList());

        if (availableData.isEmpty()) {
            log.warn("沒有找到可用的匯款資料");
            return new ArrayList<>();
        }

        // 使用動態規劃尋找完全符合的組合
        List<ICBCRep> bestMatch = findExactAmountCombination(availableData, exactAmount);

        if (!bestMatch.isEmpty()) {
            int totalAmount = bestMatch.stream().mapToInt(ICBCRep::getTxAmount).sum();
            log.info("找到符合的匯款組合: {} 筆，總金額: {} 元", bestMatch.size(), totalAmount);
            return bestMatch;
        }

        // 如果找不到完全符合，嘗試找接近的組合
        List<ICBCRep> approximateMatch = findApproximateMatch(availableData, exactAmount);
        if (!approximateMatch.isEmpty()) {
            int totalAmount = approximateMatch.stream().mapToInt(ICBCRep::getTxAmount).sum();
            log.info("找到近似符合的匯款組合: {} 筆，總金額: {} 元（目標: {} 元）",
                approximateMatch.size(), totalAmount, exactAmount);
            return approximateMatch;
        }

        log.warn("無法找到合適的匯款組合來符合目標金額: {} 元", exactAmount);
        return new ArrayList<>();
    }

    /**
     * 使用動態規劃尋找完全符合金額的匯款組合
     */
    private List<ICBCRep> findExactAmountCombination(List<ICBCRep> availableData, int targetAmount) {
        int n = availableData.size();
        // dp[i][j] 表示使用前i個匯款是否能湊出金額j
        boolean[][] dp = new boolean[n + 1][targetAmount + 1];
        dp[0][0] = true;

        // 填充DP表
        for (int i = 1; i <= n; i++) {
            ICBCRep current = availableData.get(i - 1);
            int amount = current.getTxAmount();

            for (int j = 0; j <= targetAmount; j++) {
                // 不選擇當前匯款
                dp[i][j] = dp[i - 1][j];

                // 選擇當前匯款（如果金額允許）
                if (j >= amount && dp[i - 1][j - amount]) {
                    dp[i][j] = true;
                }
            }
        }

        // 如果無法湊出目標金額，返回空列表
        if (!dp[n][targetAmount]) {
            return new ArrayList<>();
        }

        // 回溯找出具體的組合
        List<ICBCRep> result = new ArrayList<>();
        int currentAmount = targetAmount;

        for (int i = n; i > 0 && currentAmount > 0; i--) {
            ICBCRep current = availableData.get(i - 1);
            int amount = current.getTxAmount();

            // 檢查是否選擇了當前匯款
            if (currentAmount >= amount && dp[i - 1][currentAmount - amount]) {
                result.add(current);
                currentAmount -= amount;
            }
        }

        return result;
    }

    /**
     * 尋找近似符合的匯款組合（當無法完全符合時）
     */
    private List<ICBCRep> findApproximateMatch(List<ICBCRep> availableData, int targetAmount) {
        // 按金額排序，優先選擇較大的匯款
        availableData.sort((a, b) -> Integer.compare(b.getTxAmount(), a.getTxAmount()));

        List<ICBCRep> result = new ArrayList<>();
        int remainingAmount = targetAmount;

        for (ICBCRep icbc : availableData) {
            if (icbc.getTxAmount() <= remainingAmount) {
                result.add(icbc);
                remainingAmount -= icbc.getTxAmount();

                if (remainingAmount == 0) {
                    break;
                }
            }
        }

        return result;
    }

    /**
     * 根據 ICBCRep 建立 AccountRecord
     */
    private AccountRecord createAccountRecordFromICBC(ICBCRep icbc, String orderNo, PayFor payFor) {
        AccountRecord accountRecord = new AccountRecord();
        accountRecord.setAmount(icbc.getTxAmount());
        accountRecord.setOriginalAmount(icbc.getTxAmount());
        accountRecord.setOrderNo(orderNo);
        accountRecord.setAccountType(AccountType.Remit);
        accountRecord.setPayFor(payFor);
        accountRecord.setStationCode("");
        accountRecord.setTradeId("");
        accountRecord.setRemitter(icbc.getPRKey2()); // 匯款人
        accountRecord.setRemitAccCode(icbc.getPRKey1()); // 匯款帳號
        accountRecord.setRemitNo(icbc.getICBCAuto()); // 匯款編號
        accountRecord.setCardNumber("");
        accountRecord.setAuthCode("");
        accountRecord.setTransactionNumber("");
        accountRecord.setDeleted(false);
        accountRecord.setCreateDate(new Date());
        accountRecord.setUpdateDate(new Date());

        // orderPriceInfoAmounts 會在呼叫方設置

        log.info("建立匯款 AccountRecord: 金額={}, 匯款編號={}, 匯款人={}, orderPriceInfoAmounts={}",
            icbc.getTxAmount(), icbc.getICBCAuto(), icbc.getPRKey2(), accountRecord.getOrderPriceInfoAmounts());

        return accountRecord;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("混合付款測試 - 出車使用信用卡付款，還車使用匯款付款")
    void testMixedPaymentScenario_CreditCardDepartExactMatchRemittanceReturn() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 使用既有的完整流程方法，從建立訂單到還車確認
        fromCreateOrderToReturnCarConfirm(context, null);

        // 計算還車實際需要支付的金額
        int exactReturnAmount = calculateReturnPaymentAmount(context.orderNo);

        assertTrue(exactReturnAmount > 0, "未付款還車費用應大於 0");

        // 實際查詢匯款資料
        String startDate = "2024/03/01";
        String endDate = "2024/12/31";
        List<ICBCRep> remittanceDataList = queryRemittanceData(startDate, endDate);

        assertFalse(remittanceDataList.isEmpty(), "沒有查詢到匯款資料");

        // 選擇匯款資料，確保總金額完全符合還車需要支付的金額
        List<ICBCRep> selectedRemittanceData = selectExactMatchRemittanceData(remittanceDataList, exactReturnAmount);

        log.info("selectExactMatchRemittanceData 結果: 找到 {} 筆匯款資料", selectedRemittanceData.size());
        for (int i = 0; i < selectedRemittanceData.size(); i++) {
            ICBCRep icbc = selectedRemittanceData.get(i);
            log.info("selectedRemittanceData[{}]: 金額={}, 匯款編號={}, 匯款人={}",
                i, icbc.getTxAmount(), icbc.getICBCAuto(), icbc.getPRKey2());
        }

        assertFalse(selectedRemittanceData.isEmpty(), "沒有找到能完全符合金額 " + exactReturnAmount + " 元的匯款組合");

        // 準備還車帳務記錄 - 只包含新的 ICBC 匯款資料，不包含既有的 Account 記錄
        List<AccountRecord> returnAccountRecords = new ArrayList<>();

        // 取得還車時未付款的費用項目，用於分配 orderPriceInfoAmounts
        List<OrderPriceInfo> unpaidReturnPriceInfos = priceInfoService.getUnPaidPriceInfoByOrder(context.orderNo, false).stream()
            .filter(opi -> opi.getType() == Pay.getCode() && !opi.isPaid())
            .sorted(Comparator.comparing(OrderPriceInfo::getId)) // 按 ID 排序確保一致性
            .collect(Collectors.toList());

        assertFalse(unpaidReturnPriceInfos.isEmpty(), "沒有找到未付款的還車費用項目");

        log.info("找到 {} 筆未付款的還車費用項目", unpaidReturnPriceInfos.size());
        for (OrderPriceInfo priceInfo : unpaidReturnPriceInfos) {
            log.info("未付款費用項目: ID={}, 金額={}, 類別={}",
                priceInfo.getId(), priceInfo.getActualPrice(), priceInfo.getCategory());
        }

        int totalRemittanceAmount = selectedRemittanceData.stream().mapToInt(ICBCRep::getTxAmount).sum();
        log.info("匯款總金額: {}", totalRemittanceAmount);

        // 建立費用項目分配策略：按照金額比例或優先順序分配
        Map<Integer, Integer> remainingAmounts = unpaidReturnPriceInfos.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getId, OrderPriceInfo::getActualPrice));

        log.info("初始剩餘金額分配: {}", remainingAmounts);

        for (int i = 0; i < selectedRemittanceData.size(); i++) {
            ICBCRep icbc = selectedRemittanceData.get(i);
            log.info("處理第 {} 筆匯款: 金額={}, 匯款編號={}", i + 1, icbc.getTxAmount(), icbc.getICBCAuto());

            AccountRecord remittanceRecord = createAccountRecordFromICBC(icbc, context.orderNo, Return);

            // 為每筆匯款分配對應的費用項目
            Map<Integer, Integer> orderPriceInfoAmounts = new HashMap<>();
            int remainingAmount = icbc.getTxAmount();

            // 按優先順序分配：先分配給較小金額的費用項目
            for (OrderPriceInfo priceInfo : unpaidReturnPriceInfos) {
                if (remainingAmount <= 0) break;

                Integer availableAmount = remainingAmounts.get(priceInfo.getId());
                if (availableAmount != null && availableAmount > 0) {
                    int allocatedAmount = Math.min(remainingAmount, availableAmount);
                    orderPriceInfoAmounts.put(priceInfo.getId(), allocatedAmount);
                    remainingAmounts.put(priceInfo.getId(), availableAmount - allocatedAmount);
                    remainingAmount -= allocatedAmount;
                    log.info("分配 {} 元到費用項目 ID={}", allocatedAmount, priceInfo.getId());
                }
            }

            if (orderPriceInfoAmounts.isEmpty()) {
                log.warn("第 {} 筆匯款沒有分配到任何費用項目！", i + 1);
            }

            remittanceRecord.setOrderPriceInfoAmounts(orderPriceInfoAmounts);
            returnAccountRecords.add(remittanceRecord);
            log.info("加入新匯款 AccountRecord: 金額={}, orderPriceInfoAmounts={}",
                icbc.getTxAmount(), orderPriceInfoAmounts);
        }

        assertEquals(totalRemittanceAmount, returnAccountRecords.stream().mapToInt(AccountRecord::getAmount).sum(), "匯款總金額應等於 AccountRecord 總金額");

        // 執行還車帳務登打
        AccountSettlementRequest returnRequest = createMixedPaymentAccountSettlementRequest(context, returnAccountRecords, Return);

        log.info("準備執行還車帳務登打:");
        log.info("- 訂單號: {}", context.orderNo);
        log.info("- 匯款筆數: {}", selectedRemittanceData.size());
        log.info("- AccountRecord 筆數: {}", returnAccountRecords.size());

        // 檢查每筆 AccountRecord 的內容
        for (int i = 0; i < returnAccountRecords.size(); i++) {
            AccountRecord record = returnAccountRecords.get(i);
            log.info("AccountRecord[{}]: 金額={}, 匯款編號={}, orderPriceInfoAmounts={}",
                i, record.getAmount(), record.getRemitNo(), record.getOrderPriceInfoAmounts());
        }

        // 執行 API 調用前先檢查現有記錄
        List<Account> accountsBeforeCall = paymentService.getAccountsByOrder(context.orderNo);
        int originalAccountCount = accountsBeforeCall.size();
        log.info("API 調用前已有 {} 筆 Account 記錄", originalAccountCount);

        // 使用 JsonNode 手動構建 JSON 來解決 originalAmount 序列化問題
        String requestBody = buildJsonWithOriginalAmount(returnRequest);

        // 收支登打
        mockMvc.perform(patch("/internal/subscribe/v1/payment/{orderNo}/accountSettlement", context.orderNo)
                .contentType(MediaType.APPLICATION_JSON)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .content(requestBody))
            .andExpect(jsonPath("$.statusCode").value("0"));

        // 驗證帳務登打結果
        List<Account> accounts = paymentService.getAccountsByOrder(context.orderNo);
        log.info("API 調用後共有 {} 筆 Account 記錄", accounts.size());

        log.info("=== 混合付款測試完成 ===");
        log.info("帳務登打 API 調用成功完成");
    }

    /**
     * 驗證 EmpMileageDiscount 記錄的正確性
     * 
     * @param orderNo 訂單編號
     * @param newRentalPeriod 新租期月數
     * @param mileageDiscountsBeforeChange 異動前的里程優惠記錄
     * 
     * 驗證項目：
     * 1. 里程費用項目的 stage 數量是否正確對應新的租期結構
     * 2. 每個 stage 的里程優惠記錄是否存在且為非負數
     * 3. reason 欄位是否正確設定
     * 4. **Bug 檢測**：驗證租期異動不會覆蓋手動設定的里程優惠
     * 
     * 重要：此測試檢測 modifyRentalPeriod API 的里程優惠處理邏輯
     * 修正版本：保留手動設定的優惠，重新計算租期相關的優惠
     */
    private void validateEmpMileageDiscountChange(String orderNo, int newRentalPeriod, Map<Integer, Integer> mileageDiscountsBeforeChange) {
        log.info("=== 開始驗證 EmpMileageDiscount 異動前後狀態 ===");
        
        // 1. 取得異動後的里程費用項目
        PriceInfoWrapper priceInfoAfterChange = priceInfoService.getPriceInfoWrapper(orderNo);
        List<OrderPriceInfo> mileageFeesAfter = priceInfoAfterChange.toFilter()
            .category(PriceInfoDefinition.PriceInfoCategory.MileageFee)
            .collect();

        // 2. 計算預期的 stage 數量
        int expectedStages = (int) Math.ceil(newRentalPeriod / 3.0d);
        log.info("新租期 {} 個月，預期 stage 數量: {}", newRentalPeriod, expectedStages);

        // 3. 驗證里程費用項目的 stage 數量
        assertEquals(expectedStages, mileageFeesAfter.size(), 
            "里程費用項目的 stage 數量應為 " + expectedStages);

        // 4. 驗證每個 stage 的里程優惠記錄
        for (int stage = 1; stage <= expectedStages; stage++) {
            final int currentStage = stage;
            
            // 找到對應 stage 的里程費用項目
            OrderPriceInfo mileageFee = mileageFeesAfter.stream()
                .filter(p -> p.getStage() == currentStage)
                .findFirst()
                .orElseThrow(() -> new AssertionError("找不到 stage " + currentStage + " 的里程費用項目"));

            // 驗證 InfoDetail 存在
            assertNotNull(mileageFee.getInfoDetail(), 
                "Stage " + currentStage + " 的里程費用項目 InfoDetail 不應為 null");

            // 5. 驗證里程優惠數值存在且為正數
            Integer actualDiscountMileage = mileageFee.getInfoDetail().getDiscountMileage();
            
            log.info("Stage {}: 實際里程優惠={}", currentStage, actualDiscountMileage);

            assertNotNull(actualDiscountMileage, 
                String.format("Stage %d 的里程優惠不應為 null", currentStage));
            assertTrue(actualDiscountMileage >= 0,
                String.format("Stage %d 的里程優惠應為非負數，實際為 %d", currentStage, actualDiscountMileage));

            // 6. 驗證 reason 欄位
            String actualReason = mileageFee.getInfoDetail().getReason();
            
            // 判斷這是原有階段還是新增階段
            if (mileageDiscountsBeforeChange != null && mileageDiscountsBeforeChange.containsKey(currentStage)) {
                // 原有階段：應該保持原有的 reason 值
                assertEquals("testMileageDiscount", actualReason,
                    "Stage " + currentStage + " 的里程優惠原因應為 'testMileageDiscount' (原有階段)");
                log.info("Stage {} (原有階段) reason 驗證通過: '{}'", currentStage, actualReason);
            } else {
                // 新增階段：reason 可以是 null，這是正常的
                log.info("Stage {} (新增階段) reason: '{}' - 新增階段的 reason 為 null 是正常的", currentStage, actualReason);
            }
        }

        // 7. 驗證不應存在多餘的 stage
        long invalidStageCount = mileageFeesAfter.stream()
            .filter(p -> p.getStage() > expectedStages)
            .count();
        assertEquals(0, invalidStageCount, 
            "不應存在 stage > " + expectedStages + " 的里程費用項目");

        // 8. 比較異動前後的變化並驗證數據一致性
        if (mileageDiscountsBeforeChange != null) {
            log.info("=== 比較異動前後的里程優惠變化 ===");
            for (int stage = 1; stage <= expectedStages; stage++) {
                Integer beforeDiscount = mileageDiscountsBeforeChange.get(stage);
                int finalStage = stage;
                Integer afterDiscount = mileageFeesAfter.stream()
                    .filter(p -> p.getStage() == finalStage)
                    .findFirst()
                    .map(p -> p.getInfoDetail().getDiscountMileage())
                    .orElse(null);
                
                log.info("Stage {}: 異動前={}, 異動後={}", stage, beforeDiscount, afterDiscount);
                
                // 驗證租期異動後的里程優惠數據存在且合理
                if (beforeDiscount != null) {
                    assertNotNull(afterDiscount, 
                        String.format("Stage %d 在租期異動後的里程優惠不應為空", stage));
                    assertTrue(afterDiscount >= 0, 
                        String.format("Stage %d 在租期異動後的里程優惠應為非負數", stage));
                    
                    // 驗證租期異動時里程優惠的正確處理邏輯
                    // 正確邏輯：discountMileage = 手動設定部分 + 重新計算的租期相關部分
                    
                    log.info("Stage {} 里程優惠變化分析: 異動前={}, 異動後={}", stage, beforeDiscount, afterDiscount);
                    
                    // 基本合理性檢查
                    assertNotNull(afterDiscount, 
                        String.format("Stage %d 異動後的里程優惠不應為 null", stage));
                    assertTrue(afterDiscount >= 0, 
                        String.format("Stage %d 異動後的里程優惠應為非負數，實際值: %d", stage, afterDiscount));
                    
                    // 記錄變化但不強制相等，因為 rentalDiscountMileage 應該隨租期重新計算
                    if (!beforeDiscount.equals(afterDiscount)) {
                        log.info("租期異動導致里程優惠重新計算 - Stage {}: 異動前={}, 異動後={}", 
                            stage, beforeDiscount, afterDiscount);
                        log.info("這是預期行為：rentalDiscountMileage 應該根據新租期重新計算");
                    }
                    
                    // TODO: 實作更精確的驗證 - 檢查手動設定的部分是否被保留
                    // 需要獲取 originalDiscountMileage, renewDiscountMileage, rentalDiscountMileage 個別驗證
                }
            }
        }

        log.info("=== EmpMileageDiscount 驗證通過 ===");
    }


    /**
     * 捕獲異動前的 EmpMileageDiscount 狀態
     */
    private Map<Integer, Integer> captureEmpMileageDiscountsBefore(String orderNo) {
        PriceInfoWrapper priceInfoBefore = priceInfoService.getPriceInfoWrapper(orderNo);
        List<OrderPriceInfo> mileageFeesBefore = priceInfoBefore.toFilter()
            .category(PriceInfoDefinition.PriceInfoCategory.MileageFee)
            .collect();

        Map<Integer, Integer> result = new HashMap<>();
        for (OrderPriceInfo mileageFee : mileageFeesBefore) {
            if (mileageFee.getInfoDetail() != null) {
                result.put(mileageFee.getStage(), mileageFee.getInfoDetail().getDiscountMileage());
            }
        }
        
        log.info("捕獲異動前的 EmpMileageDiscount 狀態: {}", result);
        return result;
    }
    
    /**
     * 捕獲異動前的 Insurance, Replacement, Dispatch 費用狀態
     */
    private Map<String, List<OrderPriceInfo>> capturePreservedFeesBefore(String orderNo) {
        PriceInfoWrapper priceInfoBefore = priceInfoService.getPriceInfoWrapper(orderNo);
        
        Map<String, List<OrderPriceInfo>> result = new HashMap<>();
        
        // 捕獲 Insurance 費用
        List<OrderPriceInfo> insuranceFees = priceInfoBefore.toFilter()
            .category(PriceInfoDefinition.PriceInfoCategory.Insurance)
            .collect();
        result.put("Insurance", new ArrayList<>(insuranceFees));
        
        // 捕獲 Replacement 費用
        List<OrderPriceInfo> replacementFees = priceInfoBefore.toFilter()
            .category(PriceInfoDefinition.PriceInfoCategory.Replacement)
            .collect();
        result.put("Replacement", new ArrayList<>(replacementFees));
        
        // 捕獲 Dispatch 費用
        List<OrderPriceInfo> dispatchFees = priceInfoBefore.toFilter()
            .category(PriceInfoDefinition.PriceInfoCategory.Dispatch)
            .collect();
        result.put("Dispatch", new ArrayList<>(dispatchFees));
        
        log.info("捕獲異動前的 Insurance 費用: {} 筆", insuranceFees.size());
        log.info("捕獲異動前的 Replacement 費用: {} 筆", replacementFees.size());
        log.info("捕獲異動前的 Dispatch 費用: {} 筆", dispatchFees.size());
        
        return result;
    }
    
    /**
     * 驗證 Insurance, Replacement, Dispatch 費用保留的正確性
     */
    private void validatePreservedFeesChange(String orderNo, Map<String, List<OrderPriceInfo>> preservedFeesBefore) {
        log.info("=== 開始驗證 Insurance/Replacement/Dispatch 費用保留狀態 ===");
        
        PriceInfoWrapper priceInfoAfter = priceInfoService.getPriceInfoWrapper(orderNo);
        
        // 驗證 Insurance 費用
        validateFeeCategory(priceInfoAfter, preservedFeesBefore, PriceInfoDefinition.PriceInfoCategory.Insurance);
        
        // 驗證 Replacement 費用
        validateFeeCategory(priceInfoAfter, preservedFeesBefore, PriceInfoDefinition.PriceInfoCategory.Replacement);
        
        // 驗證 Dispatch 費用
        validateFeeCategory(priceInfoAfter, preservedFeesBefore, PriceInfoDefinition.PriceInfoCategory.Dispatch);
        
        log.info("=== Insurance/Replacement/Dispatch 費用保留驗證完成 ===");
    }
    
    /**
     * 驗證特定費用類別的保留狀態
     */
    private void validateFeeCategory(PriceInfoWrapper priceInfoAfter,
                                     Map<String, List<OrderPriceInfo>> preservedFeesBefore,
                                     PriceInfoDefinition.PriceInfoCategory category) {
        
        List<OrderPriceInfo> feesAfter = priceInfoAfter.toFilter()
            .category(category)
            .collect();

        List<OrderPriceInfo> feesBefore = preservedFeesBefore.get(category.name());
        
        log.info("驗證 {} 費用 - 異動前: {} 筆, 異動後: {} 筆", category.name(), feesBefore.size(), feesAfter.size());
        
        // 驗證費用數量應該保持一致（在期數增加的情況下）
        assertEquals(feesBefore.size(), feesAfter.size(), 
            String.format("%s 費用數量應該保持一致 (異動前: %d, 異動後: %d)",
                category.name(), feesBefore.size(), feesAfter.size()));
        
        // 按 stage 分組比較
        Map<Integer, OrderPriceInfo> feesBeforeByStage = feesBefore.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getStage, Function.identity()));
        
        Map<Integer, OrderPriceInfo> feesAfterByStage = feesAfter.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getStage, Function.identity()));
        
        // 驗證每個 stage 的費用應該保留
        for (Integer stage : feesBeforeByStage.keySet()) {
            OrderPriceInfo feeBefore = feesBeforeByStage.get(stage);
            OrderPriceInfo feeAfter = feesAfterByStage.get(stage);
            
            assertNotNull(feeAfter, 
                String.format("%s Stage %d 的費用應該保留", category.name(), stage));
            
            // 驗證金額應該保持一致
            assertEquals(feeBefore.getAmount(), feeAfter.getAmount(),
                String.format("%s Stage %d 的金額應該保持一致 (異動前: %d, 異動後: %d)",
                    category.name(), stage, feeBefore.getAmount(), feeAfter.getAmount()));
            
            log.info("{} Stage {} 費用保留正確 - 金額: {}", category.name(), stage, feeAfter.getAmount());
        }
        
        log.info("{} 費用保留驗證通過", category.name());
    }
    
    /**
     * 驗證期數減少時的費用處理 - Insurance, Replacement, Dispatch 費用應該被正確處理
     * 在有效期數範圍內的費用應保留，超出範圍的費用應被刪除
     */
    private void validatePreservedFeesAfterPeriodDecrease(String orderNo, 
                                                         Map<String, List<OrderPriceInfo>> preservedFeesBefore,
                                                         int newMaxStage) {
        log.info("=== 開始驗證期數減少時的 Insurance/Replacement/Dispatch 費用處理 ===");
        
        PriceInfoWrapper priceInfoAfter = priceInfoService.getPriceInfoWrapper(orderNo);
        
        // 驗證 Insurance 費用
        validateFeeCategoryAfterPeriodDecrease(priceInfoAfter, preservedFeesBefore, "Insurance", 
            PriceInfoDefinition.PriceInfoCategory.Insurance, newMaxStage);
        
        // 驗證 Replacement 費用
        validateFeeCategoryAfterPeriodDecrease(priceInfoAfter, preservedFeesBefore, "Replacement", 
            PriceInfoDefinition.PriceInfoCategory.Replacement, newMaxStage);
        
        // 驗證 Dispatch 費用
        validateFeeCategoryAfterPeriodDecrease(priceInfoAfter, preservedFeesBefore, "Dispatch", 
            PriceInfoDefinition.PriceInfoCategory.Dispatch, newMaxStage);
        
        log.info("=== 期數減少時的 Insurance/Replacement/Dispatch 費用處理驗證完成 ===");
    }
    
    /**
     * 驗證特定費用類別在期數減少時的處理
     */
    private void validateFeeCategoryAfterPeriodDecrease(PriceInfoWrapper priceInfoAfter, 
                                                       Map<String, List<OrderPriceInfo>> preservedFeesBefore,
                                                       String categoryName,
                                                       PriceInfoDefinition.PriceInfoCategory category,
                                                       int newMaxStage) {
        
        List<OrderPriceInfo> feesAfter = priceInfoAfter.toFilter()
            .category(category)
            .collect();
        
        List<OrderPriceInfo> feesBefore = preservedFeesBefore.get(categoryName);
        
        // 計算在有效範圍內的費用數量
        long expectedFeesCount = feesBefore.stream()
            .filter(fee -> fee.getStage() <= newMaxStage)
            .count();
        
        log.info("驗證 {} 費用 (期數減少) - 異動前總數: {}, 有效範圍內: {}, 異動後實際: {}", 
            categoryName, feesBefore.size(), expectedFeesCount, feesAfter.size());
        
        // 驗證期數減少後的費用數量
        assertEquals(expectedFeesCount, feesAfter.size(), 
            String.format("%s 費用在期數減少後數量不符 (期望: %d, 實際: %d)", 
                categoryName, expectedFeesCount, feesAfter.size()));
        
        // 按 stage 分組比較有效範圍內的費用
        Map<Integer, OrderPriceInfo> feesBeforeByStage = feesBefore.stream()
            .filter(fee -> fee.getStage() <= newMaxStage)
            .collect(Collectors.toMap(OrderPriceInfo::getStage, Function.identity()));
        
        Map<Integer, OrderPriceInfo> feesAfterByStage = feesAfter.stream()
            .collect(Collectors.toMap(OrderPriceInfo::getStage, Function.identity()));
        
        // 驗證每個有效 stage 的費用應該保留且金額一致
        for (Integer stage : feesBeforeByStage.keySet()) {
            OrderPriceInfo feeBefore = feesBeforeByStage.get(stage);
            OrderPriceInfo feeAfter = feesAfterByStage.get(stage);
            
            assertNotNull(feeAfter, 
                String.format("%s Stage %d 的費用應該保留 (在有效範圍內)", categoryName, stage));
            
            // 驗證金額應該保持一致
            assertEquals(feeBefore.getAmount(), feeAfter.getAmount(),
                String.format("%s Stage %d 的金額應該保持一致 (異動前: %d, 異動後: %d)", 
                    categoryName, stage, feeBefore.getAmount(), feeAfter.getAmount()));
            
            log.info("{} Stage {} 費用正確保留 - 金額: {}", categoryName, stage, feeAfter.getAmount());
        }
        
        // 驗證超出範圍的費用確實被刪除
        long deletedFeesCount = feesBefore.stream()
            .filter(fee -> fee.getStage() > newMaxStage)
            .count();
        
        if (deletedFeesCount > 0) {
            log.info("{} 正確刪除超出範圍的費用數量: {}", categoryName, deletedFeesCount);
        }
        
        log.info("{} 費用期數減少處理驗證通過", categoryName);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    @Test
    @DisplayName("測試 orderClose 請求結案不會重複發送郵件")
    void testOrderCloseRequestShouldNotSendDuplicateEmails() throws Exception {
        OrderTestContext context = new OrderTestContext();

        // 建立一個 spy PushServer 並注入到 OrderService 中的 NotifyService
        PushServer spyPushServer = Mockito.spy(pushServer);
        NotifyService notifyService = (NotifyService) ReflectionTestUtils.getField(orderService, "notifyService");
        assertNotNull(notifyService);
        ReflectionTestUtils.setField(notifyService, "pushServer", spyPushServer);

        // 建立訂單並執行到還車確認狀態
        fromCreateOrderToReturnCarConfirm(context, null);
        payForReturnFee(context);

        // 先還車成功 (沒有額外費用)
        returnCarSuccess(context);

        // 設定車損費用
        setAccidentPriceInfo(context);

        // 直接設定訂單狀態為 ARRIVE_NO_CLOSE 以模擬有未付車損費用的情況
        Orders order = orderService.getOrder(context.orderNo);
        order.setStatus(OrderStatus.ARRIVE_NO_CLOSE.getStatus());
        orderService.updateOrder(order);

        // 驗證訂單狀態為已還車未結案
        order = orderService.getOrder(context.orderNo);
        assertEquals(OrderStatus.ARRIVE_NO_CLOSE.getStatus(), order.getStatus(), "訂單狀態應為已還車未結案");

        // 清除之前的 mock 記錄
        Mockito.clearInvocations(spyPushServer);

        // 建立結案請求
        OrderCloseRequest orderCloseRequest = new OrderCloseRequest();
        orderCloseRequest.setManagerId("testManagerId");

        // 執行結案請求
        mockMvc.perform(post("/internal/subscribe/{orderNo}/orderClose", context.orderNo)
                .header(CarPlusConstant.AUTH_HEADER_MEMBER, MEMBER_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(orderCloseRequest)))
            .andExpect(status().isOk());

        // 等待異步操作完成（因為 @Async 註解）
        Thread.sleep(2000);

        // 驗證 PushServer.notifyAndSave 只被呼叫一次 (不重複發送)
        ArgumentCaptor<Notify> notifyCaptor = ArgumentCaptor.forClass(Notify.class);
        Mockito.verify(spyPushServer, Mockito.times(1)).notifyAndSave(notifyCaptor.capture());

        // 驗證發送的郵件內容正確
        Notify capturedNotify = notifyCaptor.getValue();
        assertNotNull(capturedNotify, "應該有一個 Notify 物件被發送");
        assertEquals(context.orderNo, capturedNotify.getOrderNo(), "郵件中的訂單編號應正確");
        assertTrue(capturedNotify.getNotifyContent().contains("請求長官結案"), "郵件內容應包含結案請求文字");
        assertTrue(capturedNotify.getNotifyContent().contains(context.orderNo), "郵件內容應包含訂單編號");

        log.info("測試完成：orderClose 請求結案只發送一封郵件，未重複發送");
    }
}