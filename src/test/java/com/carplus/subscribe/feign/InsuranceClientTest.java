package com.carplus.subscribe.feign;

import com.carplus.subscribe.service.InsuranceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class InsuranceClientTest {

    @Autowired
    private InsuranceService insuranceService;

    @Test
    void queryPolicy() {
        boolean hasValidArbitraryPolicy = insuranceService.hasValidArbitraryPolicy("RAS-3225");
        System.out.println(hasValidArbitraryPolicy);
    }

}