# Business Logic Documentation

## Document Update History
### [2025-09-01] - [Feature Implementation] Coupon Application System
- **Updated by**: Claude Code
- **Changes**: Implemented comprehensive coupon application system including order coupon application, trial calculation, and cancellation handling
- **Impact**: Enhanced order management with full coupon lifecycle support for both cashier and customer portals
- **Related files**: 
  - OrderService.java (added applyCouponToOrder, validateCouponApplication, generateCouponPriceInfos, deleteCouponPriceInfos methods)
  - PriceInfoService.java (added getUserUnPaidPriceInfoByOrderResponseWithCoupon, calculateCouponDiscountSimulation methods)
  - ContractInternalController.java (added POST /internal/subscribe/coupon/{orderNo} endpoint)
  - ContractPublicController.java (added POST /subscribe/coupon/calculate/{orderNo} endpoint)
  - PriceInfoPublicController.java (modified GET /subscribe/priceInfo/{orderNo}/unpaid with sequenceId support)
  - PriceInfoDefinition.java (added Coupon category)
  - PriceInfoDetail.java (added coupon-related fields)
  - CouponApplyRequest.java (new request model)
  - SubscribeHttpExceptionCode.java (added coupon-related error codes)
### [2025-08-26] - [Feature Enhancement] Order Available Coupons Filtering
- **Updated by**: Claude Code
- **Changes**: Implemented `getAvailableCoupons` method in OrderService with comprehensive coupon validation and filtering logic
- **Impact**: Enhanced order management with intelligent coupon filtering based on discount types, payment amounts, car model matching, and response enhancement
- **Related files**: 
  - OrderService.java (added getAvailableCoupons and helper methods)
  - CouponServer.java (integrated for coupon data retrieval)
  - CarModelService.java (integrated for car model and brand code population)
  - Coupon.java, CarSeries.java (coupon model structures)
  - CarBrandModelDTO.java (safe car model code retrieval)

### [2025-08-21] - [Feature Enhancement] Dealer Order Mileage Management
- **Updated by**: Claude Code
- **Changes**: Implemented mileage tracking functionality for dealer orders including start mileage (出車時) and end mileage (還車時)
- **Impact**: Enhanced dealer order management with comprehensive mileage tracking, validation logic, and Excel import/export support
- **Related files**: 
  - DealerOrder.java (added startMileage, endMileage fields)
  - DealerSubscriptionInfoForDepart.java, DealerSubscriptionInfoForClose.java (added mileage fields with validation)
  - DealerOrderExcel.java, DealerOrderExcelColumn.java (Excel support)
  - DealerOrderService.java (validation logic)
  - DealerOrderInternalControllerTest.java (comprehensive test cases)

### [2025-08-20] - [Development Guidelines] Code Optimization and Naming Conventions
- **Updated by**: Claude Code
- **Changes**: Added development guidelines and preferences section covering code optimization approach, naming conventions, and performance optimization standards
- **Impact**: Established consistent development standards for future code modifications, emphasizing minimal changes with maximum performance benefit
- **Related files**: CarWishlistService.java (implemented memory-optimized CSV generation)

### [2025-08-19] - [Performance Optimization] Security Deposit Refund Early Return Logic
- **Updated by**: Claude Code
- **Changes**: Implemented early return optimization in orderCloseAgree method to skip security deposit refund when already refunded
- **Impact**: Improved order closure performance, reduced unnecessary database operations, enhanced system efficiency
- **Related files**: OrderService.java (orderCloseAgree method), ContractInternalControllerTest.java (added comprehensive refund test cases)

### [2025-08-14] - [Bug Fix] EmpMileageDiscount Reason Field Preservation
- **Updated by**: Claude Code
- **Changes**: Fixed issue where EmpMileageDiscount reason field was lost during rental period modifications, now preserves manually set reason values
- **Impact**: EmpMileageDiscount data integrity, rental period modification functionality
- **Related files**: OrderService.java (updateExistingFeeData method)

### [2025-08-14] - [Bug Fix] Rental Period Fee Preservation Logic Fix
- **Updated by**: Claude Code
- **Changes**: Fixed issue where Insurance, Replacement, Dispatch fees were incorrectly deleted during rental period modifications
- **Impact**: Rental period modification functionality, fee preservation logic, order fee management
- **Related files**: OrderService.java (updateExistingFeeData method), ContractInternalControllerTest.java (added fee preservation validation)

### [2025-08-14] - [Bug Fix] EmpMileageDiscount Rental Period Logic Fix
- **Updated by**: Claude Code
- **Changes**: Fixed rental period modification mileage discount calculation logic, preserving manually set discounts while recalculating rental-dependent parts
- **Impact**: Rental period modification functionality, mileage discount preservation logic
- **Related files**: OrderService.java (updateExistingFeeData, updateMileageDiscountLogic methods)

### [2025-08-14] - [Feature Addition] EmpMileageDiscount Test Validation Logic
- **Updated by**: Claude Code
- **Changes**: Added EmpMileageDiscount validation logic to rental period modification tests
- **Impact**: Improved test coverage, ensuring rental period modifications don't affect mileage discount logic
- **Related files**: ContractInternalControllerTest.java

## Core Business Concepts

### Vehicle Subscription Service Overview
This system provides vehicle subscription services, allowing customers to use vehicles on a monthly rental basis, including insurance, maintenance and other services.

### Key Term Definitions
- **Orders**: Customer subscription applications, containing rental period, vehicle model and other information
- **Contract**: Official rental contract, can contain multiple orders
- **MainContract**: Customer's main contract, can be renewed
- **Renewal**: Extending rental period based on existing contract
- **EmpMileageDiscount**: Discounts provided for mileage fees

## Main Business Processes

### Order Lifecycle
1. **Create Order**: Customer selects vehicle model, rental period, creates initial order
2. **Credit Review**: System performs automatic or manual credit review
3. **Payment Processing**: Process security deposit and related fees
   - **Coupon Application**: Optional coupon application for discount on various fees
4. **Vehicle Preparation**: Prepare vehicle, create departure task
5. **Departure**: Official vehicle handover to customer
6. **Rental Management**: Rental period modifications, renewals, etc.
7. **Return**: Customer returns vehicle, settle fees
8. **Order Closure**: Final order settlement with optimized security deposit refund handling

### Coupon Application Process
1. **Coupon Validation**: Verify coupon availability, expiration, and applicability
2. **Discount Calculation**: Calculate applicable discount amounts based on unpaid fees
3. **Fee Association**: Create coupon OrderPriceInfo entries with references to discounted fees
4. **Trial Calculation**: Support for preview of discount effects without database modification
5. **Cancellation Handling**: Automatic coupon cancellation when orders are cancelled

### Rental Period Modification Process
1. **Modification Request**: Customer applies to modify rental period
2. **Fee Recalculation**: System recalculates related fees
3. **Fee Preservation Logic**: Determine whether to preserve or recalculate based on fee category
4. **Contract Merge Decision**: Determine if can merge to original contract
5. **Update Order Information**: Update rental period and related information

## Business Rules Collection

### Rental Period Modification Rules
1. **Merge Condition**: Can merge to original contract when original contract effective rental period + new rental period ≤ 12 months
2. **Cancelled Order Processing**: Ignore cancelled renewal orders when calculating merge feasibility
3. **Mileage Discount Preservation**: Manually set mileage discounts must be preserved during rental period modifications
4. **Fee Category Processing Strategy**:
   - **Period Decrease (stage > newMaxStage)**: Delete all fees beyond range
   - **Period Increase or Maintain**:
     - **MileageFee**: Recalculate based on new rental period
     - **MonthlyFee**: Recalculate based on new rental period
     - **Insurance**: Preserve existing fees unchanged
     - **Replacement**: Preserve existing fees unchanged
     - **Dispatch**: Preserve existing fees unchanged
     - **Other Categories**: Preserve existing fees unchanged

### Mileage Discount Calculation Rules
- **Composition Formula**: `discountMileage = originalDiscountMileage + renewDiscountMileage + rentalDiscountMileage`
- **Manual Discount Detection**: `manuallyAddedDiscount = existingDiscountMileage - (renewDiscountMileage + rentalDiscountMileage)`
- **Modification Preservation Logic**: Preserve manual parts, recalculate rental-dependent parts

### Security Deposit Refund Optimization Rules
- **Early Return Check**: Before processing refund, check if security deposit is already refunded using `isSecurityDepositAlreadyRefunded()`
- **Refund Status Detection**: A security deposit is considered refunded when `paymentId != null` for SecurityDeposit category refund records
- **Performance Optimization**: Skip entire refund process when already refunded, reducing database operations and improving system performance
- **Transaction Safety**: Early return check is performed before any database modifications, ensuring transaction consistency

### Order Available Coupons Filtering Rules
- **Discount Type Validation**: 
  - Types 3 (滿額折價) and 4 (滿額折扣) require payment amount ≥ `overTotalPrice` threshold
  - Other discount types (1, 2) are always valid regardless of payment amount
- **Payment Amount Calculation Strategy**:
  - **Specific Items**: When `orderPriceInfoIdList` is provided, sum amounts from specified OrderPriceInfo items
  - **Total Unpaid**: When `orderPriceInfoIdList` is null/empty, use PriceInfoWrapper to get total unpaid receivable amount
- **Car Model Filtering Logic**:
  - **Parameter Priority**: Use provided `carModelCode` parameter if available
  - **Fallback Strategy**: When not provided, safely retrieve from order's car via `CarsService.getCarBrandModelByPlateNo()`
  - **Matching Rule**: Car model code must exist in any of the coupon's `carSeries` list (match against `CarSeries.id`)
  - **Error Handling**: Throw `CAR_MODEL_NOT_FOUND` exception if unable to determine car model code
- **Response Enhancement Optimization**:
  - **Pre-check**: Only execute `CarModelService.findAll()` if at least one coupon has non-empty `carSeries`
  - **Selective Population**: Only populate `carModelCodes` and `carBrandCodes` for coupons with matching carSeries
  - **Matching Strategy**: Match `CarModel.carModelCode` against `CarSeries.id`, populate both model codes and distinct brand codes

### Dealer Order Mileage Management Rules
- **Start Mileage Validation**: Must be non-negative integer (>= 0), recorded during vehicle departure
- **End Mileage Validation**: Must be non-negative integer (>= 0), recorded during vehicle return
- **Mileage Consistency Rule**: End mileage must be greater than or equal to start mileage when both values exist
- **Optional Fields**: Both start and end mileage are optional fields to maintain backward compatibility
- **API Integration Points**:
  - **Departure API** (`departDealerOrder`): Accepts and validates `startMileage` parameter
  - **Return API** (`closeDealerOrder`): Accepts and validates `endMileage` parameter with consistency check
  - **Query API** (`queryDealerOrderDetail`): Returns both mileage values in response
- **Excel Support**: Full import/export functionality with validation for mileage fields
- **Business Logic**: Supports tracking vehicle usage patterns and maintenance scheduling

### Coupon Application Rules
- **Duplicate Application Check**: Orders cannot apply new coupons if existing coupon OrderPriceInfo has any unpaid referenced fees
- **Expiration Validation**: Coupon end time must be after current time
- **Subscription Applicability**: Coupons must be applicable to subscription services (SourceItem.SUBSCRIBE)
- **Minimum Amount Threshold**: Current unpaid amount (excluding ETag, Merchandise, SecurityDeposit) must meet coupon's `overTotalPrice` requirement
- **Discount Amount Limit**: Coupon discount amount cannot exceed total discountable unpaid fees
- **Fee Association Strategy**: Discounts are applied to unpaid fees in descending order by amount
- **Excluded Categories**: ETag, Merchandise, SecurityDeposit, and Coupon categories are excluded from discount calculations
- **Trial Calculation Logic**: 
  - Returns original fee list if coupon is invalid, expired, or insufficient payment amount
  - Generates simulated coupon discount entries without database persistence
  - Respects existing paid coupon associations (skips new trial if related fees are paid)
- **Cancellation Rules**: 
  - Only allow coupon deletion when all referenced fees are unpaid
  - Automatically cancel coupon usage via CouponServer when deleting coupon OrderPriceInfo
  - Integrate with order cancellation process to handle coupon cleanup

## Data Models and Relationships

### Core Entity Relationships
- **Orders** ↔ **Contract**: Orders belong to specific contracts
- **Contract** ↔ **MainContract**: Contracts belong to main contracts
- **Orders** ↔ **OrderPriceInfo**: Orders contain multiple fee information items
- **OrderPriceInfo** ↔ **PriceInfoDetail**: Fee detailed information
- **Orders** ↔ **Coupon**: Orders can have available coupons filtered by business rules
- **Coupon** ↔ **CarSeries**: Coupons specify applicable car models via CarSeries list
- **CarSeries** ↔ **CarModel**: CarSeries.id matches CarModel.carModelCode for filtering
- **OrderPriceInfo** ↔ **Coupon**: Coupon applications create OrderPriceInfo entries with Coupon category
- **CouponPriceInfo** ↔ **ReferencedFees**: Coupon OrderPriceInfo references discounted fees via refPriceInfoNo

### Fee Structure
- **PriceInfoCategory**: Fee categories (MileageFee, Insurance, Replacement, Dispatch, Coupon, etc.)
- **PriceInfoType**: Fee types (Pay, Discount, Refund)
- **Stage**: Fee periods, corresponding to different stages of rental period
- **Coupon OrderPriceInfo Structure**:
  - **Category**: Always `PriceInfoCategory.Coupon`
  - **Type**: Always `PriceInfoType.Pay.getCode()`
  - **RefPriceInfoNo**: References the discounted fee's OrderPriceInfo ID
  - **PriceInfoDetail Fields**: Contains `couponSequenceId`, `couponName`, `description`

## External System Integration

### Main External Services
- **CRS**: Vehicle Registration System
- **Finance Service**: Financial services
- **Insurance Service**: Insurance services
- **Payment Gateway**: Payment gateway
- **Task Service**: Task management service
- **Coupon Server**: Coupon management system for member coupon validation and retrieval

## Business Exception Handling

### Coupon Filtering Exceptions
- **CAR_MODEL_NOT_FOUND**: Thrown when unable to determine car model code from either parameter or order's car information
- **External Service Failures**: CarsService integration failures are logged as warnings, not blocking the process
- **Coupon Server Integration**: External coupon service failures are propagated to maintain data integrity
- **Payment Amount Calculation**: Invalid OrderPriceInfo IDs result in calculation errors

### Coupon Application Exceptions
- **COUPON_NOT_FOUND** (80001): Coupon does not exist or is invalid
- **COUPON_EXPIRED** (80002): Coupon has expired
- **COUPON_ALREADY_APPLIED** (80003): Order already has incomplete coupon discount
- **COUPON_MIN_AMOUNT_NOT_MET** (80004): Order amount does not meet coupon usage threshold
- **COUPON_DISCOUNT_EXCEEDS_TOTAL** (80005): Coupon discount amount exceeds total discountable fees
- **COUPON_CANNOT_DELETE_PAID_FEES** (80006): Cannot delete coupon when associated fees are paid

### Rental Period Modification Exceptions
- **12 Month Limit Exceeded**: Cannot merge to original contract when merged rental period exceeds 12 months
- **Fee Calculation Errors**: System logs detailed information to assist debugging
- **Mileage Discount Override**: Fixed, ensuring manual discounts are not overridden
- **Incorrect Fee Deletion**: Fixed, ensuring Insurance/Replacement/Dispatch fees are not deleted during period increases

## Business Configuration Description

### Fee-Related Configuration
- **Standard Fees**: Insurance(660), Replacement(966), Dispatch(4000)
- **Addable Fee Categories**: Others, Dispatch, Insurance, Replacement

## Known Limitations and Considerations

### Rental Period Modification Limitations
1. **12 Month Limit**: Contract total rental period cannot exceed 12 months
2. **Cancelled Orders**: Must correctly ignore cancelled renewal orders during calculation
3. **Fee Preservation**: Specific fee categories must be preserved during period increases, cannot be deleted
4. **Fee Classification Strategy**: Different fee categories have different processing logic during rental period modifications, must be strictly followed

## Development Guidelines and Preferences

### Code Optimization Preferences
1. **Minimal Code Changes**: Prefer minimal modifications that achieve maximum performance improvement
2. **Memory Efficiency**: Focus on reducing memory usage, especially avoiding large List collections when possible
3. **Performance Over Complexity**: Choose simpler solutions that provide significant performance gains over complex architectural changes

### Naming Conventions and Comments
1. **Avoid Technical Jargon in Names**: Do not use technical terms like "streaming", "batching" in method names or variables
   - ✅ Good: `generateWishlistReportData()`, `buildReportData()`
   - ❌ Avoid: `generateWishlistReportDataStreaming()`, `buildReportDataStreaming()`

2. **Natural Method Names**: Use business-focused names that describe what the method does, not how it does it
   - Focus on business functionality rather than implementation details

3. **Comment Style**: 
   - Keep comments focused on business logic and purpose
   - Avoid emphasizing technical implementation details in comments
   - ✅ Good: "直接寫入 CSV，不建立 List", "逐筆處理並寫入 CSV"
   - ❌ Avoid: "使用 streaming 方式處理", "streaming 寫入"

### Performance Optimization Approach
1. **Step-by-Step Optimization**: Prefer incremental improvements over complete rewrites
2. **Memory-First**: Prioritize memory usage reduction as the primary optimization target
3. **Preserve Original Logic**: Maintain existing business logic and comments while improving performance
4. **Remove Unused Code**: Clean up unused methods and imports after optimizations

### Code Review Standards
1. **Preserve Business Comments**: All original business logic comments must be retained during refactoring
2. **Clean Code**: Remove unused methods and dead code after making changes
3. **Consistent Naming**: Ensure all method and variable names follow the same natural, business-focused conventions