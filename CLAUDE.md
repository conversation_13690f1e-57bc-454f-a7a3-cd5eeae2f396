# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 自動化任務 - 業務邏輯文檔更新

**重要：每次完成開發任務時，Claude Code 必須自動檢查並更新業務邏輯文檔**

### 強制執行條件
- 任何 `src/` 下的檔案變更

### 自動執行步驟
1. 立即檢查 `docs/business-logic.md` 是否存在，不存在則創建
2. 分析變更的業務邏輯影響
3. 更新對應文檔章節
4. 在文檔頂部記錄變更

## 重要執行規則 ⚠️

**禁止自動執行 Maven 命令**
- Claude Code 不得自動執行任何 `mvn` 命令
- 不要執行 `mvn clean compile`、`mvn test`、`mvn package` 等建置命令  
- 不要執行 `mvn spring-boot:run` 啟動應用程式
- 僅在用戶明確要求時才執行 Maven 命令
- 專注於程式碼分析、撰寫和文檔更新，不執行建置或測試

## 工作模式
**Claude Code 應該：**
- ✅ 分析和撰寫程式碼
- ✅ 更新文檔和註解
- ✅ 進行程式碼重構和優化
- ✅ 回答技術問題
- ❌ 不自動執行 Maven 建置命令
- ❌ 不自動執行測試
- ❌ 不自動啟動應用程式

## Build System & Development Commands

This is a Maven-based Spring Boot 2.4.13 application with Java 8. 

**重要：以下命令僅供參考，Claude Code 不應自動執行**

可用的 Maven 命令（僅在用戶明確要求時執行）：

```bash
# Build the project (僅在用戶要求時執行)
mvn clean compile

# Run tests (僅在用戶要求時執行)
mvn test

# Run specific test class
mvn test -Dtest=ServiceNameTest

# Run with code style checking (Google Java Style)
mvn checkstyle:check

# Build package
mvn clean package

# Run Spring Boot application locally
mvn spring-boot:run

# Code analysis with SonarQube
mvn sonar:sonar
```

## Architecture Overview

### Core Application Structure
- **Main Package**: `com.carplus.subscribe`
- **Application Type**: Spring Boot REST API service for car subscription management
- **Base Package Structure**:
    - `controller/` - REST endpoints organized by domain (cars, contract, payment, etc.)
    - `service/` - Business logic layer
    - `db/mysql/entity/` - JPA entities for database mapping
    - `feign/` - External service clients (CRS, Finance, Insurance, etc.)
    - `model/` - DTOs and request/response objects
    - `enums/` - Business domain enums

### Key Business Domains
1. **Orders & Contracts**: Core subscription order management (`contract/Orders.java`, `contract/Contract.java`)
2. **SKU & Shipments**: Product and delivery management (`contract/Sku.java`, `contract/SkuShipment.java`)
3. **Cars Management**: Vehicle inventory and registration (`cars/` package)
4. **Payment Processing**: Payment handling and invoice generation (`payment/` package)
5. **Pricing**: Dynamic pricing calculation (`priceinfo/` package)

### Database Architecture
- **Primary Database**: MySQL with JPA/Hibernate
- **Entity Hierarchy**: Most entities extend `GeneralEntity` for common audit fields
- **Change Tracking**: Custom entity change logging system in `entity/change/` package
- **Key Entities**:
    - `Orders` - Main subscription orders
    - `SkuShipment` - Product delivery tracking
    - `Contract` - Subscription contracts
    - `Cars` - Vehicle inventory

### External Service Integration
Uses OpenFeign clients for integration with:
- CRS (Car Registration System)
- Finance Service
- Insurance Service
- Task Service
- Payment Gateway

### Configuration Management
- **Main Config**: `application.yml` with environment-specific settings
- **Profiles**: Uses Spring profiles for different environments
- **External Services**: Configured via `carplus.service.*` properties

### Code Quality & Standards
- **Style Guide**: Google Java Style Guide enforced via Checkstyle
- **Line Length**: Max 250 characters
- **Testing**: JUnit for unit tests, located in `src/test/java/`
- **Lombok**: Used extensively for reducing boilerplate

### Key Features
1. **Entity Change Logging**: Automatic tracking of database changes via `EntityListener`
2. **Multi-Database Support**: MySQL primary, SQL Server secondary
3. **Async Processing**: Spring `@EnableAsync` for background tasks
4. **Scheduled Tasks**: `@EnableScheduling` for periodic operations
5. **API Documentation**: SpringDoc OpenAPI 3 available at `/doc/swagger-ui.html`

### Performance Considerations
- **Database Connection**: P6Spy for SQL logging and performance monitoring
- **Connection Pooling**: Configured timeouts and max lifetime
- **Pagination**: Custom pagination for large datasets
- **Feign Timeouts**: 30-second read/connect timeouts for external services

### Testing Strategy
- Unit tests in `src/test/java/` mirror main package structure
- Integration tests for services that interact with external systems
- Test profiles configured for different environments

### Development Guidelines
When working with this codebase:
1. Follow the existing package structure and naming conventions
2. Extend `GeneralEntity` for new database entities
3. Use Lombok annotations to reduce boilerplate
4. Place business logic in service layer, keep controllers thin
5. Use existing Feign clients for external service calls
6. Run checkstyle validation before commits
7. Use constructor injection over field injection for better testability
8. Implement proper exception handling using `@ControllerAdvice` and `@ExceptionHandler`
9. Use `@Valid` for request validation and create custom validators when needed

---

## **業務邏輯文檔維護 - 重要指示** ⭐

### 強制文檔更新規則
**Claude Code，每當你完成任何開發任務時，必須執行以下步驟：**

#### 1. 業務邏輯文檔位置
- **統一文檔**: `docs/business-logic.md` - 所有業務邏輯的集中文檔

#### 2. 業務邏輯變更判斷標準
以下任何情況**必須**更新業務邏輯文檔：

**Service 層變更**:
- 新增、修改或刪除任何 Service 類別的業務方法
- 變更業務流程邏輯或規則
- 修改業務驗證邏輯

**Entity 層變更**:
- 新增、修改或刪除實體類別
- 變更實體間的關聯關係
- 修改業務相關的欄位或約束

**Controller 層業務邏輯**:
- 新增或修改 API 端點的業務功能
- 變更請求/回應的業務邏輯處理
- 修改業務相關的驗證規則

**External Service Integration**:
- 新增、修改或刪除 Feign 客戶端
- 變更外部服務調用的業務邏輯
- 修改外部服務整合的錯誤處理

**Configuration 業務相關變更**:
- 修改影響業務邏輯的配置項目
- 變更業務規則相關的參數設定

**Database Schema 變更**:
- 新增、修改或刪除資料表
- 變更資料表關聯或約束
- 修改影響業務邏輯的資料結構

**Business Enums 變更**:
- 新增、修改或刪除業務相關的列舉值
- 變更列舉的業務語義或用途

#### 3. 文檔更新格式要求
**`docs/business-logic.md` 統一格式**:
```markdown
# 業務邏輯文檔

## 文檔更新記錄
### [YYYY-MM-DD] - [變更類型]
- **更新人**: Claude Code
- **變更內容**: [具體描述本次變更]
- **影響範圍**: [受影響的功能或模組]
- **相關檔案**: [相關的程式碼檔案清單]

## 核心業務概念
[整個系統的核心業務概念和術語定義]

## 主要業務流程
[按功能或模組組織的主要業務流程]

## 業務規則集合
[所有重要的業務驗證、約束和規則]

## 資料模型與關係
[關鍵實體的業務含義和關聯關係]

## 外部系統整合
[與外部服務的業務整合點和邏輯]

## 業務異常處理
[業務層面的異常處理策略和規則]

## 業務配置說明
[影響業務邏輯的配置項目說明]

## 已知限制與注意事項
[業務邏輯的限制、邊界條件和特殊情況]
```

#### 4. 執行流程
**每次完成開發任務後，你必須：**
1. ✅ **檢查變更範圍** - 檢視所有修改的檔案，判斷是否涉及業務邏輯
2. ✅ **識別業務影響** - 分析變更對整體業務邏輯的影響
3. ✅ **立即更新文檔** - 在 `docs/business-logic.md` 對應章節更新內容
4. ✅ **記錄變更歷史** - 在文檔頂部的更新記錄中新增條目
5. ✅ **檢查一致性** - 確保文檔描述與實際程式碼邏輯一致
6. ✅ **同步提交** - 將文檔變更與程式碼變更一起提交

#### 5. 通用更新策略
- **增量更新**: 不重寫整個文檔，只更新受影響的部分
- **保持結構**: 維持文檔的既有章節結構，必要時新增章節
- **詳細記錄**: 在更新記錄中清楚說明變更的原因和影響
- **交叉引用**: 適當引用相關的程式碼檔案和配置項目
- **版本追蹤**: 每次更新都要記錄日期和變更摘要

#### 6. Commit Message 規範
當有業務邏輯變更時，commit message 格式：
```
feat: [功能描述]

- [具體實作內容]
- [業務邏輯變更說明]

Closes #[issue-number]
```

#### 7. 重要提醒
**Claude Code，請牢記：**
- 🚨 **業務邏輯文檔維護是強制性的，不是可選的**
- ⏱️ **每次任務完成後都要主動檢查是否需要更新文檔**
- 🎯 **保持 `docs/business-logic.md` 與程式碼的完全一致性**
- 📝 **詳細記錄每次業務邏輯變更的原因、影響和實作細節**
- 🔄 **將文檔更新視為開發流程的必要步驟，不是事後補充**
- 💡 **如果不確定是否為業務邏輯變更，選擇更新文檔而非忽略**
- 📋 **維持文檔的可讀性和結構性，方便後續維護和查閱**

### 文檔初始化
如果 `docs/business-logic.md` 不存在，請先創建基礎文檔架構，然後開始維護。

---

## **Claude 記憶指令 - Java Optional 最佳實踐** 🧠

### orElse vs orElseGet 自動檢查規則

當審查 PR 或撰寫 Java 8+ 代碼時，遇到 `Optional.orElse(...)` 請自動檢查：

#### 檢查規則
1. **輕量常數** → 允許使用 `orElse`
   - `Collections.emptyList()`, `Collections.singletonList("default")`
   - 字串常數、數字常數、boolean 值
   - `new ArrayList<>()`, `new HashMap<>()`

2. **昂貴計算或有副作用** → 提醒改用 `orElseGet`
   - 資料庫查詢：`userRepository.findDefault()`
   - IO 操作：`loadFromFile()`, `callExternalAPI()`
   - 複雜計算：`calculateDefault()`, `processData()`
   - 建立複雜物件：`new ComplexObject(param1, param2)`

3. **拋出例外** → 建議改用 `orElseThrow`
   - `throw new RuntimeException(...)`
   - 任何例外拋出場景

#### 自動提示語
當發現不當使用時，提供以下提示：

```
⚠️ Optional 使用建議：
• 輕量常數 → 保持 orElse
• 昂貴計算/IO/副作用 → 改用 orElseGet
• 拋出例外 → 使用 orElseThrow

範例：
// ❌ 昂貴操作
user.orElse(loadUserFromDB())

// ✅ 改用 orElseGet
user.orElseGet(() -> loadUserFromDB())
```

#### 技術背景
```java
// orElse - 總是執行預設值建立
public T orElse(T other) {
    return value != null ? value : other;
}

// orElseGet - 延遲執行
public T orElseGet(Supplier<? extends T> other) {
    return value != null ? value : other.get();
}
```

#### 常見場景對照

| 場景 | 推薦方法 | 範例 |
|------|----------|------|
| 空集合 | `orElse` | `list.orElse(Collections.emptyList())` |
| 預設字串 | `orElse` | `name.orElse("Unknown")` |
| 資料庫查詢 | `orElseGet` | `user.orElseGet(() -> repo.findDefault())` |
| API 呼叫 | `orElseGet` | `data.orElseGet(() -> callAPI())` |
| 拋出例外 | `orElseThrow` | `user.orElseThrow(() -> new UserNotFound())` |

---

**啟用指令**：在 Java 後端開發情境中，自動套用以上檢查規則。