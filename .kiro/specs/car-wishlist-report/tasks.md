# Implementation Plan

- [ ] 1. Create data models and request/response classes
  - Create CarWishlistReportRequest class for filtering parameters
  - Create CarWishlistReportData class for report data structure
  - Create CarWishlistOrderTracking entity class
  - _Requirements: 1.1, 2.1, 3.1, 4.1, 5.1_

- [ ] 2. Add custom exception codes for wishlist report
  - Add new SubscribeHttpExceptionCode entries for wishlist report errors
  - Include codes for validation errors, data not found, and generation failures
  - _Requirements: 8.1, 9.1_

- [ ] 3. Create CarWishlistOrderTracking repository
  - Implement CarWishlistOrderTrackingRepository extending SimpleJpaRepository
  - Add methods for checking wishlist-order correlation
  - Add query methods for data migration from existing remarks
  - _Requirements: 6.1, 7.2_

- [ ] 4. Implement CarWishlistOrderTrackingService
  - Create service class for managing wishlist-order correlation tracking
  - Implement recordWishlistOrder method to record when customer orders wishlisted car
  - Implement wasCarInWishlistWhenOrdered method to check correlation status
  - Implement migrateExistingRemarks method to extract data from existing order remarks
  - _Requirements: 6.2, 6.3, 7.3, 7.4_

- [ ] 5. Create CSV report generator utility
  - Implement CSVReportGenerator class for generating CSV files
  - Add method to format CSV headers with proper column names
  - Add method to format data rows with proper data transformation
  - Handle special characters and CSV escaping
  - _Requirements: 5.1_

- [ ] 6. Enhance CarWishlistRepository with report query methods
  - Add method to query wishlists with phone number filter
  - Add method to query wishlists with car model codes filter
  - Add method to query wishlists with date range filter (updateDate)
  - Optimize queries with proper joins for performance
  - _Requirements: 2.1, 3.1, 4.1, 4.2, 4.3, 4.4_

- [ ] 7. Enhance CarWishlistService with report generation methods
  - Add generateWishlistReport method as main report generation logic
  - Add buildReportData method to transform wishlist data for report
  - Add enrichWithCustomerData method to fetch customer information via AuthServer
  - Add enrichWithVehicleData method to fetch vehicle information via CarsService
  - Add addWishlistOrderCorrelation method to add order correlation data
  - Implement proper error handling with custom exception codes
  - _Requirements: 5.1, 6.1, 8.1_

- [ ] 8. Add report download endpoint to CarWishlistInternalController
  - Add downloadWishlistReport endpoint accepting CarWishlistReportRequest
  - Implement proper request validation with custom error messages
  - Return CSV file as downloadable response with proper headers
  - Add proper authorization checks for internal users
  - _Requirements: 1.1, 1.2, 1.3, 9.1, 9.2_

- [ ] 9. Integrate wishlist tracking into order creation process
  - Modify ContractLogic.createContract method to use CarWishlistOrderTrackingService
  - Replace existing remark-based tracking with new table-based tracking
  - Ensure backward compatibility during transition period
  - _Requirements: 6.2, 7.2_

- [ ] 10. Implement data migration for existing wishlist-order correlations
  - Create migration method to extract wishlist information from existing order remarks
  - Parse remarks containing "有加入用戶收藏清單" to identify correlations
  - Populate CarWishlistOrderTracking table with historical data
  - Add validation to ensure migration accuracy
  - _Requirements: 7.3, 7.4_

- [ ] 11. Add comprehensive unit tests for new functionality
  - Test CarWishlistOrderTrackingService methods with various scenarios
  - Test CSVReportGenerator with different data sets and edge cases
  - Test CarWishlistService report generation methods with various filters
  - Test error handling and validation scenarios
  - _Requirements: 8.1_

- [ ] 12. Add integration tests for report generation flow
  - Test end-to-end report generation from request to CSV download
  - Test database integration with complex filter combinations
  - Test performance with large datasets to ensure acceptable response times
  - Test authorization and access control for internal endpoints
  - _Requirements: 8.2, 9.1, 9.3_