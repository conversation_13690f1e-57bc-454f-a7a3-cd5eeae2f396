# Requirements Document

## Introduction

This feature implements a car wishlist report download functionality that allows internal users to generate CSV reports containing detailed information about customer car wishlists. The report provides insights into customer preferences, wishlist behavior, and subscription patterns to support business analysis and decision-making.

## Requirements

### Requirement 1

**User Story:** As an internal user, I want to download a car wishlist report in CSV format, so that I can analyze customer wishlist data for business insights.

#### Acceptance Criteria

1. WH<PERSON> I access the car wishlist report download feature THEN the system SHALL provide a user interface for specifying report criteria
2. WHEN I submit the report request THEN the system SHALL generate a CSV file containing the requested data
3. WHEN the CSV file is generated THEN the system SHALL provide a download link or automatically trigger the download

### Requirement 2

**User Story:** As an internal user, I want to filter the car wishlist report by customer phone number, so that I can analyze specific customer wishlist behavior.

#### Acceptance Criteria

1. WHEN I specify a customer phone number filter THEN the system SHALL only include wishlist records for customers with matching phone numbers
2. IF no phone number is specified THEN the system SHALL include all customer wishlist records
3. WHEN I enter an invalid phone number format THEN the system SHALL display appropriate validation errors

### Requirement 3

**User Story:** As an internal user, I want to filter the car wishlist report by car model codes, so that I can analyze wishlist patterns for specific vehicle types.

#### Acceptance Criteria

1. WHEN I select one or more car model codes THEN the system SHALL only include wishlist records for cars with matching model codes
2. WHEN I select multiple car model codes THEN the system SHALL include records matching any of the selected codes
3. IF no car model codes are specified THEN the system SHALL include all car model types

### Requirement 4

**User Story:** As an internal user, I want to filter the car wishlist report by date range, so that I can analyze wishlist activity within specific time periods.

#### Acceptance Criteria

1. WHEN I specify a date range THEN the system SHALL filter records based on the car_wishlist.updateDate field
2. WHEN I specify only a start date THEN the system SHALL include all records from that date onwards
3. WHEN I specify only an end date THEN the system SHALL include all records up to that date
4. WHEN I specify both start and end dates THEN the system SHALL include records within that range (inclusive)
5. WHEN I enter invalid date formats THEN the system SHALL display appropriate validation errors

### Requirement 5

**User Story:** As an internal user, I want the CSV report to contain comprehensive customer and vehicle information, so that I can perform detailed analysis.

#### Acceptance Criteria

1. WHEN the CSV is generated THEN it SHALL include the following columns in order:
   - Customer Name (客戶姓名)
   - Phone Number (手機號碼)
   - Total Wishlist Count (收藏總台數) - excluding deleted items
   - Wishlist Add Date (加入收藏日期) - car_wishlist.updateDate
   - License Plate Number (車牌號碼)
   - Brand Name (廠牌名稱)
   - Car Model Name (車型名稱)
   - Subscription Type (訂閱類別)
   - Manufacturing Year (出廠年份)
   - Subscription Plan Name (訂閱方案名稱)
   - Inventory Status (庫存狀態)
   - Was In Wishlist When Ordered (是否已訂閱)
   - Monthly Fee at Wishlist Time (收藏時月費)
   - Mileage Rate at Wishlist Time (收藏時里程費率)
   - Security Deposit at Wishlist Time (收藏時保證金)

### Requirement 6

**User Story:** As an internal user, I want to track whether customers had cars in their wishlist when they placed orders, so that I can analyze the correlation between wishlist usage and actual subscriptions.

#### Acceptance Criteria

1. WHEN generating the report THEN the system SHALL determine if each car was in the customer's wishlist when they placed an order
2. WHEN a customer ordered a car that was in their wishlist THEN the "Was In Wishlist When Ordered" field SHALL show "是" (Yes)
3. WHEN a customer ordered a car that was not in their wishlist THEN the "Was In Wishlist When Ordered" field SHALL show "否" (No)
4. WHEN there is no order history for a car THEN the "Was In Wishlist When Ordered" field SHALL show "無訂單記錄" (No Order Record)

### Requirement 7

**User Story:** As a system administrator, I want an efficient way to track wishlist-to-order correlations, so that report generation performance is optimized.

#### Acceptance Criteria

1. WHEN the system needs to track wishlist-to-order correlations THEN it SHALL use an efficient storage mechanism
2. WHEN a new order is created THEN the system SHALL record whether the car was in the customer's wishlist at that time
3. WHEN migrating existing data THEN the system SHALL extract wishlist information from existing order remarks where available
4. WHEN generating reports THEN the system SHALL use the optimized storage mechanism for better performance

### Requirement 8

**User Story:** As an internal user, I want the report to handle large datasets efficiently, so that I can generate reports without system performance issues.

#### Acceptance Criteria

1. WHEN generating reports with large datasets THEN the system SHALL implement pagination or streaming to avoid memory issues
2. WHEN the report generation takes longer than expected THEN the system SHALL provide progress indicators or async processing
3. WHEN the system encounters errors during report generation THEN it SHALL provide meaningful error messages to the user

### Requirement 9

**User Story:** As an internal user, I want proper access control for the report feature, so that only authorized personnel can access sensitive customer data.

#### Acceptance Criteria

1. WHEN accessing the report feature THEN the system SHALL verify user authorization
2. WHEN an unauthorized user attempts to access the feature THEN the system SHALL deny access and log the attempt
3. WHEN generating reports THEN the system SHALL log the activity for audit purposes