# Design Document

## Overview

The car wishlist report feature provides internal users with the ability to generate comprehensive CSV reports containing detailed customer wishlist data. The system will implement efficient data retrieval, filtering capabilities, and optimized storage for wishlist-to-order correlation tracking.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Internal User Interface] --> B[CarWishlistInternalController]
    B --> C[CarWishlistService]
    C --> D[CarWishlistRepository]
    C --> E[AuthServer]
    C --> F[CarsService]
    C --> G[CarWishlistOrderTrackingService]
    G --> H[CarWishlistOrderTracking Table]
    D --> I[CarWishlist Table]
    E --> J[Auth Service]
    F --> K[Cars/CarModel/CarBrand Tables]
    
    C --> L[CSVReportGenerator]
    L --> M[CSV File Output]
```

### Component Interaction Flow

1. **Request Processing**: Controller receives report request with filters
2. **Data Retrieval**: Service layer queries wishlist data with applied filters
3. **Data Enrichment**: Additional customer and vehicle information is fetched
4. **Correlation Analysis**: Wishlist-to-order correlation data is retrieved
5. **Report Generation**: CSV file is generated with all required columns
6. **Response**: CSV file is returned to the user

## Components and Interfaces

### 1. CarWishlistInternalController (Existing - Enhanced)

**Purpose**: Handle HTTP requests for internal wishlist operations including report generation

**New Methods**:
- `downloadWishlistReport(CarWishlistReportRequest request)`: Generate and download CSV report

**Request Model**:
```java
public class CarWishlistReportRequest {
    private String phoneNumber;           // Optional customer phone filter
    private List<String> carModelCodes;   // Optional car model filter
    private LocalDate startDate;          // Optional start date filter
    private LocalDate endDate;            // Optional end date filter
}
```

### 2. CarWishlistService (Existing - Enhanced)

**Purpose**: Core business logic for wishlist operations including report generation

**New Methods**:
- `generateWishlistReport(CarWishlistReportRequest request)`: Main report generation logic
- `buildReportData(List<CarWishlist> wishlists)`: Transform data for report
- `enrichWithCustomerData(List<CarWishlistReportData> data)`: Add customer information
- `enrichWithVehicleData(List<CarWishlistReportData> data)`: Add vehicle information
- `addWishlistOrderCorrelation(List<CarWishlistReportData> data)`: Add order correlation data

### 3. CarWishlistOrderTrackingService

**Purpose**: Manage wishlist-to-order correlation tracking

**Key Methods**:
- `recordWishlistOrder(String orderNo, Integer acctId, String plateNo)`: Record when a customer orders a car that was in their wishlist
- `wasCarInWishlistWhenOrdered(Integer acctId, String plateNo)`: Check if car was in wishlist when any order was placed
- `migrateExistingRemarks()`: Migrate data from existing order remarks

### 4. CSVReportGenerator

**Purpose**: Generate CSV files from report data

**Key Methods**:
- `generateCSV(List<CarWishlistReportData> data)`: Create CSV content
- `formatHeaders()`: Define CSV column headers
- `formatRow(CarWishlistReportData data)`: Format individual data rows

## Data Models

### CarWishlistReportData

```java
public class CarWishlistReportData {
    private String customerName;           // 客戶姓名
    private String phoneNumber;            // 手機號碼
    private Integer totalWishlistCount;    // 收藏總台數
    private Instant wishlistAddDate;       // 加入收藏日期
    private String plateNo;                // 車牌號碼
    private String brandName;              // 廠牌名稱
    private String carModelName;           // 車型名稱
    private String subscriptionType;       // 訂閱類別
    private String manufacturingYear;      // 出廠年份
    private String subscriptionPlanName;   // 訂閱方案名稱
    private String inventoryStatus;        // 庫存狀態
    private String wasInWishlistWhenOrdered; // 是否已訂閱
    private Integer monthlyFeeAtWishlist;  // 收藏時月費
    private Double mileageRateAtWishlist;  // 收藏時里程費率
    private Integer securityDepositAtWishlist; // 收藏時保證金
}
```

### CarWishlistOrderTracking (New Entity)

```java
@Entity
@Table(name = "car_wishlist_order_tracking")
public class CarWishlistOrderTracking extends GeneralEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "order_no", nullable = false)
    private String orderNo;
    
    @Column(name = "acct_id", nullable = false)
    private Integer acctId;
    
    @Column(name = "plate_no", nullable = false)
    private String plateNo;
    
    // Constructors, getters, setters
}
```

Note: 
- The actual database table will be created manually in another project
- Records are only created when a customer orders a car that was in their wishlist
- The existence of a record indicates the car was in the customer's wishlist when ordered
- Inherits createDate and updateDate from GeneralEntity for tracking purposes

## Error Handling

### Validation Errors
- **Invalid Phone Number**: Throw SubscribeException with custom error code for invalid phone format
- **Invalid Date Range**: Throw SubscribeException with custom error code for invalid date range
- **Invalid Car Model Codes**: Throw SubscribeException with custom error code for invalid model codes
- **Empty Report Data**: Throw SubscribeException with custom error code when no data matches filters

### System Errors
- **Database Connection Issues**: Throw SubscribeException with custom error code for database errors
- **Memory Issues**: Implement streaming/pagination for large datasets, throw SubscribeException for memory limits
- **File Generation Errors**: Throw SubscribeException with custom error code for CSV generation failures

### Custom Exception Codes
New SubscribeHttpExceptionCode entries needed:
- `WISHLIST_REPORT_INVALID_PHONE_FORMAT`: Invalid phone number format
- `WISHLIST_REPORT_INVALID_DATE_RANGE`: Invalid date range specified
- `WISHLIST_REPORT_INVALID_CAR_MODEL_CODES`: Invalid car model codes
- `WISHLIST_REPORT_NO_DATA_FOUND`: No data found matching the specified criteria
- `WISHLIST_REPORT_GENERATION_FAILED`: Failed to generate wishlist report
- `WISHLIST_REPORT_CSV_GENERATION_FAILED`: Failed to generate CSV file

### Error Response Format
```json
{
    "statusCode": "94010",
    "message": "Invalid phone number format",
    "data": null
}
```

## Testing Strategy

### Unit Tests
- **CarWishlistReportService**: Test report generation logic with various filter combinations
- **CSVReportGenerator**: Test CSV formatting and content generation
- **CarWishlistOrderTrackingService**: Test correlation tracking and data migration

### Integration Tests
- **End-to-End Report Generation**: Test complete flow from request to CSV download
- **Database Integration**: Test data retrieval with complex filters
- **Performance Testing**: Test with large datasets to ensure acceptable response times

### Test Data Requirements
- Sample wishlist data with various customer and vehicle combinations
- Order data with existing remarks containing wishlist information
- Edge cases: empty results, maximum dataset sizes, invalid filters

## Performance Considerations

### Database Optimization
- **Indexing Strategy**: 
  - Index on `car_wishlist.acct_id` and `update_date` for filtering
  - Index on `car_wishlist_order_tracking.acct_id` and `plate_no` for correlation lookup
  - Composite indexes for common filter combinations

### Memory Management
- **Streaming Processing**: Process large datasets in chunks to avoid memory issues
- **Lazy Loading**: Load related data only when needed
- **Connection Pooling**: Efficient database connection management

### Caching Strategy
- **Customer Data**: Cache frequently accessed customer information
- **Vehicle Data**: Cache car model and brand information
- **Report Metadata**: Cache report configuration and formatting rules

## Security Considerations

### Access Control
- **Role-Based Authorization**: Only internal users with appropriate roles can access reports
- **API Authentication**: Secure API endpoints with proper authentication
- **Audit Logging**: Log all report generation activities for compliance

### Data Protection
- **Sensitive Data Handling**: Ensure customer PII is handled according to privacy regulations
- **Data Masking**: Consider masking sensitive information in logs
- **Secure File Transfer**: Ensure CSV files are transmitted securely

## Migration Strategy

### Existing Data Migration
1. **Identify Existing Remarks**: Query orders with remarks containing "有加入用戶收藏清單"
2. **Extract Correlation Data**: Parse existing remarks to determine wishlist status
3. **Populate New Table**: Insert correlation data into `car_wishlist_order_tracking`
4. **Validation**: Verify migration accuracy with sample data checks

### Deployment Considerations
- **Database Schema Changes**: Deploy new table creation scripts
- **Backward Compatibility**: Ensure existing functionality continues to work
- **Rollback Plan**: Prepare rollback procedures in case of issues

## Monitoring and Maintenance

### Performance Monitoring
- **Report Generation Time**: Monitor average and maximum generation times
- **Database Query Performance**: Track slow queries and optimize as needed
- **Memory Usage**: Monitor memory consumption during large report generation

### Maintenance Tasks
- **Data Cleanup**: Regular cleanup of old tracking records if needed
- **Index Maintenance**: Monitor and maintain database indexes
- **Log Rotation**: Manage audit logs and system logs appropriately