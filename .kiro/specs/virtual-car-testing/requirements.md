# Requirements Document

## Introduction

This feature focuses on comprehensive testing of the `addSingleCars` API endpoint for virtual car scenarios using MockMvc. The testing will cover both failure and success cases to ensure robust validation and proper functionality of virtual car creation in the subscription system.

## Requirements

### Requirement 1

**User Story:** As a QA engineer, I want to test virtual car creation failure scenarios, so that I can ensure proper validation and error handling.

#### Acceptance Criteria

1. WHEN a virtual car is created with invalid plate number format THEN the system SHALL return appropriate validation error
2. WHEN a virtual car is created with missing required fields THEN the system SHALL return field validation errors
3. WHEN a virtual car is created with invalid energy type THEN the system SHALL return energy type validation error
4. WHEN a virtual car is created with invalid displacement value THEN the system SHALL return displacement validation error
5. WHEN a virtual car is created with invalid seat count THEN the system SHALL return seat count validation error
6. WHEN a virtual car is created with invalid manufacturing year THEN the system SHALL return manufacturing year validation error
7. WHEN a virtual car is created with invalid standard price THEN the system SHALL return price validation error
8. WHEN a virtual car is created with missing location station code THEN the system SHALL return location validation error

### Requirement 2

**User Story:** As a QA engineer, I want to test virtual car creation success scenarios, so that I can verify the system correctly processes valid virtual car data.

#### Acceptance Criteria

1. WHEN a virtual car is created with valid RAA prefix plate number THEN the system SHALL successfully create the virtual car
2. WHEN a virtual car is created with all required fields THEN the system SHALL persist the car data correctly
3. WHEN a virtual car is created successfully THEN the system SHALL return status code 0
4. WHEN a virtual car is created successfully THEN the system SHALL generate proper car number encoding
5. WHEN a virtual car is created successfully THEN the system SHALL set isVirtualCar flag to true
6. WHEN a virtual car is created successfully THEN the system SHALL validate the car can be retrieved via GET endpoint

### Requirement 3

**User Story:** As a QA engineer, I want to test virtual car validation rules, so that I can ensure business logic is properly enforced.

#### Acceptance Criteria

1. WHEN a virtual car plate number does not start with "RAA" THEN the system SHALL reject the request
2. WHEN a virtual car plate number is not exactly 7 characters THEN the system SHALL reject the request
3. WHEN a virtual car has null energy type THEN the system SHALL return validation error
4. WHEN a virtual car has null displacement THEN the system SHALL return validation error
5. WHEN a virtual car has null seat count THEN the system SHALL return validation error
6. WHEN a virtual car has null manufacturing year THEN the system SHALL return validation error
7. WHEN a virtual car has null standard price THEN the system SHALL return validation error
8. WHEN a virtual car has null location station code THEN the system SHALL return validation error

### Requirement 4

**User Story:** As a QA engineer, I want to test edge cases and boundary conditions, so that I can ensure system stability under various input conditions.

#### Acceptance Criteria

1. WHEN a virtual car is created with minimum valid values THEN the system SHALL accept the request
2. WHEN a virtual car is created with maximum valid values THEN the system SHALL accept the request
3. WHEN a virtual car is created with duplicate plate number THEN the system SHALL return appropriate error
4. WHEN a virtual car is created with invalid subscribe level THEN the system SHALL return subscribe level error
5. WHEN a virtual car is created with conflicting tag combinations THEN the system SHALL return tag validation error