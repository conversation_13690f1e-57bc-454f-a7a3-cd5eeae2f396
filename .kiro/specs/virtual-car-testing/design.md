# Design Document

## Overview

This design outlines a comprehensive testing strategy for the `addSingleCars` API endpoint focusing on virtual car scenarios. The testing framework will use <PERSON> Boot's MockMvc to simulate HTTP requests and validate both success and failure scenarios for virtual car creation.

## Architecture

### Test Structure Extension
The tests will be added to the existing `CarsInternalControllerTest` class, following the established patterns:

```
CarsInternalControllerTest (existing)
├── Existing Test Methods
├── NEW: Virtual Car Failure Test Methods
│   ├── addSingleCars_VirtualCar_InvalidPlateFormat_Failure()
│   ├── addSingleCars_VirtualCar_MissingRequiredFields_Failure()
│   ├── addSingleCars_VirtualCar_InvalidFieldValues_Failure()
│   └── addSingleCars_VirtualCar_BusinessRuleViolations_Failure()
├── NEW: Virtual Car Success Test Methods
│   ├── addSingleCars_VirtualCar_ValidCreation_Success()
│   ├── addSingleCars_VirtualCar_DataPersistence_Success()
│   └── addSingleCars_VirtualCar_Retrieval_Success()
└── NEW: Virtual Car Edge Case Test Methods
    ├── addSingleCars_VirtualCar_BoundaryValues_Success()
    ├── addSingleCars_VirtualCar_DuplicatePlate_Failure()
    └── addSingleCars_VirtualCar_ComplexValidations_Failure()
```

### Reuse Existing Patterns

#### 1. Follow Existing Test Method Naming
- Use existing naming convention: `methodName_scenario_expectedResult()`
- Reuse existing MockMvc request building patterns
- Follow existing assertion patterns with `jsonPath()` and `status()`

#### 2. Reuse Existing Infrastructure
- Use existing `@Autowired MockMvc mockMvc`
- Use existing `@Autowired CarsService carsService`
- Use existing `MEMBER_ID` constant
- Follow existing `@Transactional` approach

#### 3. Reuse Existing JSON Building Patterns
- Follow existing inline JSON string building approach
- Use existing parameter substitution patterns
- Reuse existing content type and header configurations
```

## Data Models

### Virtual Car Test Data Structure

```json
{
  "plateNo": "RAA0001",
  "currentMileage": 0,
  "carModelCode": "S000P",
  "carState": "NEW",
  "subscribeLevel": 29,
  "launched": "open",
  "isSealandLaunched": false,
  "locationStationCode": "233",
  "tagIds": [3],
  "equipIds": [23, 22, 2, 21, 15, 16],
  "gearType": "at",
  "colorDesc": "白色",
  "fuelType": "petrol95",
  "energyType": "GASOLINE",
  "vatNo": "12208883",
  "displacement": 1997,
  "seat": 5,
  "mfgYear": "2025",
  "mfgMonth": "01",
  "prepWorkdays": 7,
  "carType": "sedan",
  "stdPrice": 1500000,
  "isVirtualCar": true
}
```

### Test Scenarios Matrix

| Test Case | Plate No | Expected Result | Validation Point |
|-----------|----------|-----------------|------------------|
| Valid Virtual Car | RAA0001 | Success | Complete flow |
| Invalid Prefix | XAA0001 | Failure | Plate format |
| Invalid Length | RAA001 | Failure | Plate length |
| Missing Energy Type | RAA0002 | Failure | Required field |
| Missing Displacement | RAA0003 | Failure | Required field |
| Missing Seat | RAA0004 | Failure | Required field |
| Missing Mfg Year | RAA0005 | Failure | Required field |
| Missing Std Price | RAA0006 | Failure | Required field |
| Missing Location | RAA0007 | Failure | Required field |
| Duplicate Plate | RAA0001 | Failure | Business rule |

## Error Handling

### Expected Error Responses

1. **Validation Errors**: HTTP 400 with field-specific error messages
2. **Business Rule Violations**: HTTP 400 with business logic error messages  
3. **Duplicate Entries**: HTTP 400 with duplicate error message
4. **System Errors**: HTTP 500 with generic error message

### Error Message Validation

Tests will validate:
- Correct HTTP status codes
- Appropriate error messages in response body
- Proper error code mapping
- Response structure consistency

## Testing Strategy

### Test Execution Flow

1. **Setup Phase**
   - Initialize test database state
   - Prepare test data builders
   - Configure MockMvc environment

2. **Failure Testing Phase**
   - Execute invalid input tests
   - Validate error responses
   - Verify no data persistence on failures

3. **Success Testing Phase**
   - Execute valid input tests
   - Validate success responses
   - Verify correct data persistence
   - Confirm data retrieval

4. **Edge Case Testing Phase**
   - Execute boundary value tests
   - Test complex validation scenarios
   - Verify system stability

### Assertions Strategy

- **Response Validation**: Status codes, response structure, error messages
- **Data Persistence**: Database state verification after operations
- **Business Logic**: Virtual car specific validations and rules
- **Integration**: End-to-end flow validation from request to database

## Performance Considerations

- Tests will use `@Transactional` for database rollback
- MockMvc provides fast in-memory testing without full server startup
- Test data builders minimize object creation overhead
- Parameterized tests reduce code duplication

## Security Considerations

- Tests will include proper authentication headers
- Input validation testing covers security edge cases
- SQL injection prevention through parameterized queries
- Authorization testing for different user roles