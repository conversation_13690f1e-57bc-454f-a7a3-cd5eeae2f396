# Implementation Plan

- [x] 1. Add virtual car failure test methods to existing test class
  - Create test methods for invalid plate number format validation
  - Create test methods for missing required fields validation  
  - Create test methods for invalid field values validation
  - Create test methods for business rule violations validation
  - Follow existing test naming conventions and patterns
  - Use existing MockMvc and assertion patterns
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8_

- [ ] 2. Add virtual car success test methods to existing test class
  - Create test method for valid virtual car creation with RAA prefix
  - Create test method to verify data persistence after successful creation
  - Create test method to verify car retrieval via GET endpoint
  - Validate proper car number encoding for virtual cars
  - Verify isVirtualCar flag is set correctly
  - Use existing CarsService for data verification
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 3. Add virtual car edge case test methods to existing test class
  - Create test method for boundary value testing (min/max valid values)
  - Create test method for duplicate plate number handling
  - Create test method for complex validation scenarios (tag combinations)
  - Create test method for subscribe level validation
  - Follow existing error assertion patterns
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 4. Implement comprehensive virtual car validation test scenarios
  - Add test for virtual car with invalid plate prefix (non-RAA)
  - Add test for virtual car with incorrect plate length (not 7 chars)
  - Add test for virtual car with missing energy type
  - Add test for virtual car with missing displacement
  - Add test for virtual car with missing seat count
  - Add test for virtual car with missing manufacturing year
  - Add test for virtual car with missing standard price
  - Add test for virtual car with missing location station code
  - Use existing JSON request building patterns
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8_

- [ ] 5. Implement virtual car success flow validation tests
  - Add test for complete virtual car creation with all valid fields
  - Add test to verify virtual car data is correctly persisted in database
  - Add test to verify virtual car can be retrieved via GET /internal/subscribe/cars/{plateNo}
  - Add test to verify virtual car properties (isVirtualCar flag, car number encoding)
  - Add test to verify virtual car appears in car listing endpoints
  - Use existing database verification patterns with CarsService
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 6. Implement virtual car edge case and boundary testing
  - Add test for virtual car with minimum valid values (boundary testing)
  - Add test for virtual car with maximum valid values (boundary testing)
  - Add test for duplicate virtual car plate number creation
  - Add test for virtual car with invalid subscribe level
  - Add test for virtual car with conflicting tag combinations
  - Add test for virtual car with various invalid field combinations
  - Follow existing error response validation patterns
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 7. Add comprehensive error response validation for virtual car tests
  - Validate HTTP status codes for all failure scenarios
  - Validate error message content and structure
  - Validate error codes match expected business logic errors
  - Ensure no data persistence occurs on validation failures
  - Use existing jsonPath assertions for error response validation
  - Follow existing error testing patterns from other test methods
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 4.3, 4.4, 4.5_

- [ ] 8. Integrate virtual car tests with existing test infrastructure
  - Ensure all new tests use existing @Transactional rollback mechanism
  - Reuse existing MEMBER_ID constant and authentication headers
  - Follow existing MockMvc request builder patterns
  - Use existing assertion helper patterns and jsonPath validations
  - Ensure tests are independent and can run in any order
  - Verify tests work with existing test database setup
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 4.1, 4.2, 4.3, 4.4, 4.5_