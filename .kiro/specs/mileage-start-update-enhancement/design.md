# Design Document

## Overview

This design document outlines the enhancement to the mileage fee setting API to support updating both start mileage (previousMileage) and end mileage (currentMileage). The enhancement will extend the existing `InternalUpdateMileagePriceInfoRequest` class and modify the related service methods to handle the new functionality while maintaining backward compatibility.

## Architecture

The enhancement follows the existing layered architecture pattern:

```
Controller Layer (InternalPriceInfoController)
    ↓
Service Layer (PriceInfoService)
    ↓
Repository Layer (OrderPriceInfoRepository)
    ↓
Database (MySQL)
```

The modification will primarily affect:
- Request DTOs: `UpdateMileagePriceInfoRequest` and `InternalUpdateMileagePriceInfoRequest`
- Service methods: `calculateMillageFeeValidate` and `calculateMillageFee`
- Validation logic for mileage consistency
- Fee calculation logic

## Components and Interfaces

### 1. Request DTO Enhancement

#### UpdateMileagePriceInfoRequest
No changes needed - this base class remains unchanged to maintain compatibility with other usages.

#### InternalUpdateMileagePriceInfoRequest
```java
@Data
public class InternalUpdateMileagePriceInfoRequest extends UpdateMileagePriceInfoRequest {
    @NotNull(message = "使用者編號不可為空")
    @Schema(description = "使用者編號")
    private Integer acctId;
    
    // NEW FIELD
    @Schema(description = "前次里程 (起始里程)")
    private Integer previousMileage;
}
```

### 2. Service Layer Enhancements

#### PriceInfoService.calculateMillageFeeValidate
Enhanced validation method signature:
```java
public void calculateMillageFeeValidate(String orderNo, int acctId, Integer orderPriceInfoId, Integer previousMileage)
```

New validation logic:
- Validate previousMileage is positive when provided
- Validate previousMileage ≤ currentMileage when both provided
- Validate previousMileage is not smaller than previous stage's endMileage
- Validate previousMileage business rules for specific stages
- Validate stage-specific constraints for previousMileage

#### PriceInfoService.calculateMillageFee
Enhanced calculation method signature:
```java
public OrderPriceInfo calculateMillageFee(String orderNo, int acctId, int currentMileage, Integer orderPriceInfoId, Integer previousMileage)
```

New calculation logic:
- Use previousMileage for startMileage when provided
- Fall back to existing logic when previousMileage is null
- Allow discontinuous mileage records (no cascading updates to previous stages)
- Recalculate current stage's dependent fields only
- Do NOT update previous stage's endMileage when updating current stage's startMileage

### 3. Controller Layer Updates

#### InternalPriceInfoController.setMileage
Updated method to pass previousMileage to service methods:
```java
@PatchMapping(value = "/subscribe/v1/priceInfo/{orderNo}/mileageAmt")
public OrderPriceInfo setMileage(
    @RequestHeader(name = CarPlusConstant.AUTH_HEADER_MEMBER) String memberId,
    @RequestBody @Validated InternalUpdateMileagePriceInfoRequest updateMileagePriceInfoRequest,
    @PathVariable("orderNo") String orderNo) {
    
    priceInfoService.calculateMillageFeeValidate(
        orderNo, 
        updateMileagePriceInfoRequest.getAcctId(), 
        updateMileagePriceInfoRequest.getOrderPriceInfoId(),
        updateMileagePriceInfoRequest.getPreviousMileage()
    );
    
    return priceInfoService.calculateMillageFee(
        orderNo, 
        updateMileagePriceInfoRequest.getAcctId(), 
        updateMileagePriceInfoRequest.getCurrentMileage(), 
        updateMileagePriceInfoRequest.getOrderPriceInfoId(),
        updateMileagePriceInfoRequest.getPreviousMileage()
    );
}
```

## Data Models

### PriceInfoDetail
No changes to the data model - existing fields will be used:
- `startMileage` - updated when previousMileage is provided
- `endMileage` - updated with currentMileage as before
- `totalMileage` - recalculated based on new mileage range
- `discountMileage` - remains unchanged
- `mileageFee` - remains unchanged

### OrderPriceInfo
No structural changes - existing fields will be updated:
- `amount` - recalculated based on new mileage range
- `infoDetail` - updated with new mileage values

## Error Handling

### New Validation Errors

1. **PREVIOUS_MILEAGE_INVALID**
   - Code: 400
   - Message: "前次里程必須為正整數"
   - Condition: previousMileage ≤ 0

2. **PREVIOUS_MILEAGE_GREATER_THAN_CURRENT**
   - Code: 400
   - Message: "前次里程不可大於目前里程"
   - Condition: previousMileage > currentMileage

3. **PREVIOUS_MILEAGE_TOO_SMALL**
   - Code: 400
   - Message: "前次里程不可小於前一期結束里程"
   - Condition: previousMileage < previous stage's endMileage

4. **STAGE_ONE_PREVIOUS_MILEAGE_CONFLICT**
   - Code: 400
   - Message: "第一期前次里程必須與出車里程一致"
   - Condition: Stage 1 previousMileage doesn't match departMileage

### Existing Error Handling
All existing error conditions remain unchanged:
- ORDER_PRICE_INFO_MILEAGE_FEE_HAS_PAYED
- ORDER_PRICE_INFO_LAST_END_MILEAGE_NOT_FOUND
- UPDATED_DEPART_MILEAGE_SHOULD_NOT_GREATER_THAN_DETAIL_END_MILEAGE

## Testing Strategy

### Integration Tests
1. **ContractInternalControllerTest Enhancement**
   - Update existing mileage fee tests to use OrderTestContext
   - Add new test scenarios for previousMileage functionality
   - Test end-to-end mileage fee setting with both parameters

2. **Database Integration Tests**
   - Test data persistence with new mileage values
   - Test transaction rollback on validation failures
   - Test isolated updates without affecting previous stages

### Test Scenarios
1. **Successful Updates**
   - Update both previousMileage and currentMileage
   - Update only currentMileage (backward compatibility)
   - Update with discontinuous mileage records (previousMileage > previous stage's endMileage)

2. **Validation Failures**
   - previousMileage > currentMileage
   - previousMileage is smaller than previous stage's endMileage
   - Stage 1 previousMileage conflicts with departMileage
   - Attempt to update paid mileage fee

3. **Edge Cases**
   - Stage 1 mileage fee updates
   - Final stage mileage fee updates
   - Multiple unpaid mileage fees

## Implementation Considerations

### Backward Compatibility
- All existing API calls without previousMileage will work unchanged
- Existing validation logic remains intact
- Response format remains the same

### Performance Impact
- Minimal performance impact as the enhancement reuses existing logic
- Additional validation steps are lightweight
- Database queries remain similar in complexity

### Data Consistency
- Mileage validation ensures data integrity
- Transaction boundaries prevent partial updates
- Each stage's mileage data is updated independently without affecting other stages

### Security Considerations
- Existing authentication and authorization remain unchanged
- Input validation prevents malicious data injection
- Audit trail through existing tracking mechanisms