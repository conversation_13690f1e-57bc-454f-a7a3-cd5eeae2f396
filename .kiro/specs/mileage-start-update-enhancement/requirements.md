# Requirements Document

## Introduction

This feature enhances the existing mileage fee setting API endpoint `[PATCH] /internal/subscribe/v1/priceInfo/{orderNo}/mileageAmt` to support updating both the start mileage (previousMileage) and end mileage (currentMileage) of a mileage fee record. Currently, the API only allows updating the end mileage, but business requirements now need the ability to also modify the start mileage for more flexible mileage fee calculations.

## Requirements

### Requirement 1

**User Story:** As an internal system administrator, I want to update both the start mileage and end mileage when setting mileage fees, so that I can correct mileage records and ensure accurate fee calculations.

#### Acceptance Criteria

1. WHEN the API receives a request with previousMileage field THEN the system SHALL update the OrderPriceInfo.infoDetail.startMileage with the provided previousMileage value
2. WHEN the API receives a request with currentMileage field THEN the system SHALL update the OrderPriceInfo.infoDetail.endMileage with the provided currentMileage value
3. WHEN both previousMileage and currentMileage are provided THEN the system SHALL validate that previousMileage is less than or equal to currentMileage
4. WHEN the startMileage is updated THEN the system SHALL recalculate the mileage fee amount based on the new mileage range
5. WHEN the startMileage is updated THEN the system SHALL update all dependent fields including totalMileage and amount

### Requirement 2

**User Story:** As an internal system administrator, I want the system to validate mileage updates properly, so that data integrity is maintained and business rules are enforced.

#### Acceptance Criteria

1. WHEN previousMileage is provided THEN the system SHALL validate that it is a positive integer
2. WHEN previousMileage is provided for stage 1 mileage fee THEN the system SHALL validate that it matches or is compatible with the order's departMileage
3. WHEN previousMileage is provided for non-stage 1 mileage fee THEN the system SHALL validate that it is consistent with the previous stage's endMileage
4. WHEN updating startMileage THEN the system SHALL check that the mileage fee record has not been paid
5. WHEN updating startMileage THEN the system SHALL validate that the new startMileage does not conflict with existing mileage records

### Requirement 3

**User Story:** As an internal system administrator, I want the system to validate mileage constraints properly, so that data integrity is maintained while allowing flexible mileage adjustments.

#### Acceptance Criteria

1. WHEN previousMileage is provided THEN the system SHALL validate that it is not smaller than the previous stage's endMileage
2. WHEN previousMileage is greater than the previous stage's endMileage THEN the system SHALL allow the discontinuous mileage record
3. WHEN mileage updates are made THEN the system SHALL recalculate the current stage's fee amount based on the new mileage range
4. WHEN mileage updates are completed THEN the system SHALL update the order's reportMileage with the currentMileage
5. WHEN startMileage is updated THEN the system SHALL NOT automatically update subsequent stage records

### Requirement 4

**User Story:** As an internal system administrator, I want proper error handling and validation messages, so that I can understand and resolve any issues with mileage updates.

#### Acceptance Criteria

1. WHEN previousMileage is greater than currentMileage THEN the system SHALL return a validation error with a clear message
2. WHEN attempting to update a paid mileage fee record THEN the system SHALL return an error indicating the record cannot be modified
3. WHEN previousMileage conflicts with existing mileage chain THEN the system SHALL return a specific error message explaining the conflict
4. WHEN validation fails THEN the system SHALL not modify any data and return appropriate error codes
5. WHEN updates are successful THEN the system SHALL return the updated OrderPriceInfo with all recalculated values

### Requirement 5

**User Story:** As a system integrator, I want the API to maintain backward compatibility, so that existing integrations continue to work without modification.

#### Acceptance Criteria

1. WHEN the API is called without the previousMileage field THEN the system SHALL behave exactly as before the enhancement
2. WHEN only currentMileage is provided THEN the system SHALL update endMileage using existing logic
3. WHEN the API response is returned THEN it SHALL maintain the same structure as the current implementation
4. WHEN existing validation rules apply THEN they SHALL continue to work as before
5. WHEN the previousMileage field is null or not provided THEN the system SHALL use the existing startMileage calculation logic

### Requirement 6

**User Story:** As a developer, I want comprehensive test coverage for the mileage fee enhancement, so that the functionality is properly validated and regression issues are prevented.

#### Acceptance Criteria

1. WHEN implementing the enhancement THEN existing mileage fee tests in ContractInternalControllerTest SHALL be updated to accommodate the new functionality
2. WHEN no existing relevant tests exist THEN new comprehensive tests SHALL be created for the mileage fee setting functionality
3. WHEN writing tests THEN they SHALL use OrderTestContext instead of hardcoded values for better maintainability
4. WHEN testing the previousMileage functionality THEN tests SHALL cover both successful updates and validation error scenarios
5. WHEN testing backward compatibility THEN tests SHALL verify that existing API behavior remains unchanged when previousMileage is not provided