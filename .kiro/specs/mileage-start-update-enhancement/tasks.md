# Implementation Plan

- [x] 1. <PERSON><PERSON><PERSON> request DTO to support previousMileage parameter





  - Add previousMileage field to InternalUpdateMileagePriceInfoRequest class
  - Add appropriate validation annotations and Swagger documentation
  - Ensure backward compatibility by making the field optional
  - _Requirements: 1.1, 5.5_

- [x] 2. Update service validation method to handle previousMileage





  - Modify calculateMillageFeeValidate method signature to accept previousMileage parameter
  - Add validation logic for previousMileage when provided
  - Implement validation for previousMileage ≤ currentMileage
  - Add validation for previousMileage ≥ previous stage's endMileage (allow discontinuous records)
  - Add special validation for stage 1 previousMileage vs departMileage
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 3.1_

- [x] 3. Update service calculation method to use previousMileage

  - Modify calculateMillageFee method signature to accept previousMileage parameter
  - Update startMileage setting logic to use previousMileage when provided
  - Maintain existing logic when previousMileage is null for backward compatibility
  - Ensure proper recalculation of totalMileage and amount fields for current stage only
  - _Requirements: 1.1, 1.4, 1.5, 3.3, 5.1, 5.2_

- [x] 4. Update controller endpoint to pass previousMileage to service methods


  - Modify setMileage method in InternalPriceInfoController
  - Pass previousMileage parameter to both validation and calculation service methods
  - Ensure proper error handling and response format consistency
  - _Requirements: 1.1, 4.3, 5.3_

- [x] 5. Add comprehensive error handling and validation messages





  - Create new exception constants for previousMileage validation errors
  - Implement user-friendly error messages for validation failures
  - Ensure proper error codes and response formats
  - Test error handling for all validation scenarios
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 6. Update integration tests in ContractInternalControllerTest





  - Identify existing mileage fee setting tests and update them to use OrderTestContext
  - Create new test methods for previousMileage functionality
  - Test successful updates with both previousMileage and currentMileage
  - Test backward compatibility scenarios with only currentMileage
  - Test validation error scenarios for previousMileage
  - Test discontinuous mileage record scenarios
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 7. Verify backward compatibility and integration





  - Test existing API calls without previousMileage parameter
  - Verify response format consistency
  - Test integration with existing mileage fee calculation workflows
  - Perform end-to-end testing with real order scenarios
  - _Requirements: 3.4, 3.5, 5.1, 5.2, 5.3, 5.4, 5.5_