# Implementation Plan

## Phase 1: 人工授信流程補強

- [x] 1. 修改 OrderService.handleFailedAutoCredit() 方法




  - 在進入人工授信階段時新增 checkIsUnpaid() 調用
  - 確保狀態檢查在通知發送前完成
  - _Requirements: 1.1, 1.2_

- [ ] 2. 修改 OrderService.approveCredit() 方法
  - 在人工授信通過後新增 checkIsUnpaid() 調用
  - 確保狀態更新在訂單保存前完成
  - _Requirements: 1.2_

- [ ] 3. 修改 OrderService.rejectCredit() 方法
  - 確保人工授信不通過時 isUnpaid 設為 false
  - 驗證狀態設定的正確性
  - _Requirements: 1.3_

- [ ] 4. 修改 OrderService.autoCredit() 方法
  - 在自動授信流程完成後新增 checkIsUnpaid() 調用
  - 處理自動授信通過和失敗的不同情況
  - _Requirements: 1.5_

- [ ] 5. 新增人工授信流程的單元測試
  - 在 ContractInternalControllerTest 中新增 testApproveCredit_ShouldUpdateUnpaidStatus()
  - 在 ContractInternalControllerTest 中新增 testRejectCredit_ShouldSetUnpaidFalse()
  - 驗證各種授信狀態下的 isUnpaid 更新
  - _Requirements: 1.1, 1.2, 1.3_

## Phase 2: AccountSettlement 流程補強

- [ ] 6. 修改 PaymentServiceV2.accountSettlement() 方法
  - 在收支登打完成後新增 checkIsUnpaid() 調用
  - 確保狀態更新在回應返回前完成
  - _Requirements: 2.2_

- [ ] 7. 修改 PaymentServiceV2.payAndDiscountBalance() 方法
  - 在付款折扣平衡後新增 checkIsUnpaid() 調用
  - 確保狀態反映最新的付款折扣情況
  - _Requirements: 2.4_

- [ ] 8. 修改 PaymentServiceV2.accountSettlementForLegalOperation() 方法
  - 在法務作業的收支登打完成後新增 checkIsUnpaid() 調用
  - 確保法務流程中的狀態一致性
  - _Requirements: 2.2, 2.5_

- [ ] 9. 新增 AccountSettlement 流程的單元測試
  - 在 ContractInternalControllerTest 中新增 testAccountSettlement_ShouldUpdateUnpaidStatus()
  - 在 ContractInternalControllerTest 中新增 testPayAndDiscountBalance_ShouldUpdateUnpaidStatus()
  - 驗證收支登打流程中的 isUnpaid 更新
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

## Phase 3: 統一狀態檢查機制強化

- [ ] 10. 檢查並優化 PriceInfoService.checkIsUnpaid() 方法
  - 確保基於當前時間和應收款項的準確計算
  - 新增必要的日誌記錄
  - _Requirements: 3.2, 3.4_

- [ ] 11. 在關鍵業務流程中新增 isUnpaid 狀態檢查
  - 檢查 OrderService.updateOrder() 方法
  - 檢查 OrderService.cancelOrder() 方法
  - 確保狀態變更時的一致性
  - _Requirements: 3.1, 3.3_

- [ ] 12. 新增批次狀態檢查支援
  - 實作批次處理大量訂單的 isUnpaid 狀態更新
  - 新增相關的管理介面或排程任務
  - _Requirements: 3.5_

## Phase 4: 查詢介面優化

- [ ] 13. 驗證訂單查詢介面的 isUnpaid 狀態顯示
  - 檢查 ContractInternalController.queryOrder() 方法
  - 確保查詢結果反映最新狀態
  - _Requirements: 4.1, 4.3_

- [ ] 14. 驗證 CSV 匯出功能的 isUnpaid 狀態
  - 檢查 ContractInternalController.orderCsv() 方法
  - 確保匯出資料包含正確的狀態資訊
  - _Requirements: 4.2_

- [ ] 15. 優化 isUnpaid 條件篩選功能
  - 檢查相關的查詢條件處理
  - 確保篩選結果符合實際付款狀態
  - _Requirements: 4.4_

## Phase 5: 整合測試與驗證

- [ ] 16. 執行完整的整合測試
  - 測試官網訂車到人工授信的完整流程
  - 測試收支登打的完整流程
  - 驗證各種邊界情況
  - _Requirements: 1.1-1.5, 2.1-2.5_

- [ ] 17. 效能測試與優化
  - 測試批次狀態更新的效能
  - 優化頻繁調用的 checkIsUnpaid() 方法
  - _Requirements: 3.5_

- [ ] 18. 文件更新與程式碼審查
  - 更新相關的 API 文件
  - 進行程式碼審查確保品質
  - 更新操作手冊
  - _Requirements: 4.5_