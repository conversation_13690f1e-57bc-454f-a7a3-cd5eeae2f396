# Requirements Document

## Introduction

本需求旨在補強訂閱系統中 `isUnpaid`（是否有未繳費用）狀態檢查機制，特別針對官網訂車流程中的待人工授信階段，以及收支登打與開立發票（accountSettlement）流程中的未繳費用狀態更新。

目前系統已有基本的 `isUnpaid` 檢查邏輯，但在特定業務場景下可能存在狀態更新不及時或檢查時機不當的問題，需要進行補強以確保費用狀態的準確性。

## Requirements

### Requirement 1

**User Story:** As a 系統管理員, I want 在官網訂車流程的待人工授信階段能正確檢查並更新 isUnpaid 狀態, so that 可以確保訂單的未繳費用狀態在人工授信決策時是準確的

#### Acceptance Criteria

1. WHEN 訂單進入待人工授信狀態（CREDIT_PENDING）THEN 系統 SHALL 自動檢查並更新該訂單的 isUnpaid 狀態
2. WHEN 人工授信通過（approveCredit）THEN 系統 SHALL 重新檢查並更新 isUnpaid 狀態
3. WHEN 人工授信不通過（rejectCredit）THEN 系統 SHALL 將 isUnpaid 設為 false
4. IF 訂單狀態小於 CREDITED THEN 系統 SHALL 將 isUnpaid 設為 false
5. WHEN 自動授信流程完成後 THEN 系統 SHALL 檢查並更新 isUnpaid 狀態

### Requirement 2 

**User Story:** As a 收銀台操作人員, I want 在執行收支登打與開立發票（accountSettlement）流程時能確保 isUnpaid 狀態即時更新, so that 可以準確反映訂單的實際付款狀態

#### Acceptance Criteria

1. WHEN accountSettlement 流程開始前 THEN 系統 SHALL 檢查當前訂單的 isUnpaid 狀態
2. WHEN 收支登打完成後 THEN 系統 SHALL 重新計算並更新 isUnpaid 狀態
3. WHEN 發票開立/作廢/折讓完成後 THEN 系統 SHALL 檢查並更新相關的 isUnpaid 狀態
4. WHEN payAndDiscountBalance 執行後 THEN 系統 SHALL 確保 isUnpaid 狀態反映最新的付款折扣情況
5. IF 收支登打過程中發生錯誤 THEN 系統 SHALL 保持 isUnpaid 狀態的一致性

### Requirement 3

**User Story:** As a 系統開發人員, I want 有統一的 isUnpaid 狀態檢查機制, so that 可以確保在各個業務流程中狀態更新的一致性和可靠性

#### Acceptance Criteria

1. WHEN 任何影響訂單費用的操作完成後 THEN 系統 SHALL 自動觸發 isUnpaid 狀態檢查
2. WHEN checkIsUnpaid 方法被調用 THEN 系統 SHALL 基於當前時間和應收款項計算準確的 isUnpaid 狀態
3. WHEN 訂單狀態變更時 THEN 系統 SHALL 根據新狀態適當更新 isUnpaid 狀態
4. IF 系統檢測到 isUnpaid 狀態不一致 THEN 系統 SHALL 記錄相關日誌並進行修正
5. WHEN 批次檢查任務執行時 THEN 系統 SHALL 能夠處理大量訂單的 isUnpaid 狀態更新

### Requirement 4

**User Story:** As a 業務人員, I want 能夠透過查詢介面準確獲取訂單的未繳費用狀態, so that 可以及時處理客戶的付款問題

#### Acceptance Criteria

1. WHEN 查詢訂單列表時 THEN 系統 SHALL 顯示準確的 isUnpaid 狀態
2. WHEN 匯出訂單 CSV 時 THEN 系統 SHALL 包含正確的 isUnpaid 狀態資訊
3. WHEN 訂單狀態發生變化時 THEN 查詢結果 SHALL 即時反映最新的 isUnpaid 狀態
4. IF 使用 isUnpaid 條件進行篩選 THEN 系統 SHALL 返回符合實際付款狀態的訂單
5. WHEN 系統進行定期狀態檢查時 THEN 所有相關查詢介面 SHALL 反映更新後的狀態