# Design Document

## Overview

本設計文件描述如何在兩個關鍵業務流程中補強 `isUnpaid` 狀態檢查：
1. **官網訂車 - 待人工授信階段**：確保在人工授信決策時有正確的未繳費用狀態
2. **收支登打 & 開立發票 (accountSettlement)**：確保收支登打流程中狀態即時更新

## Architecture

### Current System Analysis

當前系統已具備：
- `PriceInfoService.checkIsUnpaid()` - 核心檢查邏輯
- `OrderService.checkIsUnpaid()` - 訂單服務層封裝
- `Orders.isUnpaid` - 資料庫欄位儲存狀態

### Target Integration Points

```mermaid
graph LR
    A[官網訂車] --> B[待人工授信]
    B --> C[checkIsUnpaid]
    
    D[收支登打] --> E[accountSettlement]
    E --> F[checkIsUnpaid]
    
    C --> G[Orders.isUnpaid更新]
    F --> G
```

## Components and Interfaces

### 1. 待人工授信階段 - isUnpaid 狀態檢查

**位置**: 當訂單狀態為 `CREDIT_PENDING` 時的相關流程

**修改內容**:
```java
public class OrderService {
    
    // 在自動授信流程中，當需要人工授信時檢查狀態
    private void handleFailedAutoCredit(Orders order, AuthUser user, boolean isNotify, boolean needManualCredit) {
        if (!needManualCredit) {
            // ... 現有邏輯 ...
        } else {
            // 新增: 進入人工授信階段時檢查未繳狀態
            checkIsUnpaid(order);
            
            // 需要人工授信
            if (isNotify) {
                notifyService.notifyCreditDemand(order, user);
            }
        }
    }
    
    // 在人工授信通過後檢查未繳狀態
    private Orders approveCredit(Orders order, String memberId, CreditApprovalRequest request) {
        // 狀態檢查 - 確保是 CREDIT_PENDING 狀態
        if (order.getStatus() != CREDIT_PENDING.getStatus()) {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }
        
        // ... 現有邏輯 ...
        
        // 新增: 授信通過後檢查未繳狀態
        checkIsUnpaid(order);
        
        return orderRepository.save(order);
    }
    
    // 在人工授信不通過後設定狀態
    public Orders rejectCredit(CloseRequest closeRequest, String orderNo, String memberId) {
        Orders order = getOrder(orderNo);
        
        // 狀態檢查 - 確保是 CREDIT_PENDING 狀態
        if (order.getStatus() != CREDIT_PENDING.getStatus()) {
            throw new SubscribeException(NOT_CREDIT_ORDER);
        }
        
        // ... 現有邏輯 ...
        order.setIsUnpaid(false); // 確保拒絕後狀態正確
        
        return orderRepository.save(order);
    }
}
```

### 2. AccountSettlement 流程 - isUnpaid 狀態檢查

**位置**: `PaymentServiceV2.accountSettlement()` 和 `PaymentServiceV2.payAndDiscountBalance()`

**修改內容**:
```java
public class PaymentServiceV2 {
    
    @Transactional(transactionManager = "mysqlTransactionManager")
    public AccountSettlementResponse accountSettlement(AccountSettlementRequest request, String orderNo, String memberId) {
        // ... 現有邏輯 ...
        
        // 新增: 完成後重新檢查狀態
        Orders order = orderService.getOrder(orderNo);
        orderService.checkIsUnpaid(order);
        orderService.updateOrder(order);
        
        return response;
    }
    
    public void payAndDiscountBalance(String orderNo) {
        // ... 現有邏輯 ...
        
        // 新增: 付款折扣平衡後檢查狀態
        Orders order = orderService.getOrder(orderNo);
        orderService.checkIsUnpaid(order);
        orderService.updateOrder(order);
    }
}
```

## Data Models

無需修改現有資料模型，使用現有的 `Orders.isUnpaid` 欄位。

## Error Handling

使用現有的異常處理機制，確保在人工授信和 accountSettlement 流程中的異常不會影響 isUnpaid 狀態的正確性。

## Testing Strategy

### 1. Integration Tests in ContractInternalControllerTest

**測試範圍**:
- 人工授信通過/不通過後的 isUnpaid 狀態更新
- 收支登打流程中的 isUnpaid 狀態更新

**測試案例設計**:
```java
// 在 ContractInternalControllerTest 中新增測試方法

@Test
public void testApproveCredit_ShouldUpdateUnpaidStatus() {
    // 使用現有的測試流程建立訂單
    OrderTestContext context = createOrderTestContext();
    createContract(context);
    
    // 執行人工授信通過
    CreditApprovalRequest request = new CreditApprovalRequest("測試通過");
    Orders result = orderService.approveCredit(context.orderNo, MEMBER_ID, request);
    
    // 驗證 isUnpaid 狀態已更新
    assertNotNull(result.getIsUnpaid());
}

@Test
public void testAccountSettlement_ShouldUpdateUnpaidStatus() {
    // 使用現有的測試流程
    OrderTestContext context = createOrderTestContext();
    createContract(context);
    
    // 執行收支登打
    accountSettlementAndCheckOut(context, PayFor.Depart);
    
    // 驗證 isUnpaid 狀態已更新
    Orders order = orderService.getOrder(context.orderNo);
    assertNotNull(order.getIsUnpaid());
}
```

## Implementation Phases

### Phase 1: 人工授信流程補強
1. 在 `OrderService.handleFailedAutoCredit()` 中，當進入人工授信階段時新增 `checkIsUnpaid()` 調用
2. 在 `OrderService.approveCredit()` 中新增 `checkIsUnpaid()` 調用
3. 在 `OrderService.rejectCredit()` 中確保 `isUnpaid` 設為 false
4. 在 `ContractInternalControllerTest` 中新增相關測試

### Phase 2: AccountSettlement 流程補強
1. 在 `PaymentServiceV2.accountSettlement()` 中新增 `checkIsUnpaid()` 調用
2. 在 `PaymentServiceV2.payAndDiscountBalance()` 中新增 `checkIsUnpaid()` 調用
3. 在 `ContractInternalControllerTest` 中新增相關測試