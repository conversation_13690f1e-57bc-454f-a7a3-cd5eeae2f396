## 拆分提交的判斷標準
- 不同的關注點或功能
- 混合不同類型的變更
- 檔案模式差異過大
- 變更過於龐大

## 選項
如果想要分析特定的變更，可以先手動暫存檔案：
```
git add <特定檔案>
/commit
```

---
description: "分析變更並生成符合慣例標準的正體中文 Git 提交訊息"
allowed-tools:
[
"Bash(git status:*)",
"Bash(git diff:*)",
"Bash(git log:*)",
]
---

# Claude 指令：提交訊息生成

分析 Git 變更並生成符合慣例標準且使用正體中文的提交訊息，但不執行實際提交。

## 使用方式
```
/commit
/commit --no-verify
```

## 執行流程
1. 執行 `git status` 檢查檔案狀態
2. 執行 `git diff --staged` 分析已暫存的變更
3. 如果沒有暫存檔案，分析 `git diff` 的變更
4. 分析變更內容，檢查是否包含多個邏輯變更
5. 如有需要，建議拆分提交
6. **僅生成**符合慣例格式的提交訊息，不執行實際提交
7. 提供建議的 git commit 命令供手動執行

## 提交格式
`<類型>: <描述>`

**類型定義：**
- `feat`: 新功能
- `fix`: 修復錯誤
- `docs`: 文件更新
- `style`: 程式碼格式調整
- `refactor`: 程式碼重構
- `perf`: 效能優化
- `test`: 測試相關
- `chore`: 建置工具或輔助工具的變動
- `ci`: 持續整合相關
- `security`: 安全性修復

**撰寫規則：**
- 使用正體中文
- 採用祈使語氣（例如：「新增」而非「已新增」）
- 第一行不超過 72 個字元
- 每個提交應該是原子性的（單一目的）
- 將不相關的變更分開提交
- 描述要清楚具體，說明做了什麼而非為什麼

## 重要注意事項
- **只生成提交訊息**，不執行實際的 git commit
- **不使用表情符號**，保持簡潔的文字格式
- **絕對不要**在提交訊息中新增 Claude 共同作者註腳
- 會分析已暫存和未暫存的變更
- 分析差異並建議是否需要拆分提交
- 優先考慮提交的原子性和清晰度
- 確保提交訊息對團隊成員具有良好的可讀性
- 提供完整的 git commit 命令供手動執行

## 輸出範例
```
建議的提交訊息：
feat: 新增使用者登入功能

執行命令：
git commit -m "feat: 新增使用者登入功能"
```

## 提交訊息範例
```
feat: 新增使用者登入功能
fix: 修復購物車計算錯誤
docs: 更新 API 文件
refactor: 重構使用者服務層
perf: 優化資料庫查詢效能
test: 新增使用者註冊測試案例
chore: 更新相依套件版本
```