# Design Document

## Overview

The account settlement validation failure is caused by incorrect calculation logic in the `CheckoutService.validateAmounts()` method. Based on the log analysis, the system is failing to properly aggregate payment amounts and transaction item totals, particularly when handling refunds and multiple payment types. This design addresses the core validation logic, enhanced logging, and robust error handling.

## Architecture

The solution focuses on three main components:

1. **Enhanced Validation Logic** - Improved calculation methods in `CheckoutService`
2. **Comprehensive Logging Framework** - Detailed financial calculation logging
3. **Robust Error Handling** - Better error messages and tolerance handling

### Current Flow Analysis

From the logs, the current flow shows:
- Order M202507219932 has multiple price info items (73880: 20400, 73882: 2898, 73883: 2999, 73884: -2898)
- Account record shows amount: 26297 with refund: 2898
- Invoice amounts: 20400 + 2999 = 23399
- The validation is failing because the calculation logic isn't properly handling the refund component

## Components and Interfaces

### 1. Enhanced CheckoutService

Modify the existing `CheckoutService.validateAmounts()` method to:

```java
// In existing CheckoutService class
private void validateAmounts(String orderNo, 
                           List<AccountRecord> accountRecords, 
                           List<OrderPriceInfo> priceInfos) {
    
    // Calculate payment amounts with detailed logging
    BigDecimal totalPaymentAmount = calculateTotalPaymentAmount(accountRecords, orderNo);
    
    // Calculate transaction amounts with detailed logging  
    BigDecimal totalTransactionAmount = calculateTotalTransactionAmount(priceInfos, orderNo);
    
    // Compare with tolerance and detailed error reporting
    validateAmountMatch(totalPaymentAmount, totalTransactionAmount, orderNo, accountRecords, priceInfos);
}

private BigDecimal calculateTotalPaymentAmount(List<AccountRecord> records, String orderNo) {
    // Enhanced calculation with logging
}

private BigDecimal calculateTotalTransactionAmount(List<OrderPriceInfo> priceInfos, String orderNo) {
    // Enhanced calculation with logging
}

private void validateAmountMatch(BigDecimal paymentAmount, BigDecimal transactionAmount, 
                               String orderNo, List<AccountRecord> accountRecords, 
                               List<OrderPriceInfo> priceInfos) {
    // Enhanced validation with detailed error messages
}
```

## Data Models

### Enhanced AccountRecord Processing

The current issue shows that account records contain:
- `amount`: 26297 (total payment amount)
- `refundAmount`: 2898 (refund component)
- `orderPriceAmounts`: JSON mapping of price info IDs to amounts

The validation logic needs to properly interpret these fields:

```java
public class AccountAmountCalculator {
    
    public BigDecimal calculateNetPaymentAmount(AccountRecord record) {
        // For the failing case: 26297 - 2898 = 23399
        return record.getAmount().subtract(record.getRefundAmount());
    }
    
    public Map<Long, BigDecimal> parseOrderPriceAmounts(String orderPriceAmountsJson) {
        // Parse: {"73880":20400,"73883":2999,"73882":2898,"73884":-2898}
        // Handle negative amounts correctly
    }
}
```

### Transaction Amount Aggregation

```java
public class TransactionAmountCalculator {
    
    public BigDecimal calculateNetTransactionAmount(List<OrderPriceInfo> priceInfos) {
        // Sum: 20400 + 2999 + 2898 + (-2898) = 23399
        return priceInfos.stream()
            .map(OrderPriceInfo::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
```

## Error Handling

### Detailed Error Messages

Instead of generic "付款金額和其下的交易項目金額合計不同", provide:

```java
public class ValidationErrorBuilder {
    
    public String buildDetailedErrorMessage(ValidationResult result) {
        return String.format(
            "Payment validation failed for order %s:\n" +
            "Payment Amount: %s (Total: %s, Refunds: %s)\n" +
            "Transaction Amount: %s (Positive: %s, Negative: %s)\n" +
            "Difference: %s\n" +
            "Payment Breakdown: %s\n" +
            "Transaction Breakdown: %s",
            // ... format with actual values
        );
    }
}
```

### Tolerance Handling

```java
public class AmountComparator {
    
    private static final BigDecimal TOLERANCE = new BigDecimal("0.01");
    
    public boolean amountsMatch(BigDecimal amount1, BigDecimal amount2) {
        BigDecimal difference = amount1.subtract(amount2).abs();
        return difference.compareTo(TOLERANCE) <= 0;
    }
}
```

## Testing Strategy

### Unit Tests

1. **Payment Calculation Tests**
   - Test single payment records
   - Test multiple payment records with refunds
   - Test ETag payment integration
   - Test edge cases with zero amounts

2. **Transaction Calculation Tests**
   - Test positive-only amounts
   - Test mixed positive/negative amounts
   - Test refund scenarios
   - Test empty transaction lists

3. **Validation Logic Tests**
   - Test exact matches
   - Test within-tolerance differences
   - Test out-of-tolerance differences
   - Test detailed error message generation

### Integration Tests

1. **End-to-End Settlement Tests**
   - Test complete settlement flow with valid data
   - Test settlement flow with the failing scenario from logs
   - Test settlement with various payment combinations

2. **Database Integration Tests**
   - Test with real order data structures
   - Test with complex account record scenarios
   - Test with invoice integration

### Performance Considerations

The enhanced logging and calculation logic should not significantly impact performance:

- Use conditional logging based on log levels
- Cache calculation results where appropriate
- Optimize database queries for price info and account records
- Consider async logging for detailed financial calculations

## Implementation Approach

1. **Phase 1**: Fix core validation logic
   - Implement correct payment amount calculation
   - Implement correct transaction amount calculation
   - Add basic tolerance handling

2. **Phase 2**: Enhanced logging and error reporting
   - Add detailed calculation logging
   - Implement comprehensive error messages
   - Add validation result tracking

3. **Phase 3**: Comprehensive testing
   - Unit tests for all calculation components
   - Integration tests with real data scenarios
   - Performance testing and optimization