FROM asia-east1-docker.pkg.dev/sincere-strata-288408/carplus/subscribe/cache as build

ARG SONARHOST
ARG SONARTOKEN

WORKDIR /app

COPY . .

RUN mvn package -DskipTests -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dmaven.wagon.http.ssl.ignore.validity.dates=true \
    && mvn dependency:copy-dependencies -DoutputDirectory=/app/lib -DincludeScope=runtime \
    && mvn verify sonar:sonar -Dsonar.projectKey=subscribe -Dsonar.host.url=${SONARHOST} -Dsonar.login=${SONARTOKEN} -DskipTests

FROM asia-east1-docker.pkg.dev/sincere-strata-288408/carplus/share/java-base-image:1.8.0r

WORKDIR /app

COPY --from=build /app/lib ./lib
COPY --from=build /app/target/*.jar ./subscribe.jar
